name: Jsonnet

on:
  push:
    branches:
    - main
    - release-*
  pull_request:
    branches:
    - main
    - release-*
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              "*.jsonnet",
              "*.libsonnet",
              ".github/workflows/jsonnet.yml",
              ".go-version",
              "deploy/**"
            ]
          skip_after_successful_duplicate: false

  build:
    name: Jsonnet Build
    needs: skip-check
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version
          check-latest: true

      - name: Set up Jsonnet
        run: ./env-jsonnet.sh

      - name: Generate
        run: cd deploy && make --always-make vendor manifests

      - name: Archive generated manifests
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: manifests
          path: |
            deploy/manifests
