// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/metastore/v1alpha1/metastore.proto

package metastorev1alpha1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetOrCreateMappingsRequest contains all information about mappings that are
// requested to be retrieved or created if they don't already exist.
type GetOrCreateMappingsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Mappings to be created or retrieved.
	Mappings      []*Mapping `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateMappingsRequest) Reset() {
	*x = GetOrCreateMappingsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateMappingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateMappingsRequest) ProtoMessage() {}

func (x *GetOrCreateMappingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateMappingsRequest.ProtoReflect.Descriptor instead.
func (*GetOrCreateMappingsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{0}
}

func (x *GetOrCreateMappingsRequest) GetMappings() []*Mapping {
	if x != nil {
		return x.Mappings
	}
	return nil
}

// GetOrCreateMappingsResponse contains information about mappings requested.
type GetOrCreateMappingsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Mappings that are known to the backing metastore. If any mappings didn't
	// exist before the request they have now been persisted and are uniquely
	// identifyable through their key.
	Mappings      []*Mapping `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateMappingsResponse) Reset() {
	*x = GetOrCreateMappingsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateMappingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateMappingsResponse) ProtoMessage() {}

func (x *GetOrCreateMappingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateMappingsResponse.ProtoReflect.Descriptor instead.
func (*GetOrCreateMappingsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrCreateMappingsResponse) GetMappings() []*Mapping {
	if x != nil {
		return x.Mappings
	}
	return nil
}

// GetOrCreateFunctionsRequest contains all information about functions that are
// requested to be retrieved or created if they don't already exist.
type GetOrCreateFunctionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Functions to be created or retrieved.
	Functions     []*Function `protobuf:"bytes,1,rep,name=functions,proto3" json:"functions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateFunctionsRequest) Reset() {
	*x = GetOrCreateFunctionsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateFunctionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateFunctionsRequest) ProtoMessage() {}

func (x *GetOrCreateFunctionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateFunctionsRequest.ProtoReflect.Descriptor instead.
func (*GetOrCreateFunctionsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{2}
}

func (x *GetOrCreateFunctionsRequest) GetFunctions() []*Function {
	if x != nil {
		return x.Functions
	}
	return nil
}

// GetOrCreateFunctionsResponse contains information about functions requested.
type GetOrCreateFunctionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Functions that are known to the backing metastore. If any functions didn't
	// exist before the request they have now been persisted and are uniquely
	// identifyable through their key.
	Functions     []*Function `protobuf:"bytes,1,rep,name=functions,proto3" json:"functions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateFunctionsResponse) Reset() {
	*x = GetOrCreateFunctionsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateFunctionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateFunctionsResponse) ProtoMessage() {}

func (x *GetOrCreateFunctionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateFunctionsResponse.ProtoReflect.Descriptor instead.
func (*GetOrCreateFunctionsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{3}
}

func (x *GetOrCreateFunctionsResponse) GetFunctions() []*Function {
	if x != nil {
		return x.Functions
	}
	return nil
}

// GetOrCreateLocationsRequest contains all information about locations that are
// requested to be retrieved or created if they don't already exist.
type GetOrCreateLocationsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Locations to be created or retrieved.
	Locations     []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateLocationsRequest) Reset() {
	*x = GetOrCreateLocationsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateLocationsRequest) ProtoMessage() {}

func (x *GetOrCreateLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateLocationsRequest.ProtoReflect.Descriptor instead.
func (*GetOrCreateLocationsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{4}
}

func (x *GetOrCreateLocationsRequest) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

// GetOrCreateLocationsResponse contains information about locations requested.
type GetOrCreateLocationsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Locations that are known to the backing metastore. If any locations didn't
	// exist before the request they have now been persisted and are uniquely
	// identifyable through their key.
	Locations     []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateLocationsResponse) Reset() {
	*x = GetOrCreateLocationsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateLocationsResponse) ProtoMessage() {}

func (x *GetOrCreateLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateLocationsResponse.ProtoReflect.Descriptor instead.
func (*GetOrCreateLocationsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{5}
}

func (x *GetOrCreateLocationsResponse) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

// GetOrCreateStracktracesRequest contains all information about stacktraces
// that are requested to be retrieved or created if they don't already exist.
type GetOrCreateStacktracesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Stacktraces to be created or retrieved.
	Stacktraces   []*Stacktrace `protobuf:"bytes,1,rep,name=stacktraces,proto3" json:"stacktraces,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateStacktracesRequest) Reset() {
	*x = GetOrCreateStacktracesRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateStacktracesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateStacktracesRequest) ProtoMessage() {}

func (x *GetOrCreateStacktracesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateStacktracesRequest.ProtoReflect.Descriptor instead.
func (*GetOrCreateStacktracesRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{6}
}

func (x *GetOrCreateStacktracesRequest) GetStacktraces() []*Stacktrace {
	if x != nil {
		return x.Stacktraces
	}
	return nil
}

// GetOrCreateStacktracesResponse contains information about locations requested.
type GetOrCreateStacktracesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Stacktraces that are known to the backing metastore. If any stacktraces
	// didn't exist before the request they have now been persisted and are
	// uniquely identifyable through their key.
	Stacktraces   []*Stacktrace `protobuf:"bytes,1,rep,name=stacktraces,proto3" json:"stacktraces,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrCreateStacktracesResponse) Reset() {
	*x = GetOrCreateStacktracesResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrCreateStacktracesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrCreateStacktracesResponse) ProtoMessage() {}

func (x *GetOrCreateStacktracesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrCreateStacktracesResponse.ProtoReflect.Descriptor instead.
func (*GetOrCreateStacktracesResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{7}
}

func (x *GetOrCreateStacktracesResponse) GetStacktraces() []*Stacktrace {
	if x != nil {
		return x.Stacktraces
	}
	return nil
}

// UnsymbolizedLocationsRequest contains information about the unsymbolized
// locations requested. While currently empty, this could in the future contain
// a sharding configuration or limit the number of locations to return.
type UnsymbolizedLocationsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The maximum number of locations to return.
	Limit uint32 `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	// The minimum key to start returning locations from.
	MinKey        string `protobuf:"bytes,2,opt,name=min_key,json=minKey,proto3" json:"min_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsymbolizedLocationsRequest) Reset() {
	*x = UnsymbolizedLocationsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsymbolizedLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsymbolizedLocationsRequest) ProtoMessage() {}

func (x *UnsymbolizedLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsymbolizedLocationsRequest.ProtoReflect.Descriptor instead.
func (*UnsymbolizedLocationsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{8}
}

func (x *UnsymbolizedLocationsRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *UnsymbolizedLocationsRequest) GetMinKey() string {
	if x != nil {
		return x.MinKey
	}
	return ""
}

// UnsymbolizedLocationsResponse contains information about the requested
// locations that should be symbolizable but potentially haven't been
// symbolized yet.
type UnsymbolizedLocationsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Locations that have a mapping and address that should be symbolizable.
	Locations []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	// Key of the last location returned. This can be used in a subsequent call
	// to UnsymbolizedLocations to continue from the last returned location.
	MaxKey        string `protobuf:"bytes,2,opt,name=max_key,json=maxKey,proto3" json:"max_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsymbolizedLocationsResponse) Reset() {
	*x = UnsymbolizedLocationsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsymbolizedLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsymbolizedLocationsResponse) ProtoMessage() {}

func (x *UnsymbolizedLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsymbolizedLocationsResponse.ProtoReflect.Descriptor instead.
func (*UnsymbolizedLocationsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{9}
}

func (x *UnsymbolizedLocationsResponse) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *UnsymbolizedLocationsResponse) GetMaxKey() string {
	if x != nil {
		return x.MaxKey
	}
	return ""
}

// CreateLocationLinesRequest contains locations and their location lines to be
// saved.
type CreateLocationLinesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Locations that have location lines to be saved.
	Locations     []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLocationLinesRequest) Reset() {
	*x = CreateLocationLinesRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLocationLinesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLocationLinesRequest) ProtoMessage() {}

func (x *CreateLocationLinesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLocationLinesRequest.ProtoReflect.Descriptor instead.
func (*CreateLocationLinesRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{10}
}

func (x *CreateLocationLinesRequest) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

// CreateLocationLinesResponse details about the location lines creation.
type CreateLocationLinesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLocationLinesResponse) Reset() {
	*x = CreateLocationLinesResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLocationLinesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLocationLinesResponse) ProtoMessage() {}

func (x *CreateLocationLinesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLocationLinesResponse.ProtoReflect.Descriptor instead.
func (*CreateLocationLinesResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{11}
}

// StacktracesRequest contains information about the stacktraces requested.
type StacktracesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IDs of stacktraces to retrieve.
	StacktraceIds []string `protobuf:"bytes,1,rep,name=stacktrace_ids,json=stacktraceIds,proto3" json:"stacktrace_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StacktracesRequest) Reset() {
	*x = StacktracesRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StacktracesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StacktracesRequest) ProtoMessage() {}

func (x *StacktracesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StacktracesRequest.ProtoReflect.Descriptor instead.
func (*StacktracesRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{12}
}

func (x *StacktracesRequest) GetStacktraceIds() []string {
	if x != nil {
		return x.StacktraceIds
	}
	return nil
}

// StacktracesRequest contains the requested stacktraces.
type StacktracesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Stacktraces that are known to the backing metastore.
	Stacktraces   []*Stacktrace `protobuf:"bytes,1,rep,name=stacktraces,proto3" json:"stacktraces,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StacktracesResponse) Reset() {
	*x = StacktracesResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StacktracesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StacktracesResponse) ProtoMessage() {}

func (x *StacktracesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StacktracesResponse.ProtoReflect.Descriptor instead.
func (*StacktracesResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{13}
}

func (x *StacktracesResponse) GetStacktraces() []*Stacktrace {
	if x != nil {
		return x.Stacktraces
	}
	return nil
}

// LocationsRequest contains information about the locations requested.
type LocationsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IDs of locations to retrieve.
	LocationIds   []string `protobuf:"bytes,1,rep,name=location_ids,json=locationIds,proto3" json:"location_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationsRequest) Reset() {
	*x = LocationsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationsRequest) ProtoMessage() {}

func (x *LocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationsRequest.ProtoReflect.Descriptor instead.
func (*LocationsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{14}
}

func (x *LocationsRequest) GetLocationIds() []string {
	if x != nil {
		return x.LocationIds
	}
	return nil
}

// LocationsResponse contains the requested locations.
type LocationsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Locations that are known to the backing metastore.
	Locations     []*Location `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationsResponse) Reset() {
	*x = LocationsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationsResponse) ProtoMessage() {}

func (x *LocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationsResponse.ProtoReflect.Descriptor instead.
func (*LocationsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{15}
}

func (x *LocationsResponse) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

// LocationLinesRequest contains information about the location's lines requested.
type LocationLinesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IDs of locations to retrieve location lines for.
	LocationIds   []string `protobuf:"bytes,1,rep,name=location_ids,json=locationIds,proto3" json:"location_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocationLinesRequest) Reset() {
	*x = LocationLinesRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocationLinesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationLinesRequest) ProtoMessage() {}

func (x *LocationLinesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationLinesRequest.ProtoReflect.Descriptor instead.
func (*LocationLinesRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{16}
}

func (x *LocationLinesRequest) GetLocationIds() []string {
	if x != nil {
		return x.LocationIds
	}
	return nil
}

// FunctionsRequest contains information about the functions requested.
type FunctionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IDs of functions to retrieve.
	FunctionIds   []string `protobuf:"bytes,1,rep,name=function_ids,json=functionIds,proto3" json:"function_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FunctionsRequest) Reset() {
	*x = FunctionsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FunctionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionsRequest) ProtoMessage() {}

func (x *FunctionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionsRequest.ProtoReflect.Descriptor instead.
func (*FunctionsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{17}
}

func (x *FunctionsRequest) GetFunctionIds() []string {
	if x != nil {
		return x.FunctionIds
	}
	return nil
}

// FunctionsResponse contains the requested functions.
type FunctionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Functions that are known to the backing metastore.
	Functions     []*Function `protobuf:"bytes,1,rep,name=functions,proto3" json:"functions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FunctionsResponse) Reset() {
	*x = FunctionsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FunctionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionsResponse) ProtoMessage() {}

func (x *FunctionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionsResponse.ProtoReflect.Descriptor instead.
func (*FunctionsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{18}
}

func (x *FunctionsResponse) GetFunctions() []*Function {
	if x != nil {
		return x.Functions
	}
	return nil
}

// MappingsRequest contains information about the mappings requested.
type MappingsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IDs of mappings to retrieve.
	MappingIds    []string `protobuf:"bytes,1,rep,name=mapping_ids,json=mappingIds,proto3" json:"mapping_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MappingsRequest) Reset() {
	*x = MappingsRequest{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MappingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MappingsRequest) ProtoMessage() {}

func (x *MappingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MappingsRequest.ProtoReflect.Descriptor instead.
func (*MappingsRequest) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{19}
}

func (x *MappingsRequest) GetMappingIds() []string {
	if x != nil {
		return x.MappingIds
	}
	return nil
}

// MappingsResponse contains the requested mappings.
type MappingsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Mappings that are known to the backing metastore.
	Mappings      []*Mapping `protobuf:"bytes,1,rep,name=mappings,proto3" json:"mappings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MappingsResponse) Reset() {
	*x = MappingsResponse{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MappingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MappingsResponse) ProtoMessage() {}

func (x *MappingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MappingsResponse.ProtoReflect.Descriptor instead.
func (*MappingsResponse) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{20}
}

func (x *MappingsResponse) GetMappings() []*Mapping {
	if x != nil {
		return x.Mappings
	}
	return nil
}

// Sample is a stack trace with optional labels.
type Sample struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// stacktrace_id references stack trace of the sample.
	StacktraceId string `protobuf:"bytes,1,opt,name=stacktrace_id,json=stacktraceId,proto3" json:"stacktrace_id,omitempty"`
	// labels are extra labels for a stack trace.
	Labels map[string]*SampleLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// num_labels are the num of labels.
	NumLabels map[string]*SampleNumLabel `protobuf:"bytes,3,rep,name=num_labels,json=numLabels,proto3" json:"num_labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// num_units are the units for the labels.
	NumUnits      map[string]*SampleNumUnit `protobuf:"bytes,4,rep,name=num_units,json=numUnits,proto3" json:"num_units,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Sample) Reset() {
	*x = Sample{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sample) ProtoMessage() {}

func (x *Sample) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sample.ProtoReflect.Descriptor instead.
func (*Sample) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{21}
}

func (x *Sample) GetStacktraceId() string {
	if x != nil {
		return x.StacktraceId
	}
	return ""
}

func (x *Sample) GetLabels() map[string]*SampleLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Sample) GetNumLabels() map[string]*SampleNumLabel {
	if x != nil {
		return x.NumLabels
	}
	return nil
}

func (x *Sample) GetNumUnits() map[string]*SampleNumUnit {
	if x != nil {
		return x.NumUnits
	}
	return nil
}

// Stacktrace is a collection of locations.
type Stacktrace struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// stacktrace_id references stack trace of the stacktrace.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// locations are the locations in the stack trace.
	LocationIds   []string `protobuf:"bytes,2,rep,name=location_ids,json=locationIds,proto3" json:"location_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Stacktrace) Reset() {
	*x = Stacktrace{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Stacktrace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stacktrace) ProtoMessage() {}

func (x *Stacktrace) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stacktrace.ProtoReflect.Descriptor instead.
func (*Stacktrace) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{22}
}

func (x *Stacktrace) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Stacktrace) GetLocationIds() []string {
	if x != nil {
		return x.LocationIds
	}
	return nil
}

// SampleLabel are the labels added to a Sample.
type SampleLabel struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// labels for a label in a Sample.
	Labels        []string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SampleLabel) Reset() {
	*x = SampleLabel{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleLabel) ProtoMessage() {}

func (x *SampleLabel) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleLabel.ProtoReflect.Descriptor instead.
func (*SampleLabel) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{23}
}

func (x *SampleLabel) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// SampleNumLabel are the num of labels of a Sample.
type SampleNumLabel struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// num_labels are the num_label of a Sample.
	NumLabels     []int64 `protobuf:"varint,1,rep,packed,name=num_labels,json=numLabels,proto3" json:"num_labels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SampleNumLabel) Reset() {
	*x = SampleNumLabel{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleNumLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleNumLabel) ProtoMessage() {}

func (x *SampleNumLabel) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleNumLabel.ProtoReflect.Descriptor instead.
func (*SampleNumLabel) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{24}
}

func (x *SampleNumLabel) GetNumLabels() []int64 {
	if x != nil {
		return x.NumLabels
	}
	return nil
}

// SampleNumUnit are the num units of a Sample.
type SampleNumUnit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// units of a labels of a Sample.
	Units         []string `protobuf:"bytes,1,rep,name=units,proto3" json:"units,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SampleNumUnit) Reset() {
	*x = SampleNumUnit{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleNumUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleNumUnit) ProtoMessage() {}

func (x *SampleNumUnit) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleNumUnit.ProtoReflect.Descriptor instead.
func (*SampleNumUnit) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{25}
}

func (x *SampleNumUnit) GetUnits() []string {
	if x != nil {
		return x.Units
	}
	return nil
}

// Location describes a single location of a stack traces.
type Location struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is the unique identifier for the location.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// address is the memory address of the location if present.
	Address uint64 `protobuf:"varint,2,opt,name=address,proto3" json:"address,omitempty"`
	// mapping_id is the unique identifier for the mapping associated with the location.
	MappingId string `protobuf:"bytes,4,opt,name=mapping_id,json=mappingId,proto3" json:"mapping_id,omitempty"`
	// is_folded indicates whether the location is folded into the previous location.
	IsFolded bool `protobuf:"varint,5,opt,name=is_folded,json=isFolded,proto3" json:"is_folded,omitempty"`
	// lines are the call frames represented by this location. Multiple lines
	// indicate they have been inlined.
	Lines []*Line `protobuf:"bytes,6,rep,name=lines,proto3" json:"lines,omitempty"`
	// mapping_index has the index into the mapping table where mappings are sent deduplicated.
	MappingIndex  uint32 `protobuf:"varint,7,opt,name=mapping_index,json=mappingIndex,proto3" json:"mapping_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Location) Reset() {
	*x = Location{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{26}
}

func (x *Location) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Location) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *Location) GetMappingId() string {
	if x != nil {
		return x.MappingId
	}
	return ""
}

func (x *Location) GetIsFolded() bool {
	if x != nil {
		return x.IsFolded
	}
	return false
}

func (x *Location) GetLines() []*Line {
	if x != nil {
		return x.Lines
	}
	return nil
}

func (x *Location) GetMappingIndex() uint32 {
	if x != nil {
		return x.MappingIndex
	}
	return 0
}

// Line describes a source code function and its line number.
type Line struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// function_id is the ID of the function.
	FunctionId string `protobuf:"bytes,1,opt,name=function_id,json=functionId,proto3" json:"function_id,omitempty"`
	// line is the line number in the source file of the referenced function.
	Line int64 `protobuf:"varint,2,opt,name=line,proto3" json:"line,omitempty"`
	// function_index is the index in the functions table.
	FunctionIndex uint32 `protobuf:"varint,3,opt,name=function_index,json=functionIndex,proto3" json:"function_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Line) Reset() {
	*x = Line{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Line) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Line) ProtoMessage() {}

func (x *Line) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Line.ProtoReflect.Descriptor instead.
func (*Line) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{27}
}

func (x *Line) GetFunctionId() string {
	if x != nil {
		return x.FunctionId
	}
	return ""
}

func (x *Line) GetLine() int64 {
	if x != nil {
		return x.Line
	}
	return 0
}

func (x *Line) GetFunctionIndex() uint32 {
	if x != nil {
		return x.FunctionIndex
	}
	return 0
}

// Function describes metadata of a source code function.
type Function struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is the unique identifier for the function.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// start_line is the line number in the source file of the first line of the function.
	StartLine int64 `protobuf:"varint,2,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	// name is the name of the function.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// system_name describes the name of the function, as identified by the
	// system. For instance, it can be a C++ mangled name.
	SystemName string `protobuf:"bytes,4,opt,name=system_name,json=systemName,proto3" json:"system_name,omitempty"`
	// filename is the name of the source file of the function.
	Filename string `protobuf:"bytes,5,opt,name=filename,proto3" json:"filename,omitempty"`
	// name_string_index is the index in the string table to the name associated with the function.
	NameStringIndex uint32 `protobuf:"varint,6,opt,name=name_string_index,json=nameStringIndex,proto3" json:"name_string_index,omitempty"`
	// system_name_string_index is the index in the string table to the system_name associated with the function.
	SystemNameStringIndex uint32 `protobuf:"varint,7,opt,name=system_name_string_index,json=systemNameStringIndex,proto3" json:"system_name_string_index,omitempty"`
	// filename_string_index is the index in the string table to the filename associated with the function.
	FilenameStringIndex uint32 `protobuf:"varint,8,opt,name=filename_string_index,json=filenameStringIndex,proto3" json:"filename_string_index,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Function) Reset() {
	*x = Function{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Function) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Function) ProtoMessage() {}

func (x *Function) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Function.ProtoReflect.Descriptor instead.
func (*Function) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{28}
}

func (x *Function) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Function) GetStartLine() int64 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *Function) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Function) GetSystemName() string {
	if x != nil {
		return x.SystemName
	}
	return ""
}

func (x *Function) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *Function) GetNameStringIndex() uint32 {
	if x != nil {
		return x.NameStringIndex
	}
	return 0
}

func (x *Function) GetSystemNameStringIndex() uint32 {
	if x != nil {
		return x.SystemNameStringIndex
	}
	return 0
}

func (x *Function) GetFilenameStringIndex() uint32 {
	if x != nil {
		return x.FilenameStringIndex
	}
	return 0
}

// Mapping describes a memory mapping.
type Mapping struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is the unique identifier for the mapping.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// start is the start address of the mapping.
	Start uint64 `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	// limit is the length of the address space of the mapping.
	Limit uint64 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	// offset in the binary that corresponds to the first mapped address.
	Offset uint64 `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	// file is the name of the file associated with the mapping.
	File string `protobuf:"bytes,5,opt,name=file,proto3" json:"file,omitempty"`
	// build_id is the build ID of the mapping.
	BuildId string `protobuf:"bytes,6,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// has_functions indicates whether the mapping has associated functions.
	HasFunctions bool `protobuf:"varint,7,opt,name=has_functions,json=hasFunctions,proto3" json:"has_functions,omitempty"`
	// has_filenames indicates whether the mapping has associated filenames.
	HasFilenames bool `protobuf:"varint,8,opt,name=has_filenames,json=hasFilenames,proto3" json:"has_filenames,omitempty"`
	// has_line_numbers indicates whether the mapping has associated line numbers.
	HasLineNumbers bool `protobuf:"varint,9,opt,name=has_line_numbers,json=hasLineNumbers,proto3" json:"has_line_numbers,omitempty"`
	// has_inline_frames indicates whether the mapping has associated inline frames.
	HasInlineFrames bool `protobuf:"varint,10,opt,name=has_inline_frames,json=hasInlineFrames,proto3" json:"has_inline_frames,omitempty"`
	// fileStringIndex is the index in the string table to the file name associated with the mapping.
	FileStringIndex uint32 `protobuf:"varint,11,opt,name=file_string_index,json=fileStringIndex,proto3" json:"file_string_index,omitempty"`
	// build_id_string_index is the index in the string table to the build ID of the mapping.
	BuildIdStringIndex uint32 `protobuf:"varint,12,opt,name=build_id_string_index,json=buildIdStringIndex,proto3" json:"build_id_string_index,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Mapping) Reset() {
	*x = Mapping{}
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mapping) ProtoMessage() {}

func (x *Mapping) ProtoReflect() protoreflect.Message {
	mi := &file_parca_metastore_v1alpha1_metastore_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mapping.ProtoReflect.Descriptor instead.
func (*Mapping) Descriptor() ([]byte, []int) {
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP(), []int{29}
}

func (x *Mapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Mapping) GetStart() uint64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *Mapping) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *Mapping) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *Mapping) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *Mapping) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *Mapping) GetHasFunctions() bool {
	if x != nil {
		return x.HasFunctions
	}
	return false
}

func (x *Mapping) GetHasFilenames() bool {
	if x != nil {
		return x.HasFilenames
	}
	return false
}

func (x *Mapping) GetHasLineNumbers() bool {
	if x != nil {
		return x.HasLineNumbers
	}
	return false
}

func (x *Mapping) GetHasInlineFrames() bool {
	if x != nil {
		return x.HasInlineFrames
	}
	return false
}

func (x *Mapping) GetFileStringIndex() uint32 {
	if x != nil {
		return x.FileStringIndex
	}
	return 0
}

func (x *Mapping) GetBuildIdStringIndex() uint32 {
	if x != nil {
		return x.BuildIdStringIndex
	}
	return 0
}

var File_parca_metastore_v1alpha1_metastore_proto protoreflect.FileDescriptor

const file_parca_metastore_v1alpha1_metastore_proto_rawDesc = "" +
	"\n" +
	"(parca/metastore/v1alpha1/metastore.proto\x12\x18parca.metastore.v1alpha1\"[\n" +
	"\x1aGetOrCreateMappingsRequest\x12=\n" +
	"\bmappings\x18\x01 \x03(\v2!.parca.metastore.v1alpha1.MappingR\bmappings\"\\\n" +
	"\x1bGetOrCreateMappingsResponse\x12=\n" +
	"\bmappings\x18\x01 \x03(\v2!.parca.metastore.v1alpha1.MappingR\bmappings\"_\n" +
	"\x1bGetOrCreateFunctionsRequest\x12@\n" +
	"\tfunctions\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.FunctionR\tfunctions\"`\n" +
	"\x1cGetOrCreateFunctionsResponse\x12@\n" +
	"\tfunctions\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.FunctionR\tfunctions\"_\n" +
	"\x1bGetOrCreateLocationsRequest\x12@\n" +
	"\tlocations\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.LocationR\tlocations\"`\n" +
	"\x1cGetOrCreateLocationsResponse\x12@\n" +
	"\tlocations\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.LocationR\tlocations\"g\n" +
	"\x1dGetOrCreateStacktracesRequest\x12F\n" +
	"\vstacktraces\x18\x01 \x03(\v2$.parca.metastore.v1alpha1.StacktraceR\vstacktraces\"h\n" +
	"\x1eGetOrCreateStacktracesResponse\x12F\n" +
	"\vstacktraces\x18\x01 \x03(\v2$.parca.metastore.v1alpha1.StacktraceR\vstacktraces\"M\n" +
	"\x1cUnsymbolizedLocationsRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\rR\x05limit\x12\x17\n" +
	"\amin_key\x18\x02 \x01(\tR\x06minKey\"z\n" +
	"\x1dUnsymbolizedLocationsResponse\x12@\n" +
	"\tlocations\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.LocationR\tlocations\x12\x17\n" +
	"\amax_key\x18\x02 \x01(\tR\x06maxKey\"^\n" +
	"\x1aCreateLocationLinesRequest\x12@\n" +
	"\tlocations\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.LocationR\tlocations\"\x1d\n" +
	"\x1bCreateLocationLinesResponse\";\n" +
	"\x12StacktracesRequest\x12%\n" +
	"\x0estacktrace_ids\x18\x01 \x03(\tR\rstacktraceIds\"]\n" +
	"\x13StacktracesResponse\x12F\n" +
	"\vstacktraces\x18\x01 \x03(\v2$.parca.metastore.v1alpha1.StacktraceR\vstacktraces\"5\n" +
	"\x10LocationsRequest\x12!\n" +
	"\flocation_ids\x18\x01 \x03(\tR\vlocationIds\"U\n" +
	"\x11LocationsResponse\x12@\n" +
	"\tlocations\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.LocationR\tlocations\"9\n" +
	"\x14LocationLinesRequest\x12!\n" +
	"\flocation_ids\x18\x01 \x03(\tR\vlocationIds\"5\n" +
	"\x10FunctionsRequest\x12!\n" +
	"\ffunction_ids\x18\x01 \x03(\tR\vfunctionIds\"U\n" +
	"\x11FunctionsResponse\x12@\n" +
	"\tfunctions\x18\x01 \x03(\v2\".parca.metastore.v1alpha1.FunctionR\tfunctions\"2\n" +
	"\x0fMappingsRequest\x12\x1f\n" +
	"\vmapping_ids\x18\x01 \x03(\tR\n" +
	"mappingIds\"Q\n" +
	"\x10MappingsResponse\x12=\n" +
	"\bmappings\x18\x01 \x03(\v2!.parca.metastore.v1alpha1.MappingR\bmappings\"\xc0\x04\n" +
	"\x06Sample\x12#\n" +
	"\rstacktrace_id\x18\x01 \x01(\tR\fstacktraceId\x12D\n" +
	"\x06labels\x18\x02 \x03(\v2,.parca.metastore.v1alpha1.Sample.LabelsEntryR\x06labels\x12N\n" +
	"\n" +
	"num_labels\x18\x03 \x03(\v2/.parca.metastore.v1alpha1.Sample.NumLabelsEntryR\tnumLabels\x12K\n" +
	"\tnum_units\x18\x04 \x03(\v2..parca.metastore.v1alpha1.Sample.NumUnitsEntryR\bnumUnits\x1a`\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12;\n" +
	"\x05value\x18\x02 \x01(\v2%.parca.metastore.v1alpha1.SampleLabelR\x05value:\x028\x01\x1af\n" +
	"\x0eNumLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12>\n" +
	"\x05value\x18\x02 \x01(\v2(.parca.metastore.v1alpha1.SampleNumLabelR\x05value:\x028\x01\x1ad\n" +
	"\rNumUnitsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12=\n" +
	"\x05value\x18\x02 \x01(\v2'.parca.metastore.v1alpha1.SampleNumUnitR\x05value:\x028\x01\"?\n" +
	"\n" +
	"Stacktrace\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\flocation_ids\x18\x02 \x03(\tR\vlocationIds\"%\n" +
	"\vSampleLabel\x12\x16\n" +
	"\x06labels\x18\x01 \x03(\tR\x06labels\"/\n" +
	"\x0eSampleNumLabel\x12\x1d\n" +
	"\n" +
	"num_labels\x18\x01 \x03(\x03R\tnumLabels\"%\n" +
	"\rSampleNumUnit\x12\x14\n" +
	"\x05units\x18\x01 \x03(\tR\x05units\"\xcb\x01\n" +
	"\bLocation\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\x04R\aaddress\x12\x1d\n" +
	"\n" +
	"mapping_id\x18\x04 \x01(\tR\tmappingId\x12\x1b\n" +
	"\tis_folded\x18\x05 \x01(\bR\bisFolded\x124\n" +
	"\x05lines\x18\x06 \x03(\v2\x1e.parca.metastore.v1alpha1.LineR\x05lines\x12#\n" +
	"\rmapping_index\x18\a \x01(\rR\fmappingIndex\"b\n" +
	"\x04Line\x12\x1f\n" +
	"\vfunction_id\x18\x01 \x01(\tR\n" +
	"functionId\x12\x12\n" +
	"\x04line\x18\x02 \x01(\x03R\x04line\x12%\n" +
	"\x0efunction_index\x18\x03 \x01(\rR\rfunctionIndex\"\xa3\x02\n" +
	"\bFunction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"start_line\x18\x02 \x01(\x03R\tstartLine\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1f\n" +
	"\vsystem_name\x18\x04 \x01(\tR\n" +
	"systemName\x12\x1a\n" +
	"\bfilename\x18\x05 \x01(\tR\bfilename\x12*\n" +
	"\x11name_string_index\x18\x06 \x01(\rR\x0fnameStringIndex\x127\n" +
	"\x18system_name_string_index\x18\a \x01(\rR\x15systemNameStringIndex\x122\n" +
	"\x15filename_string_index\x18\b \x01(\rR\x13filenameStringIndex\"\x8b\x03\n" +
	"\aMapping\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05start\x18\x02 \x01(\x04R\x05start\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x04R\x05limit\x12\x16\n" +
	"\x06offset\x18\x04 \x01(\x04R\x06offset\x12\x12\n" +
	"\x04file\x18\x05 \x01(\tR\x04file\x12\x19\n" +
	"\bbuild_id\x18\x06 \x01(\tR\abuildId\x12#\n" +
	"\rhas_functions\x18\a \x01(\bR\fhasFunctions\x12#\n" +
	"\rhas_filenames\x18\b \x01(\bR\fhasFilenames\x12(\n" +
	"\x10has_line_numbers\x18\t \x01(\bR\x0ehasLineNumbers\x12*\n" +
	"\x11has_inline_frames\x18\n" +
	" \x01(\bR\x0fhasInlineFrames\x12*\n" +
	"\x11file_string_index\x18\v \x01(\rR\x0ffileStringIndex\x121\n" +
	"\x15build_id_string_index\x18\f \x01(\rR\x12buildIdStringIndex2\xf4\t\n" +
	"\x10MetastoreService\x12\x84\x01\n" +
	"\x13GetOrCreateMappings\x124.parca.metastore.v1alpha1.GetOrCreateMappingsRequest\x1a5.parca.metastore.v1alpha1.GetOrCreateMappingsResponse\"\x00\x12\x87\x01\n" +
	"\x14GetOrCreateFunctions\x125.parca.metastore.v1alpha1.GetOrCreateFunctionsRequest\x1a6.parca.metastore.v1alpha1.GetOrCreateFunctionsResponse\"\x00\x12\x87\x01\n" +
	"\x14GetOrCreateLocations\x125.parca.metastore.v1alpha1.GetOrCreateLocationsRequest\x1a6.parca.metastore.v1alpha1.GetOrCreateLocationsResponse\"\x00\x12\x8d\x01\n" +
	"\x16GetOrCreateStacktraces\x127.parca.metastore.v1alpha1.GetOrCreateStacktracesRequest\x1a8.parca.metastore.v1alpha1.GetOrCreateStacktracesResponse\"\x00\x12\x8a\x01\n" +
	"\x15UnsymbolizedLocations\x126.parca.metastore.v1alpha1.UnsymbolizedLocationsRequest\x1a7.parca.metastore.v1alpha1.UnsymbolizedLocationsResponse\"\x00\x12\x84\x01\n" +
	"\x13CreateLocationLines\x124.parca.metastore.v1alpha1.CreateLocationLinesRequest\x1a5.parca.metastore.v1alpha1.CreateLocationLinesResponse\"\x00\x12f\n" +
	"\tLocations\x12*.parca.metastore.v1alpha1.LocationsRequest\x1a+.parca.metastore.v1alpha1.LocationsResponse\"\x00\x12f\n" +
	"\tFunctions\x12*.parca.metastore.v1alpha1.FunctionsRequest\x1a+.parca.metastore.v1alpha1.FunctionsResponse\"\x00\x12c\n" +
	"\bMappings\x12).parca.metastore.v1alpha1.MappingsRequest\x1a*.parca.metastore.v1alpha1.MappingsResponse\"\x00\x12l\n" +
	"\vStacktraces\x12,.parca.metastore.v1alpha1.StacktracesRequest\x1a-.parca.metastore.v1alpha1.StacktracesResponse\"\x00B\x84\x02\n" +
	"\x1ccom.parca.metastore.v1alpha1B\x0eMetastoreProtoP\x01ZRgithub.com/parca-dev/parca/gen/proto/go/parca/metastore/v1alpha1;metastorev1alpha1\xa2\x02\x03PMX\xaa\x02\x18Parca.Metastore.V1alpha1\xca\x02\x18Parca\\Metastore\\V1alpha1\xe2\x02$Parca\\Metastore\\V1alpha1\\GPBMetadata\xea\x02\x1aParca::Metastore::V1alpha1b\x06proto3"

var (
	file_parca_metastore_v1alpha1_metastore_proto_rawDescOnce sync.Once
	file_parca_metastore_v1alpha1_metastore_proto_rawDescData []byte
)

func file_parca_metastore_v1alpha1_metastore_proto_rawDescGZIP() []byte {
	file_parca_metastore_v1alpha1_metastore_proto_rawDescOnce.Do(func() {
		file_parca_metastore_v1alpha1_metastore_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_metastore_v1alpha1_metastore_proto_rawDesc), len(file_parca_metastore_v1alpha1_metastore_proto_rawDesc)))
	})
	return file_parca_metastore_v1alpha1_metastore_proto_rawDescData
}

var file_parca_metastore_v1alpha1_metastore_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_parca_metastore_v1alpha1_metastore_proto_goTypes = []any{
	(*GetOrCreateMappingsRequest)(nil),     // 0: parca.metastore.v1alpha1.GetOrCreateMappingsRequest
	(*GetOrCreateMappingsResponse)(nil),    // 1: parca.metastore.v1alpha1.GetOrCreateMappingsResponse
	(*GetOrCreateFunctionsRequest)(nil),    // 2: parca.metastore.v1alpha1.GetOrCreateFunctionsRequest
	(*GetOrCreateFunctionsResponse)(nil),   // 3: parca.metastore.v1alpha1.GetOrCreateFunctionsResponse
	(*GetOrCreateLocationsRequest)(nil),    // 4: parca.metastore.v1alpha1.GetOrCreateLocationsRequest
	(*GetOrCreateLocationsResponse)(nil),   // 5: parca.metastore.v1alpha1.GetOrCreateLocationsResponse
	(*GetOrCreateStacktracesRequest)(nil),  // 6: parca.metastore.v1alpha1.GetOrCreateStacktracesRequest
	(*GetOrCreateStacktracesResponse)(nil), // 7: parca.metastore.v1alpha1.GetOrCreateStacktracesResponse
	(*UnsymbolizedLocationsRequest)(nil),   // 8: parca.metastore.v1alpha1.UnsymbolizedLocationsRequest
	(*UnsymbolizedLocationsResponse)(nil),  // 9: parca.metastore.v1alpha1.UnsymbolizedLocationsResponse
	(*CreateLocationLinesRequest)(nil),     // 10: parca.metastore.v1alpha1.CreateLocationLinesRequest
	(*CreateLocationLinesResponse)(nil),    // 11: parca.metastore.v1alpha1.CreateLocationLinesResponse
	(*StacktracesRequest)(nil),             // 12: parca.metastore.v1alpha1.StacktracesRequest
	(*StacktracesResponse)(nil),            // 13: parca.metastore.v1alpha1.StacktracesResponse
	(*LocationsRequest)(nil),               // 14: parca.metastore.v1alpha1.LocationsRequest
	(*LocationsResponse)(nil),              // 15: parca.metastore.v1alpha1.LocationsResponse
	(*LocationLinesRequest)(nil),           // 16: parca.metastore.v1alpha1.LocationLinesRequest
	(*FunctionsRequest)(nil),               // 17: parca.metastore.v1alpha1.FunctionsRequest
	(*FunctionsResponse)(nil),              // 18: parca.metastore.v1alpha1.FunctionsResponse
	(*MappingsRequest)(nil),                // 19: parca.metastore.v1alpha1.MappingsRequest
	(*MappingsResponse)(nil),               // 20: parca.metastore.v1alpha1.MappingsResponse
	(*Sample)(nil),                         // 21: parca.metastore.v1alpha1.Sample
	(*Stacktrace)(nil),                     // 22: parca.metastore.v1alpha1.Stacktrace
	(*SampleLabel)(nil),                    // 23: parca.metastore.v1alpha1.SampleLabel
	(*SampleNumLabel)(nil),                 // 24: parca.metastore.v1alpha1.SampleNumLabel
	(*SampleNumUnit)(nil),                  // 25: parca.metastore.v1alpha1.SampleNumUnit
	(*Location)(nil),                       // 26: parca.metastore.v1alpha1.Location
	(*Line)(nil),                           // 27: parca.metastore.v1alpha1.Line
	(*Function)(nil),                       // 28: parca.metastore.v1alpha1.Function
	(*Mapping)(nil),                        // 29: parca.metastore.v1alpha1.Mapping
	nil,                                    // 30: parca.metastore.v1alpha1.Sample.LabelsEntry
	nil,                                    // 31: parca.metastore.v1alpha1.Sample.NumLabelsEntry
	nil,                                    // 32: parca.metastore.v1alpha1.Sample.NumUnitsEntry
}
var file_parca_metastore_v1alpha1_metastore_proto_depIdxs = []int32{
	29, // 0: parca.metastore.v1alpha1.GetOrCreateMappingsRequest.mappings:type_name -> parca.metastore.v1alpha1.Mapping
	29, // 1: parca.metastore.v1alpha1.GetOrCreateMappingsResponse.mappings:type_name -> parca.metastore.v1alpha1.Mapping
	28, // 2: parca.metastore.v1alpha1.GetOrCreateFunctionsRequest.functions:type_name -> parca.metastore.v1alpha1.Function
	28, // 3: parca.metastore.v1alpha1.GetOrCreateFunctionsResponse.functions:type_name -> parca.metastore.v1alpha1.Function
	26, // 4: parca.metastore.v1alpha1.GetOrCreateLocationsRequest.locations:type_name -> parca.metastore.v1alpha1.Location
	26, // 5: parca.metastore.v1alpha1.GetOrCreateLocationsResponse.locations:type_name -> parca.metastore.v1alpha1.Location
	22, // 6: parca.metastore.v1alpha1.GetOrCreateStacktracesRequest.stacktraces:type_name -> parca.metastore.v1alpha1.Stacktrace
	22, // 7: parca.metastore.v1alpha1.GetOrCreateStacktracesResponse.stacktraces:type_name -> parca.metastore.v1alpha1.Stacktrace
	26, // 8: parca.metastore.v1alpha1.UnsymbolizedLocationsResponse.locations:type_name -> parca.metastore.v1alpha1.Location
	26, // 9: parca.metastore.v1alpha1.CreateLocationLinesRequest.locations:type_name -> parca.metastore.v1alpha1.Location
	22, // 10: parca.metastore.v1alpha1.StacktracesResponse.stacktraces:type_name -> parca.metastore.v1alpha1.Stacktrace
	26, // 11: parca.metastore.v1alpha1.LocationsResponse.locations:type_name -> parca.metastore.v1alpha1.Location
	28, // 12: parca.metastore.v1alpha1.FunctionsResponse.functions:type_name -> parca.metastore.v1alpha1.Function
	29, // 13: parca.metastore.v1alpha1.MappingsResponse.mappings:type_name -> parca.metastore.v1alpha1.Mapping
	30, // 14: parca.metastore.v1alpha1.Sample.labels:type_name -> parca.metastore.v1alpha1.Sample.LabelsEntry
	31, // 15: parca.metastore.v1alpha1.Sample.num_labels:type_name -> parca.metastore.v1alpha1.Sample.NumLabelsEntry
	32, // 16: parca.metastore.v1alpha1.Sample.num_units:type_name -> parca.metastore.v1alpha1.Sample.NumUnitsEntry
	27, // 17: parca.metastore.v1alpha1.Location.lines:type_name -> parca.metastore.v1alpha1.Line
	23, // 18: parca.metastore.v1alpha1.Sample.LabelsEntry.value:type_name -> parca.metastore.v1alpha1.SampleLabel
	24, // 19: parca.metastore.v1alpha1.Sample.NumLabelsEntry.value:type_name -> parca.metastore.v1alpha1.SampleNumLabel
	25, // 20: parca.metastore.v1alpha1.Sample.NumUnitsEntry.value:type_name -> parca.metastore.v1alpha1.SampleNumUnit
	0,  // 21: parca.metastore.v1alpha1.MetastoreService.GetOrCreateMappings:input_type -> parca.metastore.v1alpha1.GetOrCreateMappingsRequest
	2,  // 22: parca.metastore.v1alpha1.MetastoreService.GetOrCreateFunctions:input_type -> parca.metastore.v1alpha1.GetOrCreateFunctionsRequest
	4,  // 23: parca.metastore.v1alpha1.MetastoreService.GetOrCreateLocations:input_type -> parca.metastore.v1alpha1.GetOrCreateLocationsRequest
	6,  // 24: parca.metastore.v1alpha1.MetastoreService.GetOrCreateStacktraces:input_type -> parca.metastore.v1alpha1.GetOrCreateStacktracesRequest
	8,  // 25: parca.metastore.v1alpha1.MetastoreService.UnsymbolizedLocations:input_type -> parca.metastore.v1alpha1.UnsymbolizedLocationsRequest
	10, // 26: parca.metastore.v1alpha1.MetastoreService.CreateLocationLines:input_type -> parca.metastore.v1alpha1.CreateLocationLinesRequest
	14, // 27: parca.metastore.v1alpha1.MetastoreService.Locations:input_type -> parca.metastore.v1alpha1.LocationsRequest
	17, // 28: parca.metastore.v1alpha1.MetastoreService.Functions:input_type -> parca.metastore.v1alpha1.FunctionsRequest
	19, // 29: parca.metastore.v1alpha1.MetastoreService.Mappings:input_type -> parca.metastore.v1alpha1.MappingsRequest
	12, // 30: parca.metastore.v1alpha1.MetastoreService.Stacktraces:input_type -> parca.metastore.v1alpha1.StacktracesRequest
	1,  // 31: parca.metastore.v1alpha1.MetastoreService.GetOrCreateMappings:output_type -> parca.metastore.v1alpha1.GetOrCreateMappingsResponse
	3,  // 32: parca.metastore.v1alpha1.MetastoreService.GetOrCreateFunctions:output_type -> parca.metastore.v1alpha1.GetOrCreateFunctionsResponse
	5,  // 33: parca.metastore.v1alpha1.MetastoreService.GetOrCreateLocations:output_type -> parca.metastore.v1alpha1.GetOrCreateLocationsResponse
	7,  // 34: parca.metastore.v1alpha1.MetastoreService.GetOrCreateStacktraces:output_type -> parca.metastore.v1alpha1.GetOrCreateStacktracesResponse
	9,  // 35: parca.metastore.v1alpha1.MetastoreService.UnsymbolizedLocations:output_type -> parca.metastore.v1alpha1.UnsymbolizedLocationsResponse
	11, // 36: parca.metastore.v1alpha1.MetastoreService.CreateLocationLines:output_type -> parca.metastore.v1alpha1.CreateLocationLinesResponse
	15, // 37: parca.metastore.v1alpha1.MetastoreService.Locations:output_type -> parca.metastore.v1alpha1.LocationsResponse
	18, // 38: parca.metastore.v1alpha1.MetastoreService.Functions:output_type -> parca.metastore.v1alpha1.FunctionsResponse
	20, // 39: parca.metastore.v1alpha1.MetastoreService.Mappings:output_type -> parca.metastore.v1alpha1.MappingsResponse
	13, // 40: parca.metastore.v1alpha1.MetastoreService.Stacktraces:output_type -> parca.metastore.v1alpha1.StacktracesResponse
	31, // [31:41] is the sub-list for method output_type
	21, // [21:31] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_parca_metastore_v1alpha1_metastore_proto_init() }
func file_parca_metastore_v1alpha1_metastore_proto_init() {
	if File_parca_metastore_v1alpha1_metastore_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_metastore_v1alpha1_metastore_proto_rawDesc), len(file_parca_metastore_v1alpha1_metastore_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parca_metastore_v1alpha1_metastore_proto_goTypes,
		DependencyIndexes: file_parca_metastore_v1alpha1_metastore_proto_depIdxs,
		MessageInfos:      file_parca_metastore_v1alpha1_metastore_proto_msgTypes,
	}.Build()
	File_parca_metastore_v1alpha1_metastore_proto = out.File
	file_parca_metastore_v1alpha1_metastore_proto_goTypes = nil
	file_parca_metastore_v1alpha1_metastore_proto_depIdxs = nil
}
