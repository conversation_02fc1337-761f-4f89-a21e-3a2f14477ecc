# Deep-eBPF Universal Function Tracer - Client Installation Guide

## 🎯 Overview

This guide will help you install and run the Deep-eBPF Universal Function Tracer in your environment. The system provides real-time userland function tracing with human-readable output showing function names, arguments, memory analysis, and runtime information.

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Linux (Ubuntu 20.04+, CentOS 8+, or similar)
- **Kernel Version**: Linux 5.8+ (for CO-RE eBPF support)
- **Architecture**: x86_64 (AMD64)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **CPU**: 2 cores minimum, 4 cores recommended
- **Disk Space**: 2GB free space for installation and logs

### Required Privileges
- **Root Access**: Required for eBPF program loading and system tracing
- **Sudo Access**: Must be able to run commands with sudo

### Supported Environments
- ✅ **Physical Servers**: Full support
- ✅ **Virtual Machines**: Full support (VMware, VirtualBox, KVM)
- ✅ **Cloud Instances**: AWS EC2, Google Cloud, Azure VMs
- ✅ **Containers**: Docker containers with privileged mode
- ⚠️ **WSL**: Limited support (development only)

## 🛠️ Installation Steps

### Step 1: System Preparation

```bash
# Update your system
sudo apt update && sudo apt upgrade -y

# Install essential build tools
sudo apt install -y build-essential git curl wget unzip

# Install eBPF development dependencies
sudo apt install -y clang llvm libbpf-dev linux-headers-$(uname -r)

# Install Go (required for compilation)
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# Verify installations
clang --version    # Should be 10+
go version        # Should be 1.19+
uname -r          # Note your kernel version
```

### Step 2: Download Deep-eBPF System

```bash
# Create installation directory
sudo mkdir -p /opt/deep-ebpf
cd /opt/deep-ebpf

# Download the system (replace with actual download location)
# Option A: From GitHub releases
wget https://github.com/yourusername/deep-ebpf/releases/latest/download/deep-ebpf-system.tar.gz
tar -xzf deep-ebpf-system.tar.gz

# Option B: Clone from repository
git clone https://github.com/yourusername/deep-ebpf-node.git
git clone https://github.com/yourusername/deep-ebpf-server.git

# Set proper ownership
sudo chown -R $USER:$USER /opt/deep-ebpf
```

### Step 3: Build the System

```bash
# Build deep-ebpf-node (agent)
cd /opt/deep-ebpf/deep-ebpf-node
go mod tidy
go build -o deep-ebpf-node ./main.go

# Build deep-ebpf-server
cd /opt/deep-ebpf/deep-ebpf-server
go mod tidy
go build -o deep-ebpf-server ./main.go

# Build test programs
cd /opt/deep-ebpf/deep-ebpf-node/test
make all

# Verify builds
ls -la /opt/deep-ebpf/deep-ebpf-node/deep-ebpf-node
ls -la /opt/deep-ebpf/deep-ebpf-server/deep-ebpf-server
```

### Step 4: System Configuration

```bash
# Create configuration directory
sudo mkdir -p /etc/deep-ebpf

# Create basic configuration file
sudo tee /etc/deep-ebpf/config.yaml > /dev/null << 'EOF'
# Deep-eBPF Configuration
server:
  listen_port: 8080
  data_dir: /var/lib/deep-ebpf
  
agent:
  server_endpoint: http://localhost:8080
  output_format: human
  enable_stack_traces: true
  enable_arguments: true
  max_stack_depth: 64
  
logging:
  level: info
  file: /var/log/deep-ebpf/deep-ebpf.log
EOF

# Create data and log directories
sudo mkdir -p /var/lib/deep-ebpf
sudo mkdir -p /var/log/deep-ebpf
sudo chown $USER:$USER /var/lib/deep-ebpf /var/log/deep-ebpf
```

### Step 5: Create System Services (Optional)

```bash
# Create systemd service for deep-ebpf-server
sudo tee /etc/systemd/system/deep-ebpf-server.service > /dev/null << 'EOF'
[Unit]
Description=Deep-eBPF Server
After=network.target

[Service]
Type=simple
User=root
ExecStart=/opt/deep-ebpf/deep-ebpf-server/deep-ebpf-server --config /etc/deep-ebpf/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
sudo systemctl daemon-reload
sudo systemctl enable deep-ebpf-server
sudo systemctl start deep-ebpf-server
```

## 🚀 Quick Start

### Basic Usage

```bash
# Start the server (in one terminal)
cd /opt/deep-ebpf/deep-ebpf-server
sudo ./deep-ebpf-server --dev --listen :8080

# Run the tracer (in another terminal)
cd /opt/deep-ebpf/deep-ebpf-node
sudo ./deep-ebpf-node --target-pid <PID> --format human

# Example: Trace a specific process
sudo ./deep-ebpf-node --target-pid 1234 --format human --enable-stack-traces

# Example: Trace by binary name
sudo ./deep-ebpf-node --target-binary /usr/bin/python3 --format human
```

### Test the Installation

```bash
# Run the test program
cd /opt/deep-ebpf/deep-ebpf-node/test
./simple_test &
TEST_PID=$!

# Trace the test program
cd /opt/deep-ebpf/deep-ebpf-node
sudo ./deep-ebpf-node --target-pid $TEST_PID --format human --debug

# You should see output like:
# ┌─ [16:12:25.831726] simple_test:947/947 on CPU 7 [entry]
# ├─ Function: fibonacci [user]
# ├─ Address:  0x401234
# ├─ Duration: 29.943µs
# └─────────────────────────────────────────────────────────────────────────────────
```

## 🔧 Configuration Options

### Command Line Flags

```bash
# Core targeting options
--target-pid=<PID>              # Target specific process ID
--target-binary=<PATH>          # Target specific binary path
--target-function=<NAME>        # Target specific function name

# Output options
--format=<FORMAT>               # Output format: human, json, server, otlp
--output=<FILE>                 # Output file (default: stdout)
--server-endpoint=<URL>         # Server endpoint for data export

# Tracing options
--enable-stack-traces           # Enable call stack collection
--enable-arguments              # Enable function argument capture
--max-stack-depth=<N>           # Maximum stack trace depth (default: 64)

# Filtering options
--filter-functions=<PATTERNS>   # Function name patterns to trace
--filter-binaries=<PATTERNS>    # Binary path patterns to trace
--pid-whitelist=<PIDS>          # List of PIDs to trace

# Performance options
--sample-rate=<N>               # Sampling rate (1-100, default: 1)
--max-events-per-sec=<N>        # Maximum events per second
--ring-buffer-size=<BYTES>      # eBPF ring buffer size

# Debug options
--debug                         # Enable debug mode
--dry-run                       # Dry run mode (don't attach probes)
--verbose                       # Enable verbose logging
```

### Environment Variables

```bash
# Set environment variables for easier usage
export DEEP_EBPF_SERVER_ENDPOINT="http://localhost:8080"
export DEEP_EBPF_OUTPUT_FORMAT="human"
export DEEP_EBPF_LOG_LEVEL="info"

# Use in commands
sudo -E ./deep-ebpf-node --target-pid 1234
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Permission Denied
```bash
# Ensure you're running with sudo
sudo ./deep-ebpf-node --target-pid 1234

# Or set capabilities (advanced)
sudo setcap cap_sys_admin,cap_bpf+ep ./deep-ebpf-node
```

#### 2. eBPF Not Supported
```bash
# Check kernel version
uname -r  # Should be 5.8+

# Check eBPF support
ls /sys/kernel/debug/tracing/
mount | grep debugfs

# Enable if needed
sudo mount -t debugfs debugfs /sys/kernel/debug
```

#### 3. No Events Captured
```bash
# Check if target process exists
ps aux | grep <process_name>

# Verify eBPF programs are loaded
sudo bpftool prog list

# Check system logs
sudo dmesg | grep -i bpf
journalctl -u deep-ebpf-server
```

#### 4. High CPU Usage
```bash
# Reduce sampling rate
sudo ./deep-ebpf-node --target-pid 1234 --sample-rate 10

# Filter specific functions
sudo ./deep-ebpf-node --target-pid 1234 --filter-functions="main,init"

# Limit event rate
sudo ./deep-ebpf-node --target-pid 1234 --max-events-per-sec 1000
```

### Debug Commands

```bash
# Check system compatibility
/opt/deep-ebpf/deep-ebpf-node/deep-ebpf-node --version

# Test eBPF environment
sudo /opt/deep-ebpf/deep-ebpf-node/deep-ebpf-node --dry-run --debug

# Monitor system resources
top -p $(pgrep deep-ebpf)
```

## 📞 Support

### Log Files
- **Server Logs**: `/var/log/deep-ebpf/server.log`
- **Agent Logs**: `/var/log/deep-ebpf/agent.log`
- **System Logs**: `journalctl -u deep-ebpf-server`

### Health Checks
```bash
# Check server status
curl http://localhost:8080/health

# Check API status
curl http://localhost:8080/api/status
```

### Getting Help
1. **Check logs** for error messages
2. **Run with --debug** for detailed output
3. **Use --dry-run** to test configuration
4. **Verify system requirements** are met
5. **Contact support** with log files and system information

## 🎯 Next Steps

After successful installation:
1. **Read the Demo Guide** for step-by-step usage examples
2. **Configure filtering** for your specific use case
3. **Set up monitoring** for production environments
4. **Integrate with your observability stack** using JSON/OTLP output

The Deep-eBPF Universal Function Tracer is now ready to provide real-time insights into your application's function-level behavior! 🚀
