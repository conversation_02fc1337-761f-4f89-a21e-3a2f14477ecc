{"coredump-ref": "e22272d28a18768e22cedb96f46f2365f7fe3bcc2a4655c7841699dfc9818a90", "threads": [{"lwp": 8370, "frames": ["libc.so.6+0xd7d10", "node+0x194c477", "node+0xebc9c3", "node+0xeb8abb", "node+0xeb8bcb", "V8::ExitFrame+0 in :0", "handleWriteReq+0 in node:internal/stream_base_commons:0", "writeGeneric+0 in node:internal/stream_base_commons:0", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+0 in node:internal/streams/writable:0", "_write+0 in node:internal/streams/writable:0", "Writable.write+0 in node:internal/streams/writable:0", "value+0 in node:internal/console/constructor:0", "warn+0 in node:internal/console/constructor:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0x113b4eb", "node+0x113c253", "node+0xffc27b", "node+0xee5397", "V8::ExitFrame+0 in :0", "trace+0 in node:internal/console/constructor:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0x113b4eb", "node+0x113c253", "node+0xffc27b", "node+0xee5397", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:3", "add3+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:8", "test+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:12", "submain+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:17", "main+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:23", "<anonymous>+26 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1376", "Module._extensions..js+46 in node:internal/modules/cjs/loader:1435", "Module.load+13 in node:internal/modules/cjs/loader:1207", "Module._load+73 in node:internal/modules/cjs/loader:1023", "executeUserEntryPoint+0 in node:internal/modules/run_main:0", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0x113b4eb", "node+0x113c253", "node+0xffc27b", "node+0xd9bc17", "node+0xe3a1c3", "node+0xd7600b", "node+0xd77ffb", "node+0xce5dd3", "node+0xe002af", "node+0xe00bff", "node+0xd7aecb", "libc.so.6+0x273fb", "libc.so.6+0x274cb", "node+0xce03cf"]}, {"lwp": 8371, "frames": ["<unwinding aborted due to error native_zero_pc>"]}, {"lwp": 8372, "frames": ["libc.so.6+0xe5f3c", "node+0x19542c3", "node+0x1941a5b", "node+0xe2d1cf", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8373, "frames": ["<unwinding aborted due to error native_zero_pc>"]}, {"lwp": 8374, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x194f93b", "node+0xe28677", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8375, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x194f93b", "node+0xe28677", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8376, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x194f93b", "node+0xe28677", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8377, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x194f93b", "node+0xe28677", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8378, "frames": ["<unwinding aborted due to error native_zero_pc>"]}, {"lwp": 8379, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x85a5b", "node+0x194f767", "node+0xed7623", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}], "modules": [{"ref": "fd04b635d29b5cb3faaf502a6c5cd68a623d66a736f4d0561ff280c2fa411c79", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "e647ee3042517f06cbebbbf0f66ba25486d0722222404f1d067683767a055566", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "c4834bd79254443665af96b6d5e71d124b7f92f2eea121e61f9fff5204fc594d", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30"}, {"ref": "594545a9720b4a16973a823d18c71fbf070d3c07fb17b01df5376273e91644a1", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "22a0986a1047cd3c9a55368fdc6bb6e5a4455aceb53ec15dbe19112d95583642", "local-path": "/usr/lib/aarch64-linux-gnu/libpthread.so.0"}, {"ref": "acfe23cf0f5f4ac35b4c314415bb31d6f52a9bd39df8be04dfb2818a571533fd", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}, {"ref": "c564268acd39e5b7bcebf777fb2c36c3f3747e29ce455c6b0f41f17521aef073", "local-path": "/home/<USER>/.nvm/versions/node/v21.1.0/bin/node"}, {"ref": "f1935c0616a48e7ec471c26886df0411beffd4e56c92539d9a6a94641950badf", "local-path": "/usr/lib/aarch64-linux-gnu/libdl.so.2"}]}