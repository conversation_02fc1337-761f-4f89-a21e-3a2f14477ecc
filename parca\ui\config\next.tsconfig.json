{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "paths": {"@parca/*": ["../packages/shared/*"]}, "strictNullChecks": true, "types": ["vite/client", "vitest/globals"], "declaration": true, "declarationMap": true}}