#!/usr/bin/env bash

# Copyright 2022-2025 The Parca Authors
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -euo pipefail

# On Fedora $SNAP is under /var and there is some magic to map it to /snap.
# We need to handle that case and reset $SNAP
SNAP="${SNAP//\/var\/lib\/snapd/}"

storage_active_memory="$(snapctl get storage-active-memory)"
if [[ -z "$storage_active_memory" ]]; then
    snapctl set storage-active-memory=536870912
fi

log_level="$(snapctl get log-level)"
if [[ -z "$log_level" ]]; then
    snapctl set log-level=info
fi

storage_persist="$(snapctl get enable-persistence)"
if [[ -z "$storage_persist" ]]; then
    snapctl set enable-persistence=false
fi

# Address to bind HTTP server to.
http_address="$(snapctl get http-address)"
if [[ -z "$http_address" ]]; then
    # Older versions of the snap exposed the port option.
    # Migrate that to http-address if it was set.
    port="$(snapctl get port)"
    if [[ -n "${port}" ]]; then
        snapctl set http-address=":${port}"
    else
        snapctl set http-address=":7070"
    fi
fi

# gRPC address to send profiles and symbols to.
remote_store="$(snapctl get remote-store-address)"
if [[ -z "$remote_store" ]]; then
    snapctl set remote-store-address="grpc.polarsignals.com:443"
fi

# Send gRPC requests via plaintext instead of TLS
remote_store_insecure="$(snapctl get remote-store-insecure)"
if [[ -z "$remote_store_insecure" ]]; then
    snapctl set remote-store-insecure=false
fi

# Set the bearer token for the remote store
remote_store_token="$(snapctl get remote-store-bearer-token)"
if [[ -z "$remote_store_token" ]]; then
    snapctl set remote-store-bearer-token=""
fi

mkdir -p "${SNAP_COMMON}/profiles"
cp -pnr "${SNAP}/usr/share/parca/example-config.yaml" "${SNAP_DATA}/parca.yaml"
