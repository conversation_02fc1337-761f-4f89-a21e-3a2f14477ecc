{"coredump-ref": "bbe8fe6b51893ce3e0f9b0b9f26adb399c5056f7e0e315a53efcdf805e0d8ff8", "threads": [{"lwp": 18947, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x62943", "libuv.so.1.0.0+0x1abce", "node+0x9fd556", "node+0x9f8d6c", "node+0x9f8f68", "V8::ExitFrame+0 in :0", "handleWriteReq+0 in node:internal/stream_base_commons:0", "writeGeneric+0 in node:internal/stream_base_commons:0", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+0 in node:internal/streams/writable:0", "_write+0 in node:internal/streams/writable:0", "Writable.write+0 in node:internal/streams/writable:0", "value+0 in node:internal/console/constructor:0", "log+0 in node:internal/console/constructor:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xc93180", "node+0xc940ba", "node+0xb2af62", "node+0xa2f3a4", "V8::ExitFrame+0 in :0", "bar+1 in /home/<USER>/work/elastic/node/hello2.js:2", "foo+1 in /home/<USER>/work/elastic/node/hello2.js:7", "doit+2 in /home/<USER>/work/elastic/node/hello2.js:12", "<anonymous>+18 in /home/<USER>/work/elastic/node/hello2.js:19", "Module._compile+46 in node:internal/modules/cjs/loader:1376", "Module._extensions..js+46 in node:internal/modules/cjs/loader:1435", "Module.load+13 in node:internal/modules/cjs/loader:1207", "Module._load+73 in node:internal/modules/cjs/loader:1023", "executeUserEntryPoint+8 in node:internal/modules/run_main:135", "<anonymous>+27 in node:internal/main/run_main_module:28", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xc93180", "node+0xc940ba", "node+0xb2af62", "node+0x8a84e8", "node+0x95fe29", "node+0x87c455", "node+0x7d49ea", "node+0x91f890", "node+0x9202fb", "node+0x88126e", "ld-musl-x86_64.so.1+0x1c6d0", "node+0x7a12dd"]}, {"lwp": 18948, "frames": ["<unwinding aborted due to error native_zero_pc>"]}, {"lwp": 18949, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x20285", "libuv.so.1.0.0+0x224f3", "libuv.so.1.0.0+0xf442", "node+0x953310", "ld-musl-x86_64.so.1+0x5c22d", "ld-musl-x86_64.so.1+0x5e82e"]}, {"lwp": 18950, "frames": ["<unwinding aborted due to error native_zero_pc>"]}, {"lwp": 18951, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x5aa2d", "ld-musl-x86_64.so.1+0x5b893", "libuv.so.1.0.0+0x1d749", "node+0x94dd33", "ld-musl-x86_64.so.1+0x5c22d", "ld-musl-x86_64.so.1+0x5e82e"]}, {"lwp": 18952, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x5aa2d", "ld-musl-x86_64.so.1+0x5b893", "libuv.so.1.0.0+0x1d749", "node+0x94dd33", "ld-musl-x86_64.so.1+0x5c22d", "ld-musl-x86_64.so.1+0x5e82e"]}, {"lwp": 18953, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x5aa2d", "ld-musl-x86_64.so.1+0x5b893", "libuv.so.1.0.0+0x1d749", "node+0x94dd33", "ld-musl-x86_64.so.1+0x5c22d", "ld-musl-x86_64.so.1+0x5e82e"]}, {"lwp": 18954, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x5aa2d", "ld-musl-x86_64.so.1+0x5b893", "libuv.so.1.0.0+0x1d749", "node+0x94dd33", "ld-musl-x86_64.so.1+0x5c22d", "ld-musl-x86_64.so.1+0x5e82e"]}, {"lwp": 18955, "frames": ["<unwinding aborted due to error native_zero_pc>"]}, {"lwp": 18956, "frames": ["ld-musl-x86_64.so.1+0x5e862", "ld-musl-x86_64.so.1+0x5b4fb", "ld-musl-x86_64.so.1+0x5aa2d", "ld-musl-x86_64.so.1+0x5e389", "libuv.so.1.0.0+0x1d5c3", "node+0xa1d1d2", "ld-musl-x86_64.so.1+0x5c22d", "ld-musl-x86_64.so.1+0x5e82e"]}], "modules": [{"ref": "a93c22f55a8d82467d9973d40d225a241203b23bf60c249919c706bfbd511818", "local-path": "/usr/bin/node"}, {"ref": "9e9263659e630c6a6c8cbcc6d0caa726ecbf7c125e791089f9014543afaf2323", "local-path": "/usr/share/icu/73.2/icudt73l.dat"}, {"ref": "496613c0201dcb5f15a4e0b778a3928d8a5286713392d5b78a21b7845ebc3589", "local-path": "/usr/lib/libstdc++.so.6.0.32"}, {"ref": "f6fcac31afe4fd648a630dd11f52feb9bc7d93a372f799f5e2dd6d77a4493ffa", "local-path": "/usr/lib/libicuuc.so.73.2"}, {"ref": "c298df913d5a0ed6845313a7ee73c8984e9eb97058c63846362924dfbf471b0a", "local-path": "/usr/lib/libicui18n.so.73.2"}, {"ref": "6729c64d0903df39f6c457bda34f57c3eef67047e846513af23471dca6896905", "local-path": "/lib/libcrypto.so.3"}, {"ref": "26db3154da8971320275367f65a673c9d574ecb0eacbf43b5e8b6a216a076790", "local-path": "/usr/lib/libicudata.so.73.2"}, {"ref": "69daeca81eb911328eb4a1731c7a5ca1ab5cbe12507d6cbbd2f2d5ea373363e5", "local-path": "/usr/lib/libbrotlicommon.so.1.1.0"}, {"ref": "a3a07607d41a4475162c5d53834944b6ec1dfbda4fcd41eb7d9d6740f328deb1", "local-path": "/usr/lib/libgcc_s.so.1"}, {"ref": "26d4781c4ec3a72bf6a6ec4a679a4267d3113fbfd2d3d40f1518f8dca628948e", "local-path": "/lib/libssl.so.3"}, {"ref": "d5239d27f3ca8193e067e8c89e7bfe3473cb2c1403aa000836cef685bcc0d19c", "local-path": "/usr/lib/libnghttp2.so.14.25.0"}, {"ref": "2c934fa476809d24c9a5b1b7b96cd6f98abb673e7827ffe0a0041e85d592f47f", "local-path": "/usr/lib/libcares.so.2.7.1"}, {"ref": "2fbc85dc18f92dae2bceca7eaba0c75589b29254bdffe64b8e9074ee727d5814", "local-path": "/usr/lib/libbrotlienc.so.1.1.0"}, {"ref": "6d65dbbc78ff68b69d459a245b04e841db0360eec3cd91b05949d7483bc06078", "local-path": "/usr/lib/libbrotlidec.so.1.1.0"}, {"ref": "312ef720edfec8fbe431952edafede1cac81030d20044706d4402a934bd7ca6f", "local-path": "/usr/lib/libuv.so.1.0.0"}, {"ref": "75fb8b12584466bc4965f2c6c8f37e948a57175d82636af1dc4e90f961c9977f", "local-path": "/lib/libz.so.1.3"}, {"ref": "b4c5ae19cd527ab6f922f82c99423c06929433b5bf3add6e02bfa9b0d6565b39", "local-path": "/usr/lib/libada.so.2.6.0"}, {"ref": "494982426b423f09a034681dcbaca31545f7bec018053198932d54a7160987c5", "local-path": "/lib/ld-musl-x86_64.so.1"}, {"ref": "f40f0ee1aa34f077b676a3b6efd477adbe37090673e31ac90dcde115e8305405", "local-path": "/usr/lib/debug/lib/ld-musl-x86_64.so.1.debug"}]}