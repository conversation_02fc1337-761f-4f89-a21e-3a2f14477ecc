{"coredump-ref": "fb4603a8b17d570c2ea644d95a3547098288189b23f8d0bd70c9b4ad34a3fa1f", "threads": [{"lwp": 7417, "frames": ["libc.so.6+0xd7d10", "node+0x14fa2cf", "node+0x14fb487", "node+0x14fb7ab", "node+0xbd449f", "node+0xbcf923", "node+0xbcfcf3", "V8::ExitFrame+0 in :0", "handleWriteReq+16 in node:internal/stream_base_commons:61", "writeGeneric+2 in node:internal/stream_base_commons:149", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+24 in node:internal/streams/writable:389", "_write+47 in node:internal/streams/writable:330", "Writable.write+1 in node:internal/streams/writable:334", "value+28 in node:internal/console/constructor:289", "warn+1 in node:internal/console/constructor:368", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe30c7f", "node+0xe3184b", "node+0xcf005f", "node+0xbfec2b", "V8::ExitFrame+0 in :0", "trace+6 in node:internal/console/constructor:414", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe30c7f", "node+0xe3184b", "node+0xcf005f", "node+0xbfec2b", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:3", "add3+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:8", "test+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:12", "submain+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:17", "main+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:23", "<anonymous>+26 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1108", "Module._extensions..js+20 in node:internal/modules/cjs/loader:1137", "Module.load+12 in node:internal/modules/cjs/loader:988", "Module._load+76 in node:internal/modules/cjs/loader:828", "executeUserEntryPoint+0 in node:internal/modules/run_main:0", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe30c7f", "node+0xe3184b", "node+0xcf005f", "node+0xacf31f", "node+0xacf53f", "node+0xad082f", "node+0xa5ca2f", "node+0xb40293", "node+0xad25bf", "libc.so.6+0x273fb", "libc.so.6+0x274cb", "node+0xa583e3"]}, {"lwp": 7418, "frames": ["libc.so.6+0xe5f3c", "node+0x1500d3b", "node+0x14efc2f", "node+0xb6add7", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7419, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14fd073", "node+0xb66667", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7420, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14fd073", "node+0xb66667", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7421, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14fd073", "node+0xb66667", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7422, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14fd073", "node+0xb66667", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7423, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x85a5b", "node+0x14fce5b", "node+0xbf25cf", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}], "modules": [{"ref": "c4834bd79254443665af96b6d5e71d124b7f92f2eea121e61f9fff5204fc594d", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30"}, {"ref": "22a0986a1047cd3c9a55368fdc6bb6e5a4455aceb53ec15dbe19112d95583642", "local-path": "/usr/lib/aarch64-linux-gnu/libpthread.so.0"}, {"ref": "fd04b635d29b5cb3faaf502a6c5cd68a623d66a736f4d0561ff280c2fa411c79", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "f1935c0616a48e7ec471c26886df0411beffd4e56c92539d9a6a94641950badf", "local-path": "/usr/lib/aarch64-linux-gnu/libdl.so.2"}, {"ref": "594545a9720b4a16973a823d18c71fbf070d3c07fb17b01df5376273e91644a1", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "acfe23cf0f5f4ac35b4c314415bb31d6f52a9bd39df8be04dfb2818a571533fd", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}, {"ref": "e647ee3042517f06cbebbbf0f66ba25486d0722222404f1d067683767a055566", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "f02199ad95138bb55687957e80ce730496d80a5f629cc7718bbcf5af3299d8b7", "local-path": "/home/<USER>/.nvm/versions/node/v16.0.0/bin/node"}]}