{"flamegraph": {"root": {"name": "root", "cumulative": 48, "childrenList": [{"name": "grpc.(*Server).handleRawConn.func1 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:821", "fullName": "google.golang.org/grpc.(*Server).handleRawConn.func1 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:821", "cumulative": 9, "childrenList": [{"name": "grpc.(*Server).serveStreams /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:862", "fullName": "google.golang.org/grpc.(*Server).serveStreams /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:862", "cumulative": 9, "childrenList": [{"name": "transport.(*http2Server).HandleStreams /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:513", "fullName": "google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:513", "cumulative": 1, "childrenList": [{"name": "transport.(*http2Server).handleData /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:617", "fullName": "google.golang.org/grpc/internal/transport.(*http2Server).handleData /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:617", "cumulative": 1, "childrenList": [{"name": "transport.(*controlBuffer).put /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:311", "fullName": "google.golang.org/grpc/internal/transport.(*controlBuffer).put /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:311", "cumulative": 1, "childrenList": [{"name": "transport.(*controlBuffer).executeAndPut /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:332", "fullName": "google.golang.org/grpc/internal/transport.(*controlBuffer).executeAndPut /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:332", "cumulative": 1, "childrenList": [{"name": "transport.(*itemList).enqueue /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:47", "fullName": "google.golang.org/grpc/internal/transport.(*itemList).enqueue /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:47", "cumulative": 1, "childrenList": [{"name": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "fullName": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "cumulative": 1, "childrenList": [{"name": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1090", "fullName": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1090", "cumulative": 1, "childrenList": [{"name": "runtime.heapBitsSetType /usr/local/go/src/runtime/mbitmap.go:939", "fullName": "runtime.heapBitsSetType /usr/local/go/src/runtime/mbitmap.go:939", "cumulative": 1, "childrenList": [{"name": "runtime.heapBitsForAddr /usr/local/go/src/runtime/mbitmap.go:340", "fullName": "runtime.heapBitsForAddr /usr/local/go/src/runtime/mbitmap.go:340", "cumulative": 1}]}]}]}]}]}]}]}]}, {"name": "transport.(*http2Server).HandleStreams /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:474", "fullName": "google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:474", "cumulative": 8, "childrenList": [{"name": "http2.(*Framer).ReadFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:492", "fullName": "golang.org/x/net/http2.(*Framer).ReadFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:492", "cumulative": 6, "childrenList": [{"name": "http2.readFrameHeader /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:237", "fullName": "golang.org/x/net/http2.readFrameHeader /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:237", "cumulative": 6, "childrenList": [{"name": "io.ReadFull /usr/local/go/src/io/io.go:333", "fullName": "io.ReadFull /usr/local/go/src/io/io.go:333", "cumulative": 6, "childrenList": [{"name": "io.ReadAtLeast /usr/local/go/src/io/io.go:314", "fullName": "io.ReadAtLeast /usr/local/go/src/io/io.go:314", "cumulative": 6, "childrenList": [{"name": "bufio.(*Reader).Read /usr/local/go/src/bufio/bufio.go:227", "fullName": "bufio.(*Reader).Read /usr/local/go/src/bufio/bufio.go:227", "cumulative": 6, "childrenList": [{"name": "net.(*conn).Read /usr/local/go/src/net/net.go:182", "fullName": "net.(*conn).Read /usr/local/go/src/net/net.go:182", "cumulative": 6, "childrenList": [{"name": "net.(*netFD).Read /usr/local/go/src/net/fd_posix.go:55", "fullName": "net.(*netFD).Read /usr/local/go/src/net/fd_posix.go:55", "cumulative": 6, "childrenList": [{"name": "poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 6, "childrenList": [{"name": "poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "fullName": "internal/poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "cumulative": 6, "childrenList": [{"name": "poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 6, "childrenList": [{"name": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "fullName": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "cumulative": 6, "childrenList": [{"name": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "fullName": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "cumulative": 6, "childrenList": [{"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "cumulative": 4}, {"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:18", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:18", "cumulative": 2, "childrenList": [{"name": "runtime.entersyscall /usr/local/go/src/runtime/proc.go:3141", "fullName": "runtime.entersyscall /usr/local/go/src/runtime/proc.go:3141", "cumulative": 2, "childrenList": [{"name": "runtime.reentersyscall /usr/local/go/src/runtime/proc.go:3109", "fullName": "runtime.reentersyscall /usr/local/go/src/runtime/proc.go:3109", "cumulative": 2, "childrenList": [{"name": "runtime.systemstack /usr/local/go/src/runtime/asm_amd64.s:370", "fullName": "runtime.systemstack /usr/local/go/src/runtime/asm_amd64.s:370", "cumulative": 2, "childrenList": [{"name": "runtime.entersyscall_sysmon /usr/local/go/src/runtime/proc.go:3148", "fullName": "runtime.entersyscall_sysmon /usr/local/go/src/runtime/proc.go:3148", "cumulative": 2, "childrenList": [{"name": "runtime.notewakeup /usr/local/go/src/runtime/lock_futex.go:144", "fullName": "runtime.notewakeup /usr/local/go/src/runtime/lock_futex.go:144", "cumulative": 2, "childrenList": [{"name": "runtime.futexwakeup /usr/local/go/src/runtime/os_linux.go:56", "fullName": "runtime.futexwakeup /usr/local/go/src/runtime/os_linux.go:56", "cumulative": 1}, {"name": "runtime.futexwakeup /usr/local/go/src/runtime/os_linux.go:57", "fullName": "runtime.futexwakeup /usr/local/go/src/runtime/os_linux.go:57", "cumulative": 1, "childrenList": [{"name": "runtime.futex /usr/local/go/src/runtime/sys_linux_amd64.s:588", "fullName": "runtime.futex /usr/local/go/src/runtime/sys_linux_amd64.s:588", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"name": "http2.(*Framer).ReadFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:503", "fullName": "golang.org/x/net/http2.(*Framer).ReadFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:503", "cumulative": 1, "childrenList": [{"name": "http2.parseHeadersFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:992", "fullName": "golang.org/x/net/http2.parseHeadersFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:992", "cumulative": 1, "childrenList": [{"name": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "fullName": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "cumulative": 1, "childrenList": [{"name": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1090", "fullName": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1090", "cumulative": 1, "childrenList": [{"name": "runtime.heapBitsSetType /usr/local/go/src/runtime/mbitmap.go:1139", "fullName": "runtime.heapBitsSetType /usr/local/go/src/runtime/mbitmap.go:1139", "cumulative": 1}]}]}]}]}, {"name": "http2.(*Framer).ReadFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:500", "fullName": "golang.org/x/net/http2.(*Framer).ReadFrame /go/pkg/mod/golang.org/x/net@v0.0.0-20210505214959-0714010a04ed/http2/frame.go:500", "cumulative": 1, "childrenList": [{"name": "io.ReadFull /usr/local/go/src/io/io.go:333", "fullName": "io.ReadFull /usr/local/go/src/io/io.go:333", "cumulative": 1, "childrenList": [{"name": "io.ReadAtLeast /usr/local/go/src/io/io.go:314", "fullName": "io.ReadAtLeast /usr/local/go/src/io/io.go:314", "cumulative": 1, "childrenList": [{"name": "bufio.(*Reader).Read /usr/local/go/src/bufio/bufio.go:227", "fullName": "bufio.(*Reader).Read /usr/local/go/src/bufio/bufio.go:227", "cumulative": 1, "childrenList": [{"name": "net.(*conn).Read /usr/local/go/src/net/net.go:182", "fullName": "net.(*conn).Read /usr/local/go/src/net/net.go:182", "cumulative": 1, "childrenList": [{"name": "net.(*netFD).Read /usr/local/go/src/net/fd_posix.go:55", "fullName": "net.(*netFD).Read /usr/local/go/src/net/fd_posix.go:55", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 1, "childrenList": [{"name": "poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "fullName": "internal/poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 1, "childrenList": [{"name": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "fullName": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "cumulative": 1, "childrenList": [{"name": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "fullName": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "cumulative": 1, "childrenList": [{"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"name": "grpc.(*Server).serveStreams.func1.2 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:878", "fullName": "google.golang.org/grpc.(*Server).serveStreams.func1.2 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:878", "cumulative": 11, "childrenList": [{"name": "grpc.(*Server).handleStream /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1540", "fullName": "google.golang.org/grpc.(*Server).handleStream /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1540", "cumulative": 11, "childrenList": [{"name": "grpc.(*Server).processUnaryRPC /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1217", "fullName": "google.golang.org/grpc.(*Server).processUnaryRPC /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1217", "cumulative": 11, "childrenList": [{"name": "storepb._WritableProfileStore_Write_Handler /app/pkg/store/storepb/rpc.pb.go:1359", "fullName": "github.com/conprof/conprof/pkg/store/storepb._WritableProfileStore_Write_Handler /app/pkg/store/storepb/rpc.pb.go:1359", "cumulative": 11, "childrenList": [{"name": "grpc.chainUnaryServerInterceptors.func1 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1044", "fullName": "google.golang.org/grpc.chainUnaryServerInterceptors.func1 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1044", "cumulative": 11, "childrenList": [{"name": "go-grpc-middleware.ChainUnaryServer.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:36", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2.ChainUnaryServer.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:36", "cumulative": 11, "childrenList": [{"name": "go-grpc-middleware.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "cumulative": 11, "childrenList": [{"name": "recovery.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/recovery/interceptors.go:31", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/recovery.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/recovery/interceptors.go:31", "cumulative": 11, "childrenList": [{"name": "go-grpc-middleware.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "cumulative": 11, "childrenList": [{"name": "go-grpc-prometheus.(*ServerMetrics).UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-prometheus@v1.2.0/server_metrics.go:107", "fullName": "github.com/grpc-ecosystem/go-grpc-prometheus.(*ServerMetrics).UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-prometheus@v1.2.0/server_metrics.go:107", "cumulative": 11, "childrenList": [{"name": "go-grpc-middleware.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "cumulative": 11, "childrenList": [{"name": "interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:22", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:22", "cumulative": 10, "childrenList": [{"name": "go-grpc-middleware.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "cumulative": 10, "childrenList": [{"name": "tracing.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/thanos-io/thanos@v0.21.0-rc.0/pkg/tracing/grpc.go:30", "fullName": "github.com/thanos-io/thanos/pkg/tracing.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/thanos-io/thanos@v0.21.0-rc.0/pkg/tracing/grpc.go:30", "cumulative": 10, "childrenList": [{"name": "interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:22", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:22", "cumulative": 10, "childrenList": [{"name": "go-grpc-middleware.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2.ChainUnaryServer.func1.1.1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/chain.go:27", "cumulative": 10, "childrenList": [{"name": "interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:22", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:22", "cumulative": 9, "childrenList": [{"name": "grpc.getChainUnaryHandler.func1 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1058", "fullName": "google.golang.org/grpc.getChainUnaryHandler.func1 /go/pkg/mod/google.golang.org/grpc@v1.37.0/server.go:1058", "cumulative": 9, "childrenList": [{"name": "otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:319", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:319", "cumulative": 2, "childrenList": [{"name": "otelgrpc.Extract /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/grpctrace.go:125", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.Extract /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/grpctrace.go:125", "cumulative": 2, "childrenList": [{"name": "propagation.TraceContext.Extract /go/pkg/mod/go.opentelemetry.io/otel@v0.20.0/propagation/trace_context.go:74", "fullName": "go.opentelemetry.io/otel/propagation.TraceContext.Extract /go/pkg/mod/go.opentelemetry.io/otel@v0.20.0/propagation/trace_context.go:74", "cumulative": 2, "childrenList": [{"name": "propagation.TraceContext.extract /go/pkg/mod/go.opentelemetry.io/otel@v0.20.0/propagation/trace_context.go:87", "fullName": "go.opentelemetry.io/otel/propagation.TraceContext.extract /go/pkg/mod/go.opentelemetry.io/otel@v0.20.0/propagation/trace_context.go:87", "cumulative": 2, "childrenList": [{"name": "regexp.(*Regexp).FindStringSubmatch /usr/local/go/src/regexp/regexp.go:1039", "fullName": "regexp.(*Regexp).FindStringSubmatch /usr/local/go/src/regexp/regexp.go:1039", "cumulative": 2, "childrenList": [{"name": "regexp.(*Regexp).doExecute /usr/local/go/src/regexp/exec.go:532", "fullName": "regexp.(*Regexp).doExecute /usr/local/go/src/regexp/exec.go:532", "cumulative": 2, "childrenList": [{"name": "regexp.(*Regexp).doOnePass /usr/local/go/src/regexp/exec.go:445", "fullName": "regexp.(*Regexp).doOnePass /usr/local/go/src/regexp/exec.go:445", "cumulative": 1}, {"name": "regexp.(*Regexp).doOnePass /usr/local/go/src/regexp/exec.go:447", "fullName": "regexp.(*Regexp).doOnePass /usr/local/go/src/regexp/exec.go:447", "cumulative": 1}]}]}]}]}]}]}, {"name": "otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:349", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:349", "cumulative": 1, "childrenList": [{"name": "trace.(*span).End /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:242", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*span).End /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:242", "cumulative": 1, "childrenList": [{"name": "trace.(*batchSpanProcessor).OnEnd /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:126", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*batchSpanProcessor).OnEnd /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:126", "cumulative": 1, "childrenList": [{"name": "trace.(*span).Snapshot /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:445", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*span).Snapshot /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:445", "cumulative": 1}]}]}]}, {"name": "otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:336", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:336", "cumulative": 1, "childrenList": [{"name": "otelgrpc.messageType.Event /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:52", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.messageType.Event /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:52", "cumulative": 1, "childrenList": [{"name": "proto.Size /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/wire.go:17", "fullName": "github.com/golang/protobuf/proto.Size /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/wire.go:17", "cumulative": 1, "childrenList": [{"name": "proto.MessageV2 /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/proto.go:61", "fullName": "github.com/golang/protobuf/proto.MessageV2 /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/proto.go:61", "cumulative": 1, "childrenList": [{"name": "impl.Export.ProtoMessageV2Of /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/api_export.go:134", "fullName": "google.golang.org/protobuf/internal/impl.Export.ProtoMessageV2Of /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/api_export.go:134", "cumulative": 1, "childrenList": [{"name": "impl.legacyWrapMessage /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/legacy_message.go:31", "fullName": "google.golang.org/protobuf/internal/impl.legacyWrapMessage /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/legacy_message.go:31", "cumulative": 1, "childrenList": [{"name": "impl.legacyLoadMessageInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/legacy_message.go:52", "fullName": "google.golang.org/protobuf/internal/impl.legacyLoadMessageInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/legacy_message.go:52", "cumulative": 1, "childrenList": [{"name": "sync.(*Map).Load /usr/local/go/src/sync/map.go:104", "fullName": "sync.(*Map).Load /usr/local/go/src/sync/map.go:104", "cumulative": 1, "childrenList": [{"name": "runtime.mapaccess2 /usr/local/go/src/runtime/map.go:488", "fullName": "runtime.mapaccess2 /usr/local/go/src/runtime/map.go:488", "cumulative": 1}]}]}]}]}]}]}]}]}, {"name": "otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:338", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:338", "cumulative": 4, "childrenList": [{"name": "storepb._WritableProfileStore_Write_Handler.func1 /app/pkg/store/storepb/rpc.pb.go:1357", "fullName": "github.com/conprof/conprof/pkg/store/storepb._WritableProfileStore_Write_Handler.func1 /app/pkg/store/storepb/rpc.pb.go:1357", "cumulative": 4, "childrenList": [{"name": "store.(*profileStore).Write /app/pkg/store/store.go:102", "fullName": "github.com/conprof/conprof/pkg/store.(*profileStore).Write /app/pkg/store/store.go:102", "cumulative": 3, "childrenList": [{"name": "tsdb.dbAppender.Commit /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/db.go:776", "fullName": "github.com/conprof/db/tsdb.dbAppender.Commit /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/db.go:776", "cumulative": 3, "childrenList": [{"name": "tsdb.(*headAppender).Commit /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:1258", "fullName": "github.com/conprof/db/tsdb.(*headAppender).Commit /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:1258", "cumulative": 2, "childrenList": [{"name": "tsdb.(*headAppender).log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:1246", "fullName": "github.com/conprof/db/tsdb.(*headAppender).log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:1246", "cumulative": 2, "childrenList": [{"name": "wal.(*WAL).Log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:595", "fullName": "github.com/conprof/db/tsdb/wal.(*WAL).Log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:595", "cumulative": 2, "childrenList": [{"name": "wal.(*WAL).log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:676", "fullName": "github.com/conprof/db/tsdb/wal.(*WAL).log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:676", "cumulative": 1, "childrenList": [{"name": "wal.(*WAL).flushPage /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:529", "fullName": "github.com/conprof/db/tsdb/wal.(*WAL).flushPage /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:529", "cumulative": 1, "childrenList": [{"name": "os.(*File).Write /usr/local/go/src/os/file.go:173", "fullName": "os.(*File).Write /usr/local/go/src/os/file.go:173", "cumulative": 1, "childrenList": [{"name": "os.(*File).write /usr/local/go/src/os/file_posix.go:48", "fullName": "os.(*File).write /usr/local/go/src/os/file_posix.go:48", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Write /usr/local/go/src/internal/poll/fd_unix.go:267", "fullName": "internal/poll.(*FD).Write /usr/local/go/src/internal/poll/fd_unix.go:267", "cumulative": 1, "childrenList": [{"name": "poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "fullName": "internal/poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Write.func1 /usr/local/go/src/internal/poll/fd_unix.go:267", "fullName": "internal/poll.(*FD).Write.func1 /usr/local/go/src/internal/poll/fd_unix.go:267", "cumulative": 1, "childrenList": [{"name": "syscall.Write /usr/local/go/src/syscall/syscall_unix.go:212", "fullName": "syscall.Write /usr/local/go/src/syscall/syscall_unix.go:212", "cumulative": 1, "childrenList": [{"name": "syscall.write /usr/local/go/src/syscall/zsyscall_linux_amd64.go:914", "fullName": "syscall.write /usr/local/go/src/syscall/zsyscall_linux_amd64.go:914", "cumulative": 1, "childrenList": [{"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "cumulative": 1}]}]}]}]}]}]}]}]}]}, {"name": "wal.(*WAL).log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:633", "fullName": "github.com/conprof/db/tsdb/wal.(*WAL).log /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/wal/wal.go:633", "cumulative": 1, "childrenList": [{"name": "snappy.Encode /go/pkg/mod/github.com/golang/snappy@v0.0.3/encode.go:39", "fullName": "github.com/golang/snappy.Encode /go/pkg/mod/github.com/golang/snappy@v0.0.3/encode.go:39", "cumulative": 1, "childrenList": [{"name": "snappy.encodeBlock /go/pkg/mod/github.com/golang/snappy@v0.0.3/encode_amd64.s:356", "fullName": "github.com/golang/snappy.encodeBlock /go/pkg/mod/github.com/golang/snappy@v0.0.3/encode_amd64.s:356", "cumulative": 1}]}]}]}]}]}, {"name": "tsdb.(*headAppender).Commit /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:1274", "fullName": "github.com/conprof/db/tsdb.(*headAppender).Commit /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:1274", "cumulative": 1, "childrenList": [{"name": "tsdb.(*memSeries).append /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:2185", "fullName": "github.com/conprof/db/tsdb.(*memSeries).append /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/head.go:2185", "cumulative": 1, "childrenList": [{"name": "chunkenc.(*BytesAppender).Append /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/chunkenc/byte.go:167", "fullName": "github.com/conprof/db/tsdb/chunkenc.(*BytesAppender).Append /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/chunkenc/byte.go:167", "cumulative": 1, "childrenList": [{"name": "chunkenc.(*valueAppender).Append /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/chunkenc/value.go:106", "fullName": "github.com/conprof/db/tsdb/chunkenc.(*valueAppender).Append /go/pkg/mod/github.com/conprof/db@v0.0.0-20210317165925-a59fb33c527d/tsdb/chunkenc/value.go:106", "cumulative": 1, "childrenList": [{"name": "runtime.growslice /usr/local/go/src/runtime/slice.go:227", "fullName": "runtime.growslice /usr/local/go/src/runtime/slice.go:227", "cumulative": 1, "childrenList": [{"name": "runtime.memclrNoHeapPointers /usr/local/go/src/runtime/memclr_amd64.s:77", "fullName": "runtime.memclrNoHeapPointers /usr/local/go/src/runtime/memclr_amd64.s:77", "cumulative": 1}]}]}]}]}]}]}]}, {"name": "store.(*profileStore).Write /app/pkg/store/store.go:95", "fullName": "github.com/conprof/conprof/pkg/store.(*profileStore).Write /app/pkg/store/store.go:95", "cumulative": 1}]}]}, {"name": "otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:345", "fullName": "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1 /go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.20.0/interceptor.go:345", "cumulative": 1, "childrenList": [{"name": "trace.(*span).SetAttributes /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:183", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*span).SetAttributes /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:183", "cumulative": 1, "childrenList": [{"name": "trace.(*span).copyToCappedAttributes /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:487", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*span).copyToCappedAttributes /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/span.go:487", "cumulative": 1, "childrenList": [{"name": "trace.(*attributesMap).add /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/attributesmap.go:54", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*attributesMap).add /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/attributesmap.go:54", "cumulative": 1, "childrenList": [{"name": "list.(*List).PushFront /usr/local/go/src/container/list/list.go:149", "fullName": "container/list.(*List).PushFront /usr/local/go/src/container/list/list.go:149", "cumulative": 1, "childrenList": [{"name": "list.(*List).insertValue /usr/local/go/src/container/list/list.go:104", "fullName": "container/list.(*List).insertValue /usr/local/go/src/container/list/list.go:104", "cumulative": 1, "childrenList": [{"name": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "fullName": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "cumulative": 1, "childrenList": [{"name": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1059", "fullName": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1059", "cumulative": 1, "childrenList": [{"name": "runtime.nextFreeFast /usr/local/go/src/runtime/malloc.go:855", "fullName": "runtime.nextFreeFast /usr/local/go/src/runtime/malloc.go:855", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}, {"name": "interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:19", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:19", "cumulative": 1, "childrenList": [{"name": "logging.(*reportable).ServerReporter /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/logging/interceptors.go:84", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/logging.(*reportable).ServerReporter /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/logging/interceptors.go:84", "cumulative": 1, "childrenList": [{"name": "logging.(*reportable).reporter /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/logging/interceptors.go:95", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/logging.(*reportable).reporter /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/logging/interceptors.go:95", "cumulative": 1, "childrenList": [{"name": "time.Time.Format /usr/local/go/src/time/format.go:505", "fullName": "time.Time.Format /usr/local/go/src/time/format.go:505", "cumulative": 1, "childrenList": [{"name": "time.Time.AppendFormat /usr/local/go/src/time/format.go:610", "fullName": "time.Time.AppendFormat /usr/local/go/src/time/format.go:610", "cumulative": 1}]}]}]}]}]}]}]}]}]}, {"name": "interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:19", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors.UnaryServerInterceptor.func1 /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/server.go:19", "cumulative": 1, "childrenList": [{"name": "tags.(*reportable).ServerReporter /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/tags/interceptors.go:52", "fullName": "github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/tags.(*reportable).ServerReporter /go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware/v2@v2.0.0-rc.2.0.20201207153454-9f6bf00c00a7/interceptors/tags/interceptors.go:52", "cumulative": 1, "childrenList": [{"name": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "fullName": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "cumulative": 1, "childrenList": [{"name": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1090", "fullName": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1090", "cumulative": 1, "childrenList": [{"name": "runtime.heapBitsSetType /usr/local/go/src/runtime/mbitmap.go:340", "fullName": "runtime.heapBitsSetType /usr/local/go/src/runtime/mbitmap.go:340", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"name": "runtime.mcall /usr/local/go/src/runtime/asm_amd64.s:318", "fullName": "runtime.mcall /usr/local/go/src/runtime/asm_amd64.s:318", "cumulative": 10, "childrenList": [{"name": "runtime.park_m /usr/local/go/src/runtime/proc.go:2851", "fullName": "runtime.park_m /usr/local/go/src/runtime/proc.go:2851", "cumulative": 10, "childrenList": [{"name": "runtime.schedule /usr/local/go/src/runtime/proc.go:2683", "fullName": "runtime.schedule /usr/local/go/src/runtime/proc.go:2683", "cumulative": 10, "childrenList": [{"name": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2240", "fullName": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2240", "cumulative": 1, "childrenList": [{"name": "runtime.netpoll /usr/local/go/src/runtime/netpoll_epoll.go:126", "fullName": "runtime.netpoll /usr/local/go/src/runtime/netpoll_epoll.go:126", "cumulative": 1, "childrenList": [{"name": "runtime.epollwait /usr/local/go/src/runtime/sys_linux_amd64.s:725", "fullName": "runtime.epollwait /usr/local/go/src/runtime/sys_linux_amd64.s:725", "cumulative": 1}]}]}, {"name": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2448", "fullName": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2448", "cumulative": 4, "childrenList": [{"name": "runtime.netpoll /usr/local/go/src/runtime/netpoll_epoll.go:126", "fullName": "runtime.netpoll /usr/local/go/src/runtime/netpoll_epoll.go:126", "cumulative": 4, "childrenList": [{"name": "runtime.epollwait /usr/local/go/src/runtime/sys_linux_amd64.s:725", "fullName": "runtime.epollwait /usr/local/go/src/runtime/sys_linux_amd64.s:725", "cumulative": 4}]}]}, {"name": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2485", "fullName": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2485", "cumulative": 4, "childrenList": [{"name": "runtime.stopm /usr/local/go/src/runtime/proc.go:1924", "fullName": "runtime.stopm /usr/local/go/src/runtime/proc.go:1924", "cumulative": 4, "childrenList": [{"name": "runtime.notesleep /usr/local/go/src/runtime/lock_futex.go:159", "fullName": "runtime.notesleep /usr/local/go/src/runtime/lock_futex.go:159", "cumulative": 4, "childrenList": [{"name": "runtime.futexsleep /usr/local/go/src/runtime/os_linux.go:45", "fullName": "runtime.futexsleep /usr/local/go/src/runtime/os_linux.go:45", "cumulative": 4, "childrenList": [{"name": "runtime.futex /usr/local/go/src/runtime/sys_linux_amd64.s:588", "fullName": "runtime.futex /usr/local/go/src/runtime/sys_linux_amd64.s:588", "cumulative": 3}, {"name": "runtime.futex /usr/local/go/src/runtime/sys_linux_amd64.s:589", "fullName": "runtime.futex /usr/local/go/src/runtime/sys_linux_amd64.s:589", "cumulative": 1}]}]}]}]}, {"name": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2265", "fullName": "runtime.findrunnable /usr/local/go/src/runtime/proc.go:2265", "cumulative": 1, "childrenList": [{"name": "runtime.(*randomOrder).start /usr/local/go/src/runtime/proc.go:5601", "fullName": "runtime.(*randomOrder).start /usr/local/go/src/runtime/proc.go:5601", "cumulative": 1}]}]}]}]}, {"name": "http.(*conn).serve /usr/local/go/src/net/http/server.go:1925", "fullName": "net/http.(*conn).serve /usr/local/go/src/net/http/server.go:1925", "cumulative": 6, "childrenList": [{"name": "http.serverHandler.ServeHTTP /usr/local/go/src/net/http/server.go:2843", "fullName": "net/http.serverHandler.ServeHTTP /usr/local/go/src/net/http/server.go:2843", "cumulative": 6, "childrenList": [{"name": "http.(*ServeMux).ServeHTTP /usr/local/go/src/net/http/server.go:2417", "fullName": "net/http.(*ServeMux).ServeHTTP /usr/local/go/src/net/http/server.go:2417", "cumulative": 6, "childrenList": [{"name": "http.HandlerFunc.ServeHTTP /usr/local/go/src/net/http/server.go:2042", "fullName": "net/http.HandlerFunc.ServeHTTP /usr/local/go/src/net/http/server.go:2042", "cumulative": 6, "childrenList": [{"name": "pprof.Index /usr/local/go/src/net/http/pprof/pprof.go:367", "fullName": "net/http/pprof.Index /usr/local/go/src/net/http/pprof/pprof.go:367", "cumulative": 6, "childrenList": [{"name": "pprof.handler.ServeHTTP /usr/local/go/src/net/http/pprof/pprof.go:256", "fullName": "net/http/pprof.handler.ServeHTTP /usr/local/go/src/net/http/pprof/pprof.go:256", "cumulative": 6, "childrenList": [{"name": "pprof.(*Profile).WriteTo /usr/local/go/src/runtime/pprof/pprof.go:331", "fullName": "runtime/pprof.(*Profile).WriteTo /usr/local/go/src/runtime/pprof/pprof.go:331", "cumulative": 6, "childrenList": [{"name": "pprof.writeAlloc /usr/local/go/src/runtime/pprof/pprof.go:542", "fullName": "runtime/pprof.writeAlloc /usr/local/go/src/runtime/pprof/pprof.go:542", "cumulative": 3, "childrenList": [{"name": "pprof.writeHeapInternal /usr/local/go/src/runtime/pprof/pprof.go:576", "fullName": "runtime/pprof.writeHeapInternal /usr/local/go/src/runtime/pprof/pprof.go:576", "cumulative": 3, "childrenList": [{"name": "pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:46", "fullName": "runtime/pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:46", "cumulative": 1, "childrenList": [{"name": "pprof.(*profileBuilder).appendLocsForStack /usr/local/go/src/runtime/pprof/proto.go:414", "fullName": "runtime/pprof.(*profileBuilder).appendLocsForStack /usr/local/go/src/runtime/pprof/proto.go:414", "cumulative": 1, "childrenList": [{"name": "pprof.allFrames /usr/local/go/src/runtime/pprof/proto.go:217", "fullName": "runtime/pprof.allFrames /usr/local/go/src/runtime/pprof/proto.go:217", "cumulative": 1, "childrenList": [{"name": "runtime.(*Frames).Next /usr/local/go/src/runtime/symtab.go:147", "fullName": "runtime.(*Frames).Next /usr/local/go/src/runtime/symtab.go:147", "cumulative": 1, "childrenList": [{"name": "runtime.funcline1 /usr/local/go/src/runtime/symtab.go:856", "fullName": "runtime.funcline1 /usr/local/go/src/runtime/symtab.go:856", "cumulative": 1, "childrenList": [{"name": "runtime.pcvalue /usr/local/go/src/runtime/symtab.go:769", "fullName": "runtime.pcvalue /usr/local/go/src/runtime/symtab.go:769", "cumulative": 1, "childrenList": [{"name": "runtime.step /usr/local/go/src/runtime/symtab.go:941", "fullName": "runtime.step /usr/local/go/src/runtime/symtab.go:941", "cumulative": 1}]}]}]}]}]}]}, {"name": "pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:38", "fullName": "runtime/pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:38", "cumulative": 1, "childrenList": [{"name": "runtime.FuncForPC /usr/local/go/src/runtime/symtab.go:582", "fullName": "runtime.FuncForPC /usr/local/go/src/runtime/symtab.go:582", "cumulative": 1, "childrenList": [{"name": "runtime.pcdatavalue1 /usr/local/go/src/runtime/symtab.go:913", "fullName": "runtime.pcdatavalue1 /usr/local/go/src/runtime/symtab.go:913", "cumulative": 1, "childrenList": [{"name": "runtime.pcvalue /usr/local/go/src/runtime/symtab.go:769", "fullName": "runtime.pcvalue /usr/local/go/src/runtime/symtab.go:769", "cumulative": 1, "childrenList": [{"name": "runtime.step /usr/local/go/src/runtime/symtab.go:957", "fullName": "runtime.step /usr/local/go/src/runtime/symtab.go:957", "cumulative": 1}]}]}]}]}, {"name": "pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:59", "fullName": "runtime/pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:59", "cumulative": 1, "childrenList": [{"name": "pprof.(*profileBuilder).pbSample /usr/local/go/src/runtime/pprof/proto.go:170", "fullName": "runtime/pprof.(*profileBuilder).pbSample /usr/local/go/src/runtime/pprof/proto.go:170", "cumulative": 1, "childrenList": [{"name": "pprof.(*profileBuilder).flush /usr/local/go/src/runtime/pprof/proto.go:148", "fullName": "runtime/pprof.(*profileBuilder).flush /usr/local/go/src/runtime/pprof/proto.go:148", "cumulative": 1, "childrenList": [{"name": "gzip.(*Writer).Write /usr/local/go/src/compress/gzip/gzip.go:196", "fullName": "compress/gzip.(*Writer).Write /usr/local/go/src/compress/gzip/gzip.go:196", "cumulative": 1, "childrenList": [{"name": "flate.(*Writer).Write /usr/local/go/src/compress/flate/deflate.go:712", "fullName": "compress/flate.(*Writer).Write /usr/local/go/src/compress/flate/deflate.go:712", "cumulative": 1, "childrenList": [{"name": "flate.(*compressor).write /usr/local/go/src/compress/flate/deflate.go:554", "fullName": "compress/flate.(*compressor).write /usr/local/go/src/compress/flate/deflate.go:554", "cumulative": 1, "childrenList": [{"name": "flate.(*compressor).encSpeed /usr/local/go/src/compress/flate/deflate.go:365", "fullName": "compress/flate.(*compressor).encSpeed /usr/local/go/src/compress/flate/deflate.go:365", "cumulative": 1, "childrenList": [{"name": "flate.(*huffmanBitWriter).writeBlockDynamic /usr/local/go/src/compress/flate/huffman_bit_writer.go:528", "fullName": "compress/flate.(*huffmanBitWriter).writeBlockDynamic /usr/local/go/src/compress/flate/huffman_bit_writer.go:528", "cumulative": 1, "childrenList": [{"name": "flate.(*huffmanBitWriter).writeTokens /usr/local/go/src/compress/flate/huffman_bit_writer.go:581", "fullName": "compress/flate.(*huffmanBitWriter).writeTokens /usr/local/go/src/compress/flate/huffman_bit_writer.go:581", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}, {"name": "pprof.writeBlock /usr/local/go/src/runtime/pprof/pprof.go:860", "fullName": "runtime/pprof.writeBlock /usr/local/go/src/runtime/pprof/pprof.go:860", "cumulative": 1, "childrenList": [{"name": "pprof.printCountCycleProfile /usr/local/go/src/runtime/pprof/pprof.go:379", "fullName": "runtime/pprof.printCountCycleProfile /usr/local/go/src/runtime/pprof/pprof.go:379", "cumulative": 1, "childrenList": [{"name": "pprof.newProfileBuilder /usr/local/go/src/runtime/pprof/proto.go:267", "fullName": "runtime/pprof.newProfileBuilder /usr/local/go/src/runtime/pprof/proto.go:267", "cumulative": 1, "childrenList": [{"name": "pprof.(*profileBuilder).readMapping /usr/local/go/src/runtime/pprof/proto.go:578", "fullName": "runtime/pprof.(*profileBuilder).readMapping /usr/local/go/src/runtime/pprof/proto.go:578", "cumulative": 1, "childrenList": [{"name": "ioutil.ReadFile /usr/local/go/src/io/ioutil/ioutil.go:73", "fullName": "io/ioutil.ReadFile /usr/local/go/src/io/ioutil/ioutil.go:73", "cumulative": 1, "childrenList": [{"name": "ioutil.readAll /usr/local/go/src/io/ioutil/ioutil.go:36", "fullName": "io/ioutil.readAll /usr/local/go/src/io/ioutil/ioutil.go:36", "cumulative": 1, "childrenList": [{"name": "bytes.(*<PERSON><PERSON>er).ReadFrom /usr/local/go/src/bytes/buffer.go:204", "fullName": "bytes.(*<PERSON><PERSON>er).ReadFrom /usr/local/go/src/bytes/buffer.go:204", "cumulative": 1, "childrenList": [{"name": "os.(*File).Read /usr/local/go/src/os/file.go:116", "fullName": "os.(*File).Read /usr/local/go/src/os/file.go:116", "cumulative": 1, "childrenList": [{"name": "os.(*File).read /usr/local/go/src/os/file_posix.go:31", "fullName": "os.(*File).read /usr/local/go/src/os/file_posix.go:31", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 1, "childrenList": [{"name": "poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "fullName": "internal/poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 1, "childrenList": [{"name": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "fullName": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "cumulative": 1, "childrenList": [{"name": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "fullName": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "cumulative": 1, "childrenList": [{"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"name": "pprof.writeHeap /usr/local/go/src/runtime/pprof/pprof.go:536", "fullName": "runtime/pprof.writeHeap /usr/local/go/src/runtime/pprof/pprof.go:536", "cumulative": 2, "childrenList": [{"name": "pprof.writeHeapInternal /usr/local/go/src/runtime/pprof/pprof.go:576", "fullName": "runtime/pprof.writeHeapInternal /usr/local/go/src/runtime/pprof/pprof.go:576", "cumulative": 2, "childrenList": [{"name": "pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:46", "fullName": "runtime/pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:46", "cumulative": 1, "childrenList": [{"name": "pprof.(*profileBuilder).appendLocsForStack /usr/local/go/src/runtime/pprof/proto.go:414", "fullName": "runtime/pprof.(*profileBuilder).appendLocsForStack /usr/local/go/src/runtime/pprof/proto.go:414", "cumulative": 1, "childrenList": [{"name": "pprof.allFrames /usr/local/go/src/runtime/pprof/proto.go:216", "fullName": "runtime/pprof.allFrames /usr/local/go/src/runtime/pprof/proto.go:216", "cumulative": 1, "childrenList": [{"name": "runtime.CallersFrames /usr/local/go/src/runtime/symtab.go:66", "fullName": "runtime.CallersFrames /usr/local/go/src/runtime/symtab.go:66", "cumulative": 1, "childrenList": [{"name": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "fullName": "runtime.newobject /usr/local/go/src/runtime/malloc.go:1195", "cumulative": 1, "childrenList": [{"name": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1061", "fullName": "runtime.mallocgc /usr/local/go/src/runtime/malloc.go:1061", "cumulative": 1, "childrenList": [{"name": "runtime.(*mcache).nextFree /usr/local/go/src/runtime/malloc.go:880", "fullName": "runtime.(*mcache).nextFree /usr/local/go/src/runtime/malloc.go:880", "cumulative": 1, "childrenList": [{"name": "runtime.(*mcache).refill /usr/local/go/src/runtime/mcache.go:135", "fullName": "runtime.(*mcache).refill /usr/local/go/src/runtime/mcache.go:135", "cumulative": 1, "childrenList": [{"name": "runtime.(*mcentral).uncacheSpan /usr/local/go/src/runtime/mcentral.go:391", "fullName": "runtime.(*mcentral).uncacheSpan /usr/local/go/src/runtime/mcentral.go:391", "cumulative": 1, "childrenList": [{"name": "runtime.(*spanSet).push /usr/local/go/src/runtime/mspanset.go:137", "fullName": "runtime.(*spanSet).push /usr/local/go/src/runtime/mspanset.go:137", "cumulative": 1}]}]}]}]}]}]}]}]}]}, {"name": "pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:16", "fullName": "runtime/pprof.writeHeapProto /usr/local/go/src/runtime/pprof/protomem.go:16", "cumulative": 1, "childrenList": [{"name": "pprof.newProfileBuilder /usr/local/go/src/runtime/pprof/proto.go:267", "fullName": "runtime/pprof.newProfileBuilder /usr/local/go/src/runtime/pprof/proto.go:267", "cumulative": 1, "childrenList": [{"name": "pprof.(*profileBuilder).readMapping /usr/local/go/src/runtime/pprof/proto.go:578", "fullName": "runtime/pprof.(*profileBuilder).readMapping /usr/local/go/src/runtime/pprof/proto.go:578", "cumulative": 1, "childrenList": [{"name": "ioutil.ReadFile /usr/local/go/src/io/ioutil/ioutil.go:73", "fullName": "io/ioutil.ReadFile /usr/local/go/src/io/ioutil/ioutil.go:73", "cumulative": 1, "childrenList": [{"name": "ioutil.readAll /usr/local/go/src/io/ioutil/ioutil.go:36", "fullName": "io/ioutil.readAll /usr/local/go/src/io/ioutil/ioutil.go:36", "cumulative": 1, "childrenList": [{"name": "bytes.(*<PERSON><PERSON>er).ReadFrom /usr/local/go/src/bytes/buffer.go:204", "fullName": "bytes.(*<PERSON><PERSON>er).ReadFrom /usr/local/go/src/bytes/buffer.go:204", "cumulative": 1, "childrenList": [{"name": "os.(*File).Read /usr/local/go/src/os/file.go:116", "fullName": "os.(*File).Read /usr/local/go/src/os/file.go:116", "cumulative": 1, "childrenList": [{"name": "os.(*File).read /usr/local/go/src/os/file_posix.go:31", "fullName": "os.(*File).read /usr/local/go/src/os/file_posix.go:31", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 1, "childrenList": [{"name": "poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "fullName": "internal/poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "cumulative": 1, "childrenList": [{"name": "poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "fullName": "internal/poll.(*FD).Read.func1 /usr/local/go/src/internal/poll/fd_unix.go:155", "cumulative": 1, "childrenList": [{"name": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "fullName": "syscall.Read /usr/local/go/src/syscall/syscall_unix.go:187", "cumulative": 1, "childrenList": [{"name": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "fullName": "syscall.read /usr/local/go/src/syscall/zsyscall_linux_amd64.go:686", "cumulative": 1, "childrenList": [{"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, {"name": "transport.newHTTP2Server.func2 /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:292", "fullName": "google.golang.org/grpc/internal/transport.newHTTP2Server.func2 /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http2_server.go:292", "cumulative": 8, "childrenList": [{"name": "transport.(*loopyWriter).run /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:555", "fullName": "google.golang.org/grpc/internal/transport.(*loopyWriter).run /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/controlbuf.go:555", "cumulative": 8, "childrenList": [{"name": "transport.(*bufWriter).Flush /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http_util.go:574", "fullName": "google.golang.org/grpc/internal/transport.(*bufWriter).Flush /go/pkg/mod/google.golang.org/grpc@v1.37.0/internal/transport/http_util.go:574", "cumulative": 8, "childrenList": [{"name": "net.(*conn).Write /usr/local/go/src/net/net.go:194", "fullName": "net.(*conn).Write /usr/local/go/src/net/net.go:194", "cumulative": 8, "childrenList": [{"name": "net.(*netFD).Write /usr/local/go/src/net/fd_posix.go:73", "fullName": "net.(*netFD).Write /usr/local/go/src/net/fd_posix.go:73", "cumulative": 8, "childrenList": [{"name": "poll.(*FD).Write /usr/local/go/src/internal/poll/fd_unix.go:267", "fullName": "internal/poll.(*FD).Write /usr/local/go/src/internal/poll/fd_unix.go:267", "cumulative": 8, "childrenList": [{"name": "poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "fullName": "internal/poll.ignoringEINTR /usr/local/go/src/internal/poll/fd_unix.go:567", "cumulative": 8, "childrenList": [{"name": "poll.(*FD).Write.func1 /usr/local/go/src/internal/poll/fd_unix.go:267", "fullName": "internal/poll.(*FD).Write.func1 /usr/local/go/src/internal/poll/fd_unix.go:267", "cumulative": 8, "childrenList": [{"name": "syscall.Write /usr/local/go/src/syscall/syscall_unix.go:212", "fullName": "syscall.Write /usr/local/go/src/syscall/syscall_unix.go:212", "cumulative": 8, "childrenList": [{"name": "syscall.write /usr/local/go/src/syscall/zsyscall_linux_amd64.go:914", "fullName": "syscall.write /usr/local/go/src/syscall/zsyscall_linux_amd64.go:914", "cumulative": 8, "childrenList": [{"name": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "fullName": "syscall.Syscall /usr/local/go/src/syscall/asm_linux_amd64.s:24", "cumulative": 8}]}]}]}]}]}]}]}]}]}]}, {"name": "runtime.mstart /usr/local/go/src/runtime/proc.go:1137", "fullName": "runtime.mstart /usr/local/go/src/runtime/proc.go:1137", "cumulative": 2, "childrenList": [{"name": "runtime.mstart1 /usr/local/go/src/runtime/proc.go:1172", "fullName": "runtime.mstart1 /usr/local/go/src/runtime/proc.go:1172", "cumulative": 2, "childrenList": [{"name": "runtime.sysmon /usr/local/go/src/runtime/proc.go:4660", "fullName": "runtime.sysmon /usr/local/go/src/runtime/proc.go:4660", "cumulative": 2, "childrenList": [{"name": "runtime.usleep /usr/local/go/src/runtime/sys_linux_amd64.s:146", "fullName": "runtime.usleep /usr/local/go/src/runtime/sys_linux_amd64.s:146", "cumulative": 2}]}]}]}, {"name": "runtime._System /usr/local/go/src/runtime/proc.go:3938", "fullName": "runtime._System /usr/local/go/src/runtime/proc.go:3938", "cumulative": 1, "childrenList": [{"name": "runtime.gogo /usr/local/go/src/runtime/asm_amd64.s:272", "fullName": "runtime.gogo /usr/local/go/src/runtime/asm_amd64.s:272", "cumulative": 1}]}, {"name": "trace.NewBatchSpanProcessor.func1 /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:110", "fullName": "go.opentelemetry.io/otel/sdk/trace.NewBatchSpanProcessor.func1 /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:110", "cumulative": 1, "childrenList": [{"name": "trace.(*batchSpanProcessor).processQueue /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:241", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*batchSpanProcessor).processQueue /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:241", "cumulative": 1, "childrenList": [{"name": "trace.(*batchSpanProcessor).exportSpans /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:220", "fullName": "go.opentelemetry.io/otel/sdk/trace.(*batchSpanProcessor).exportSpans /go/pkg/mod/go.opentelemetry.io/otel/sdk@v0.20.0/trace/batch_span_processor.go:220", "cumulative": 1, "childrenList": [{"name": "otlp.(*Exporter).ExportSpans /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlp.go:135", "fullName": "go.opentelemetry.io/otel/exporters/otlp.(*Exporter).ExportSpans /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlp.go:135", "cumulative": 1, "childrenList": [{"name": "otlpgrpc.(*driver).ExportTraces /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlpgrpc/driver.go:175", "fullName": "go.opentelemetry.io/otel/exporters/otlp/otlpgrpc.(*driver).ExportTraces /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlpgrpc/driver.go:175", "cumulative": 1, "childrenList": [{"name": "otlpgrpc.(*tracesDriver).uploadTraces /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlpgrpc/driver.go:190", "fullName": "go.opentelemetry.io/otel/exporters/otlp/otlpgrpc.(*tracesDriver).uploadTraces /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlpgrpc/driver.go:190", "cumulative": 1, "childrenList": [{"name": "otlpgrpc.(*tracesDriver).uploadTraces.func1 /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlpgrpc/driver.go:186", "fullName": "go.opentelemetry.io/otel/exporters/otlp/otlpgrpc.(*tracesDriver).uploadTraces.func1 /go/pkg/mod/go.opentelemetry.io/otel/exporters/otlp@v0.20.0/otlpgrpc/driver.go:186", "cumulative": 1, "childrenList": [{"name": "v1.(*traceServiceClient).Export /go/pkg/mod/go.opentelemetry.io/proto/otlp@v0.7.0/collector/trace/v1/trace_service_grpc.pb.go:35", "fullName": "go.opentelemetry.io/proto/otlp/collector/trace/v1.(*traceServiceClient).Export /go/pkg/mod/go.opentelemetry.io/proto/otlp@v0.7.0/collector/trace/v1/trace_service_grpc.pb.go:35", "cumulative": 1, "childrenList": [{"name": "grpc.(*ClientConn).Invoke /go/pkg/mod/google.golang.org/grpc@v1.37.0/call.go:37", "fullName": "google.golang.org/grpc.(*ClientConn).Invoke /go/pkg/mod/google.golang.org/grpc@v1.37.0/call.go:37", "cumulative": 1, "childrenList": [{"name": "grpc.invoke /go/pkg/mod/google.golang.org/grpc@v1.37.0/call.go:70", "fullName": "google.golang.org/grpc.invoke /go/pkg/mod/google.golang.org/grpc@v1.37.0/call.go:70", "cumulative": 1, "childrenList": [{"name": "grpc.(*clientStream).SendMsg /go/pkg/mod/google.golang.org/grpc@v1.37.0/stream.go:765", "fullName": "google.golang.org/grpc.(*clientStream).SendMsg /go/pkg/mod/google.golang.org/grpc@v1.37.0/stream.go:765", "cumulative": 1, "childrenList": [{"name": "grpc.prepareMsg /go/pkg/mod/google.golang.org/grpc@v1.37.0/stream.go:1584", "fullName": "google.golang.org/grpc.prepareMsg /go/pkg/mod/google.golang.org/grpc@v1.37.0/stream.go:1584", "cumulative": 1, "childrenList": [{"name": "grpc.encode /go/pkg/mod/google.golang.org/grpc@v1.37.0/rpc_util.go:592", "fullName": "google.golang.org/grpc.encode /go/pkg/mod/google.golang.org/grpc@v1.37.0/rpc_util.go:592", "cumulative": 1, "childrenList": [{"name": "proto.codec.Marshal /go/pkg/mod/google.golang.org/grpc@v1.37.0/encoding/proto/proto.go:45", "fullName": "google.golang.org/grpc/encoding/proto.codec.Marshal /go/pkg/mod/google.golang.org/grpc@v1.37.0/encoding/proto/proto.go:45", "cumulative": 1, "childrenList": [{"name": "proto.Marshal /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/wire.go:23", "fullName": "github.com/golang/protobuf/proto.Marshal /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/wire.go:23", "cumulative": 1, "childrenList": [{"name": "proto.marshalAppend /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/wire.go:40", "fullName": "github.com/golang/protobuf/proto.marshalAppend /go/pkg/mod/github.com/golang/protobuf@v1.5.2/proto/wire.go:40", "cumulative": 1, "childrenList": [{"name": "proto.MarshalOptions.MarshalAppend /go/pkg/mod/google.golang.org/protobuf@v1.26.0/proto/encode.go:122", "fullName": "google.golang.org/protobuf/proto.MarshalOptions.MarshalAppend /go/pkg/mod/google.golang.org/protobuf@v1.26.0/proto/encode.go:122", "cumulative": 1, "childrenList": [{"name": "proto.MarshalOptions.marshal /go/pkg/mod/google.golang.org/protobuf@v1.26.0/proto/encode.go:163", "fullName": "google.golang.org/protobuf/proto.MarshalOptions.marshal /go/pkg/mod/google.golang.org/protobuf@v1.26.0/proto/encode.go:163", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshal /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:107", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshal /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:107", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "fullName": "google.golang.org/protobuf/internal/impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "fullName": "google.golang.org/protobuf/internal/impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "fullName": "google.golang.org/protobuf/internal/impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "fullName": "google.golang.org/protobuf/internal/impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "fullName": "google.golang.org/protobuf/internal/impl.appendMessageSliceInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:485", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.appendMessageInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:238", "fullName": "google.golang.org/protobuf/internal/impl.appendMessageInfo /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:238", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).marshalAppendPointer /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/encode.go:139", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).initOneofFieldCoders.func4 /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:92", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).initOneofFieldCoders.func4 /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:92", "cumulative": 1, "childrenList": [{"name": "impl.(*MessageInfo).initOneofFieldCoders.func2 /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:73", "fullName": "google.golang.org/protobuf/internal/impl.(*MessageInfo).initOneofFieldCoders.func2 /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/codec_field.go:73", "cumulative": 1, "childrenList": [{"name": "impl.pointer.AsValueOf /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/pointer_unsafe.go:77", "fullName": "google.golang.org/protobuf/internal/impl.pointer.AsValueOf /go/pkg/mod/google.golang.org/protobuf@v1.26.0/internal/impl/pointer_unsafe.go:77", "cumulative": 1}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}, "total": 48}}