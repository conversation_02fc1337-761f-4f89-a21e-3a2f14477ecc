package ebpftracer

import (
	"bytes"
	"debug/elf"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/cilium/ebpf"
	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/perf"
	"github.com/mexyusef/deep-ebpf-node/common"
	"github.com/mexyusef/deep-ebpf-node/proc"
	"github.com/vishvananda/netns"
	"golang.org/x/sys/unix"
	"inet.af/netaddr"
	"k8s.io/klog/v2"
)

const MaxPayloadSize = 1024

type EventType uint32
type EventReason uint32

const (
	// Original coroot event types
	EventTypeProcessStart     EventType = 1
	EventTypeProcessExit      EventType = 2
	EventTypeConnectionOpen   EventType = 3
	EventTypeConnectionClose  EventType = 4
	EventTypeConnectionError  EventType = 5
	EventTypeListenOpen       EventType = 6
	EventTypeListenClose      EventType = 7
	EventTypeFileOpen         EventType = 8
	EventTypeTCPRetransmit    EventType = 9
	EventTypeL7Request        EventType = 10
	EventTypePythonThreadLock EventType = 11

	// New function tracing event types
	EventTypeFunctionEntry EventType = 20
	EventTypeFunctionExit  EventType = 21
	EventTypeStackTrace    EventType = 22
	EventTypeFunctionError EventType = 23

	EventReasonNone    EventReason = 0
	EventReasonOOMKill EventReason = 1
)

type TrafficStats struct {
	BytesSent     uint64
	BytesReceived uint64
}

// FunctionData represents function call information
type FunctionData struct {
	FunctionName string
	FunctionAddr uint64
	ReturnAddr   uint64
	Arguments    []uint64
	ReturnValue  uint64
	StackTrace   []uint64
	StackDepth   uint32
}

type Event struct {
	Type          EventType
	Reason        EventReason
	Pid           uint32
	SrcAddr       netaddr.IPPort
	DstAddr       netaddr.IPPort
	ActualDstAddr netaddr.IPPort
	Fd            uint64
	Timestamp     uint64
	Duration      time.Duration
	TrafficStats  *TrafficStats
	Mnt           uint64
	Log           bool

	// Function tracing specific fields
	FunctionData *FunctionData
}

type perfMapType uint8

const (
	perfMapTypeProcEvents         perfMapType = 1
	perfMapTypeTCPEvents          perfMapType = 2
	perfMapTypeFileEvents         perfMapType = 3
	perfMapTypeL7Events           perfMapType = 4
	perfMapTypePythonThreadEvents perfMapType = 5
	perfMapTypeFunctionEvents     perfMapType = 6 // New function events
)

type Tracer struct {
	disableL7Tracing bool
	hostNetNs        netns.NsHandle
	selfNetNs        netns.NsHandle

	collection *ebpf.Collection
	readers    map[string]*perf.Reader
	links      []link.Link
	uprobes    map[string]*ebpf.Program

	// Function tracing specific
	functionLinks map[uint32][]link.Link // PID -> links
	targetPIDs    map[uint32]bool
	targetBinaries map[string]bool
}

func NewTracer(hostNetNs, selfNetNs netns.NsHandle, disableL7Tracing bool) *Tracer {
	if disableL7Tracing {
		klog.Infoln("L7 tracing is disabled")
	}
	return &Tracer{
		disableL7Tracing: disableL7Tracing,
		hostNetNs:        hostNetNs,
		selfNetNs:        selfNetNs,

		readers:        map[string]*perf.Reader{},
		uprobes:        map[string]*ebpf.Program{},
		functionLinks:  map[uint32][]link.Link{},
		targetPIDs:     map[uint32]bool{},
		targetBinaries: map[string]bool{},
	}
}

func (t *Tracer) Run(events chan<- Event) error {
	if err := proc.ExecuteInNetNs(t.hostNetNs, t.selfNetNs, ensureConntrackEventsAreEnabled); err != nil {
		return err
	}
	if err := t.ebpf(events); err != nil {
		return err
	}
	if err := t.init(events); err != nil {
		return err
	}
	return nil
}

func (t *Tracer) Close() {
	for _, links := range t.functionLinks {
		for _, l := range links {
			_ = l.Close()
		}
	}
	for _, p := range t.uprobes {
		_ = p.Close()
	}
	for _, l := range t.links {
		_ = l.Close()
	}
	for _, r := range t.readers {
		_ = r.Close()
	}
	if t.collection != nil {
		t.collection.Close()
	}
}

// AttachToProcess attaches function tracing probes to a specific process
func (t *Tracer) AttachToProcess(pid uint32) error {
	klog.Infof("⏳ PHASE 2/3: Attaching function probes to PID %d", pid)
	
	t.targetPIDs[pid] = true
	
	// Get the binary path for this process
	binaryPath, err := t.getBinaryPath(pid)
	if err != nil {
		return fmt.Errorf("failed to get binary path for PID %d: %w", pid, err)
	}
	
	klog.Infof("⏳ PHASE 2/3: PID %d binary path: %s", pid, binaryPath)
	
	// Attach function probes to the binary
	links, err := t.attachFunctionProbes(pid, binaryPath)
	if err != nil {
		return fmt.Errorf("failed to attach function probes: %w", err)
	}
	
	t.functionLinks[pid] = links
	klog.Infof("attached %d function probes to PID %d", len(links), pid)
	
	return nil
}

// AttachToBinary attaches function tracing probes to all processes running a specific binary
func (t *Tracer) AttachToBinary(binaryPath string) error {
	klog.Infof("attaching function probes to binary %s", binaryPath)

	t.targetBinaries[binaryPath] = true

	// Find all processes running this binary
	pids, err := t.findProcessesByBinary(binaryPath)
	if err != nil {
		return fmt.Errorf("failed to find processes for binary %s: %w", binaryPath, err)
	}

	klog.Infof("found %d processes running %s", len(pids), binaryPath)

	// Attach to each process
	for _, pid := range pids {
		if err := t.AttachToProcess(pid); err != nil {
			klog.Warningf("failed to attach to PID %d: %v", pid, err)
			continue
		}
	}

	return nil
}

// AttachSystemWide attaches function tracing probes to all processes system-wide
func (t *Tracer) AttachSystemWide() error {
	klog.Infoln("🚀 INITIALIZATION STARTED: Beginning system-wide function tracing setup...")
	klog.Infoln("⏳ PHASE 1/3: Discovering all running processes...")

	// Get all running processes
	processes, err := t.getAllProcesses()
	if err != nil {
		return fmt.Errorf("failed to get all processes: %w", err)
	}

	klog.Infof("✅ PHASE 1 COMPLETE: Found %d processes for system-wide tracing", len(processes))
	klog.Infoln("⏳ PHASE 2/3: Attaching function probes to all processes (this may take 30+ seconds)...")

	attachedCount := 0
	totalProcesses := len(processes)
	startTime := time.Now()
	processedCount := 0
	skippedCount := 0
	failedCount := 0

	// Show the process list for client visibility
	klog.Infof("⏳ PHASE 2/3 ACTIVITY LIST: Processing %d processes...", totalProcesses)
	for i, process := range processes {
		if i < 10 { // Show first 10 processes for visibility
			klog.Infof("⏳ PHASE 2/3 QUEUE: [%d] PID %d (%s)", i+1, process.PID, process.Name)
		} else if i == 10 {
			klog.Infof("⏳ PHASE 2/3 QUEUE: ... and %d more processes", totalProcesses-10)
			break
		}
	}

	for _, process := range processes {
		processedCount++

		// Progress indicator every 3 processes for better visibility
		if processedCount%3 == 0 {
			elapsed := time.Since(startTime)
			progress := float64(processedCount) / float64(totalProcesses) * 100
			remaining := totalProcesses - processedCount

			// Show current activity and next steps
			currentActivity := "Attaching function probes"
			nextSteps := fmt.Sprintf("Next: %d more processes", remaining)
			if remaining == 0 {
				nextSteps = "Next: Phase 3/3 (Real-time monitoring)"
			}

			klog.Infof("⏳ PHASE 2/3 PROGRESS: %.1f%% (%d/%d) - %s - %s - Elapsed: %v",
				progress, processedCount, totalProcesses, currentActivity, nextSteps, elapsed.Round(time.Second))
		}

		// Skip kernel threads and excluded PIDs
		if t.shouldSkipProcess(process) {
			skippedCount++
			klog.V(2).Infof("⏳ PHASE 2/3: Skipping PID %d (%s) - kernel thread or excluded", process.PID, process.Name)
			continue
		}

		klog.Infof("⏳ PHASE 2/3 CURRENT: Attaching to PID %d (%s) - Step %d/%d",
			process.PID, process.Name, processedCount, totalProcesses)
		processStart := time.Now()

		// Timeout mechanism to prevent hanging
		done := make(chan error, 1)
		go func() {
			done <- t.AttachToProcess(uint32(process.PID))
		}()

		select {
		case err := <-done:
			if err != nil {
				failedCount++
				klog.V(2).Infof("⚠️ PHASE 2/3: Failed to attach to PID %d: %v", process.PID, err)
				continue
			}
		case <-time.After(10 * time.Second):
			failedCount++
			klog.Warningf("⏰ PHASE 2/3 TIMEOUT: PID %d (%s) attachment timed out after 10s", process.PID, process.Name)
			continue
		}

		// Detect slow processes (potential bottlenecks)
		processDuration := time.Since(processStart)
		if processDuration > 3*time.Second {
			klog.Warningf("🐌 PHASE 2/3 BOTTLENECK: PID %d (%s) took %v to attach",
				process.PID, process.Name, processDuration.Round(time.Millisecond))
		}

		attachedCount++
	}

	totalElapsed := time.Since(startTime)
	klog.Infof("✅ PHASE 2/3 COMPLETE: Successfully attached to %d processes for system-wide tracing", attachedCount)
	klog.Infof("📊 PHASE 2/3 SUMMARY: Processed: %d, Attached: %d, Skipped: %d, Failed: %d, Duration: %v",
		processedCount, attachedCount, skippedCount, failedCount, totalElapsed.Round(time.Millisecond))
	klog.Infof("⏳ PHASE 3/3 STARTING: Initializing real-time process monitoring...")
	klog.Infoln("⏳ PHASE 3/3: Setting up real-time process detection...")

	// Attach process event tracepoints for real-time process detection
	phase3Start := time.Now()
	if err := t.attachProcessEventTracepoints(); err != nil {
		klog.Warningf("❌ PHASE 3/3 FAILED: Failed to attach process event tracepoints: %v", err)
		klog.Warningln("⚠️  Real-time detection disabled, but existing process tracing still works")
		// Don't fail the whole tracer if tracepoints fail
	} else {
		phase3Duration := time.Since(phase3Start)
		klog.Infof("✅ PHASE 3/3 COMPLETE: Real-time process monitoring active (Duration: %v)",
			phase3Duration.Round(time.Millisecond))
		klog.Infof("🎯 INITIALIZATION COMPLETE: Universal eBPF Function Tracer is now monitoring all processes")
		klog.Infof("📡 READY: Watching for new processes and performing dynamic function-level tracing")
	}

	klog.Infoln("🎉 INITIALIZATION COMPLETE! 🎉")
	klog.Infoln("🟢 READY FOR TESTING: You can now run test programs and they will be detected!")
	klog.Infoln("📋 STATUS: Universal eBPF Function Tracer is fully operational")

	return nil
}

func (t *Tracer) getBinaryPath(pid uint32) (string, error) {
	exePath := proc.Path(pid, "exe")
	target, err := os.Readlink(exePath)
	if err != nil {
		return "", err
	}
	return target, nil
}

func (t *Tracer) findProcessesByBinary(binaryPath string) ([]uint32, error) {
	var pids []uint32
	
	// Read /proc to find matching processes
	procDir, err := os.Open("/proc")
	if err != nil {
		return nil, err
	}
	defer procDir.Close()
	
	entries, err := procDir.Readdir(-1)
	if err != nil {
		return nil, err
	}
	
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}
		
		pidStr := entry.Name()
		pid, err := strconv.ParseUint(pidStr, 10, 32)
		if err != nil {
			continue
		}
		
		procBinaryPath, err := t.getBinaryPath(uint32(pid))
		if err != nil {
			continue
		}
		
		if procBinaryPath == binaryPath {
			pids = append(pids, uint32(pid))
		}
	}
	
	return pids, nil
}

// ProcessInfo represents basic process information
type ProcessInfo struct {
	PID  int
	Name string
	Path string
}

// getAllProcesses returns all running processes
func (t *Tracer) getAllProcesses() ([]ProcessInfo, error) {
	var processes []ProcessInfo

	// Read /proc directory
	procDir, err := os.Open("/proc")
	if err != nil {
		return nil, fmt.Errorf("failed to open /proc: %w", err)
	}
	defer procDir.Close()

	entries, err := procDir.Readdir(-1)
	if err != nil {
		return nil, fmt.Errorf("failed to read /proc: %w", err)
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		// Check if directory name is a PID (numeric)
		pid, err := strconv.Atoi(entry.Name())
		if err != nil {
			continue
		}

		// Get process info
		name, path := t.getProcessInfo(pid)
		if name == "" {
			continue
		}

		processes = append(processes, ProcessInfo{
			PID:  pid,
			Name: name,
			Path: path,
		})
	}

	return processes, nil
}

// getProcessInfo gets process name and executable path
func (t *Tracer) getProcessInfo(pid int) (string, string) {
	// Read process name from /proc/PID/comm
	commPath := fmt.Sprintf("/proc/%d/comm", pid)
	commData, err := os.ReadFile(commPath)
	if err != nil {
		return "", ""
	}
	name := strings.TrimSpace(string(commData))

	// Read executable path from /proc/PID/exe
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	path, err := os.Readlink(exePath)
	if err != nil {
		// If we can't read the exe link, use cmdline
		cmdlinePath := fmt.Sprintf("/proc/%d/cmdline", pid)
		cmdlineData, err := os.ReadFile(cmdlinePath)
		if err != nil {
			return name, ""
		}
		// First argument is usually the executable
		args := strings.Split(string(cmdlineData), "\x00")
		if len(args) > 0 && args[0] != "" {
			path = args[0]
		}
	}

	return name, path
}

// shouldSkipProcess determines if a process should be skipped in system-wide tracing
func (t *Tracer) shouldSkipProcess(process ProcessInfo) bool {
	// Skip kernel threads (processes with no executable path)
	if process.Path == "" {
		return true
	}

	// Skip if path looks like a kernel thread
	if strings.HasPrefix(process.Path, "[") && strings.HasSuffix(process.Path, "]") {
		return true
	}

	// Skip our own process and problematic processes that might cause bottlenecks
	skipNames := []string{
		"deep-ebpf-node",
		"deep-ebpf-server",
		"bpftool",
		"kthreadd",
		"ksoftirqd",
		"migration",
		"rcu_",
		"watchdog",
		"systemd",           // System manager - can be slow
		"containerd",        // Container runtime - complex and slow
		"dockerd",          // Docker daemon - complex and slow
		"redis-server",     // Database - might have complex symbol tables
		"python3.12",       // Python interpreter - complex symbol resolution
	}

	// Skip privileged system paths that might cause permission issues
	skipPaths := []string{
		"/usr/lib/systemd/",
		"/usr/sbin/",
		"/sbin/",
	}

	for _, skipName := range skipNames {
		if strings.Contains(process.Name, skipName) {
			return true
		}
	}

	// Check for problematic paths
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(process.Path, skipPath) {
			return true
		}
	}

	// Skip system processes (PID < 100) unless explicitly included
	if process.PID < 100 && process.PID != 1 {
		return true
	}

	return false
}

func (t *Tracer) attachFunctionProbes(pid uint32, binaryPath string) ([]link.Link, error) {
	var links []link.Link
	
	// Open the executable
	exe, err := link.OpenExecutable(binaryPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open executable %s: %w", binaryPath, err)
	}
	
	// Get function symbols from the binary
	functions, err := t.getFunctionSymbols(binaryPath)
	if err != nil {
		klog.Warningf("failed to get function symbols from %s: %v", binaryPath, err)
		// Continue with main function as fallback
		functions = []string{"main"}
	}
	
	klog.Infof("⏳ PHASE 2/3: Found %d functions in %s: %v", len(functions), binaryPath, functions)
	
	options := &link.UprobeOptions{PID: int(pid)}
	
	// Attach uprobes to functions
	for _, funcName := range functions {
		// Attach entry probe
		if entryProg, exists := t.uprobes["function_entry"]; exists {
			uprobe, err := exe.Uprobe(funcName, entryProg, options)
			if err != nil {
				klog.V(2).Infof("failed to attach entry uprobe to %s: %v", funcName, err)
				continue
			}
			links = append(links, uprobe)
			klog.V(2).Infof("attached entry uprobe to %s", funcName)
		}
		
		// Attach exit probe
		if exitProg, exists := t.uprobes["function_exit"]; exists {
			uretprobe, err := exe.Uretprobe(funcName, exitProg, options)
			if err != nil {
				klog.V(2).Infof("failed to attach exit uretprobe to %s: %v", funcName, err)
				continue
			}
			links = append(links, uretprobe)
			klog.V(2).Infof("attached exit uretprobe to %s", funcName)
		}
	}
	
	if len(links) == 0 {
		return nil, fmt.Errorf("failed to attach any function probes to %s", binaryPath)
	}
	
	return links, nil
}

func (t *Tracer) getFunctionSymbols(binaryPath string) ([]string, error) {
	// Real ELF symbol extraction following coroot/tracee pattern
	file, err := elf.Open(binaryPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open ELF file %s: %w", binaryPath, err)
	}
	defer file.Close()

	var allSymbols []elf.Symbol
	var functionNames []string

	// Try to get regular symbols first
	if symbols, err := file.Symbols(); err == nil {
		allSymbols = append(allSymbols, symbols...)
	}

	// Try to get dynamic symbols as fallback
	if dynSymbols, err := file.DynamicSymbols(); err == nil {
		allSymbols = append(allSymbols, dynSymbols...)
	}

	if len(allSymbols) == 0 {
		// Fallback to common function names if no symbols found
		return []string{"main", "_start"}, nil
	}

	// Extract function symbols (STT_FUNC type)
	for _, symbol := range allSymbols {
		if elf.ST_TYPE(symbol.Info) == elf.STT_FUNC && symbol.Name != "" && symbol.Size > 0 {
			functionNames = append(functionNames, symbol.Name)
		}
	}

	// If no function symbols found, use fallback
	if len(functionNames) == 0 {
		return []string{"main", "_start"}, nil
	}

	// Limit to first 10 functions to avoid too many attachments
	if len(functionNames) > 10 {
		functionNames = functionNames[:10]
	}

	// Log the actual function names found for debugging
	klog.V(1).Infof("Function symbols found in %s: %v", binaryPath, functionNames)

	return functionNames, nil
}

// handleNewProcess handles newly started processes (event-based detection)
func handleNewProcess(pid uint32) {
	// Add a small delay to let the process initialize
	time.Sleep(100 * time.Millisecond)

	// Check if process still exists
	if _, err := os.Stat(fmt.Sprintf("/proc/%d", pid)); os.IsNotExist(err) {
		klog.V(2).Infof("⚠️  Process PID %d exited before attachment", pid)
		return
	}

	// Get binary path following Parca/coroot pattern
	binaryPath, err := getBinaryPathForPID(pid)
	if err != nil {
		klog.V(2).Infof("⚠️  Failed to get binary path for PID %d: %v", pid, err)
		return
	}

	// Skip if it's a kernel thread or system process
	if shouldSkipDynamicProcess(pid, binaryPath) {
		klog.V(2).Infof("⏭️  Skipping dynamic attachment to PID %d (%s)", pid, binaryPath)
		return
	}

	klog.Infof("🔗 DYNAMIC ATTACHMENT: Attaching to new process PID %d [%s]", pid, binaryPath)

	// TODO: Get tracer instance and perform actual attachment
	// For now, we'll implement this step by step
	klog.Infof("📊 DEEP TRACING: Process PID %d (%s) ready for function-level tracing", pid, binaryPath)
}

// handleProcessExit handles process exit cleanup
func handleProcessExit(pid uint32) {
	klog.V(2).Infof("Cleaning up resources for exited process: PID %d", pid)

	// TODO: Clean up function links and resources for this PID
	// This would involve removing entries from functionLinks map
}

// attachProcessEventTracepoints attaches tracepoints for real-time process detection
func (t *Tracer) attachProcessEventTracepoints() error {
	// Attach task_newtask tracepoint for process creation
	newTaskLink, err := link.Tracepoint("task", "task_newtask", t.collection.Programs["trace_task_newtask"], nil)
	if err != nil {
		return fmt.Errorf("failed to attach task_newtask tracepoint: %w", err)
	}
	t.links = append(t.links, newTaskLink)

	// Attach sched_process_exit tracepoint for process exit
	exitLink, err := link.Tracepoint("sched", "sched_process_exit", t.collection.Programs["trace_sched_process_exit"], nil)
	if err != nil {
		return fmt.Errorf("failed to attach sched_process_exit tracepoint: %w", err)
	}
	t.links = append(t.links, exitLink)

	klog.V(2).Infof("Successfully attached process event tracepoints")
	return nil
}

// getProcessInfo gets process binary path and determines if it's kernel or user process
func getProcessInfo(pid uint32) (string, bool) {
	// Read process executable path first
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	if link, err := os.Readlink(exePath); err == nil {
		// User process with valid executable
		return link, false
	}

	// Try to read comm (command name) for kernel threads or processes without exe
	commPath := fmt.Sprintf("/proc/%d/comm", pid)
	if data, err := os.ReadFile(commPath); err == nil {
		comm := strings.TrimSpace(string(data))
		// Kernel threads typically have names in brackets
		if strings.HasPrefix(comm, "[") && strings.HasSuffix(comm, "]") {
			return comm, true
		}
		// Return comm name for processes without readable exe
		return fmt.Sprintf("(%s)", comm), false
	}

	return fmt.Sprintf("PID_%d", pid), false
}

// getBinaryPathForPID gets the binary path for a process (following Parca/coroot pattern)
func getBinaryPathForPID(pid uint32) (string, error) {
	// Read the executable path from /proc/PID/exe
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	binaryPath, err := os.Readlink(exePath)
	if err != nil {
		return "", fmt.Errorf("failed to read executable path: %w", err)
	}
	return binaryPath, nil
}

// shouldSkipDynamicProcess determines if we should skip dynamic attachment to a process
func shouldSkipDynamicProcess(pid uint32, binaryPath string) bool {
	// Skip kernel threads (no real binary path)
	if binaryPath == "" || binaryPath == "/" {
		return true
	}

	// Skip if path looks like a kernel thread
	if strings.HasPrefix(binaryPath, "[") && strings.HasSuffix(binaryPath, "]") {
		return true
	}

	// Skip our own process
	if strings.Contains(binaryPath, "deep-ebpf-node") {
		return true
	}

	// Skip dockerd to reduce noise (as requested)
	if strings.Contains(binaryPath, "dockerd") {
		return true
	}

	// Skip very short-lived system processes
	if pid < 10 {
		return true
	}

	return false
}

// handleNewProcessWithTracer handles newly started processes with actual function tracing
func (t *Tracer) handleNewProcessWithTracer(pid uint32) {
	// Add a small delay to let the process initialize
	time.Sleep(100 * time.Millisecond)

	// Check if process still exists
	if _, err := os.Stat(fmt.Sprintf("/proc/%d", pid)); os.IsNotExist(err) {
		klog.V(2).Infof("⚠️  Process PID %d exited before attachment", pid)
		return
	}

	// Get binary path following Parca/coroot pattern
	binaryPath, err := getBinaryPathForPID(pid)
	if err != nil {
		klog.V(2).Infof("⚠️  Failed to get binary path for PID %d: %v", pid, err)
		return
	}

	// Skip if it's a kernel thread or system process
	if shouldSkipDynamicProcess(pid, binaryPath) {
		klog.V(2).Infof("⏭️  Skipping dynamic attachment to PID %d (%s)", pid, binaryPath)
		return
	}

	klog.Infof("🔗 DYNAMIC ATTACHMENT: Attaching to new process PID %d [%s]", pid, binaryPath)

	// Perform actual function tracing attachment with timeout
	done := make(chan error, 1)
	go func() {
		done <- t.AttachToProcess(pid)
	}()

	select {
	case err := <-done:
		if err != nil {
			klog.Warningf("❌ DYNAMIC ATTACHMENT FAILED: PID %d (%s): %v", pid, binaryPath, err)
			return
		}
		klog.Infof("✅ DEEP TRACING ACTIVE: Process PID %d (%s) now being traced at function level", pid, binaryPath)
	case <-time.After(5 * time.Second):
		klog.Warningf("⏰ DYNAMIC ATTACHMENT TIMEOUT: PID %d (%s) attachment timed out after 5s", pid, binaryPath)
		return
	}
}

type perfMap struct {
	name                  string
	perCPUBufferSizePages int
	typ                   perfMapType
	readTimeout           time.Duration
}

func (t *Tracer) ebpf(ch chan<- Event) error {
	// Validate eBPF support
	if err := validateEBPFSupport(); err != nil {
		return fmt.Errorf("eBPF not supported: %w", err)
	}

	// Log eBPF environment information
	logEBPFInfo()

	// Load eBPF program
	collectionSpec, err := loadEBPFProgram()
	if err != nil {
		return fmt.Errorf("failed to load eBPF program: %w", err)
	}

	// Set memory limit for eBPF
	_ = unix.Setrlimit(unix.RLIMIT_MEMLOCK, &unix.Rlimit{Cur: unix.RLIM_INFINITY, Max: unix.RLIM_INFINITY})

	// Create eBPF collection
	c, err := ebpf.NewCollectionWithOptions(collectionSpec, ebpf.CollectionOptions{
		Programs: ebpf.ProgramOptions{
			LogLevel: 1, // Enable basic logging
			LogSize:  1024 * 1024, // 1MB log buffer
		},
	})
	if err != nil {
		var vErr *ebpf.VerifierError
		if errors.As(err, &vErr) {
			klog.Errorf("eBPF verifier error: %+v", vErr)
		}
		return fmt.Errorf("failed to load eBPF collection: %w", err)
	}
	t.collection = c

	// Initialize eBPF maps
	if err := t.initializeEBPFMaps(); err != nil {
		klog.Warningf("Failed to initialize eBPF maps: %v", err)
	}

	perfMaps := []perfMap{
		{name: "proc_events", typ: perfMapTypeProcEvents, perCPUBufferSizePages: 4},
		{name: "function_events", typ: perfMapTypeFunctionEvents, perCPUBufferSizePages: 8, readTimeout: 10 * time.Millisecond},
	}

	// Only add other perf maps if not focusing purely on function tracing
	if !t.disableL7Tracing {
		perfMaps = append(perfMaps, []perfMap{
			{name: "tcp_listen_events", typ: perfMapTypeTCPEvents, perCPUBufferSizePages: 4},
			{name: "tcp_connect_events", typ: perfMapTypeTCPEvents, perCPUBufferSizePages: 8, readTimeout: 10 * time.Millisecond},
			{name: "tcp_retransmit_events", typ: perfMapTypeTCPEvents, perCPUBufferSizePages: 4},
			{name: "file_events", typ: perfMapTypeFileEvents, perCPUBufferSizePages: 4},
			{name: "python_thread_events", typ: perfMapTypePythonThreadEvents, perCPUBufferSizePages: 4},
			{name: "l7_events", typ: perfMapTypeL7Events, perCPUBufferSizePages: 32},
		}...)
	}

	pageSize := os.Getpagesize()
	for _, pm := range perfMaps {
		r, err := perf.NewReader(t.collection.Maps[pm.name], pm.perCPUBufferSizePages*pageSize)
		if err != nil {
			t.Close()
			return fmt.Errorf("failed to create ebpf reader: %w", err)
		}
		t.readers[pm.name] = r
		go runEventsReader(pm.name, r, ch, pm.typ, pm.readTimeout, t)
	}

	for _, programSpec := range collectionSpec.Programs {
		program := t.collection.Programs[programSpec.Name]
		if t.disableL7Tracing {
			switch programSpec.Name {
			case "sys_enter_writev", "sys_enter_write", "sys_enter_sendto", "sys_enter_sendmsg", "sys_enter_sendmmsg":
				continue
			case "sys_enter_read", "sys_enter_readv", "sys_enter_recvfrom", "sys_enter_recvmsg":
				continue
			case "sys_exit_read", "sys_exit_readv", "sys_exit_recvfrom", "sys_exit_recvmsg":
				continue
			}
		}
		var l link.Link
		switch programSpec.Type {
		case ebpf.TracePoint:
			parts := strings.SplitN(programSpec.AttachTo, "/", 2)
			l, err = link.Tracepoint(parts[0], parts[1], program, nil)
		case ebpf.Kprobe:
			if strings.HasPrefix(programSpec.SectionName, "uprobe/") {
				t.uprobes[programSpec.Name] = program
				continue
			}
			l, err = link.Kprobe(programSpec.AttachTo, program, nil)
			if err != nil && programSpec.SectionName == "kprobe/nf_ct_deliver_cached_events" {
				klog.Warningln("nf_conntrack may not be in use:", err)
				continue
			}
		}
		if err != nil {
			t.Close()
			return fmt.Errorf("failed to link program '%s': %w", programSpec.Name, err)
		}
		if l != nil {
			t.links = append(t.links, l)
		}
	}

	return nil
}

// Event structures for function tracing
type functionEvent struct {
	Type         EventType
	Pid          uint32
	Timestamp    uint64
	Duration     uint64
	FunctionAddr uint64
	ReturnAddr   uint64
	Args         [6]uint64 // RDI, RSI, RDX, RCX, R8, R9
	ReturnValue  uint64
	StackDepth   uint32
	StackTrace   [64]uint64
}

// Process event structure (matching eBPF struct)
type processEvent struct {
	Type   uint32
	PID    uint32
	Reason uint32
}

func runEventsReader(name string, r *perf.Reader, ch chan<- Event, typ perfMapType, readTimeout time.Duration, tracer *Tracer) {
	if readTimeout == 0 {
		readTimeout = 100 * time.Millisecond
	}
	for {
		r.SetDeadline(time.Now().Add(readTimeout))
		rec, err := r.Read()
		if err != nil {
			if errors.Is(err, perf.ErrClosed) {
				break
			}
			continue
		}
		if rec.LostSamples > 0 {
			klog.Errorln(name, "lost samples:", rec.LostSamples)
			continue
		}
		var event Event

		switch typ {
		case perfMapTypeFunctionEvents:
			v := &functionEvent{}
			if err := binary.Read(bytes.NewBuffer(rec.RawSample), binary.LittleEndian, v); err != nil {
				klog.Warningln("failed to read function event:", err)
				continue
			}

			// Convert to our Event structure
			funcData := &FunctionData{
				FunctionAddr: v.FunctionAddr,
				ReturnAddr:   v.ReturnAddr,
				Arguments:    v.Args[:],
				ReturnValue:  v.ReturnValue,
				StackTrace:   v.StackTrace[:v.StackDepth],
				StackDepth:   v.StackDepth,
			}

			event = Event{
				Type:         v.Type,
				Pid:          v.Pid,
				Timestamp:    v.Timestamp,
				Duration:     time.Duration(v.Duration),
				FunctionData: funcData,
			}

		case perfMapTypeProcEvents:
			// Handle process events (real-time process detection)
			v := &processEvent{}
			if err := binary.Read(bytes.NewBuffer(rec.RawSample), binary.LittleEndian, v); err != nil {
				klog.Warningln("failed to read process event:", err)
				continue
			}

			// Handle process start/exit events
			switch v.Type {
			case uint32(EventTypeProcessStart):
				// Get process info for better visibility
				binaryPath, isKernel := getProcessInfo(v.PID)
				processType := "USER"
				if isKernel {
					processType = "KERNEL"
				}

				// Skip dockerd to reduce noise
				if strings.Contains(binaryPath, "dockerd") {
					klog.V(2).Infof("⏭️ SKIPPING DOCKERD: PID %d [%s] %s", v.PID, processType, binaryPath)
					continue
				}

				klog.Infof("🔄 NEW PROCESS DETECTED (INIT PHASE): PID %d [%s] %s", v.PID, processType, binaryPath)
				// Trigger dynamic attachment to new process with tracer instance
				go tracer.handleNewProcessWithTracer(v.PID)
			case uint32(EventTypeProcessExit):
				// Get process info for better visibility
				binaryPath, isKernel := getProcessInfo(v.PID)
				processType := "USER"
				if isKernel {
					processType = "KERNEL"
				}

				// Skip dockerd to reduce noise
				if strings.Contains(binaryPath, "dockerd") {
					klog.V(2).Infof("⏭️ SKIPPING DOCKERD EXIT: PID %d [%s] %s", v.PID, processType, binaryPath)
					continue
				}

				klog.Infof("🔄 PROCESS EXITED (INIT PHASE): PID %d [%s] %s", v.PID, processType, binaryPath)
				// Clean up resources for exited process
				go handleProcessExit(v.PID)
			}
			continue

		default:
			continue
		}

		ch <- event
	}
}

func (t EventType) String() string {
	switch t {
	case EventTypeProcessStart:
		return "process-start"
	case EventTypeProcessExit:
		return "process-exit"
	case EventTypeConnectionOpen:
		return "connection-open"
	case EventTypeConnectionClose:
		return "connection-close"
	case EventTypeConnectionError:
		return "connection-error"
	case EventTypeListenOpen:
		return "listen-open"
	case EventTypeListenClose:
		return "listen-close"
	case EventTypeFileOpen:
		return "file-open"
	case EventTypeTCPRetransmit:
		return "tcp-retransmit"
	case EventTypeL7Request:
		return "l7-request"
	case EventTypeFunctionEntry:
		return "function-entry"
	case EventTypeFunctionExit:
		return "function-exit"
	case EventTypeStackTrace:
		return "stack-trace"
	case EventTypeFunctionError:
		return "function-error"
	}
	return "unknown: " + strconv.Itoa(int(t))
}

func (t EventReason) String() string {
	switch t {
	case EventReasonNone:
		return "none"
	case EventReasonOOMKill:
		return "oom-kill"
	}
	return "unknown: " + strconv.Itoa(int(t))
}

func isCtxExtraPaddingRequired(traceFsPath string) bool {
	f, err := os.Open(path.Join(traceFsPath, "events/task/task_newtask/format"))
	if err != nil {
		klog.Errorln(err)
		return false
	}
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		klog.Errorln(err)
		return false
	}
	for _, line := range strings.Split(string(data), "\n") {
		if strings.Contains(line, "common_preempt_lazy_count") {
			return true
		}
	}
	return false
}

const nfConntrackEventsParameterPath = "/proc/sys/net/netfilter/nf_conntrack_events"

func ensureConntrackEventsAreEnabled() error {
	v, err := common.ReadUintFromFile(nfConntrackEventsParameterPath)
	if err != nil {
		if common.IsNotExist(err) {
			klog.Warningf(
				"unable to check the value of %s, it appears that nf_conntrack is not loaded: %s",
				nfConntrackEventsParameterPath, err)
			return nil
		}
		return err
	}
	if v != 1 {
		klog.Infof("%s = %d, setting to 1", nfConntrackEventsParameterPath, v)
		if err = os.WriteFile(nfConntrackEventsParameterPath, []byte("1"), 0644); err != nil {
			return err
		}
	}
	return nil
}

// eBPF program loading is now handled in init.go

func (t *Tracer) init(events chan<- Event) error {
	// Initialize process discovery and monitoring
	// This is simplified compared to the full coroot implementation

	// For function tracing, we don't need the complex initialization
	// that coroot does for network monitoring

	return nil
}
