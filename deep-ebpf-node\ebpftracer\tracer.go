package ebpftracer

import (
	"bytes"
	"context"
	"debug/elf"
	"encoding/binary"
	"errors"
	"fmt"
	"io"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/cilium/ebpf"
	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/perf"
	"github.com/mexyusef/deep-ebpf-node/common"
	"github.com/mexyusef/deep-ebpf-node/config"
	"github.com/mexyusef/deep-ebpf-node/proc"
	"github.com/vishvananda/netns"
	"golang.org/x/sys/unix"
	"inet.af/netaddr"
	"k8s.io/klog/v2"
)

// Global variables for self-exclusion (following Parca/coroot pattern)
var (
	selfPID       uint32
	selfPPID      uint32
	selfBinaryPath string
	excludedPIDs  map[uint32]bool
	problematicPIDs map[uint32]bool // Circuit breaker for problematic processes
	processedPIDs map[uint32]bool   // Track which PIDs have been processed in Phase 2/3
	attachmentStatus map[uint32]string // Track attachment status for each PID
)

const MaxPayloadSize = 1024

type EventType uint32
type EventReason uint32

const (
	// Original coroot event types
	EventTypeProcessStart     EventType = 1
	EventTypeProcessExit      EventType = 2
	EventTypeConnectionOpen   EventType = 3
	EventTypeConnectionClose  EventType = 4
	EventTypeConnectionError  EventType = 5
	EventTypeListenOpen       EventType = 6
	EventTypeListenClose      EventType = 7
	EventTypeFileOpen         EventType = 8
	EventTypeTCPRetransmit    EventType = 9
	EventTypeL7Request        EventType = 10
	EventTypePythonThreadLock EventType = 11

	// New function tracing event types
	EventTypeFunctionEntry EventType = 20
	EventTypeFunctionExit  EventType = 21
	EventTypeStackTrace    EventType = 22
	EventTypeFunctionError EventType = 23

	EventReasonNone    EventReason = 0
	EventReasonOOMKill EventReason = 1
)

type TrafficStats struct {
	BytesSent     uint64
	BytesReceived uint64
}

// FunctionData represents function call information
type FunctionData struct {
	FunctionName string
	FunctionAddr uint64
	ReturnAddr   uint64
	Arguments    []uint64
	ReturnValue  uint64
	StackTrace   []uint64
	StackDepth   uint32
}

type Event struct {
	Type          EventType
	Reason        EventReason
	Pid           uint32
	SrcAddr       netaddr.IPPort
	DstAddr       netaddr.IPPort
	ActualDstAddr netaddr.IPPort
	Fd            uint64
	Timestamp     uint64
	Duration      time.Duration
	TrafficStats  *TrafficStats
	Mnt           uint64
	Log           bool

	// Function tracing specific fields
	FunctionData *FunctionData
}

type perfMapType uint8

const (
	perfMapTypeProcEvents         perfMapType = 1
	perfMapTypeTCPEvents          perfMapType = 2
	perfMapTypeFileEvents         perfMapType = 3
	perfMapTypeL7Events           perfMapType = 4
	perfMapTypePythonThreadEvents perfMapType = 5
	perfMapTypeFunctionEvents     perfMapType = 6 // New function events
)

type Tracer struct {
	disableL7Tracing bool
	hostNetNs        netns.NsHandle
	selfNetNs        netns.NsHandle

	collection *ebpf.Collection
	readers    map[string]*perf.Reader
	links      []link.Link
	uprobes    map[string]*ebpf.Program

	// Function tracing specific
	functionLinks map[uint32][]link.Link // PID -> links
	targetPIDs    map[uint32]bool
	targetBinaries map[string]bool
}

func NewTracer(hostNetNs, selfNetNs netns.NsHandle, disableL7Tracing bool) *Tracer {
	if disableL7Tracing {
		klog.Infoln("L7 tracing is disabled")
	}
	return &Tracer{
		disableL7Tracing: disableL7Tracing,
		hostNetNs:        hostNetNs,
		selfNetNs:        selfNetNs,

		readers:        map[string]*perf.Reader{},
		uprobes:        map[string]*ebpf.Program{},
		functionLinks:  map[uint32][]link.Link{},
		targetPIDs:     map[uint32]bool{},
		targetBinaries: map[string]bool{},
	}
}

func (t *Tracer) Run(events chan<- Event) error {
	if err := proc.ExecuteInNetNs(t.hostNetNs, t.selfNetNs, ensureConntrackEventsAreEnabled); err != nil {
		return err
	}
	if err := t.ebpf(events); err != nil {
		return err
	}
	if err := t.init(events); err != nil {
		return err
	}
	return nil
}

func (t *Tracer) Close() {
	for _, links := range t.functionLinks {
		for _, l := range links {
			_ = l.Close()
		}
	}
	for _, p := range t.uprobes {
		_ = p.Close()
	}
	for _, l := range t.links {
		_ = l.Close()
	}
	for _, r := range t.readers {
		_ = r.Close()
	}
	if t.collection != nil {
		t.collection.Close()
	}
}

// AttachToProcess attaches function tracing probes to a specific process
func (t *Tracer) AttachToProcess(pid uint32) error {
	klog.Infof("⏳ PHASE 2/3: Attaching function probes to PID %d", pid)
	
	t.targetPIDs[pid] = true
	
	// Get the binary path for this process
	binaryPath, err := t.getBinaryPath(pid)
	if err != nil {
		return fmt.Errorf("failed to get binary path for PID %d: %w", pid, err)
	}
	
	klog.Infof("⏳ PHASE 2/3: PID %d binary path: %s", pid, binaryPath)
	
	// Attach function probes to the binary
	links, err := t.attachFunctionProbes(pid, binaryPath)
	if err != nil {
		return fmt.Errorf("failed to attach function probes: %w", err)
	}
	
	t.functionLinks[pid] = links
	klog.Infof("attached %d function probes to PID %d", len(links), pid)
	
	return nil
}

// AttachToBinary attaches function tracing probes to all processes running a specific binary
func (t *Tracer) AttachToBinary(binaryPath string) error {
	klog.Infof("attaching function probes to binary %s", binaryPath)

	t.targetBinaries[binaryPath] = true

	// Find all processes running this binary
	pids, err := t.findProcessesByBinary(binaryPath)
	if err != nil {
		return fmt.Errorf("failed to find processes for binary %s: %w", binaryPath, err)
	}

	klog.Infof("found %d processes running %s", len(pids), binaryPath)

	// Attach to each process
	for _, pid := range pids {
		if err := t.AttachToProcess(pid); err != nil {
			klog.Warningf("failed to attach to PID %d: %v", pid, err)
			continue
		}
	}

	return nil
}

// AttachSystemWide attaches function tracing probes to all processes system-wide
func (t *Tracer) AttachSystemWide() error {
	klog.Infoln("🚀 INITIALIZATION STARTED: Beginning system-wide function tracing setup...")

	// Initialize self-exclusion mechanism first (following Parca/coroot pattern)
	if err := initSelfExclusion(); err != nil {
		return fmt.Errorf("failed to initialize self-exclusion: %w", err)
	}

	klog.Infoln("⏳ PHASE 1/3: Discovering all running processes...")

	// Get all running processes
	processes, err := t.getAllProcesses()
	if err != nil {
		return fmt.Errorf("failed to get all processes: %w", err)
	}

	klog.Infof("✅ PHASE 1 COMPLETE: Found %d processes for system-wide tracing", len(processes))
	klog.Infoln("⏳ PHASE 2/3: Attaching function probes to all processes (this may take 30+ seconds)...")

	attachedCount := 0
	totalProcesses := len(processes)
	startTime := time.Now()
	processedCount := 0
	skippedCount := 0
	failedCount := 0

	// Show the process list for client visibility
	klog.Infof("⏳ PHASE 2/3 ACTIVITY LIST: Processing %d processes...", totalProcesses)

	// Display comprehensive process list with status indicators (following Parca/coroot pattern)
	displayProcessList(processes, "PHASE 2/3 PROCESS QUEUE")

	for i, process := range processes {
		if i < 10 { // Show first 10 processes for visibility
			klog.Infof("⏳ PHASE 2/3 QUEUE: [%d] PID %d (%s)", i+1, process.PID, process.Name)
		} else if i == 10 {
			klog.Infof("⏳ PHASE 2/3 QUEUE: ... and %d more processes", totalProcesses-10)
			break
		}
	}

	for _, process := range processes {
		processedCount++

		// Check overall timeout (circuit breaker for entire phase)
		elapsed := time.Since(startTime)
		if elapsed > 30*time.Second {
			klog.Warningf("🚨 PHASE 2/3 TIMEOUT: Exceeded 30s limit, skipping remaining %d processes", totalProcesses-processedCount+1)
			break
		}

		// Progress indicator every 3 processes for better visibility
		if processedCount%3 == 0 {
			progress := float64(processedCount) / float64(totalProcesses) * 100
			remaining := totalProcesses - processedCount

			// Show current activity and next steps
			currentActivity := "Attaching function probes"
			nextSteps := fmt.Sprintf("Next: %d more processes", remaining)
			if remaining == 0 {
				nextSteps = "Next: Phase 3/3 (Real-time monitoring)"
			}

			klog.Infof("⏳ PHASE 2/3 PROGRESS: %.1f%% (%d/%d) - %s - %s - Elapsed: %v",
				progress, processedCount, totalProcesses, currentActivity, nextSteps, elapsed.Round(time.Second))
		}

		// Skip kernel threads and excluded PIDs
		if t.shouldSkipProcess(process) {
			skippedCount++
			klog.V(2).Infof("⏳ PHASE 2/3: Skipping PID %d (%s) - kernel thread or excluded", process.PID, process.Name)
			continue
		}

		// Check if process is problematic (circuit breaker)
		if isProblematicProcess(uint32(process.PID)) {
			skippedCount++
			klog.Warningf("🚫 PHASE 2/3: Skipping PID %d (%s) - marked as problematic", process.PID, process.Name)
			continue
		}

		// Check if process has already been processed (loop prevention - following Parca/coroot pattern)
		if processedPIDs != nil && processedPIDs[uint32(process.PID)] {
			skippedCount++
			klog.V(2).Infof("🔄 PHASE 2/3: Skipping PID %d (%s) - already processed", process.PID, process.Name)
			continue
		}

		// Validate process is still alive before attempting attachment
		if !isProcessAlive(uint32(process.PID)) {
			skippedCount++
			klog.V(2).Infof("💀 PHASE 2/3: Skipping PID %d (%s) - process no longer alive", process.PID, process.Name)
			continue
		}

		// Mark process as being processed (loop prevention)
		if processedPIDs == nil {
			processedPIDs = make(map[uint32]bool)
		}
		processedPIDs[uint32(process.PID)] = true

		// Update attachment status
		if attachmentStatus == nil {
			attachmentStatus = make(map[uint32]string)
		}
		attachmentStatus[uint32(process.PID)] = "⏳ PROCESSING"

		klog.Infof("⏳ PHASE 2/3 CURRENT: Attaching to PID %d (%s) [%s] - Step %d/%d",
			process.PID, process.Name, process.Path, processedCount, totalProcesses)
		processStart := time.Now()

		// Show next process for better visibility
		nextProcess := "END"
		if processedCount < totalProcesses {
			nextIdx := processedCount // processedCount is 1-based, but we want the next process
			if nextIdx < len(processes) {
				nextProcess = fmt.Sprintf("PID %d (%s)", processes[nextIdx].PID, processes[nextIdx].Name)
			}
		}
		klog.V(2).Infof("⏳ PHASE 2/3 NEXT: %s", nextProcess)

		// Timeout mechanism to prevent hanging (reduced timeout for faster recovery)
		done := make(chan error, 1)
		go func() {
			done <- t.AttachToProcess(uint32(process.PID))
		}()

		select {
		case err := <-done:
			if err != nil {
				failedCount++
				klog.V(2).Infof("⚠️ PHASE 2/3: Failed to attach to PID %d: %v", process.PID, err)
				// Mark as problematic if it consistently fails
				markProblematicProcess(uint32(process.PID), fmt.Sprintf("attachment failed: %v", err))
				continue
			} else {
				// Mark as successfully attached
				if attachmentStatus != nil {
					attachmentStatus[uint32(process.PID)] = "✅ SUCCESS"
				}
			}
		case <-time.After(3 * time.Second): // Reduced from 10s to 3s for faster recovery
			failedCount++
			klog.Warningf("⏰ PHASE 2/3 TIMEOUT: PID %d (%s) attachment timed out after 3s", process.PID, process.Name)
			// Mark as problematic to avoid future attempts
			markProblematicProcess(uint32(process.PID), "attachment timeout")
			continue
		}

		// Detect slow processes (potential bottlenecks)
		processDuration := time.Since(processStart)
		if processDuration > 3*time.Second {
			klog.Warningf("🐌 PHASE 2/3 BOTTLENECK: PID %d (%s) took %v to attach",
				process.PID, process.Name, processDuration.Round(time.Millisecond))
		}

		attachedCount++
	}

	totalElapsed := time.Since(startTime)

	// Force completion if we've been running too long (circuit breaker for entire phase)
	if totalElapsed > 30*time.Second {
		klog.Warningf("🚨 PHASE 2/3 FORCE COMPLETION: Exceeded 30s limit, forcing completion")
		klog.Warningf("🚨 REMAINING PROCESSES: %d processes will be skipped to ensure Phase 3/3 starts", totalProcesses-processedCount)
	}

	klog.Infof("✅ PHASE 2/3 COMPLETE: Successfully attached to %d processes for system-wide tracing", attachedCount)
	klog.Infof("📊 PHASE 2/3 SUMMARY: Processed: %d, Attached: %d, Skipped: %d, Failed: %d, Duration: %v",
		processedCount, attachedCount, skippedCount, failedCount, totalElapsed.Round(time.Millisecond))

	if len(problematicPIDs) > 0 {
		klog.Warningf("🚫 CIRCUIT BREAKER SUMMARY: %d processes marked as problematic", len(problematicPIDs))
	}

	// Display final process status summary (following Parca/coroot pattern)
	klog.Infof("📊 PHASE 2/3 FINAL STATUS SUMMARY:")
	successCount := 0
	finalFailedCount := 0
	for pid, status := range attachmentStatus {
		if status == "✅ SUCCESS" {
			successCount++
		} else if status == "❌ FAILED" {
			finalFailedCount++
		}
		klog.V(2).Infof("📊 PID %d: %s", pid, status)
	}
	klog.Infof("📊 ATTACHMENT RESULTS: ✅ %d successful, ❌ %d failed, ⏭️ %d skipped", successCount, finalFailedCount, skippedCount)

	klog.Infof("⏳ PHASE 3/3 STARTING: Initializing real-time process monitoring...")
	klog.Infoln("⏳ PHASE 3/3: Setting up real-time process detection...")

	// Attach process event tracepoints for real-time process detection (with graceful degradation)
	phase3Start := time.Now()
	if err := t.attachProcessEventTracepoints(); err != nil {
		klog.Warningf("❌ PHASE 3/3 FAILED: Failed to attach process event tracepoints: %v", err)
		klog.Warningf("⚠️ PHASE 3/3: Continuing in degraded mode - existing processes will still be traced")
		klog.Warningf("⚠️ PHASE 3/3: New processes started after this point may not be detected")
		klog.Warningf("💡 PHASE 3/3: To fix this, try: sudo pkill -f deep-ebpf-node && sudo ./deep-ebpf-node-config")
		klog.Warningln("⚠️  Real-time detection disabled, but existing process tracing still works")
		// Don't fail the whole tracer if tracepoints fail
	} else {
		phase3Duration := time.Since(phase3Start)
		klog.Infof("✅ PHASE 3/3 COMPLETE: Real-time process monitoring active (Duration: %v)",
			phase3Duration.Round(time.Millisecond))
		klog.Infof("🎯 INITIALIZATION COMPLETE: Universal eBPF Function Tracer is now monitoring all processes")
		klog.Infof("📡 READY: Watching for new processes and performing dynamic function-level tracing")
	}

	klog.Infoln("🎉 INITIALIZATION COMPLETE! 🎉")
	klog.Infoln("🟢 READY FOR TESTING: You can now run test programs and they will be detected!")
	klog.Infoln("📋 STATUS: Universal eBPF Function Tracer is fully operational")

	// If real-time detection failed, start polling-based fallback
	// Check if we have any tracepoint links attached
	hasTracepointLinks := false
	for _, link := range t.links {
		// This is a simple check - in practice you might want more sophisticated detection
		if link != nil {
			hasTracepointLinks = true
			break
		}
	}

	if !hasTracepointLinks {
		klog.Warningf("🔄 FALLBACK: No tracepoint links detected, starting polling-based process detection")
		go t.startPollingBasedProcessDetection(context.Background())
	}

	return nil
}

func (t *Tracer) getBinaryPath(pid uint32) (string, error) {
	exePath := proc.Path(pid, "exe")
	target, err := os.Readlink(exePath)
	if err != nil {
		return "", err
	}
	return target, nil
}

func (t *Tracer) findProcessesByBinary(binaryPath string) ([]uint32, error) {
	var pids []uint32
	
	// Read /proc to find matching processes
	procDir, err := os.Open("/proc")
	if err != nil {
		return nil, err
	}
	defer procDir.Close()
	
	entries, err := procDir.Readdir(-1)
	if err != nil {
		return nil, err
	}
	
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}
		
		pidStr := entry.Name()
		pid, err := strconv.ParseUint(pidStr, 10, 32)
		if err != nil {
			continue
		}
		
		procBinaryPath, err := t.getBinaryPath(uint32(pid))
		if err != nil {
			continue
		}
		
		if procBinaryPath == binaryPath {
			pids = append(pids, uint32(pid))
		}
	}
	
	return pids, nil
}

// ProcessInfo represents basic process information
type ProcessInfo struct {
	PID  int
	Name string
	Path string
}

// getAllProcesses returns all running processes
func (t *Tracer) getAllProcesses() ([]ProcessInfo, error) {
	var processes []ProcessInfo

	// Read /proc directory
	procDir, err := os.Open("/proc")
	if err != nil {
		return nil, fmt.Errorf("failed to open /proc: %w", err)
	}
	defer procDir.Close()

	entries, err := procDir.Readdir(-1)
	if err != nil {
		return nil, fmt.Errorf("failed to read /proc: %w", err)
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		// Check if directory name is a PID (numeric)
		pid, err := strconv.Atoi(entry.Name())
		if err != nil {
			continue
		}

		// Get process info
		name, path := t.getProcessInfo(pid)
		if name == "" {
			continue
		}

		processes = append(processes, ProcessInfo{
			PID:  pid,
			Name: name,
			Path: path,
		})
	}

	return processes, nil
}

// getProcessInfo gets process name and executable path
func (t *Tracer) getProcessInfo(pid int) (string, string) {
	// Read process name from /proc/PID/comm
	commPath := fmt.Sprintf("/proc/%d/comm", pid)
	commData, err := os.ReadFile(commPath)
	if err != nil {
		return "", ""
	}
	name := strings.TrimSpace(string(commData))

	// Read executable path from /proc/PID/exe
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	path, err := os.Readlink(exePath)
	if err != nil {
		// If we can't read the exe link, use cmdline
		cmdlinePath := fmt.Sprintf("/proc/%d/cmdline", pid)
		cmdlineData, err := os.ReadFile(cmdlinePath)
		if err != nil {
			return name, ""
		}
		// First argument is usually the executable
		args := strings.Split(string(cmdlineData), "\x00")
		if len(args) > 0 && args[0] != "" {
			path = args[0]
		}
	}

	return name, path
}

// shouldSkipProcess determines if a process should be skipped in system-wide tracing
func (t *Tracer) shouldSkipProcess(process ProcessInfo) bool {
	// Skip kernel threads (processes with no executable path)
	if process.Path == "" {
		return true
	}

	// Skip if path looks like a kernel thread
	if strings.HasPrefix(process.Path, "[") && strings.HasSuffix(process.Path, "]") {
		return true
	}

	// Check self-exclusion first (most important - following Parca/coroot pattern)
	if isExcludedProcess(uint32(process.PID), process.Path) {
		klog.V(2).Infof("🔒 SELF-EXCLUSION: Skipping PID %d (%s) - excluded process", process.PID, process.Path)
		return true
	}

	// Use configuration-based filtering (following Parca/coroot pattern)
	if config.GlobalConfig != nil && config.GlobalConfig.ShouldSkipProcess(uint32(process.PID), process.Name, process.Path) {
		klog.V(2).Infof("📋 CONFIG FILTER: Skipping PID %d (%s) [%s] - excluded by configuration", process.PID, process.Name, process.Path)
		return true
	}

	// Fallback to hardcoded exclusions if no configuration
	if config.GlobalConfig == nil {
		// Skip problematic processes that might cause bottlenecks
		skipNames := []string{
			"bpftool",
			"kthreadd",
			"ksoftirqd",
			"migration",
			"rcu_",
			"watchdog",
			"systemd",           // System manager - can be slow
			"containerd",        // Container runtime - complex and slow
			"dockerd",          // Docker daemon - complex and slow
			"redis-server",     // Database - might have complex symbol tables
			"python3.12",       // Python interpreter - complex symbol resolution
		}

		// Skip privileged system paths that might cause permission issues
		skipPaths := []string{
			"/usr/lib/systemd/",
			"/usr/sbin/",
			"/sbin/",
		}

		for _, skipName := range skipNames {
			if strings.Contains(process.Name, skipName) {
				return true
			}
		}

		// Check for problematic paths
		for _, skipPath := range skipPaths {
			if strings.HasPrefix(process.Path, skipPath) {
				return true
			}
		}

		// Skip system processes (PID < 100) unless explicitly included
		if process.PID < 100 && process.PID != 1 {
			return true
		}
	}

	return false
}

func (t *Tracer) attachFunctionProbes(pid uint32, binaryPath string) ([]link.Link, error) {
	var links []link.Link
	
	// Open the executable
	exe, err := link.OpenExecutable(binaryPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open executable %s: %w", binaryPath, err)
	}
	
	// Get function symbols from the binary
	functions, err := t.getFunctionSymbols(binaryPath)
	if err != nil {
		klog.Warningf("failed to get function symbols from %s: %v", binaryPath, err)
		// Continue with main function as fallback
		functions = []string{"main"}
	}
	
	klog.Infof("⏳ PHASE 2/3: Found %d functions in %s: %v", len(functions), binaryPath, functions)
	
	options := &link.UprobeOptions{PID: int(pid)}
	
	// Debug: Show available uprobe programs
	klog.V(2).Infof("🔍 DEBUG: Available uprobe programs: %v", func() []string {
		var names []string
		for name := range t.uprobes {
			names = append(names, name)
		}
		return names
	}())

	// Attach uprobes to functions
	for _, funcName := range functions {
		// Try to find the correct entry program name
		var entryProg *ebpf.Program
		for progName, prog := range t.uprobes {
			if strings.Contains(progName, "function_entry") || strings.Contains(progName, "trace_function_entry") {
				entryProg = prog
				klog.V(2).Infof("🔍 DEBUG: Using entry program: %s", progName)
				break
			}
		}

		// Attach entry probe
		if entryProg != nil {
			uprobe, err := exe.Uprobe(funcName, entryProg, options)
			if err != nil {
				klog.V(2).Infof("failed to attach entry uprobe to %s: %v", funcName, err)
				continue
			}
			links = append(links, uprobe)
			klog.Infof("✅ ATTACHED: Entry uprobe to %s in PID %d", funcName, pid)
		} else {
			klog.Warningf("⚠️ NO ENTRY PROGRAM: Could not find function_entry program")
		}

		// Try to find the correct exit program name
		var exitProg *ebpf.Program
		for progName, prog := range t.uprobes {
			if strings.Contains(progName, "function_exit") || strings.Contains(progName, "trace_function_exit") {
				exitProg = prog
				klog.V(2).Infof("🔍 DEBUG: Using exit program: %s", progName)
				break
			}
		}

		// Attach exit probe
		if exitProg != nil {
			uretprobe, err := exe.Uretprobe(funcName, exitProg, options)
			if err != nil {
				klog.V(2).Infof("failed to attach exit uretprobe to %s: %v", funcName, err)
				continue
			}
			links = append(links, uretprobe)
			klog.Infof("✅ ATTACHED: Exit uretprobe to %s in PID %d", funcName, pid)
		} else {
			klog.V(2).Infof("⚠️ NO EXIT PROGRAM: Could not find function_exit program (this is OK)")
		}
	}
	
	if len(links) == 0 {
		return nil, fmt.Errorf("failed to attach any function probes to %s", binaryPath)
	}
	
	return links, nil
}

func (t *Tracer) getFunctionSymbols(binaryPath string) ([]string, error) {
	// Real ELF symbol extraction following coroot/tracee pattern
	file, err := elf.Open(binaryPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open ELF file %s: %w", binaryPath, err)
	}
	defer file.Close()

	var allSymbols []elf.Symbol
	var functionNames []string

	// Try to get regular symbols first
	if symbols, err := file.Symbols(); err == nil {
		allSymbols = append(allSymbols, symbols...)
	}

	// Try to get dynamic symbols as fallback
	if dynSymbols, err := file.DynamicSymbols(); err == nil {
		allSymbols = append(allSymbols, dynSymbols...)
	}

	if len(allSymbols) == 0 {
		// Fallback to common function names if no symbols found
		return []string{"main", "_start"}, nil
	}

	// Extract function symbols (STT_FUNC type)
	for _, symbol := range allSymbols {
		if elf.ST_TYPE(symbol.Info) == elf.STT_FUNC && symbol.Name != "" && symbol.Size > 0 {
			functionNames = append(functionNames, symbol.Name)
		}
	}

	// If no function symbols found, use fallback
	if len(functionNames) == 0 {
		return []string{"main", "_start"}, nil
	}

	// Limit to first 10 functions to avoid too many attachments
	if len(functionNames) > 10 {
		functionNames = functionNames[:10]
	}

	// Log the actual function names found for debugging
	klog.V(1).Infof("Function symbols found in %s: %v", binaryPath, functionNames)

	return functionNames, nil
}

// handleNewProcess handles newly started processes (event-based detection)
func handleNewProcess(pid uint32) {
	// Add a small delay to let the process initialize
	time.Sleep(100 * time.Millisecond)

	// Check if process still exists
	if _, err := os.Stat(fmt.Sprintf("/proc/%d", pid)); os.IsNotExist(err) {
		klog.V(2).Infof("⚠️  Process PID %d exited before attachment", pid)
		return
	}

	// Check if process has already been processed (loop prevention - following Parca/coroot pattern)
	if processedPIDs != nil && processedPIDs[pid] {
		klog.V(2).Infof("🔄 DYNAMIC: Skipping PID %d - already processed in Phase 2/3", pid)
		return
	}

	// Check if process is problematic (circuit breaker)
	if isProblematicProcess(pid) {
		klog.V(2).Infof("🚫 DYNAMIC: Skipping PID %d - marked as problematic", pid)
		return
	}

	// Get binary path following Parca/coroot pattern
	binaryPath, err := getBinaryPathForPID(pid)
	if err != nil {
		klog.V(2).Infof("⚠️  Failed to get binary path for PID %d: %v", pid, err)
		return
	}

	// Skip if it's a kernel thread or system process
	if shouldSkipDynamicProcess(pid, binaryPath) {
		klog.V(2).Infof("⏭️  Skipping dynamic attachment to PID %d (%s)", pid, binaryPath)
		return
	}

	klog.Infof("🔗 DYNAMIC ATTACHMENT: Attaching to new process PID %d [%s]", pid, binaryPath)

	// TODO: Get tracer instance and perform actual attachment
	// For now, we'll implement this step by step
	klog.Infof("📊 DEEP TRACING: Process PID %d (%s) ready for function-level tracing", pid, binaryPath)
}

// handleProcessExit handles process exit cleanup
func handleProcessExit(pid uint32) {
	klog.V(2).Infof("Cleaning up resources for exited process: PID %d", pid)

	// TODO: Clean up function links and resources for this PID
	// This would involve removing entries from functionLinks map
}

// attachProcessEventTracepoints attaches tracepoints for real-time process detection (following Parca/coroot pattern)
func (t *Tracer) attachProcessEventTracepoints() error {
	klog.V(2).Infof("⏳ PHASE 3/3: Attempting to attach task_newtask tracepoint...")

	// Attach task_newtask tracepoint for process creation with retry mechanism
	var newTaskLink link.Link
	var err error

	// Debug: Show available programs
	klog.V(2).Infof("🔍 DEBUG: Available programs: %v", func() []string {
		var names []string
		for name := range t.collection.Programs {
			names = append(names, name)
		}
		return names
	}())

	// Try to find the correct program name
	var taskNewtaskProg *ebpf.Program

	// Try exact match first
	if prog, exists := t.collection.Programs["trace_task_newtask"]; exists {
		taskNewtaskProg = prog
		klog.V(2).Infof("🔍 DEBUG: Using task_newtask program (exact): trace_task_newtask")
	} else if prog, exists := t.collection.Programs["task_newtask"]; exists {
		taskNewtaskProg = prog
		klog.V(2).Infof("🔍 DEBUG: Using task_newtask program (coroot style): task_newtask")
	} else {
		// Fallback to contains search
		for progName, prog := range t.collection.Programs {
			if strings.Contains(progName, "task_newtask") {
				taskNewtaskProg = prog
				klog.V(2).Infof("🔍 DEBUG: Using task_newtask program (contains): %s", progName)
				break
			}
		}
	}

	if taskNewtaskProg == nil {
		return fmt.Errorf("task_newtask program not found in collection")
	}

	// Try multiple times with cleanup between attempts (following Parca/coroot pattern)
	for attempt := 1; attempt <= 3; attempt++ {
		newTaskLink, err = link.Tracepoint("task", "task_newtask", taskNewtaskProg, nil)
		if err == nil {
			klog.V(2).Infof("✅ PHASE 3/3: Successfully attached task_newtask tracepoint on attempt %d", attempt)
			break
		}

		klog.V(2).Infof("⚠️ PHASE 3/3: Attempt %d failed to attach task_newtask tracepoint: %v", attempt, err)

		// If it's a "file exists" error, try to clean up and retry
		if strings.Contains(err.Error(), "file exists") || strings.Contains(err.Error(), "already exists") {
			klog.Warningf("🔧 PHASE 3/3: BPF link conflict detected, attempting cleanup before retry %d/3", attempt)

			// Small delay before retry
			time.Sleep(time.Duration(attempt) * 100 * time.Millisecond)
			continue
		}

		// For other errors, don't retry
		break
	}

	if err != nil {
		// If we still can't attach, try to continue without real-time detection
		klog.Warningf("⚠️ PHASE 3/3: Failed to attach task_newtask tracepoint after 3 attempts: %v", err)
		klog.Warningf("⚠️ PHASE 3/3: Continuing without real-time process detection (existing processes will still be traced)")
		return fmt.Errorf("failed to attach task_newtask tracepoint: %w", err)
	}

	t.links = append(t.links, newTaskLink)

	klog.V(2).Infof("⏳ PHASE 3/3: Attempting to attach sched_process_exit tracepoint...")

	// Try to find the correct sched_process_exit program name
	var schedProcessExitProg *ebpf.Program

	// Try exact match first
	if prog, exists := t.collection.Programs["trace_sched_process_exit"]; exists {
		schedProcessExitProg = prog
		klog.V(2).Infof("🔍 DEBUG: Using sched_process_exit program (exact): trace_sched_process_exit")
	} else if prog, exists := t.collection.Programs["sched_process_exit"]; exists {
		schedProcessExitProg = prog
		klog.V(2).Infof("🔍 DEBUG: Using sched_process_exit program (coroot style): sched_process_exit")
	} else {
		// Fallback to contains search
		for progName, prog := range t.collection.Programs {
			if strings.Contains(progName, "sched_process_exit") {
				schedProcessExitProg = prog
				klog.V(2).Infof("🔍 DEBUG: Using sched_process_exit program (contains): %s", progName)
				break
			}
		}
	}

	if schedProcessExitProg == nil {
		klog.Warningf("⚠️ PHASE 3/3: sched_process_exit program not found, continuing without exit detection")
		return nil // Don't fail the whole tracer
	}

	// Attach sched_process_exit tracepoint for process exit with retry mechanism
	var exitLink link.Link
	for attempt := 1; attempt <= 3; attempt++ {
		exitLink, err = link.Tracepoint("sched", "sched_process_exit", schedProcessExitProg, nil)
		if err == nil {
			klog.V(2).Infof("✅ PHASE 3/3: Successfully attached sched_process_exit tracepoint on attempt %d", attempt)
			break
		}

		klog.V(2).Infof("⚠️ PHASE 3/3: Attempt %d failed to attach sched_process_exit tracepoint: %v", attempt, err)

		// If it's a "file exists" error, try to clean up and retry
		if strings.Contains(err.Error(), "file exists") || strings.Contains(err.Error(), "already exists") {
			klog.Warningf("🔧 PHASE 3/3: BPF link conflict detected, attempting cleanup before retry %d/3", attempt)

			// Small delay before retry
			time.Sleep(time.Duration(attempt) * 100 * time.Millisecond)
			continue
		}

		// For other errors, don't retry
		break
	}

	if err != nil {
		klog.Warningf("⚠️ PHASE 3/3: Failed to attach sched_process_exit tracepoint: %v", err)
		klog.Warningf("⚠️ PHASE 3/3: Continuing without process exit detection")
		return fmt.Errorf("failed to attach sched_process_exit tracepoint: %w", err)
	}
	t.links = append(t.links, exitLink)

	klog.V(2).Infof("Successfully attached process event tracepoints")
	return nil
}

// getProcessInfo gets process binary path and determines if it's kernel or user process
func getProcessInfo(pid uint32) (string, bool) {
	// Read process executable path first
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	if link, err := os.Readlink(exePath); err == nil {
		// User process with valid executable
		return link, false
	}

	// Try to read comm (command name) for kernel threads or processes without exe
	commPath := fmt.Sprintf("/proc/%d/comm", pid)
	if data, err := os.ReadFile(commPath); err == nil {
		comm := strings.TrimSpace(string(data))
		// Kernel threads typically have names in brackets
		if strings.HasPrefix(comm, "[") && strings.HasSuffix(comm, "]") {
			return comm, true
		}
		// Return comm name for processes without readable exe
		return fmt.Sprintf("(%s)", comm), false
	}

	return fmt.Sprintf("PID_%d", pid), false
}

// getBinaryPathForPID gets the binary path for a process (following Parca/coroot pattern)
func getBinaryPathForPID(pid uint32) (string, error) {
	// Read the executable path from /proc/PID/exe
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	binaryPath, err := os.Readlink(exePath)
	if err != nil {
		return "", fmt.Errorf("failed to read executable path: %w", err)
	}
	return binaryPath, nil
}

// shouldSkipDynamicProcess determines if we should skip dynamic attachment to a process
func shouldSkipDynamicProcess(pid uint32, binaryPath string) bool {
	// Skip kernel threads (no real binary path)
	if binaryPath == "" || binaryPath == "/" {
		return true
	}

	// Skip if path looks like a kernel thread
	if strings.HasPrefix(binaryPath, "[") && strings.HasSuffix(binaryPath, "]") {
		return true
	}

	// Check self-exclusion first (most important - following Parca/coroot pattern)
	if isExcludedProcess(pid, binaryPath) {
		return true
	}

	// Skip dockerd to reduce noise (as requested)
	if strings.Contains(binaryPath, "dockerd") {
		return true
	}

	// Skip very short-lived system processes
	if pid < 10 {
		return true
	}

	return false
}

// isProcessAlive checks if a process is still alive and accessible (following Parca/coroot pattern)
func isProcessAlive(pid uint32) bool {
	// Check if /proc/PID exists
	procPath := fmt.Sprintf("/proc/%d", pid)
	if _, err := os.Stat(procPath); os.IsNotExist(err) {
		return false
	}

	// Check if we can read the process status
	statusPath := fmt.Sprintf("/proc/%d/status", pid)
	if _, err := os.ReadFile(statusPath); err != nil {
		return false
	}

	// Check if process is not a zombie
	statPath := fmt.Sprintf("/proc/%d/stat", pid)
	if data, err := os.ReadFile(statPath); err == nil {
		fields := strings.Fields(string(data))
		if len(fields) > 2 {
			state := fields[2]
			if state == "Z" { // Zombie process
				return false
			}
		}
	}

	return true
}

// isProblematicProcess checks if a process is known to be problematic (circuit breaker pattern)
func isProblematicProcess(pid uint32) bool {
	if problematicPIDs == nil {
		return false
	}
	return problematicPIDs[pid]
}

// markProblematicProcess marks a process as problematic (circuit breaker pattern)
func markProblematicProcess(pid uint32, reason string) {
	if problematicPIDs == nil {
		problematicPIDs = make(map[uint32]bool)
	}
	problematicPIDs[pid] = true
	if attachmentStatus == nil {
		attachmentStatus = make(map[uint32]string)
	}
	attachmentStatus[pid] = "❌ FAILED"
	klog.Warningf("🚫 CIRCUIT BREAKER: Marking PID %d as problematic - %s", pid, reason)
}

// displayProcessList displays the comprehensive process list with status indicators (following Parca/coroot pattern)
func displayProcessList(processes []ProcessInfo, title string) {
	klog.Infof("📋 %s: Displaying %d processes with status indicators", title, len(processes))

	for i, process := range processes {
		status := "⏳ PENDING"
		if attachmentStatus != nil {
			if s, exists := attachmentStatus[uint32(process.PID)]; exists {
				status = s
			}
		}

		// Add emoji indicators for different process types
		typeEmoji := "🔧" // Default
		if strings.Contains(process.Path, "python") {
			typeEmoji = "🐍"
		} else if strings.Contains(process.Path, "docker") {
			typeEmoji = "🐳"
		} else if strings.Contains(process.Path, "bash") || strings.Contains(process.Path, "sh") {
			typeEmoji = "🐚"
		} else if strings.Contains(process.Path, "systemd") {
			typeEmoji = "⚙️"
		} else if strings.Contains(process.Path, "init") {
			typeEmoji = "🚀"
		} else if strings.Contains(process.Path, "java") {
			typeEmoji = "☕"
		} else if strings.Contains(process.Path, "go") || strings.Contains(process.Name, "go") {
			typeEmoji = "🐹"
		}

		klog.Infof("📋 [%d/%d] %s %s PID %d (%s) [%s]",
			i+1, len(processes), typeEmoji, status, process.PID, process.Name, process.Path)
	}
}

// cleanupExistingBPFLinks attempts to clean up any existing BPF links (following Parca/coroot pattern)
func cleanupExistingBPFLinks() {
	klog.V(2).Infof("🔧 BPF CLEANUP: Attempting to clean up any existing BPF links...")

	// Try to kill any existing deep-ebpf-node processes
	// This is a best-effort cleanup - errors are not fatal

	// Note: In a production environment, you might want to implement
	// more sophisticated cleanup mechanisms like:
	// 1. BPF link enumeration and cleanup
	// 2. Process group cleanup
	// 3. Shared memory cleanup
	// 4. Lock file mechanisms

	klog.V(2).Infof("🔧 BPF CLEANUP: Cleanup attempt completed")
}

// startPollingBasedProcessDetection starts polling-based process detection as fallback (following Parca/coroot pattern)
func (t *Tracer) startPollingBasedProcessDetection(ctx context.Context) {
	klog.Infof("🔄 FALLBACK: Starting polling-based process detection (checking every 2 seconds)")

	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	knownPIDs := make(map[uint32]bool)

	// Initialize with current processes
	processes, err := t.getAllProcesses()
	if err == nil {
		for _, process := range processes {
			knownPIDs[uint32(process.PID)] = true
		}
	}

	for {
		select {
		case <-ctx.Done():
			klog.V(2).Infof("🔄 FALLBACK: Stopping polling-based process detection")
			return
		case <-ticker.C:
			// Get current processes
			currentProcesses, err := t.getAllProcesses()
			if err != nil {
				klog.V(2).Infof("⚠️ FALLBACK: Failed to get process list: %v", err)
				continue
			}

			// Check for new processes
			currentPIDs := make(map[uint32]bool)
			for _, process := range currentProcesses {
				pid := uint32(process.PID)
				currentPIDs[pid] = true

				// If this is a new process, handle it
				if !knownPIDs[pid] {
					klog.V(2).Infof("🔄 FALLBACK: New process detected: PID %d (%s)", pid, process.Name)
					go t.handleNewProcessWithTracer(pid)
				}
			}

			// Check for exited processes
			for pid := range knownPIDs {
				if !currentPIDs[pid] {
					klog.V(2).Infof("🔄 FALLBACK: Process exited: PID %d", pid)
					go handleProcessExit(pid)
				}
			}

			// Update known PIDs
			knownPIDs = currentPIDs
		}
	}
}

// initSelfExclusion initializes self-exclusion mechanism (following Parca/coroot pattern)
func initSelfExclusion() error {
	// Get our own PID
	selfPID = uint32(os.Getpid())

	// Get our own binary path
	var err error
	selfBinaryPath, err = os.Readlink("/proc/self/exe")
	if err != nil {
		return fmt.Errorf("failed to get self binary path: %w", err)
	}

	// Get our parent PID
	if data, err := os.ReadFile("/proc/self/stat"); err == nil {
		fields := strings.Fields(string(data))
		if len(fields) > 3 {
			if ppid, err := strconv.ParseUint(fields[3], 10, 32); err == nil {
				selfPPID = uint32(ppid)
			}
		}
	}

	// Initialize excluded PIDs map
	excludedPIDs = make(map[uint32]bool)
	excludedPIDs[selfPID] = true
	excludedPIDs[selfPPID] = true

	// Initialize problematic PIDs map (circuit breaker)
	problematicPIDs = make(map[uint32]bool)

	// Initialize process tracking maps (following Parca/coroot pattern)
	processedPIDs = make(map[uint32]bool)
	attachmentStatus = make(map[uint32]string)

	klog.Infof("🔒 SELF-EXCLUSION: Own PID %d, Parent PID %d, Binary: %s", selfPID, selfPPID, selfBinaryPath)

	return nil
}

// isExcludedProcess checks if a process should be excluded (following Parca/coroot pattern)
func isExcludedProcess(pid uint32, binaryPath string) bool {
	// Check if it's in our excluded PIDs list
	if excludedPIDs[pid] {
		return true
	}

	// Check if it's our own binary
	if strings.Contains(binaryPath, "deep-ebpf-node") {
		excludedPIDs[pid] = true // Cache for future checks
		return true
	}

	// Check if it's a sudo process that might be our parent
	if strings.Contains(binaryPath, "sudo") {
		// Read the process's command line to see if it's launching our tracer
		if cmdline, err := os.ReadFile(fmt.Sprintf("/proc/%d/cmdline", pid)); err == nil {
			cmdlineStr := string(cmdline)
			if strings.Contains(cmdlineStr, "deep-ebpf-node") {
				klog.Infof("🔒 EXCLUDING SUDO: PID %d launching deep-ebpf-node", pid)
				excludedPIDs[pid] = true
				return true
			}
		}
	}

	return false
}

// handleNewProcessWithTracer handles newly started processes with actual function tracing
func (t *Tracer) handleNewProcessWithTracer(pid uint32) {
	// Add a small delay to let the process initialize
	time.Sleep(100 * time.Millisecond)

	// Check if process still exists
	if _, err := os.Stat(fmt.Sprintf("/proc/%d", pid)); os.IsNotExist(err) {
		klog.V(2).Infof("⚠️  Process PID %d exited before attachment", pid)
		return
	}

	// Get binary path following Parca/coroot pattern
	binaryPath, err := getBinaryPathForPID(pid)
	if err != nil {
		klog.V(2).Infof("⚠️  Failed to get binary path for PID %d: %v", pid, err)
		return
	}

	// Skip if it's a kernel thread or system process
	if shouldSkipDynamicProcess(pid, binaryPath) {
		klog.V(2).Infof("⏭️  Skipping dynamic attachment to PID %d (%s)", pid, binaryPath)
		return
	}

	klog.Infof("🔗 DYNAMIC ATTACHMENT: Attaching to new process PID %d [%s]", pid, binaryPath)

	// Perform actual function tracing attachment with timeout
	done := make(chan error, 1)
	go func() {
		done <- t.AttachToProcess(pid)
	}()

	select {
	case err := <-done:
		if err != nil {
			klog.Warningf("❌ DYNAMIC ATTACHMENT FAILED: PID %d (%s): %v", pid, binaryPath, err)
			return
		}
		klog.Infof("✅ DEEP TRACING ACTIVE: Process PID %d (%s) now being traced at function level", pid, binaryPath)
	case <-time.After(5 * time.Second):
		klog.Warningf("⏰ DYNAMIC ATTACHMENT TIMEOUT: PID %d (%s) attachment timed out after 5s", pid, binaryPath)
		return
	}
}

type perfMap struct {
	name                  string
	perCPUBufferSizePages int
	typ                   perfMapType
	readTimeout           time.Duration
}

func (t *Tracer) ebpf(ch chan<- Event) error {
	// Validate eBPF support
	if err := validateEBPFSupport(); err != nil {
		return fmt.Errorf("eBPF not supported: %w", err)
	}

	// Log eBPF environment information
	logEBPFInfo()

	// Load eBPF program
	collectionSpec, err := loadEBPFProgram()
	if err != nil {
		return fmt.Errorf("failed to load eBPF program: %w", err)
	}

	// Set memory limit for eBPF
	_ = unix.Setrlimit(unix.RLIMIT_MEMLOCK, &unix.Rlimit{Cur: unix.RLIM_INFINITY, Max: unix.RLIM_INFINITY})

	// Create eBPF collection
	c, err := ebpf.NewCollectionWithOptions(collectionSpec, ebpf.CollectionOptions{
		Programs: ebpf.ProgramOptions{
			LogLevel: 1, // Enable basic logging
			LogSize:  1024 * 1024, // 1MB log buffer
		},
	})
	if err != nil {
		var vErr *ebpf.VerifierError
		if errors.As(err, &vErr) {
			klog.Errorf("eBPF verifier error: %+v", vErr)
		}
		return fmt.Errorf("failed to load eBPF collection: %w", err)
	}
	t.collection = c

	// Initialize eBPF maps
	if err := t.initializeEBPFMaps(); err != nil {
		klog.Warningf("Failed to initialize eBPF maps: %v", err)
	}

	perfMaps := []perfMap{
		{name: "proc_events", typ: perfMapTypeProcEvents, perCPUBufferSizePages: 4},
		{name: "function_events", typ: perfMapTypeFunctionEvents, perCPUBufferSizePages: 8, readTimeout: 10 * time.Millisecond},
	}

	// Only add other perf maps if not focusing purely on function tracing
	if !t.disableL7Tracing {
		perfMaps = append(perfMaps, []perfMap{
			{name: "tcp_listen_events", typ: perfMapTypeTCPEvents, perCPUBufferSizePages: 4},
			{name: "tcp_connect_events", typ: perfMapTypeTCPEvents, perCPUBufferSizePages: 8, readTimeout: 10 * time.Millisecond},
			{name: "tcp_retransmit_events", typ: perfMapTypeTCPEvents, perCPUBufferSizePages: 4},
			{name: "file_events", typ: perfMapTypeFileEvents, perCPUBufferSizePages: 4},
			{name: "python_thread_events", typ: perfMapTypePythonThreadEvents, perCPUBufferSizePages: 4},
			{name: "l7_events", typ: perfMapTypeL7Events, perCPUBufferSizePages: 32},
		}...)
	}

	pageSize := os.Getpagesize()
	for _, pm := range perfMaps {
		r, err := perf.NewReader(t.collection.Maps[pm.name], pm.perCPUBufferSizePages*pageSize)
		if err != nil {
			t.Close()
			return fmt.Errorf("failed to create ebpf reader: %w", err)
		}
		t.readers[pm.name] = r
		go runEventsReader(pm.name, r, ch, pm.typ, pm.readTimeout, t)
	}

	for _, programSpec := range collectionSpec.Programs {
		program := t.collection.Programs[programSpec.Name]
		if t.disableL7Tracing {
			switch programSpec.Name {
			case "sys_enter_writev", "sys_enter_write", "sys_enter_sendto", "sys_enter_sendmsg", "sys_enter_sendmmsg":
				continue
			case "sys_enter_read", "sys_enter_readv", "sys_enter_recvfrom", "sys_enter_recvmsg":
				continue
			case "sys_exit_read", "sys_exit_readv", "sys_exit_recvfrom", "sys_exit_recvmsg":
				continue
			}
		}
		var l link.Link
		switch programSpec.Type {
		case ebpf.TracePoint:
			parts := strings.SplitN(programSpec.AttachTo, "/", 2)
			l, err = link.Tracepoint(parts[0], parts[1], program, nil)
		case ebpf.Kprobe:
			if strings.HasPrefix(programSpec.SectionName, "uprobe/") {
				t.uprobes[programSpec.Name] = program
				continue
			}
			l, err = link.Kprobe(programSpec.AttachTo, program, nil)
			if err != nil && programSpec.SectionName == "kprobe/nf_ct_deliver_cached_events" {
				klog.Warningln("nf_conntrack may not be in use:", err)
				continue
			}
		}
		if err != nil {
			t.Close()
			return fmt.Errorf("failed to link program '%s': %w", programSpec.Name, err)
		}
		if l != nil {
			t.links = append(t.links, l)
		}
	}

	return nil
}

// Event structures for function tracing
type functionEvent struct {
	Type         EventType
	Pid          uint32
	Timestamp    uint64
	Duration     uint64
	FunctionAddr uint64
	ReturnAddr   uint64
	Args         [6]uint64 // RDI, RSI, RDX, RCX, R8, R9
	ReturnValue  uint64
	StackDepth   uint32
	StackTrace   [64]uint64
}

// Process event structure (matching eBPF struct)
type processEvent struct {
	Type   uint32
	PID    uint32
	Reason uint32
}

func runEventsReader(name string, r *perf.Reader, ch chan<- Event, typ perfMapType, readTimeout time.Duration, tracer *Tracer) {
	if readTimeout == 0 {
		readTimeout = 100 * time.Millisecond
	}
	for {
		r.SetDeadline(time.Now().Add(readTimeout))
		rec, err := r.Read()
		if err != nil {
			if errors.Is(err, perf.ErrClosed) {
				break
			}
			continue
		}
		if rec.LostSamples > 0 {
			klog.Errorln(name, "lost samples:", rec.LostSamples)
			continue
		}
		var event Event

		switch typ {
		case perfMapTypeFunctionEvents:
			v := &functionEvent{}
			if err := binary.Read(bytes.NewBuffer(rec.RawSample), binary.LittleEndian, v); err != nil {
				klog.Warningln("failed to read function event:", err)
				continue
			}

			// DEBUG: Log function events being received
			klog.Infof("🎯 FUNCTION EVENT RECEIVED: PID %d, Type %d, Addr 0x%x, Timestamp %d",
				v.Pid, v.Type, v.FunctionAddr, v.Timestamp)

			// Convert to our Event structure
			funcData := &FunctionData{
				FunctionAddr: v.FunctionAddr,
				ReturnAddr:   v.ReturnAddr,
				Arguments:    v.Args[:],
				ReturnValue:  v.ReturnValue,
				StackTrace:   v.StackTrace[:v.StackDepth],
				StackDepth:   v.StackDepth,
			}

			event = Event{
				Type:         v.Type,
				Pid:          v.Pid,
				Timestamp:    v.Timestamp,
				Duration:     time.Duration(v.Duration),
				FunctionData: funcData,
			}

			klog.Infof("🎯 FUNCTION EVENT CONVERTED: Sending to channel for PID %d", v.Pid)

		case perfMapTypeProcEvents:
			// Handle process events (real-time process detection)
			v := &processEvent{}
			if err := binary.Read(bytes.NewBuffer(rec.RawSample), binary.LittleEndian, v); err != nil {
				klog.Warningln("failed to read process event:", err)
				continue
			}

			// Handle process start/exit events
			switch v.Type {
			case uint32(EventTypeProcessStart):
				// Get process info for better visibility
				binaryPath, isKernel := getProcessInfo(v.PID)
				processType := "USER"
				if isKernel {
					processType = "KERNEL"
				}

				// Skip dockerd to reduce noise
				if strings.Contains(binaryPath, "dockerd") {
					klog.V(2).Infof("⏭️ SKIPPING DOCKERD: PID %d [%s] %s", v.PID, processType, binaryPath)
					continue
				}

				klog.Infof("🔄 NEW PROCESS DETECTED (INIT PHASE): PID %d [%s] %s", v.PID, processType, binaryPath)
				// Trigger dynamic attachment to new process with tracer instance
				go tracer.handleNewProcessWithTracer(v.PID)
			case uint32(EventTypeProcessExit):
				// Get process info for better visibility
				binaryPath, isKernel := getProcessInfo(v.PID)
				processType := "USER"
				if isKernel {
					processType = "KERNEL"
				}

				// Skip dockerd to reduce noise
				if strings.Contains(binaryPath, "dockerd") {
					klog.V(2).Infof("⏭️ SKIPPING DOCKERD EXIT: PID %d [%s] %s", v.PID, processType, binaryPath)
					continue
				}

				klog.Infof("🔄 PROCESS EXITED (INIT PHASE): PID %d [%s] %s", v.PID, processType, binaryPath)
				// Clean up resources for exited process
				go handleProcessExit(v.PID)
			}
			continue

		default:
			continue
		}

		ch <- event
	}
}

func (t EventType) String() string {
	switch t {
	case EventTypeProcessStart:
		return "process-start"
	case EventTypeProcessExit:
		return "process-exit"
	case EventTypeConnectionOpen:
		return "connection-open"
	case EventTypeConnectionClose:
		return "connection-close"
	case EventTypeConnectionError:
		return "connection-error"
	case EventTypeListenOpen:
		return "listen-open"
	case EventTypeListenClose:
		return "listen-close"
	case EventTypeFileOpen:
		return "file-open"
	case EventTypeTCPRetransmit:
		return "tcp-retransmit"
	case EventTypeL7Request:
		return "l7-request"
	case EventTypeFunctionEntry:
		return "function-entry"
	case EventTypeFunctionExit:
		return "function-exit"
	case EventTypeStackTrace:
		return "stack-trace"
	case EventTypeFunctionError:
		return "function-error"
	}
	return "unknown: " + strconv.Itoa(int(t))
}

func (t EventReason) String() string {
	switch t {
	case EventReasonNone:
		return "none"
	case EventReasonOOMKill:
		return "oom-kill"
	}
	return "unknown: " + strconv.Itoa(int(t))
}

func isCtxExtraPaddingRequired(traceFsPath string) bool {
	f, err := os.Open(path.Join(traceFsPath, "events/task/task_newtask/format"))
	if err != nil {
		klog.Errorln(err)
		return false
	}
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		klog.Errorln(err)
		return false
	}
	for _, line := range strings.Split(string(data), "\n") {
		if strings.Contains(line, "common_preempt_lazy_count") {
			return true
		}
	}
	return false
}

const nfConntrackEventsParameterPath = "/proc/sys/net/netfilter/nf_conntrack_events"

func ensureConntrackEventsAreEnabled() error {
	v, err := common.ReadUintFromFile(nfConntrackEventsParameterPath)
	if err != nil {
		if common.IsNotExist(err) {
			klog.Warningf(
				"unable to check the value of %s, it appears that nf_conntrack is not loaded: %s",
				nfConntrackEventsParameterPath, err)
			return nil
		}
		return err
	}
	if v != 1 {
		klog.Infof("%s = %d, setting to 1", nfConntrackEventsParameterPath, v)
		if err = os.WriteFile(nfConntrackEventsParameterPath, []byte("1"), 0644); err != nil {
			return err
		}
	}
	return nil
}

// eBPF program loading is now handled in init.go

func (t *Tracer) init(events chan<- Event) error {
	// Initialize process discovery and monitoring
	// This is simplified compared to the full coroot implementation

	// For function tracing, we don't need the complex initialization
	// that coroot does for network monitoring

	return nil
}
