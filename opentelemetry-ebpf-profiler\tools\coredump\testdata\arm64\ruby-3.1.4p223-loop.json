{"coredump-ref": "2bb49365d369e02150f55c39b178308c12a90c451dc8bcdcc396d313592a0428", "threads": [{"lwp": 40655, "frames": ["libruby.so.3.1.4+0x297464", "is_prime+0 in /pwd/testsources/ruby/loop.rb:12", "is_prime+0 in /pwd/testsources/ruby/loop.rb:10", "sum_of_primes+0 in /pwd/testsources/ruby/loop.rb:20", "<main>+0 in /pwd/testsources/ruby/loop.rb:30", "libruby.so.3.1.4+0x29b6ff", "libruby.so.3.1.4+0x2a018b", "libruby.so.3.1.4+0x1d0fbf", "libruby.so.3.1.4+0x287c87", "libruby.so.3.1.4+0x28d203", "libruby.so.3.1.4+0x299067", "<main>+0 in /pwd/testsources/ruby/loop.rb:29", "libruby.so.3.1.4+0x29be4b", "libruby.so.3.1.4+0x2a018b", "libruby.so.3.1.4+0x1d0d83", "libruby.so.3.1.4+0x287c87", "libruby.so.3.1.4+0x28d203", "libruby.so.3.1.4+0x299067", "<main>+0 in /pwd/testsources/ruby/loop.rb:28", "libruby.so.3.1.4+0x29b6ff", "libruby.so.3.1.4+0x29ca0b", "libruby.so.3.1.4+0xcfdbb", "libruby.so.3.1.4+0xcffef", "libruby.so.3.1.4+0x287c87", "libruby.so.3.1.4+0x28d203", "libruby.so.3.1.4+0x299067", "<main>+0 in /pwd/testsources/ruby/loop.rb:28", "libruby.so.3.1.4+0x29b6ff", "libruby.so.3.1.4+0xcc2f7", "libruby.so.3.1.4+0xd2577", "ruby+0xb8b", "libc-2.31.so+0x20e17", "ruby+0xbe7"]}], "modules": [{"ref": "91e420adcbcde0ecf27b779990044db04e1b7b3340a539ec9d672fa50b4acccb", "local-path": "/usr/local/bin/ruby"}, {"ref": "986f28fb12239163cc852423409e53460f15e750b179ebef75405a2584b8def5", "local-path": "/usr/local/lib/ruby/3.1.0/aarch64-linux/monitor.so"}, {"ref": "0095f138efb823d36176e0fa1763e045ed99da4c4351a67887ec1d2e5beb597f", "local-path": "/usr/local/lib/ruby/3.1.0/aarch64-linux/enc/trans/transdb.so"}, {"ref": "829c4db114763b5862fbb77ccb7eb03534abf2e04f7e482e15b3cc98a634534f", "local-path": "/usr/local/lib/ruby/3.1.0/aarch64-linux/enc/encdb.so"}, {"ref": "2044fcff94037c2c75cc5bc3aa42877bf2b41042608e011706b6c631232b0edd", "local-path": "/usr/lib/aarch64-linux-gnu/gconv/gconv-modules.cache"}, {"ref": "e19b13d271385be968316e97e08ef7a33d7702b8c370e93dbd9e7470933411c7", "local-path": "/usr/lib/locale/C.UTF-8/LC_CTYPE"}, {"ref": "22092712a6b333a8e31f69b39a8526472bbd005d5474cfab3b10101c49b5cb6f", "local-path": "/lib/aarch64-linux-gnu/libc-2.31.so"}, {"ref": "802de3cc336bf213c176cd845250a4d03146bc2e2acd5ea922fed79f2b6dafa0", "local-path": "/lib/aarch64-linux-gnu/libm-2.31.so"}, {"ref": "daec9eb17045e5dcb418926c75e7e0ddb94cc6694fe2a162ca990557571ab510", "local-path": "/lib/aarch64-linux-gnu/libcrypt.so.1.1.0"}, {"ref": "decf9ee3921f890abab2cc427f31a2eaa9d07970fdcd7f4771cb76b7b71b2b65", "local-path": "/lib/aarch64-linux-gnu/libdl-2.31.so"}, {"ref": "58e99cc018e8b3e5b5d15dc8a92751b9b885ce80afae47ff53c921f5c5f86458", "local-path": "/usr/lib/aarch64-linux-gnu/libgmp.so.10.4.1"}, {"ref": "31f866cf19794102cbccfe81c942ed90d9aa1e0886a87c2178b38219850c6c60", "local-path": "/lib/aarch64-linux-gnu/librt-2.31.so"}, {"ref": "6caaff1c1e689fda08ee4ca3fda2dc03f197bdec96f1deef9d826cceae4d426c", "local-path": "/lib/aarch64-linux-gnu/libpthread-2.31.so"}, {"ref": "7fb37d1568085015270b83b794d78bece261bdd617b664d2263780fd1093d5f4", "local-path": "/lib/aarch64-linux-gnu/libz.so.1.2.11"}, {"ref": "f3c230fc6ebc69b8b1f7317440f8429f94eb4f5e3ee3ca3503e6013ac96ea585", "local-path": "/usr/local/lib/libruby.so.3.1.4"}, {"ref": "8ba2fddf055362d1be174e622659698d082830c47f99e9cea20f74d641553d97", "local-path": "/lib/aarch64-linux-gnu/ld-2.31.so"}]}