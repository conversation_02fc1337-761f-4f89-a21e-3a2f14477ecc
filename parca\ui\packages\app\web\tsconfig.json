{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "strictNullChecks": false, "noImplicitAny": false, "downlevelIteration": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client", "vitest/globals"]}, "include": ["src", "../../shared/*"]}