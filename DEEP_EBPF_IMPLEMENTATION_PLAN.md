# Deep-eBPF Implementation Plan: Following Coroot Architecture

## 🎯 Executive Summary

Based on the failed attempts and time constraints, we will create a **working** deep-ebpf project by directly copying and adapting the proven coroot/coroot-node-agent architecture. This approach ensures we have a functional userland function tracer that actually works.

## 📋 Project Requirements Analysis

From `UNIVERSAL_EBPF_TRACER_GUIDE.md`, our client needs:
- ✅ **Function entry/exit tracing** with flexible configuration
- ✅ **Function arguments collection** with safe eBPF helpers  
- ✅ **Accurate latency measurement** with high precision timestamps
- ✅ **Call stack reconstruction** with stack trace IDs
- ✅ **Human-readable output** with function names, arguments, runtime info
- ✅ **Memory and register details** with pointer analysis
- ✅ **Advanced filtering and sampling** for production use

## 🏗️ Architecture: Two Repository Approach

### Repository 1: `deep-ebpf` (Main Server)
- **Purpose**: Central data collection and processing server
- **Based on**: `/coroot/` codebase architecture
- **Components**: API server, data storage, web UI, configuration management

### Repository 2: `deep-ebpf-agent` (Node Agent)  
- **Purpose**: eBPF-based userland function tracing agent
- **Based on**: `/coroot-node-agent/` codebase architecture
- **Components**: eBPF tracer, event processing, data export

## 📁 Project Structure

### deep-ebpf/ (Main Server)
```
deep-ebpf/
├── main.go                    # Main server entry point (from coroot/main.go)
├── api/                       # REST API handlers (from coroot/api/)
├── collector/                 # Data collection from agents (from coroot/collector/)
├── db/                        # Database layer (from coroot/db/)
├── cache/                     # Caching layer (from coroot/cache/)
├── config/                    # Configuration management (from coroot/config/)
├── watchers/                  # Background watchers (from coroot/watchers/)
├── static/                    # Web UI assets (from coroot/static/)
└── go.mod                     # Go module definition
```

### deep-ebpf-agent/ (Node Agent)
```
deep-ebpf-agent/
├── main.go                    # Agent entry point (from coroot-node-agent/main.go)
├── ebpftracer/               # eBPF tracer implementation (from coroot-node-agent/ebpftracer/)
│   ├── tracer.go             # Main tracer logic
│   ├── init.go               # Initialization logic
│   ├── python.go             # Python-specific tracing
│   ├── ebpf/                 # eBPF C programs
│   │   └── ebpf.c            # Main eBPF program
│   └── Dockerfile            # eBPF compilation container
├── tracing/                  # OpenTelemetry tracing (from coroot-node-agent/tracing/)
├── proc/                     # Process utilities (from coroot-node-agent/proc/)
├── common/                   # Common utilities (from coroot-node-agent/common/)
├── containers/               # Container management (from coroot-node-agent/containers/)
├── flags/                    # Command-line flags (from coroot-node-agent/flags/)
└── go.mod                    # Go module definition
```

## 🔧 Implementation Strategy

### Phase 1: Copy and Adapt Core Architecture (Week 1)

#### Step 1.1: Create deep-ebpf Server
```bash
# Copy coroot codebase structure
cp -r coroot/ deep-ebpf/
cd deep-ebpf/

# Adapt main.go for function tracing focus
# Modify API endpoints for function trace data
# Update configuration for deep-ebpf specific settings
```

#### Step 1.2: Create deep-ebpf-agent
```bash  
# Copy coroot-node-agent codebase structure
cp -r coroot-node-agent/ deep-ebpf-agent/
cd deep-ebpf-agent/

# Focus on ebpftracer/ package
# Adapt eBPF programs for userland function tracing
# Modify event processing for function call events
```

#### Step 1.3: Adapt eBPF Programs
- **Source**: `coroot-node-agent/ebpftracer/ebpf/ebpf.c`
- **Target**: `deep-ebpf-agent/ebpftracer/ebpf/ebpf.c`
- **Changes**: 
  - Add uprobe attachment for userland functions
  - Implement function argument capture
  - Add stack trace collection
  - Implement duration measurement

### Phase 2: Function Tracing Implementation (Week 2)

#### Step 2.1: eBPF Kernel Programs
Based on `coroot-node-agent/ebpftracer/ebpf/ebpf.c`:

```c
// Add to existing eBPF program
SEC("uprobe/function_entry")
int trace_function_entry(struct pt_regs *ctx) {
    // Extract function arguments from registers
    // Store entry timestamp
    // Submit entry event
}

SEC("uretprobe/function_exit")  
int trace_function_exit(struct pt_regs *ctx) {
    // Calculate duration
    // Extract return value
    // Submit exit event
}
```

#### Step 2.2: Go Event Processing
Based on `coroot-node-agent/ebpftracer/tracer.go`:

```go
type FunctionEvent struct {
    Type          EventType
    Pid           uint32
    FunctionAddr  uint64
    FunctionName  string
    Args          []uint64
    ReturnValue   uint64
    Duration      time.Duration
    StackTrace    []uint64
    Timestamp     uint64
}
```

#### Step 2.3: Uprobe Attachment
Based on `coroot-node-agent/ebpftracer/python.go` pattern:

```go
func (t *Tracer) AttachUserFunctionProbes(pid uint32, binaryPath string) []link.Link {
    // Parse binary for function symbols
    // Attach uprobes to function entries
    // Attach uretprobes to function exits
    // Return link handles
}
```

### Phase 3: Data Export and Processing (Week 3)

#### Step 3.1: Event Collection
Based on `coroot/collector/collector.go`:

```go
type FunctionTraceCollector struct {
    // Collect function trace events from agents
    // Process and correlate function calls
    // Store in database/cache
}
```

#### Step 3.2: API Endpoints
Based on `coroot/api/` structure:

```go
// Add to API router
router.HandleFunc("/v1/function-traces", collector.FunctionTraces)
router.HandleFunc("/v1/function-stats", collector.FunctionStats)
```

#### Step 3.3: Human-Readable Output
Based on client requirements from `UNIVERSAL_EBPF_TRACER_GUIDE.md`:

```go
func FormatFunctionTrace(event *FunctionEvent) string {
    return fmt.Sprintf(`
┌─ [%s] %s:%d/%d on CPU %d [%s]
├─ Function: %s [%s]
├─ Address:  0x%x
├─ Duration: %s
├─ Arguments:
│  ├─ [0] arg0=0x%x (RDI)
│  ├─ [1] arg1=0x%x (RSI)
│  └─ ...
├─ Stack ID: %d (call stack available)
└─────────────────────────────────────────────────────────────────────────────────`,
        timestamp, processName, pid, tid, cpu, eventType,
        functionName, space, address, duration, args...)
}
```

## 🚀 Key Implementation Files

### 1. deep-ebpf-agent/main.go
```go
// Copy from coroot-node-agent/main.go
// Add function tracing initialization
func main() {
    // ... existing coroot-node-agent setup ...
    
    // Initialize function tracer
    functionTracer := ebpftracer.NewFunctionTracer()
    go functionTracer.Run(functionEvents)
    
    // Process function events
    go processFunctionEvents(functionEvents)
}
```

### 2. deep-ebpf-agent/ebpftracer/function_tracer.go
```go
// New file based on tracer.go pattern
type FunctionTracer struct {
    collection *ebpf.Collection
    readers    map[string]*perf.Reader
    links      []link.Link
    uprobes    map[string]*ebpf.Program
}

func (ft *FunctionTracer) AttachToProcess(pid uint32) error {
    // Attach uprobes to process functions
    // Based on coroot-node-agent uprobe attachment patterns
}
```

### 3. deep-ebpf-agent/ebpftracer/ebpf/function_ebpf.c
```c
// Add to existing ebpf.c file
// Based on coroot-node-agent eBPF program structure

struct function_event {
    u64 timestamp;
    u32 pid;
    u32 tid;
    u64 function_addr;
    u64 args[6];  // RDI, RSI, RDX, RCX, R8, R9
    u64 duration;
    u32 stack_id;
};

SEC("uprobe/generic_function")
int trace_generic_function(struct pt_regs *ctx) {
    // Function entry tracing logic
}
```

### 4. deep-ebpf/collector/function_collector.go
```go
// New file based on coroot/collector/collector.go
type FunctionCollector struct {
    // Collect function traces from agents
    // Process and store function call data
}

func (fc *FunctionCollector) FunctionTraces(w http.ResponseWriter, r *http.Request) {
    // Handle function trace data from agents
}
```

## 📊 Expected Output Format

Based on `UNIVERSAL_EBPF_TRACER_GUIDE.md` requirements:

```
┌─ [16:12:25.831726] myapp:947/947 on CPU 7 [entry]
├─ Function: fibonacci [user]
├─ Address:  0x401234
├─ Duration: 29.943µs
├─ Arguments:
│  ├─ [0] arg0=10 (RDI)
│  ├─ [1] arg1=0x7fff12345678 (RSI)
├─ Memory:
│  ├─ Ptr[1]: 0x7fff12345678 (user space)
├─ Stack ID: 17 (call stack available)
└─────────────────────────────────────────────────────────────────────────────────
```

## 🔨 Build System

### deep-ebpf-agent/Makefile
```makefile
# Based on coroot-node-agent build system
.PHONY: ebpf agent

ebpf:
	docker build -f ebpftracer/Dockerfile -t deep-ebpf-builder .
	docker run --rm -v $(PWD):/src deep-ebpf-builder

agent:
	go build -o deep-ebpf-agent ./main.go

all: ebpf agent
```

### deep-ebpf/Makefile  
```makefile
# Based on coroot build system
.PHONY: server

server:
	go build -o deep-ebpf ./main.go

all: server
```

## ✅ Success Criteria

1. **Working uprobe attachment** - No more attachment failures
2. **Actual function tracing** - Captures real userland function calls
3. **Human-readable output** - Matches client requirements exactly
4. **Multi-language support** - Works with Go, C, Python, Java
5. **Production ready** - Filtering, sampling, error handling

## 🎯 Timeline

- **Week 1**: Copy and adapt core architecture
- **Week 2**: Implement function tracing with uprobes  
- **Week 3**: Data processing and human-readable output
- **Week 4**: Testing, validation, and client demo

## 🔧 Risk Mitigation

1. **Use proven codebase** - coroot/coroot-node-agent is battle-tested
2. **Minimal changes initially** - Copy first, adapt incrementally
3. **Focus on working solution** - Function over form initially
4. **Incremental testing** - Test each component as we build

This plan ensures we deliver a **working** deep-ebpf system that actually traces userland functions by leveraging the proven coroot architecture.

## 🚨 Critical Implementation Notes

### Why This Approach Will Work

1. **Proven eBPF Implementation**: coroot-node-agent successfully attaches uprobes and traces userland functions
2. **Working Event Processing**: The event pipeline in coroot-node-agent handles high-volume eBPF events
3. **Tested Build System**: The Docker-based eBPF compilation in coroot-node-agent works across kernel versions
4. **Production Ready**: coroot/coroot-node-agent is used in production environments

### Key Files to Copy Exactly

1. **coroot-node-agent/ebpftracer/tracer.go** - Main tracer implementation
2. **coroot-node-agent/ebpftracer/ebpf/ebpf.c** - eBPF kernel programs
3. **coroot-node-agent/ebpftracer/Dockerfile** - eBPF compilation setup
4. **coroot-node-agent/main.go** - Agent initialization and setup
5. **coroot/collector/collector.go** - Data collection and processing

### Immediate Next Steps

1. **Create repositories**: `deep-ebpf` and `deep-ebpf-agent`
2. **Copy coroot structure**: Start with exact copies, then adapt
3. **Focus on uprobe attachment**: This is where previous attempts failed
4. **Test with simple functions**: Start with basic C functions before complex scenarios
5. **Validate output format**: Ensure it matches client requirements exactly

### Success Metrics

- [ ] eBPF programs compile without errors
- [ ] Uprobes attach successfully to target processes
- [ ] Function entry/exit events are captured
- [ ] Arguments and return values are extracted
- [ ] Human-readable output matches client format
- [ ] Works across multiple programming languages
- [ ] Handles production workloads with filtering/sampling

This approach prioritizes **working functionality** over perfect architecture, ensuring we deliver a solution that actually traces userland functions as required.
