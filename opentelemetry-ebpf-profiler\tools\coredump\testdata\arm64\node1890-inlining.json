{"coredump-ref": "bdb7d1d2476f406703deb2399a8d3771a7bc38f272217b02cc0fac7efc5cd4cc", "threads": [{"lwp": 8043, "frames": ["libc.so.6+0xd7d14", "node+0x15ce15f", "node+0xc2cf5b", "node+0xc2969f", "node+0xc297ff", "V8::ExitFrame+0 in :0", "handleWriteReq+16 in node:internal/stream_base_commons:61", "writeGeneric+2 in node:internal/stream_base_commons:149", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+24 in node:internal/streams/writable:392", "_write+47 in node:internal/streams/writable:333", "Writable.write+1 in node:internal/streams/writable:337", "value+28 in node:internal/console/constructor:299", "warn+1 in node:internal/console/constructor:381", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe625a7", "node+0xe634d7", "node+0xd3a70f", "node+0xc58973", "V8::ExitFrame+0 in :0", "trace+6 in node:internal/console/constructor:427", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe625a7", "node+0xe634d7", "node+0xd3a70f", "node+0xc58973", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:3", "add3+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:8", "test+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:12", "submain+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:17", "main+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:23", "<anonymous>+26 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1119", "Module._extensions..js+45 in node:internal/modules/cjs/loader:1173", "Module.load+12 in node:internal/modules/cjs/loader:997", "Module._load+68 in node:internal/modules/cjs/loader:838", "executeUserEntryPoint+0 in node:internal/modules/run_main:0", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe625a7", "node+0xe634d7", "node+0xd3a70f", "node+0xb1dc8b", "node+0xb1dd57", "node+0xb1f17b", "node+0xab0623", "node+0xb9a04f", "node+0xb1e767", "node+0xb219ab", "libc.so.6+0x273fb", "libc.so.6+0x274cb", "node+0xaab7df"]}, {"lwp": 8044, "frames": ["libc.so.6+0xe5f3c", "node+0x15d4a13", "node+0x15c30c7", "node+0xbc168b", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8045, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x15d120f", "node+0xbbc81f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8046, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x15d120f", "node+0xbbc81f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8047, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x15d120f", "node+0xbbc81f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8048, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x15d120f", "node+0xbbc81f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8049, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x85a5b", "node+0x15d0fbf", "node+0xc4ac7b", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}], "modules": [{"ref": "f1935c0616a48e7ec471c26886df0411beffd4e56c92539d9a6a94641950badf", "local-path": "/usr/lib/aarch64-linux-gnu/libdl.so.2"}, {"ref": "fd04b635d29b5cb3faaf502a6c5cd68a623d66a736f4d0561ff280c2fa411c79", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "c4834bd79254443665af96b6d5e71d124b7f92f2eea121e61f9fff5204fc594d", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30"}, {"ref": "22a0986a1047cd3c9a55368fdc6bb6e5a4455aceb53ec15dbe19112d95583642", "local-path": "/usr/lib/aarch64-linux-gnu/libpthread.so.0"}, {"ref": "bdcbe7184d2eee921b3e0b0a717e313190ec7fba7099145d119c1a74b975c8df", "local-path": "/home/<USER>/.nvm/versions/node/v18.9.0/bin/node"}, {"ref": "594545a9720b4a16973a823d18c71fbf070d3c07fb17b01df5376273e91644a1", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "e647ee3042517f06cbebbbf0f66ba25486d0722222404f1d067683767a055566", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "acfe23cf0f5f4ac35b4c314415bb31d6f52a9bd39df8be04dfb2818a571533fd", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}]}