[package]
name = "symblib"
edition = "2021"
version.workspace = true
rust-version.workspace = true
license.workspace = true

[dependencies]
base64.workspace = true
cpp_demangle.workspace = true
fallible-iterator.workspace = true
flate2.workspace = true
gimli.workspace = true
intervaltree.workspace = true
lru.workspace = true
memmap2.workspace = true
object.workspace = true
prost.workspace = true
rustc-demangle.workspace = true
sha2.workspace = true
smallvec.workspace = true
tempfile.workspace = true
thiserror.workspace = true
zstd.workspace = true
zydis.workspace = true

[build-dependencies]
prost-build.workspace = true
