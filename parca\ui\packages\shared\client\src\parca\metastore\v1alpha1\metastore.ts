// @generated by protobuf-ts 2.9.6 with parameter generate_dependencies
// @generated from protobuf file "parca/metastore/v1alpha1/metastore.proto" (package "parca.metastore.v1alpha1", syntax proto3)
// tslint:disable
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { Unknown<PERSON>ield<PERSON>and<PERSON> } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
/**
 * GetOrCreateMappingsRequest contains all information about mappings that are
 * requested to be retrieved or created if they don't already exist.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateMappingsRequest
 */
export interface GetOrCreateMappingsRequest {
    /**
     * Mappings to be created or retrieved.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Mapping mappings = 1;
     */
    mappings: Mapping[];
}
/**
 * GetOrCreateMappingsResponse contains information about mappings requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateMappingsResponse
 */
export interface GetOrCreateMappingsResponse {
    /**
     * Mappings that are known to the backing metastore. If any mappings didn't
     * exist before the request they have now been persisted and are uniquely
     * identifyable through their key.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Mapping mappings = 1;
     */
    mappings: Mapping[];
}
/**
 * GetOrCreateFunctionsRequest contains all information about functions that are
 * requested to be retrieved or created if they don't already exist.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateFunctionsRequest
 */
export interface GetOrCreateFunctionsRequest {
    /**
     * Functions to be created or retrieved.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Function functions = 1;
     */
    functions: Function[];
}
/**
 * GetOrCreateFunctionsResponse contains information about functions requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateFunctionsResponse
 */
export interface GetOrCreateFunctionsResponse {
    /**
     * Functions that are known to the backing metastore. If any functions didn't
     * exist before the request they have now been persisted and are uniquely
     * identifyable through their key.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Function functions = 1;
     */
    functions: Function[];
}
/**
 * GetOrCreateLocationsRequest contains all information about locations that are
 * requested to be retrieved or created if they don't already exist.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateLocationsRequest
 */
export interface GetOrCreateLocationsRequest {
    /**
     * Locations to be created or retrieved.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Location locations = 1;
     */
    locations: Location[];
}
/**
 * GetOrCreateLocationsResponse contains information about locations requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateLocationsResponse
 */
export interface GetOrCreateLocationsResponse {
    /**
     * Locations that are known to the backing metastore. If any locations didn't
     * exist before the request they have now been persisted and are uniquely
     * identifyable through their key.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Location locations = 1;
     */
    locations: Location[];
}
/**
 * GetOrCreateStracktracesRequest contains all information about stacktraces
 * that are requested to be retrieved or created if they don't already exist.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateStacktracesRequest
 */
export interface GetOrCreateStacktracesRequest {
    /**
     * Stacktraces to be created or retrieved.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Stacktrace stacktraces = 1;
     */
    stacktraces: Stacktrace[];
}
/**
 * GetOrCreateStacktracesResponse contains information about locations requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.GetOrCreateStacktracesResponse
 */
export interface GetOrCreateStacktracesResponse {
    /**
     * Stacktraces that are known to the backing metastore. If any stacktraces
     * didn't exist before the request they have now been persisted and are
     * uniquely identifyable through their key.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Stacktrace stacktraces = 1;
     */
    stacktraces: Stacktrace[];
}
/**
 * UnsymbolizedLocationsRequest contains information about the unsymbolized
 * locations requested. While currently empty, this could in the future contain
 * a sharding configuration or limit the number of locations to return.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.UnsymbolizedLocationsRequest
 */
export interface UnsymbolizedLocationsRequest {
    /**
     * The maximum number of locations to return.
     *
     * @generated from protobuf field: uint32 limit = 1;
     */
    limit: number;
    /**
     * The minimum key to start returning locations from.
     *
     * @generated from protobuf field: string min_key = 2;
     */
    minKey: string;
}
/**
 * UnsymbolizedLocationsResponse contains information about the requested
 * locations that should be symbolizable but potentially haven't been
 * symbolized yet.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.UnsymbolizedLocationsResponse
 */
export interface UnsymbolizedLocationsResponse {
    /**
     * Locations that have a mapping and address that should be symbolizable.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Location locations = 1;
     */
    locations: Location[];
    /**
     * Key of the last location returned. This can be used in a subsequent call
     * to UnsymbolizedLocations to continue from the last returned location.
     *
     * @generated from protobuf field: string max_key = 2;
     */
    maxKey: string;
}
/**
 * CreateLocationLinesRequest contains locations and their location lines to be
 * saved.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.CreateLocationLinesRequest
 */
export interface CreateLocationLinesRequest {
    /**
     * Locations that have location lines to be saved.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Location locations = 1;
     */
    locations: Location[];
}
/**
 * CreateLocationLinesResponse details about the location lines creation.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.CreateLocationLinesResponse
 */
export interface CreateLocationLinesResponse {
}
/**
 * StacktracesRequest contains information about the stacktraces requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.StacktracesRequest
 */
export interface StacktracesRequest {
    /**
     * IDs of stacktraces to retrieve.
     *
     * @generated from protobuf field: repeated string stacktrace_ids = 1;
     */
    stacktraceIds: string[];
}
/**
 * StacktracesRequest contains the requested stacktraces.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.StacktracesResponse
 */
export interface StacktracesResponse {
    /**
     * Stacktraces that are known to the backing metastore.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Stacktrace stacktraces = 1;
     */
    stacktraces: Stacktrace[];
}
/**
 * LocationsRequest contains information about the locations requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.LocationsRequest
 */
export interface LocationsRequest {
    /**
     * IDs of locations to retrieve.
     *
     * @generated from protobuf field: repeated string location_ids = 1;
     */
    locationIds: string[];
}
/**
 * LocationsResponse contains the requested locations.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.LocationsResponse
 */
export interface LocationsResponse {
    /**
     * Locations that are known to the backing metastore.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Location locations = 1;
     */
    locations: Location[];
}
/**
 * LocationLinesRequest contains information about the location's lines requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.LocationLinesRequest
 */
export interface LocationLinesRequest {
    /**
     * IDs of locations to retrieve location lines for.
     *
     * @generated from protobuf field: repeated string location_ids = 1;
     */
    locationIds: string[];
}
/**
 * FunctionsRequest contains information about the functions requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.FunctionsRequest
 */
export interface FunctionsRequest {
    /**
     * IDs of functions to retrieve.
     *
     * @generated from protobuf field: repeated string function_ids = 1;
     */
    functionIds: string[];
}
/**
 * FunctionsResponse contains the requested functions.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.FunctionsResponse
 */
export interface FunctionsResponse {
    /**
     * Functions that are known to the backing metastore.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Function functions = 1;
     */
    functions: Function[];
}
/**
 * MappingsRequest contains information about the mappings requested.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.MappingsRequest
 */
export interface MappingsRequest {
    /**
     * IDs of mappings to retrieve.
     *
     * @generated from protobuf field: repeated string mapping_ids = 1;
     */
    mappingIds: string[];
}
/**
 * MappingsResponse contains the requested mappings.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.MappingsResponse
 */
export interface MappingsResponse {
    /**
     * Mappings that are known to the backing metastore.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Mapping mappings = 1;
     */
    mappings: Mapping[];
}
/**
 * Sample is a stack trace with optional labels.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.Sample
 */
export interface Sample {
    /**
     * stacktrace_id references stack trace of the sample.
     *
     * @generated from protobuf field: string stacktrace_id = 1;
     */
    stacktraceId: string;
    /**
     * labels are extra labels for a stack trace.
     *
     * @generated from protobuf field: map<string, parca.metastore.v1alpha1.SampleLabel> labels = 2;
     */
    labels: {
        [key: string]: SampleLabel;
    };
    /**
     * num_labels are the num of labels.
     *
     * @generated from protobuf field: map<string, parca.metastore.v1alpha1.SampleNumLabel> num_labels = 3;
     */
    numLabels: {
        [key: string]: SampleNumLabel;
    };
    /**
     * num_units are the units for the labels.
     *
     * @generated from protobuf field: map<string, parca.metastore.v1alpha1.SampleNumUnit> num_units = 4;
     */
    numUnits: {
        [key: string]: SampleNumUnit;
    };
}
/**
 * Stacktrace is a collection of locations.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.Stacktrace
 */
export interface Stacktrace {
    /**
     * stacktrace_id references stack trace of the stacktrace.
     *
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * locations are the locations in the stack trace.
     *
     * @generated from protobuf field: repeated string location_ids = 2;
     */
    locationIds: string[];
}
/**
 * SampleLabel are the labels added to a Sample.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.SampleLabel
 */
export interface SampleLabel {
    /**
     * labels for a label in a Sample.
     *
     * @generated from protobuf field: repeated string labels = 1;
     */
    labels: string[];
}
/**
 * SampleNumLabel are the num of labels of a Sample.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.SampleNumLabel
 */
export interface SampleNumLabel {
    /**
     * num_labels are the num_label of a Sample.
     *
     * @generated from protobuf field: repeated int64 num_labels = 1;
     */
    numLabels: bigint[];
}
/**
 * SampleNumUnit are the num units of a Sample.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.SampleNumUnit
 */
export interface SampleNumUnit {
    /**
     * units of a labels of a Sample.
     *
     * @generated from protobuf field: repeated string units = 1;
     */
    units: string[];
}
/**
 * Location describes a single location of a stack traces.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.Location
 */
export interface Location {
    /**
     * id is the unique identifier for the location.
     *
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * address is the memory address of the location if present.
     *
     * @generated from protobuf field: uint64 address = 2;
     */
    address: bigint;
    /**
     * mapping_id is the unique identifier for the mapping associated with the location.
     *
     * @generated from protobuf field: string mapping_id = 4;
     */
    mappingId: string;
    /**
     * is_folded indicates whether the location is folded into the previous location.
     *
     * @generated from protobuf field: bool is_folded = 5;
     */
    isFolded: boolean;
    /**
     * lines are the call frames represented by this location. Multiple lines
     * indicate they have been inlined.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Line lines = 6;
     */
    lines: Line[];
    /**
     * mapping_index has the index into the mapping table where mappings are sent deduplicated.
     *
     * @generated from protobuf field: uint32 mapping_index = 7;
     */
    mappingIndex: number;
}
/**
 * Line describes a source code function and its line number.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.Line
 */
export interface Line {
    /**
     * function_id is the ID of the function.
     *
     * @generated from protobuf field: string function_id = 1;
     */
    functionId: string;
    /**
     * line is the line number in the source file of the referenced function.
     *
     * @generated from protobuf field: int64 line = 2;
     */
    line: bigint;
    /**
     * function_index is the index in the functions table.
     *
     * @generated from protobuf field: uint32 function_index = 3;
     */
    functionIndex: number;
}
/**
 * Function describes metadata of a source code function.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.Function
 */
export interface Function {
    /**
     * id is the unique identifier for the function.
     *
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * start_line is the line number in the source file of the first line of the function.
     *
     * @generated from protobuf field: int64 start_line = 2;
     */
    startLine: bigint;
    /**
     * name is the name of the function.
     *
     * @generated from protobuf field: string name = 3;
     */
    name: string;
    /**
     * system_name describes the name of the function, as identified by the
     * system. For instance, it can be a C++ mangled name.
     *
     * @generated from protobuf field: string system_name = 4;
     */
    systemName: string;
    /**
     * filename is the name of the source file of the function.
     *
     * @generated from protobuf field: string filename = 5;
     */
    filename: string;
    /**
     * name_string_index is the index in the string table to the name associated with the function.
     *
     * @generated from protobuf field: uint32 name_string_index = 6;
     */
    nameStringIndex: number;
    /**
     * system_name_string_index is the index in the string table to the system_name associated with the function.
     *
     * @generated from protobuf field: uint32 system_name_string_index = 7;
     */
    systemNameStringIndex: number;
    /**
     * filename_string_index is the index in the string table to the filename associated with the function.
     *
     * @generated from protobuf field: uint32 filename_string_index = 8;
     */
    filenameStringIndex: number;
}
/**
 * Mapping describes a memory mapping.
 *
 * @generated from protobuf message parca.metastore.v1alpha1.Mapping
 */
export interface Mapping {
    /**
     * id is the unique identifier for the mapping.
     *
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * start is the start address of the mapping.
     *
     * @generated from protobuf field: uint64 start = 2;
     */
    start: bigint;
    /**
     * limit is the length of the address space of the mapping.
     *
     * @generated from protobuf field: uint64 limit = 3;
     */
    limit: bigint;
    /**
     * offset in the binary that corresponds to the first mapped address.
     *
     * @generated from protobuf field: uint64 offset = 4;
     */
    offset: bigint;
    /**
     * file is the name of the file associated with the mapping.
     *
     * @generated from protobuf field: string file = 5;
     */
    file: string;
    /**
     * build_id is the build ID of the mapping.
     *
     * @generated from protobuf field: string build_id = 6;
     */
    buildId: string;
    /**
     * has_functions indicates whether the mapping has associated functions.
     *
     * @generated from protobuf field: bool has_functions = 7;
     */
    hasFunctions: boolean;
    /**
     * has_filenames indicates whether the mapping has associated filenames.
     *
     * @generated from protobuf field: bool has_filenames = 8;
     */
    hasFilenames: boolean;
    /**
     * has_line_numbers indicates whether the mapping has associated line numbers.
     *
     * @generated from protobuf field: bool has_line_numbers = 9;
     */
    hasLineNumbers: boolean;
    /**
     * has_inline_frames indicates whether the mapping has associated inline frames.
     *
     * @generated from protobuf field: bool has_inline_frames = 10;
     */
    hasInlineFrames: boolean;
    /**
     * fileStringIndex is the index in the string table to the file name associated with the mapping.
     *
     * @generated from protobuf field: uint32 file_string_index = 11;
     */
    fileStringIndex: number;
    /**
     * build_id_string_index is the index in the string table to the build ID of the mapping.
     *
     * @generated from protobuf field: uint32 build_id_string_index = 12;
     */
    buildIdStringIndex: number;
}
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateMappingsRequest$Type extends MessageType<GetOrCreateMappingsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateMappingsRequest", [
            { no: 1, name: "mappings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Mapping }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateMappingsRequest>): GetOrCreateMappingsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mappings = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateMappingsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateMappingsRequest): GetOrCreateMappingsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Mapping mappings */ 1:
                    message.mappings.push(Mapping.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateMappingsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Mapping mappings = 1; */
        for (let i = 0; i < message.mappings.length; i++)
            Mapping.internalBinaryWrite(message.mappings[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateMappingsRequest
 */
export const GetOrCreateMappingsRequest = new GetOrCreateMappingsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateMappingsResponse$Type extends MessageType<GetOrCreateMappingsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateMappingsResponse", [
            { no: 1, name: "mappings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Mapping }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateMappingsResponse>): GetOrCreateMappingsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mappings = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateMappingsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateMappingsResponse): GetOrCreateMappingsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Mapping mappings */ 1:
                    message.mappings.push(Mapping.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateMappingsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Mapping mappings = 1; */
        for (let i = 0; i < message.mappings.length; i++)
            Mapping.internalBinaryWrite(message.mappings[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateMappingsResponse
 */
export const GetOrCreateMappingsResponse = new GetOrCreateMappingsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateFunctionsRequest$Type extends MessageType<GetOrCreateFunctionsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateFunctionsRequest", [
            { no: 1, name: "functions", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Function }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateFunctionsRequest>): GetOrCreateFunctionsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functions = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateFunctionsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateFunctionsRequest): GetOrCreateFunctionsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Function functions */ 1:
                    message.functions.push(Function.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateFunctionsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Function functions = 1; */
        for (let i = 0; i < message.functions.length; i++)
            Function.internalBinaryWrite(message.functions[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateFunctionsRequest
 */
export const GetOrCreateFunctionsRequest = new GetOrCreateFunctionsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateFunctionsResponse$Type extends MessageType<GetOrCreateFunctionsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateFunctionsResponse", [
            { no: 1, name: "functions", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Function }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateFunctionsResponse>): GetOrCreateFunctionsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functions = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateFunctionsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateFunctionsResponse): GetOrCreateFunctionsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Function functions */ 1:
                    message.functions.push(Function.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateFunctionsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Function functions = 1; */
        for (let i = 0; i < message.functions.length; i++)
            Function.internalBinaryWrite(message.functions[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateFunctionsResponse
 */
export const GetOrCreateFunctionsResponse = new GetOrCreateFunctionsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateLocationsRequest$Type extends MessageType<GetOrCreateLocationsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateLocationsRequest", [
            { no: 1, name: "locations", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateLocationsRequest>): GetOrCreateLocationsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locations = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateLocationsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateLocationsRequest): GetOrCreateLocationsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Location locations */ 1:
                    message.locations.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateLocationsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Location locations = 1; */
        for (let i = 0; i < message.locations.length; i++)
            Location.internalBinaryWrite(message.locations[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateLocationsRequest
 */
export const GetOrCreateLocationsRequest = new GetOrCreateLocationsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateLocationsResponse$Type extends MessageType<GetOrCreateLocationsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateLocationsResponse", [
            { no: 1, name: "locations", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateLocationsResponse>): GetOrCreateLocationsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locations = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateLocationsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateLocationsResponse): GetOrCreateLocationsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Location locations */ 1:
                    message.locations.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateLocationsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Location locations = 1; */
        for (let i = 0; i < message.locations.length; i++)
            Location.internalBinaryWrite(message.locations[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateLocationsResponse
 */
export const GetOrCreateLocationsResponse = new GetOrCreateLocationsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateStacktracesRequest$Type extends MessageType<GetOrCreateStacktracesRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateStacktracesRequest", [
            { no: 1, name: "stacktraces", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Stacktrace }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateStacktracesRequest>): GetOrCreateStacktracesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.stacktraces = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateStacktracesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateStacktracesRequest): GetOrCreateStacktracesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Stacktrace stacktraces */ 1:
                    message.stacktraces.push(Stacktrace.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateStacktracesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Stacktrace stacktraces = 1; */
        for (let i = 0; i < message.stacktraces.length; i++)
            Stacktrace.internalBinaryWrite(message.stacktraces[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateStacktracesRequest
 */
export const GetOrCreateStacktracesRequest = new GetOrCreateStacktracesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetOrCreateStacktracesResponse$Type extends MessageType<GetOrCreateStacktracesResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.GetOrCreateStacktracesResponse", [
            { no: 1, name: "stacktraces", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Stacktrace }
        ]);
    }
    create(value?: PartialMessage<GetOrCreateStacktracesResponse>): GetOrCreateStacktracesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.stacktraces = [];
        if (value !== undefined)
            reflectionMergePartial<GetOrCreateStacktracesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetOrCreateStacktracesResponse): GetOrCreateStacktracesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Stacktrace stacktraces */ 1:
                    message.stacktraces.push(Stacktrace.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetOrCreateStacktracesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Stacktrace stacktraces = 1; */
        for (let i = 0; i < message.stacktraces.length; i++)
            Stacktrace.internalBinaryWrite(message.stacktraces[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.GetOrCreateStacktracesResponse
 */
export const GetOrCreateStacktracesResponse = new GetOrCreateStacktracesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class UnsymbolizedLocationsRequest$Type extends MessageType<UnsymbolizedLocationsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.UnsymbolizedLocationsRequest", [
            { no: 1, name: "limit", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 2, name: "min_key", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<UnsymbolizedLocationsRequest>): UnsymbolizedLocationsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.limit = 0;
        message.minKey = "";
        if (value !== undefined)
            reflectionMergePartial<UnsymbolizedLocationsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: UnsymbolizedLocationsRequest): UnsymbolizedLocationsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* uint32 limit */ 1:
                    message.limit = reader.uint32();
                    break;
                case /* string min_key */ 2:
                    message.minKey = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: UnsymbolizedLocationsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* uint32 limit = 1; */
        if (message.limit !== 0)
            writer.tag(1, WireType.Varint).uint32(message.limit);
        /* string min_key = 2; */
        if (message.minKey !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.minKey);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.UnsymbolizedLocationsRequest
 */
export const UnsymbolizedLocationsRequest = new UnsymbolizedLocationsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class UnsymbolizedLocationsResponse$Type extends MessageType<UnsymbolizedLocationsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.UnsymbolizedLocationsResponse", [
            { no: 1, name: "locations", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location },
            { no: 2, name: "max_key", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<UnsymbolizedLocationsResponse>): UnsymbolizedLocationsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locations = [];
        message.maxKey = "";
        if (value !== undefined)
            reflectionMergePartial<UnsymbolizedLocationsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: UnsymbolizedLocationsResponse): UnsymbolizedLocationsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Location locations */ 1:
                    message.locations.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* string max_key */ 2:
                    message.maxKey = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: UnsymbolizedLocationsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Location locations = 1; */
        for (let i = 0; i < message.locations.length; i++)
            Location.internalBinaryWrite(message.locations[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string max_key = 2; */
        if (message.maxKey !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.maxKey);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.UnsymbolizedLocationsResponse
 */
export const UnsymbolizedLocationsResponse = new UnsymbolizedLocationsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CreateLocationLinesRequest$Type extends MessageType<CreateLocationLinesRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.CreateLocationLinesRequest", [
            { no: 1, name: "locations", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location }
        ]);
    }
    create(value?: PartialMessage<CreateLocationLinesRequest>): CreateLocationLinesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locations = [];
        if (value !== undefined)
            reflectionMergePartial<CreateLocationLinesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CreateLocationLinesRequest): CreateLocationLinesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Location locations */ 1:
                    message.locations.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CreateLocationLinesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Location locations = 1; */
        for (let i = 0; i < message.locations.length; i++)
            Location.internalBinaryWrite(message.locations[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.CreateLocationLinesRequest
 */
export const CreateLocationLinesRequest = new CreateLocationLinesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CreateLocationLinesResponse$Type extends MessageType<CreateLocationLinesResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.CreateLocationLinesResponse", []);
    }
    create(value?: PartialMessage<CreateLocationLinesResponse>): CreateLocationLinesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<CreateLocationLinesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CreateLocationLinesResponse): CreateLocationLinesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CreateLocationLinesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.CreateLocationLinesResponse
 */
export const CreateLocationLinesResponse = new CreateLocationLinesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class StacktracesRequest$Type extends MessageType<StacktracesRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.StacktracesRequest", [
            { no: 1, name: "stacktrace_ids", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<StacktracesRequest>): StacktracesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.stacktraceIds = [];
        if (value !== undefined)
            reflectionMergePartial<StacktracesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: StacktracesRequest): StacktracesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string stacktrace_ids */ 1:
                    message.stacktraceIds.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: StacktracesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string stacktrace_ids = 1; */
        for (let i = 0; i < message.stacktraceIds.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.stacktraceIds[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.StacktracesRequest
 */
export const StacktracesRequest = new StacktracesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class StacktracesResponse$Type extends MessageType<StacktracesResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.StacktracesResponse", [
            { no: 1, name: "stacktraces", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Stacktrace }
        ]);
    }
    create(value?: PartialMessage<StacktracesResponse>): StacktracesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.stacktraces = [];
        if (value !== undefined)
            reflectionMergePartial<StacktracesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: StacktracesResponse): StacktracesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Stacktrace stacktraces */ 1:
                    message.stacktraces.push(Stacktrace.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: StacktracesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Stacktrace stacktraces = 1; */
        for (let i = 0; i < message.stacktraces.length; i++)
            Stacktrace.internalBinaryWrite(message.stacktraces[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.StacktracesResponse
 */
export const StacktracesResponse = new StacktracesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class LocationsRequest$Type extends MessageType<LocationsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.LocationsRequest", [
            { no: 1, name: "location_ids", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<LocationsRequest>): LocationsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locationIds = [];
        if (value !== undefined)
            reflectionMergePartial<LocationsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: LocationsRequest): LocationsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string location_ids */ 1:
                    message.locationIds.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: LocationsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string location_ids = 1; */
        for (let i = 0; i < message.locationIds.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.locationIds[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.LocationsRequest
 */
export const LocationsRequest = new LocationsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class LocationsResponse$Type extends MessageType<LocationsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.LocationsResponse", [
            { no: 1, name: "locations", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location }
        ]);
    }
    create(value?: PartialMessage<LocationsResponse>): LocationsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locations = [];
        if (value !== undefined)
            reflectionMergePartial<LocationsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: LocationsResponse): LocationsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Location locations */ 1:
                    message.locations.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: LocationsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Location locations = 1; */
        for (let i = 0; i < message.locations.length; i++)
            Location.internalBinaryWrite(message.locations[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.LocationsResponse
 */
export const LocationsResponse = new LocationsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class LocationLinesRequest$Type extends MessageType<LocationLinesRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.LocationLinesRequest", [
            { no: 1, name: "location_ids", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<LocationLinesRequest>): LocationLinesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locationIds = [];
        if (value !== undefined)
            reflectionMergePartial<LocationLinesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: LocationLinesRequest): LocationLinesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string location_ids */ 1:
                    message.locationIds.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: LocationLinesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string location_ids = 1; */
        for (let i = 0; i < message.locationIds.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.locationIds[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.LocationLinesRequest
 */
export const LocationLinesRequest = new LocationLinesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FunctionsRequest$Type extends MessageType<FunctionsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.FunctionsRequest", [
            { no: 1, name: "function_ids", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<FunctionsRequest>): FunctionsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functionIds = [];
        if (value !== undefined)
            reflectionMergePartial<FunctionsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FunctionsRequest): FunctionsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string function_ids */ 1:
                    message.functionIds.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FunctionsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string function_ids = 1; */
        for (let i = 0; i < message.functionIds.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.functionIds[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.FunctionsRequest
 */
export const FunctionsRequest = new FunctionsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FunctionsResponse$Type extends MessageType<FunctionsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.FunctionsResponse", [
            { no: 1, name: "functions", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Function }
        ]);
    }
    create(value?: PartialMessage<FunctionsResponse>): FunctionsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functions = [];
        if (value !== undefined)
            reflectionMergePartial<FunctionsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FunctionsResponse): FunctionsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Function functions */ 1:
                    message.functions.push(Function.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FunctionsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Function functions = 1; */
        for (let i = 0; i < message.functions.length; i++)
            Function.internalBinaryWrite(message.functions[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.FunctionsResponse
 */
export const FunctionsResponse = new FunctionsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class MappingsRequest$Type extends MessageType<MappingsRequest> {
    constructor() {
        super("parca.metastore.v1alpha1.MappingsRequest", [
            { no: 1, name: "mapping_ids", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<MappingsRequest>): MappingsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mappingIds = [];
        if (value !== undefined)
            reflectionMergePartial<MappingsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: MappingsRequest): MappingsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string mapping_ids */ 1:
                    message.mappingIds.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: MappingsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string mapping_ids = 1; */
        for (let i = 0; i < message.mappingIds.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.mappingIds[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.MappingsRequest
 */
export const MappingsRequest = new MappingsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class MappingsResponse$Type extends MessageType<MappingsResponse> {
    constructor() {
        super("parca.metastore.v1alpha1.MappingsResponse", [
            { no: 1, name: "mappings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Mapping }
        ]);
    }
    create(value?: PartialMessage<MappingsResponse>): MappingsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mappings = [];
        if (value !== undefined)
            reflectionMergePartial<MappingsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: MappingsResponse): MappingsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.metastore.v1alpha1.Mapping mappings */ 1:
                    message.mappings.push(Mapping.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: MappingsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.metastore.v1alpha1.Mapping mappings = 1; */
        for (let i = 0; i < message.mappings.length; i++)
            Mapping.internalBinaryWrite(message.mappings[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.MappingsResponse
 */
export const MappingsResponse = new MappingsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Sample$Type extends MessageType<Sample> {
    constructor() {
        super("parca.metastore.v1alpha1.Sample", [
            { no: 1, name: "stacktrace_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "labels", kind: "map", K: 9 /*ScalarType.STRING*/, V: { kind: "message", T: () => SampleLabel } },
            { no: 3, name: "num_labels", kind: "map", K: 9 /*ScalarType.STRING*/, V: { kind: "message", T: () => SampleNumLabel } },
            { no: 4, name: "num_units", kind: "map", K: 9 /*ScalarType.STRING*/, V: { kind: "message", T: () => SampleNumUnit } }
        ]);
    }
    create(value?: PartialMessage<Sample>): Sample {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.stacktraceId = "";
        message.labels = {};
        message.numLabels = {};
        message.numUnits = {};
        if (value !== undefined)
            reflectionMergePartial<Sample>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Sample): Sample {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string stacktrace_id */ 1:
                    message.stacktraceId = reader.string();
                    break;
                case /* map<string, parca.metastore.v1alpha1.SampleLabel> labels */ 2:
                    this.binaryReadMap2(message.labels, reader, options);
                    break;
                case /* map<string, parca.metastore.v1alpha1.SampleNumLabel> num_labels */ 3:
                    this.binaryReadMap3(message.numLabels, reader, options);
                    break;
                case /* map<string, parca.metastore.v1alpha1.SampleNumUnit> num_units */ 4:
                    this.binaryReadMap4(message.numUnits, reader, options);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    private binaryReadMap2(map: Sample["labels"], reader: IBinaryReader, options: BinaryReadOptions): void {
        let len = reader.uint32(), end = reader.pos + len, key: keyof Sample["labels"] | undefined, val: Sample["labels"][any] | undefined;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case 1:
                    key = reader.string();
                    break;
                case 2:
                    val = SampleLabel.internalBinaryRead(reader, reader.uint32(), options);
                    break;
                default: throw new globalThis.Error("unknown map entry field for field parca.metastore.v1alpha1.Sample.labels");
            }
        }
        map[key ?? ""] = val ?? SampleLabel.create();
    }
    private binaryReadMap3(map: Sample["numLabels"], reader: IBinaryReader, options: BinaryReadOptions): void {
        let len = reader.uint32(), end = reader.pos + len, key: keyof Sample["numLabels"] | undefined, val: Sample["numLabels"][any] | undefined;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case 1:
                    key = reader.string();
                    break;
                case 2:
                    val = SampleNumLabel.internalBinaryRead(reader, reader.uint32(), options);
                    break;
                default: throw new globalThis.Error("unknown map entry field for field parca.metastore.v1alpha1.Sample.num_labels");
            }
        }
        map[key ?? ""] = val ?? SampleNumLabel.create();
    }
    private binaryReadMap4(map: Sample["numUnits"], reader: IBinaryReader, options: BinaryReadOptions): void {
        let len = reader.uint32(), end = reader.pos + len, key: keyof Sample["numUnits"] | undefined, val: Sample["numUnits"][any] | undefined;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case 1:
                    key = reader.string();
                    break;
                case 2:
                    val = SampleNumUnit.internalBinaryRead(reader, reader.uint32(), options);
                    break;
                default: throw new globalThis.Error("unknown map entry field for field parca.metastore.v1alpha1.Sample.num_units");
            }
        }
        map[key ?? ""] = val ?? SampleNumUnit.create();
    }
    internalBinaryWrite(message: Sample, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string stacktrace_id = 1; */
        if (message.stacktraceId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.stacktraceId);
        /* map<string, parca.metastore.v1alpha1.SampleLabel> labels = 2; */
        for (let k of globalThis.Object.keys(message.labels)) {
            writer.tag(2, WireType.LengthDelimited).fork().tag(1, WireType.LengthDelimited).string(k);
            writer.tag(2, WireType.LengthDelimited).fork();
            SampleLabel.internalBinaryWrite(message.labels[k], writer, options);
            writer.join().join();
        }
        /* map<string, parca.metastore.v1alpha1.SampleNumLabel> num_labels = 3; */
        for (let k of globalThis.Object.keys(message.numLabels)) {
            writer.tag(3, WireType.LengthDelimited).fork().tag(1, WireType.LengthDelimited).string(k);
            writer.tag(2, WireType.LengthDelimited).fork();
            SampleNumLabel.internalBinaryWrite(message.numLabels[k], writer, options);
            writer.join().join();
        }
        /* map<string, parca.metastore.v1alpha1.SampleNumUnit> num_units = 4; */
        for (let k of globalThis.Object.keys(message.numUnits)) {
            writer.tag(4, WireType.LengthDelimited).fork().tag(1, WireType.LengthDelimited).string(k);
            writer.tag(2, WireType.LengthDelimited).fork();
            SampleNumUnit.internalBinaryWrite(message.numUnits[k], writer, options);
            writer.join().join();
        }
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.Sample
 */
export const Sample = new Sample$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Stacktrace$Type extends MessageType<Stacktrace> {
    constructor() {
        super("parca.metastore.v1alpha1.Stacktrace", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "location_ids", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<Stacktrace>): Stacktrace {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.locationIds = [];
        if (value !== undefined)
            reflectionMergePartial<Stacktrace>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Stacktrace): Stacktrace {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* repeated string location_ids */ 2:
                    message.locationIds.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Stacktrace, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* repeated string location_ids = 2; */
        for (let i = 0; i < message.locationIds.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.locationIds[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.Stacktrace
 */
export const Stacktrace = new Stacktrace$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SampleLabel$Type extends MessageType<SampleLabel> {
    constructor() {
        super("parca.metastore.v1alpha1.SampleLabel", [
            { no: 1, name: "labels", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<SampleLabel>): SampleLabel {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.labels = [];
        if (value !== undefined)
            reflectionMergePartial<SampleLabel>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SampleLabel): SampleLabel {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string labels */ 1:
                    message.labels.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SampleLabel, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string labels = 1; */
        for (let i = 0; i < message.labels.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.labels[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.SampleLabel
 */
export const SampleLabel = new SampleLabel$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SampleNumLabel$Type extends MessageType<SampleNumLabel> {
    constructor() {
        super("parca.metastore.v1alpha1.SampleNumLabel", [
            { no: 1, name: "num_labels", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<SampleNumLabel>): SampleNumLabel {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.numLabels = [];
        if (value !== undefined)
            reflectionMergePartial<SampleNumLabel>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SampleNumLabel): SampleNumLabel {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated int64 num_labels */ 1:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.numLabels.push(reader.int64().toBigInt());
                    else
                        message.numLabels.push(reader.int64().toBigInt());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SampleNumLabel, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated int64 num_labels = 1; */
        if (message.numLabels.length) {
            writer.tag(1, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.numLabels.length; i++)
                writer.int64(message.numLabels[i]);
            writer.join();
        }
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.SampleNumLabel
 */
export const SampleNumLabel = new SampleNumLabel$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SampleNumUnit$Type extends MessageType<SampleNumUnit> {
    constructor() {
        super("parca.metastore.v1alpha1.SampleNumUnit", [
            { no: 1, name: "units", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<SampleNumUnit>): SampleNumUnit {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.units = [];
        if (value !== undefined)
            reflectionMergePartial<SampleNumUnit>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SampleNumUnit): SampleNumUnit {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string units */ 1:
                    message.units.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SampleNumUnit, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string units = 1; */
        for (let i = 0; i < message.units.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.units[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.SampleNumUnit
 */
export const SampleNumUnit = new SampleNumUnit$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Location$Type extends MessageType<Location> {
    constructor() {
        super("parca.metastore.v1alpha1.Location", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "address", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "mapping_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "is_folded", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "lines", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Line },
            { no: 7, name: "mapping_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
    create(value?: PartialMessage<Location>): Location {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.address = 0n;
        message.mappingId = "";
        message.isFolded = false;
        message.lines = [];
        message.mappingIndex = 0;
        if (value !== undefined)
            reflectionMergePartial<Location>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Location): Location {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* uint64 address */ 2:
                    message.address = reader.uint64().toBigInt();
                    break;
                case /* string mapping_id */ 4:
                    message.mappingId = reader.string();
                    break;
                case /* bool is_folded */ 5:
                    message.isFolded = reader.bool();
                    break;
                case /* repeated parca.metastore.v1alpha1.Line lines */ 6:
                    message.lines.push(Line.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* uint32 mapping_index */ 7:
                    message.mappingIndex = reader.uint32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Location, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* uint64 address = 2; */
        if (message.address !== 0n)
            writer.tag(2, WireType.Varint).uint64(message.address);
        /* string mapping_id = 4; */
        if (message.mappingId !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.mappingId);
        /* bool is_folded = 5; */
        if (message.isFolded !== false)
            writer.tag(5, WireType.Varint).bool(message.isFolded);
        /* repeated parca.metastore.v1alpha1.Line lines = 6; */
        for (let i = 0; i < message.lines.length; i++)
            Line.internalBinaryWrite(message.lines[i], writer.tag(6, WireType.LengthDelimited).fork(), options).join();
        /* uint32 mapping_index = 7; */
        if (message.mappingIndex !== 0)
            writer.tag(7, WireType.Varint).uint32(message.mappingIndex);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.Location
 */
export const Location = new Location$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Line$Type extends MessageType<Line> {
    constructor() {
        super("parca.metastore.v1alpha1.Line", [
            { no: 1, name: "function_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "line", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "function_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
    create(value?: PartialMessage<Line>): Line {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functionId = "";
        message.line = 0n;
        message.functionIndex = 0;
        if (value !== undefined)
            reflectionMergePartial<Line>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Line): Line {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string function_id */ 1:
                    message.functionId = reader.string();
                    break;
                case /* int64 line */ 2:
                    message.line = reader.int64().toBigInt();
                    break;
                case /* uint32 function_index */ 3:
                    message.functionIndex = reader.uint32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Line, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string function_id = 1; */
        if (message.functionId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.functionId);
        /* int64 line = 2; */
        if (message.line !== 0n)
            writer.tag(2, WireType.Varint).int64(message.line);
        /* uint32 function_index = 3; */
        if (message.functionIndex !== 0)
            writer.tag(3, WireType.Varint).uint32(message.functionIndex);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.Line
 */
export const Line = new Line$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Function$Type extends MessageType<Function> {
    constructor() {
        super("parca.metastore.v1alpha1.Function", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start_line", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "system_name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "filename", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "name_string_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 7, name: "system_name_string_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 8, name: "filename_string_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
    create(value?: PartialMessage<Function>): Function {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.startLine = 0n;
        message.name = "";
        message.systemName = "";
        message.filename = "";
        message.nameStringIndex = 0;
        message.systemNameStringIndex = 0;
        message.filenameStringIndex = 0;
        if (value !== undefined)
            reflectionMergePartial<Function>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Function): Function {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* int64 start_line */ 2:
                    message.startLine = reader.int64().toBigInt();
                    break;
                case /* string name */ 3:
                    message.name = reader.string();
                    break;
                case /* string system_name */ 4:
                    message.systemName = reader.string();
                    break;
                case /* string filename */ 5:
                    message.filename = reader.string();
                    break;
                case /* uint32 name_string_index */ 6:
                    message.nameStringIndex = reader.uint32();
                    break;
                case /* uint32 system_name_string_index */ 7:
                    message.systemNameStringIndex = reader.uint32();
                    break;
                case /* uint32 filename_string_index */ 8:
                    message.filenameStringIndex = reader.uint32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Function, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* int64 start_line = 2; */
        if (message.startLine !== 0n)
            writer.tag(2, WireType.Varint).int64(message.startLine);
        /* string name = 3; */
        if (message.name !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.name);
        /* string system_name = 4; */
        if (message.systemName !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.systemName);
        /* string filename = 5; */
        if (message.filename !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.filename);
        /* uint32 name_string_index = 6; */
        if (message.nameStringIndex !== 0)
            writer.tag(6, WireType.Varint).uint32(message.nameStringIndex);
        /* uint32 system_name_string_index = 7; */
        if (message.systemNameStringIndex !== 0)
            writer.tag(7, WireType.Varint).uint32(message.systemNameStringIndex);
        /* uint32 filename_string_index = 8; */
        if (message.filenameStringIndex !== 0)
            writer.tag(8, WireType.Varint).uint32(message.filenameStringIndex);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.Function
 */
export const Function = new Function$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Mapping$Type extends MessageType<Mapping> {
    constructor() {
        super("parca.metastore.v1alpha1.Mapping", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "limit", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "offset", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "file", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "build_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "has_functions", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 8, name: "has_filenames", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 9, name: "has_line_numbers", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 10, name: "has_inline_frames", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 11, name: "file_string_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 12, name: "build_id_string_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
    create(value?: PartialMessage<Mapping>): Mapping {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.start = 0n;
        message.limit = 0n;
        message.offset = 0n;
        message.file = "";
        message.buildId = "";
        message.hasFunctions = false;
        message.hasFilenames = false;
        message.hasLineNumbers = false;
        message.hasInlineFrames = false;
        message.fileStringIndex = 0;
        message.buildIdStringIndex = 0;
        if (value !== undefined)
            reflectionMergePartial<Mapping>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Mapping): Mapping {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* uint64 start */ 2:
                    message.start = reader.uint64().toBigInt();
                    break;
                case /* uint64 limit */ 3:
                    message.limit = reader.uint64().toBigInt();
                    break;
                case /* uint64 offset */ 4:
                    message.offset = reader.uint64().toBigInt();
                    break;
                case /* string file */ 5:
                    message.file = reader.string();
                    break;
                case /* string build_id */ 6:
                    message.buildId = reader.string();
                    break;
                case /* bool has_functions */ 7:
                    message.hasFunctions = reader.bool();
                    break;
                case /* bool has_filenames */ 8:
                    message.hasFilenames = reader.bool();
                    break;
                case /* bool has_line_numbers */ 9:
                    message.hasLineNumbers = reader.bool();
                    break;
                case /* bool has_inline_frames */ 10:
                    message.hasInlineFrames = reader.bool();
                    break;
                case /* uint32 file_string_index */ 11:
                    message.fileStringIndex = reader.uint32();
                    break;
                case /* uint32 build_id_string_index */ 12:
                    message.buildIdStringIndex = reader.uint32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Mapping, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* uint64 start = 2; */
        if (message.start !== 0n)
            writer.tag(2, WireType.Varint).uint64(message.start);
        /* uint64 limit = 3; */
        if (message.limit !== 0n)
            writer.tag(3, WireType.Varint).uint64(message.limit);
        /* uint64 offset = 4; */
        if (message.offset !== 0n)
            writer.tag(4, WireType.Varint).uint64(message.offset);
        /* string file = 5; */
        if (message.file !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.file);
        /* string build_id = 6; */
        if (message.buildId !== "")
            writer.tag(6, WireType.LengthDelimited).string(message.buildId);
        /* bool has_functions = 7; */
        if (message.hasFunctions !== false)
            writer.tag(7, WireType.Varint).bool(message.hasFunctions);
        /* bool has_filenames = 8; */
        if (message.hasFilenames !== false)
            writer.tag(8, WireType.Varint).bool(message.hasFilenames);
        /* bool has_line_numbers = 9; */
        if (message.hasLineNumbers !== false)
            writer.tag(9, WireType.Varint).bool(message.hasLineNumbers);
        /* bool has_inline_frames = 10; */
        if (message.hasInlineFrames !== false)
            writer.tag(10, WireType.Varint).bool(message.hasInlineFrames);
        /* uint32 file_string_index = 11; */
        if (message.fileStringIndex !== 0)
            writer.tag(11, WireType.Varint).uint32(message.fileStringIndex);
        /* uint32 build_id_string_index = 12; */
        if (message.buildIdStringIndex !== 0)
            writer.tag(12, WireType.Varint).uint32(message.buildIdStringIndex);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.metastore.v1alpha1.Mapping
 */
export const Mapping = new Mapping$Type();
/**
 * @generated ServiceType for protobuf service parca.metastore.v1alpha1.MetastoreService
 */
export const MetastoreService = new ServiceType("parca.metastore.v1alpha1.MetastoreService", [
    { name: "GetOrCreateMappings", options: {}, I: GetOrCreateMappingsRequest, O: GetOrCreateMappingsResponse },
    { name: "GetOrCreateFunctions", options: {}, I: GetOrCreateFunctionsRequest, O: GetOrCreateFunctionsResponse },
    { name: "GetOrCreateLocations", options: {}, I: GetOrCreateLocationsRequest, O: GetOrCreateLocationsResponse },
    { name: "GetOrCreateStacktraces", options: {}, I: GetOrCreateStacktracesRequest, O: GetOrCreateStacktracesResponse },
    { name: "UnsymbolizedLocations", options: {}, I: UnsymbolizedLocationsRequest, O: UnsymbolizedLocationsResponse },
    { name: "CreateLocationLines", options: {}, I: CreateLocationLinesRequest, O: CreateLocationLinesResponse },
    { name: "Locations", options: {}, I: LocationsRequest, O: LocationsResponse },
    { name: "Functions", options: {}, I: FunctionsRequest, O: FunctionsResponse },
    { name: "Mappings", options: {}, I: MappingsRequest, O: MappingsResponse },
    { name: "Stacktraces", options: {}, I: StacktracesRequest, O: StacktracesResponse }
]);
