{"coredump-ref": "38a845c2b051a25f0fd495a319544222c4f96bdc23295e7399313a72bafa749e", "threads": [{"lwp": 23103, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x8b712", "libjli.so+0x8b0d", "libjli.so+0x5d1c", "libjli.so+0x7180", "java+0xae0", "libc.so.6+0x2958f", "libc.so.6+0x2963f", "java+0xb74", "<unwinding aborted due to error native_stack_delta_invalid>"]}, {"lwp": 23204, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd2106", "libjvm.so+0xedfeb2", "void jdk.internal.misc.Unsafe.park(boolean, long)+0 in Unsafe.java:0", "void java.util.concurrent.locks.LockSupport.parkNanos(java.lang.Object, long)+7 in LockSupport.java:269", "boolean java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(long, java.util.concurrent.TimeUnit)+16 in AbstractQueuedSynchronizer.java:1852", "void java.lang.ref.ReferenceQueue.await(long)+0 in ReferenceQueue.java:79", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove0(long)+4 in ReferenceQueue.java:151", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+7 in ReferenceQueue.java:229", "void jdk.internal.ref.CleanerImpl.run()+12 in CleanerImpl.java:140", "void java.lang.Thread.runWith(java.lang.Object, java.lang.Runnable)+1 in Thread.java:1588", "void java.lang.Thread.run()+3 in Thread.java:1575", "void jdk.internal.misc.InnocuousThread.run()+2 in InnocuousThread.java:186", "StubRoutines (initial stubs)+0 in :0", "libjvm.so+0x8cfb1a", "libjvm.so+0x8d13ee", "libjvm.so+0x9a983b", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23203, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x88faf", "libjvm.so+0xcd24cb", "libjvm.so+0xc7d458", "libjvm.so+0xc970b1", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23202, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd243b", "libjvm.so+0xc7d4db", "libjvm.so+0x642229", "libjvm.so+0x6462c5", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23201, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd243b", "libjvm.so+0xc7d4db", "libjvm.so+0x642229", "libjvm.so+0x6462c5", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23200, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd243b", "libjvm.so+0xc7d458", "libjvm.so+0xc70aa2", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23199, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd243b", "libjvm.so+0xc7d458", "libjvm.so+0xd86a96", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23198, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x91cc7", "libjvm.so+0xd78d01", "libjvm.so+0xda117e", "libjvm.so+0xcb87d4", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23197, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x88faf", "libjvm.so+0xcd1c8a", "libjvm.so+0xca2564", "libjvm.so+0xe5ceb6", "libjvm.so+0x9ae4f6", "void java.lang.Object.wait0(long)+0 in Object.java:0", "void java.lang.Object.wait(long)+1 in Object.java:378", "void java.lang.Object.wait()+0 in Object.java:352", "void java.lang.ref.NativeReferenceQueue.await()+0 in NativeReferenceQueue.java:48", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove0()+2 in ReferenceQueue.java:166", "java.lang.ref.Reference java.lang.ref.NativeReferenceQueue.remove()+1 in NativeReferenceQueue.java:89", "void java.lang.ref.Finalizer$FinalizerThread.run()+7 in Finalizer.java:173", "StubRoutines (initial stubs)+0 in :0", "libjvm.so+0x8cfb1a", "libjvm.so+0x8d13ee", "libjvm.so+0x9a983b", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23196, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x88faf", "libjvm.so+0xcd24cb", "libjvm.so+0xc7d4db", "libjvm.so+0x9ac489", "void java.lang.ref.Reference.waitForReferencePendingList()+0 in Reference.java:0", "void java.lang.ref.Reference.processPendingReferences()+0 in Reference.java:246", "void java.lang.ref.Reference$ReferenceHandler.run()+3 in Reference.java:208", "StubRoutines (initial stubs)+0 in :0", "libjvm.so+0x8cfb1a", "libjvm.so+0x8d13ee", "libjvm.so+0x9a983b", "libjvm.so+0x8e62e7", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23195, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x88faf", "libjvm.so+0xcd24cb", "libjvm.so+0xc7d458", "libjvm.so+0xf3f2f8", "libjvm.so+0xf3fd17", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23194, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd243b", "libjvm.so+0xc7d458", "libjvm.so+0xc96b1a", "libjvm.so+0xc96c20", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23193, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x892b3", "libjvm.so+0xcd243b", "libjvm.so+0xc7d458", "libjvm.so+0x80d8bd", "libjvm.so+0x80dcaf", "libjvm.so+0x65f1fa", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23192, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x88faf", "libjvm.so+0xcd24cb", "libjvm.so+0xc7d458", "libjvm.so+0x7b1582", "libjvm.so+0x7b1a80", "libjvm.so+0x65f1fa", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23191, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x91cc7", "libjvm.so+0xd78d01", "libjvm.so+0xf6c76a", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23190, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x88faf", "libjvm.so+0xcd24cb", "libjvm.so+0xc7d458", "libjvm.so+0x7ab6b9", "libjvm.so+0x65f1fa", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23189, "frames": ["libc.so.6+0x867aa", "libc.so.6+0x91cc7", "libjvm.so+0xd78d01", "libjvm.so+0xf6c76a", "libjvm.so+0xeaa40e", "libjvm.so+0xcc61b4", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}, {"lwp": 23188, "frames": ["libc.so.6+0xfdb0f", "libjava.so+0x169a7", "libjava.so+0x1641b", "libjava.so+0xf416", "void java.io.FileOutputStream.writeBytes(byte[], int, int, boolean)+0 in FileOutputStream.java:0", "void java.io.FileOutputStream.write(byte[], int, int)+5 in FileOutputStream.java:400", "void java.lang.System$Out.write(byte[], int, int)+2 in System.java:2290", "void java.io.BufferedOutputStream.flushBuffer()+1 in BufferedOutputStream.java:125", "void java.io.BufferedOutputStream.implFlush()+0 in BufferedOutputStream.java:252", "void java.io.BufferedOutputStream.flush()+3 in BufferedOutputStream.java:240", "void java.io.PrintStream.implWrite(byte[], int, int)+3 in PrintStream.java:649", "void java.io.PrintStream.write(byte[], int, int)+3 in PrintStream.java:627", "void sun.nio.cs.StreamEncoder.writeBytes()+7 in StreamEncoder.java:291", "void sun.nio.cs.StreamEncoder.implFlushBuffer()+1 in StreamEncoder.java:386", "void sun.nio.cs.StreamEncoder.lockedFlushBuffer()+1 in StreamEncoder.java:117", "void sun.nio.cs.StreamEncoder.flushBuffer()+4 in StreamEncoder.java:104", "void java.io.OutputStreamWriter.flushBuffer()+0 in OutputStreamWriter.java:194", "void java.io.PrintStream.implWriteln(java.lang.String)+4 in PrintStream.java:851", "void java.io.PrintStream.writeln(java.lang.String)+3 in PrintStream.java:828", "void java.io.PrintStream.println(java.lang.String)+1 in PrintStream.java:1170", "void HelloWorld.main(java.lang.String[])+0 in HelloWorld.java:4", "StubRoutines (initial stubs)+0 in :0", "libjvm.so+0x8cfb1a", "libjvm.so+0x98199d", "libjvm.so+0x984267", "libjli.so+0x3946", "libjli.so+0x5379", "libjli.so+0x7f98", "libc.so.6+0x89c11", "libc.so.6+0x10df53"]}], "modules": [{"ref": "588ec12f63407dd8d030f86b7929f9b742094ada9e2b9268105f2cc18f822015", "local-path": "/usr/lib64/libdl.so.2"}, {"ref": "44c8cbb157dd08b9827ec80b19a36c0225e9b349408ca6a38096499a35dc0810", "local-path": "/usr/lib64/ld-linux-x86-64.so.2"}, {"ref": "9ad130da691925ce77f83963747a7c2fbaf9a075f20c3b83113b4365bf4e1c2e", "local-path": "/usr/java/openjdk-23/bin/java"}, {"ref": "6d643c8bee87ae49e0296de43fa16361878f3a6f15b0fc6b01a76c5946429189", "local-path": "/usr/lib64/libc.so.6"}, {"ref": "0f5e6f6a097550ea2081f16928cda45df1ca18c530c02e3a3dbc940cf92fac70", "local-path": "/usr/java/openjdk-23/lib/libjimage.so"}, {"ref": "b4b0c5e673e5486af337284f26da129b8ef55d8e7bc2920ec7a8e859d138ff64", "local-path": "/usr/java/openjdk-23/lib/libjava.so"}, {"ref": "f36757729484004f495c62109ce0ccd1ebced3735fca190aedb11b1a7442caf4", "local-path": "/usr/lib64/libz.so.1.2.11"}, {"ref": "e7b02d34e20f0b6f7901321ba74c0a7b20a7815736a6c1fa13b9ea74cdcb964f", "local-path": "/usr/java/openjdk-23/lib/server/libjvm.so"}, {"ref": "ea85be0cb8233d441ee613d34e74a6b03ec54dbe695b12a3ad046f6d59252e6b", "local-path": "/usr/java/openjdk-23/lib/libjli.so"}, {"ref": "8f2233b8c069875b5f2a43bd294cbb694cf3028434128fe2640032b8fa944d20", "local-path": "/usr/java/openjdk-23/lib/libjsvml.so"}, {"ref": "aeabebd449a4e444a2ca9ec3c091afe24fdc75bf2ff3ddb55d1c2feeba47c854", "local-path": "/usr/lib64/libm.so.6"}, {"ref": "859212e5598f289a4556784319236bbb5bc077aff7da87ab5e288f94bec7d4e3", "local-path": "/usr/lib64/librt.so.1"}, {"ref": "eb5ac0e31030d3e124a2a941109b7b4c6e4a1390bdddacc425faef7805ca296e", "local-path": "/usr/lib64/libpthread.so.0"}, {"ref": "f7c20993e3573dded520a67cfefeb80eb7517ed7d10e829814b910937699fff0", "local-path": "/usr/java/openjdk-23/lib/libsimdsort.so"}]}