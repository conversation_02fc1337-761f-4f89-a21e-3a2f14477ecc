{"coredump-ref": "42083c77549bc7ea554d1288117a26fa152f6d99abb675ba7e356f36902c5db0", "threads": [{"lwp": 3577, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x58ac2", "libjava.so+0x17807", "libjava.so+0x172c3", "libjava.so+0xf796", "void java.io.FileOutputStream.writeBytes(byte[], int, int, boolean)+0 in FileOutputStream.java:0", "void java.io.FileOutputStream.write(byte[], int, int)+0 in FileOutputStream.java:347", "void java.io.BufferedOutputStream.flushBuffer()+1 in BufferedOutputStream.java:81", "void java.io.BufferedOutputStream.flush()+0 in BufferedOutputStream.java:142", "void java.io.PrintStream.write(byte[], int, int)+4 in PrintStream.java:570", "void sun.nio.cs.StreamEncoder.writeBytes()+11 in StreamEncoder.java:242", "void sun.nio.cs.StreamEncoder.implFlushBuffer()+1 in StreamEncoder.java:321", "void sun.nio.cs.StreamEncoder.flushBuffer()+2 in StreamEncoder.java:110", "void java.io.OutputStreamWriter.flushBuffer()+0 in OutputStreamWriter.java:181", "void java.io.PrintStream.write(java.lang.String)+4 in PrintStream.java:699", "void java.io.PrintStream.print(java.lang.String)+0 in PrintStream.java:863", "void Deopt.Handle(int)+9 in Deopt.java:20", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.main(java.lang.String[])+3 in Deopt.java:31", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7a1cdd", "libjvm.so+0x82e571", "libjvm.so+0x830b8e", "libjli.so+0x464c", "libjli.so+0x8578", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3576, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x53882", "libjli.so+0x8e4a", "libjli.so+0x590c", "libjli.so+0x7152", "java+0x1212", "ld-musl-x86_64.so.1+0x1ca02", "java+0x129c"]}, {"lwp": 3582, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e0af", "libjvm.so+0x6ef7e1", "libjvm.so+0x591777", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3578, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x54ea5", "libjvm.so+0xc27551", "libjvm.so+0xe6ccac", "libjvm.so+0xe6bdc8", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3579, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fb4a", "libjvm.so+0xb2e0af", "libjvm.so+0x68d38d", "libjvm.so+0x68d47f", "libjvm.so+0x591777", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3587, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x54ea5", "libjvm.so+0xc27551", "libjvm.so+0xb70d08", "libjvm.so+0xb652d4", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3581, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fb4a", "libjvm.so+0xb2e0af", "libjvm.so+0x68ff8e", "libjvm.so+0x69011a", "libjvm.so+0x591777", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3588, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fb4a", "libjvm.so+0xb2e0af", "libjvm.so+0xc27be0", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3592, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fb4a", "libjvm.so+0xb2e0af", "libjvm.so+0xb43d81", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3594, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7f565", "libjvm.so+0xb50487", "libjvm.so+0xd7b918", "libjvm.so+0x85d81d", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "void jdk.internal.ref.CleanerImpl.run()+14 in CleanerImpl.java:148", "void java.lang.Thread.run()+1 in Thread.java:832", "void jdk.internal.misc.InnocuousThread.run()+2 in InnocuousThread.java:134", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7a1cdd", "libjvm.so+0x7a3498", "libjvm.so+0x85bc79", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3586, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7f3ca", "libjvm.so+0xb505a4", "libjvm.so+0xd7b918", "libjvm.so+0x85d81d", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove()+0 in ReferenceQueue.java:176", "void java.lang.ref.Finalizer$FinalizerThread.run()+17 in Finalizer.java:170", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7a1cdd", "libjvm.so+0x7a3498", "libjvm.so+0x85bc79", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3590, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e1d2", "libjvm.so+0x57b6c4", "libjvm.so+0x57dfac", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3589, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e1d2", "libjvm.so+0x57b6c4", "libjvm.so+0x57dfac", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3580, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x54ea5", "libjvm.so+0xc27551", "libjvm.so+0xe6ccac", "libjvm.so+0xe6bdc8", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3593, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e0af", "libjvm.so+0xdcd6ac", "libjvm.so+0xdcd775", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3601, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e1d2", "libjvm.so+0x57b6c4", "libjvm.so+0x57dfac", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3584, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fb4a", "libjvm.so+0xb2e1d2", "libjvm.so+0x865643", "void java.lang.ref.Reference.waitForReferencePendingList()+0 in Reference.java:0", "void java.lang.ref.Reference.processPendingReferences()+0 in Reference.java:241", "void java.lang.ref.Reference$ReferenceHandler.run()+0 in Reference.java:213", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7a1cdd", "libjvm.so+0x7a3498", "libjvm.so+0x85bc79", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3600, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e1d2", "libjvm.so+0x57b6c4", "libjvm.so+0x57dfac", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3583, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e0af", "libjvm.so+0xe43a3a", "libjvm.so+0xe440ef", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 3591, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0xb7fa9e", "libjvm.so+0xb2e0af", "libjvm.so+0xd700c3", "libjvm.so+0xdce719", "libjvm.so+0xdd3424", "libjvm.so+0xb75cb6", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}], "modules": null}