syntax = "proto3";

package parca.profilestore.v1alpha1;

import "google/api/annotations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/parca-dev/parca/gen/go/profilestore";

// ProfileStoreService is the service the accepts pprof writes
service ProfileStoreService {
  // WriteRaw accepts a raw set of bytes of a pprof file
  rpc WriteRaw(WriteRawRequest) returns (WriteRawResponse) {
    option (google.api.http) = {
      post: "/profiles/writeraw"
      body: "*"
    };
  }

  // Write accepts profiling data encoded as an arrow record. It's a
  // bi-directional streaming RPC, because the first message can contain only
  // samples without the stacktraces, and only reference stacktrace IDs. The
  // backend can then request the full stacktrace from the client should it not
  // know the stacktrace yet.
  rpc Write(stream WriteRequest) returns (stream WriteResponse) {
    option (google.api.http) = {
      post: "/profiles/write"
      body: "*"
    };
  }
}

// WriteRequest may contain an apache arrow record that only contains profiling
// samples with a reference to a stacktrace ID, or a full stacktrace. If it
// only contains samples, the server may request the full stacktrace from the
// client should it not already know them.
message WriteRequest {
  // The bytes containing the arrow record.
  bytes record = 1;
}

// WriteResponse may be empty if the server doesn't need any further
// information, or contain an arrow record that contains the stacktrace IDs
// that are unknown and therefore requested by the client from the server.
message WriteResponse {
  // When record is non-empty it contains the bytes of an arrow record that
  // contains a column containing the stacktraces that are unknown.
  bytes record = 1;
}

// WriteRawRequest writes a pprof profile for a given tenant
message WriteRawRequest {
  // tenant is the given tenant to store the pprof profile under
  string tenant = 1 [deprecated = true];

  // series is a set raw pprof profiles and accompanying labels
  repeated RawProfileSeries series = 2;

  // normalized is a flag indicating if the addresses in the profile is normalized for position independent code
  bool normalized = 3;
}

// WriteRawResponse is the empty response
message WriteRawResponse {}

// RawProfileSeries represents the pprof profile and its associated labels
message RawProfileSeries {
  // LabelSet is the key value pairs to identify the corresponding profile
  LabelSet labels = 1;

  // samples are the set of profile bytes
  repeated RawSample samples = 2;
}

// Label is a key value pair of identifiers
message Label {
  // name is the label name
  string name = 1;

  // value is the value for the label name
  string value = 2;
}

// LabelSet is a group of labels
message LabelSet {
  // labels are the grouping of labels
  repeated Label labels = 1;
}

// RawSample is the set of bytes that correspond to a pprof profile
message RawSample {
  // raw_profile is the set of bytes of the pprof profile
  bytes raw_profile = 1;
  // information about the executable and executable section for normalizaton
  // purposes.
  repeated ExecutableInfo executable_info = 2;
}

// ExecutableInfo is the information about the executable and executable
// section for normalizaton purposes before symbolization.
message ExecutableInfo {
  // elf_type is the type of the elf executable. Technically the elf type is a
  // 16 bit integer, but protobuf's smallest unsigned integer is 32 bits.
  uint32 elf_type = 1;
  // load_segment is the load segment of the executable.
  LoadSegment load_segment = 2;
}

// LoadSegment is the load segment of the executable
message LoadSegment {
  // The offset from the beginning of the file at which the first byte of the segment resides.
  uint64 offset = 1;
  // The virtual address at which the first byte of the segment resides in memory.
  uint64 vaddr = 2;
}

// AgentsService maintains the agents
service AgentsService {
  // Agents return the agents that pushed data to the server
  rpc Agents(AgentsRequest) returns (AgentsResponse) {
    option (google.api.http) = {get: "/agents"};
  }
}

// AgentsRequest is the request to retrieve a list of agents
message AgentsRequest {}

// AgentsResponse is the request to retrieve a list of agents
message AgentsResponse {
  // agents is a list of agents
  repeated Agent agents = 1;
}

// Agent is the agent representation
message Agent {
  // id is the agent identity that either represent by the node name or the IP address.
  // When node name is not found, this will fallback to IP address.
  string id = 1;

  // last_error is the error message most recently received from a push attempt
  string last_error = 2;

  // last_push is the time stamp the last push request was performed
  google.protobuf.Timestamp last_push = 3;

  // last_push_duration is the duration of the last push request
  google.protobuf.Duration last_push_duration = 4;
}
