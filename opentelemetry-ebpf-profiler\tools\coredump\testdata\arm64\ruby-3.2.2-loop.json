{"coredump-ref": "97e9bac14375fea16557e69d30ab3de4577e59f620d727ed0a195dd802b13609", "threads": [{"lwp": 220805, "frames": ["libruby.so.3.2.2+0xd65e4", "libruby.so.3.2.2+0x255d6f", "libruby.so.3.2.2+0x31f0bf", "libruby.so.3.2.2+0x3250fb", "libruby.so.3.2.2+0x32f0c7", "is_prime+0 in /pwd/testsources/ruby/loop.rb:10", "sum_of_primes+0 in /pwd/testsources/ruby/loop.rb:20", "<main>+0 in /pwd/testsources/ruby/loop.rb:30", "libruby.so.3.2.2+0x33418f", "libruby.so.3.2.2+0x3381d7", "libruby.so.3.2.2+0x255b93", "libruby.so.3.2.2+0x31f0bf", "libruby.so.3.2.2+0x3250fb", "libruby.so.3.2.2+0x32f0c7", "<main>+0 in /pwd/testsources/ruby/loop.rb:29", "libruby.so.3.2.2+0x333e17", "libruby.so.3.2.2+0x335277", "libruby.so.3.2.2+0x152473", "libruby.so.3.2.2+0x1526ab", "libruby.so.3.2.2+0x31f0bf", "libruby.so.3.2.2+0x3250fb", "libruby.so.3.2.2+0x32f0c7", "<main>+0 in /pwd/testsources/ruby/loop.rb:28", "libruby.so.3.2.2+0x333e17", "libruby.so.3.2.2+0x14e003", "libruby.so.3.2.2+0x154677", "ruby+0xb2b", "libc.so.6+0x2777f", "libc.so.6+0x27857", "ruby+0xbaf"]}], "modules": [{"ref": "969fe88169ba3d774d8637e9229fa5ed6ba73eb1a2b81ca8069f1ebc985ef203", "local-path": "/usr/local/bin/ruby"}, {"ref": "c90e436f50748a03aa6499d1d7e87a17571c316dff26b35795cdd52c81a015c2", "local-path": "/usr/local/lib/ruby/3.2.0/aarch64-linux/monitor.so"}, {"ref": "894767d90101122dc073791a7e3cdcb528ad5a7e5334a12bd93d1b4d590c87c9", "local-path": "/usr/local/lib/ruby/3.2.0/aarch64-linux/enc/trans/transdb.so"}, {"ref": "4b97951caabf7726ba9aa57884c12e5ddff3894aebbee4f13e08d667cbf45acb", "local-path": "/usr/local/lib/ruby/3.2.0/aarch64-linux/enc/encdb.so"}, {"ref": "e4b5576b19e40be5923b0eb864750d35944404bb0a92aa68d1a9b96110c52120", "local-path": "/usr/lib/locale/C.utf8/LC_CTYPE"}, {"ref": "8526cf244c909542d584aa8454212341290a4537020b9bd30c7f2d3f7c1bc308", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}, {"ref": "e8ccdb5ffa5e29c0692e5afcb4e1f3f536067bb261e79e8a089a163d0fafdd45", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "ae6bd25b1f9616e37fb652d1052af984576d22adacfd3bced9df82d075ad92c7", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "3c70a152ded3b0eab8f2f3b3d2a80e2ce0808909e8c6828a8dcfc6b883ff30fb", "local-path": "/usr/lib/aarch64-linux-gnu/libcrypt.so.1.1.0"}, {"ref": "391b336ee37cd47a1d4748ba2d15bd125c1a082864f7bca9c92612f8a0ac305e", "local-path": "/usr/lib/aarch64-linux-gnu/libgmp.so.10.4.1"}, {"ref": "ffb1ab496e6eced03ab679075f9f2c415c7728a145cc7f63d614497102d73822", "local-path": "/usr/lib/aarch64-linux-gnu/libz.so.1.2.13"}, {"ref": "4d3221ae78a31f3c04fb94dcc164afb56c8c2d276e3e5b8689a78a8940583086", "local-path": "/usr/local/lib/libruby.so.3.2.2"}, {"ref": "c4c52c0df6f3809f1b85927a85f322d102cffebb6b517b44b37048f6fd620c01", "local-path": "/usr/lib/aarch64-linux-gnu/gconv/gconv-modules.cache"}, {"ref": "5ece65ae57fce3774af5218e7c3de93f1cc79f70b92ae3bec86a4135fc2396c2", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}]}