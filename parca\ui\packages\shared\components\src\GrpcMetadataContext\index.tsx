// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {ReactNode, createContext, useContext} from 'react';

import type {RpcMetadata} from '@protobuf-ts/runtime-rpc';

const DEFAULT_VALUE = {};

const GrpcMetadataContext = createContext<RpcMetadata>(DEFAULT_VALUE);

export const GrpcMetadataProvider = ({
  children,
  value,
}: {
  children: ReactNode;
  value?: RpcMetadata;
}): JSX.Element => {
  return (
    <GrpcMetadataContext.Provider value={value ?? DEFAULT_VALUE}>
      {children}
    </GrpcMetadataContext.Provider>
  );
};

export const useGrpcMetadata = (): RpcMetadata => {
  const context = useContext(GrpcMetadataContext);
  if (context == null) {
    return DEFAULT_VALUE;
  }
  return context;
};

export default GrpcMetadataContext;
