name: proto-pr

on:
  pull_request:
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              ".github/workflows/proto-pr.yaml",
              ".go-version",
              "buf.gen.yaml",
              "buf.work.yaml",
              "proto/**"
            ]
          skip_after_successful_duplicate: false

  build:
    name: Proto PR Checks
    needs: skip-check
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: bufbuild/buf-setup-action@a47c93e0b1648d5651a065437926377d060baa99 # v1.50.0

      - name: version
        run: buf --version

      - name: Format
        run: buf format --diff --exit-code

      - uses: bufbuild/buf-lint-action@06f9dd823d873146471cfaaf108a993fe00e5325 # v1.1.1
        with:
          input: 'proto'

      - uses: bufbuild/buf-breaking-action@c57b3d842a5c3f3b454756ef65305a50a587c5ba # v1.1.4
        with:
          input: 'proto'
          # The 'main' branch of the GitHub repository that defines the module.
          against: 'https://github.com/${GITHUB_REPOSITORY}.git#branch=main,subdir=proto'
