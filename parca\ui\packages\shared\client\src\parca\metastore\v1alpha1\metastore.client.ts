// @generated by protobuf-ts 2.9.6 with parameter generate_dependencies
// @generated from protobuf file "parca/metastore/v1alpha1/metastore.proto" (package "parca.metastore.v1alpha1", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { MetastoreService } from "./metastore";
import type { StacktracesResponse } from "./metastore";
import type { StacktracesRequest } from "./metastore";
import type { MappingsResponse } from "./metastore";
import type { MappingsRequest } from "./metastore";
import type { FunctionsResponse } from "./metastore";
import type { FunctionsRequest } from "./metastore";
import type { LocationsResponse } from "./metastore";
import type { LocationsRequest } from "./metastore";
import type { CreateLocationLinesResponse } from "./metastore";
import type { CreateLocationLinesRequest } from "./metastore";
import type { UnsymbolizedLocationsResponse } from "./metastore";
import type { UnsymbolizedLocationsRequest } from "./metastore";
import type { GetOrCreateStacktracesResponse } from "./metastore";
import type { GetOrCreateStacktracesRequest } from "./metastore";
import type { GetOrCreateLocationsResponse } from "./metastore";
import type { GetOrCreateLocationsRequest } from "./metastore";
import type { GetOrCreateFunctionsResponse } from "./metastore";
import type { GetOrCreateFunctionsRequest } from "./metastore";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { GetOrCreateMappingsResponse } from "./metastore";
import type { GetOrCreateMappingsRequest } from "./metastore";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * MetastoreService
 *
 * @generated from protobuf service parca.metastore.v1alpha1.MetastoreService
 */
export interface IMetastoreServiceClient {
    /**
     * GetOrCreateMappings checks if the mappings in the request are already
     * known and returns the known mapping (including its ID) if so. If a mapping
     * does not already exist, it is written to the backing metastore.
     *
     * @generated from protobuf rpc: GetOrCreateMappings(parca.metastore.v1alpha1.GetOrCreateMappingsRequest) returns (parca.metastore.v1alpha1.GetOrCreateMappingsResponse);
     */
    getOrCreateMappings(input: GetOrCreateMappingsRequest, options?: RpcOptions): UnaryCall<GetOrCreateMappingsRequest, GetOrCreateMappingsResponse>;
    /**
     * GetOrCreateFunctions checks if the functions in the request are already
     * known and returns the known function (including its ID) if so. If a
     * function does not already exist, it is written to the backing metastore.
     *
     * @generated from protobuf rpc: GetOrCreateFunctions(parca.metastore.v1alpha1.GetOrCreateFunctionsRequest) returns (parca.metastore.v1alpha1.GetOrCreateFunctionsResponse);
     */
    getOrCreateFunctions(input: GetOrCreateFunctionsRequest, options?: RpcOptions): UnaryCall<GetOrCreateFunctionsRequest, GetOrCreateFunctionsResponse>;
    /**
     * GetOrCreateLocations checks if the locations in the request are already
     * known and returns the known location (including its ID) if so. If a
     * location does not already exist, it is written to the backing metastore.
     *
     * @generated from protobuf rpc: GetOrCreateLocations(parca.metastore.v1alpha1.GetOrCreateLocationsRequest) returns (parca.metastore.v1alpha1.GetOrCreateLocationsResponse);
     */
    getOrCreateLocations(input: GetOrCreateLocationsRequest, options?: RpcOptions): UnaryCall<GetOrCreateLocationsRequest, GetOrCreateLocationsResponse>;
    /**
     * GetOrCreateStacktraces checks if the stacktraces in the request are
     * already known and returns the known stacktrace (including its ID) if so.
     * If a stacktrace does not already exist, it is written to the backing
     * metastore.
     *
     * @generated from protobuf rpc: GetOrCreateStacktraces(parca.metastore.v1alpha1.GetOrCreateStacktracesRequest) returns (parca.metastore.v1alpha1.GetOrCreateStacktracesResponse);
     */
    getOrCreateStacktraces(input: GetOrCreateStacktracesRequest, options?: RpcOptions): UnaryCall<GetOrCreateStacktracesRequest, GetOrCreateStacktracesResponse>;
    /**
     * UnsymbolizedLocations returns locations that can be symbolized but haven't
     * been asynchronously symbolized yet.
     *
     * @generated from protobuf rpc: UnsymbolizedLocations(parca.metastore.v1alpha1.UnsymbolizedLocationsRequest) returns (parca.metastore.v1alpha1.UnsymbolizedLocationsResponse);
     */
    unsymbolizedLocations(input: UnsymbolizedLocationsRequest, options?: RpcOptions): UnaryCall<UnsymbolizedLocationsRequest, UnsymbolizedLocationsResponse>;
    /**
     * CreateLocationLines creates the location lines contained in the provided
     * locations.
     *
     * @generated from protobuf rpc: CreateLocationLines(parca.metastore.v1alpha1.CreateLocationLinesRequest) returns (parca.metastore.v1alpha1.CreateLocationLinesResponse);
     */
    createLocationLines(input: CreateLocationLinesRequest, options?: RpcOptions): UnaryCall<CreateLocationLinesRequest, CreateLocationLinesResponse>;
    /**
     * Locations retrieves locations.
     *
     * @generated from protobuf rpc: Locations(parca.metastore.v1alpha1.LocationsRequest) returns (parca.metastore.v1alpha1.LocationsResponse);
     */
    locations(input: LocationsRequest, options?: RpcOptions): UnaryCall<LocationsRequest, LocationsResponse>;
    /**
     * Functions retrieves functions.
     *
     * @generated from protobuf rpc: Functions(parca.metastore.v1alpha1.FunctionsRequest) returns (parca.metastore.v1alpha1.FunctionsResponse);
     */
    functions(input: FunctionsRequest, options?: RpcOptions): UnaryCall<FunctionsRequest, FunctionsResponse>;
    /**
     * Mappings retrieves mappings.
     *
     * @generated from protobuf rpc: Mappings(parca.metastore.v1alpha1.MappingsRequest) returns (parca.metastore.v1alpha1.MappingsResponse);
     */
    mappings(input: MappingsRequest, options?: RpcOptions): UnaryCall<MappingsRequest, MappingsResponse>;
    /**
     * Stacktraces retrieves mappings.
     *
     * @generated from protobuf rpc: Stacktraces(parca.metastore.v1alpha1.StacktracesRequest) returns (parca.metastore.v1alpha1.StacktracesResponse);
     */
    stacktraces(input: StacktracesRequest, options?: RpcOptions): UnaryCall<StacktracesRequest, StacktracesResponse>;
}
/**
 * MetastoreService
 *
 * @generated from protobuf service parca.metastore.v1alpha1.MetastoreService
 */
export class MetastoreServiceClient implements IMetastoreServiceClient, ServiceInfo {
    typeName = MetastoreService.typeName;
    methods = MetastoreService.methods;
    options = MetastoreService.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * GetOrCreateMappings checks if the mappings in the request are already
     * known and returns the known mapping (including its ID) if so. If a mapping
     * does not already exist, it is written to the backing metastore.
     *
     * @generated from protobuf rpc: GetOrCreateMappings(parca.metastore.v1alpha1.GetOrCreateMappingsRequest) returns (parca.metastore.v1alpha1.GetOrCreateMappingsResponse);
     */
    getOrCreateMappings(input: GetOrCreateMappingsRequest, options?: RpcOptions): UnaryCall<GetOrCreateMappingsRequest, GetOrCreateMappingsResponse> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetOrCreateMappingsRequest, GetOrCreateMappingsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * GetOrCreateFunctions checks if the functions in the request are already
     * known and returns the known function (including its ID) if so. If a
     * function does not already exist, it is written to the backing metastore.
     *
     * @generated from protobuf rpc: GetOrCreateFunctions(parca.metastore.v1alpha1.GetOrCreateFunctionsRequest) returns (parca.metastore.v1alpha1.GetOrCreateFunctionsResponse);
     */
    getOrCreateFunctions(input: GetOrCreateFunctionsRequest, options?: RpcOptions): UnaryCall<GetOrCreateFunctionsRequest, GetOrCreateFunctionsResponse> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetOrCreateFunctionsRequest, GetOrCreateFunctionsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * GetOrCreateLocations checks if the locations in the request are already
     * known and returns the known location (including its ID) if so. If a
     * location does not already exist, it is written to the backing metastore.
     *
     * @generated from protobuf rpc: GetOrCreateLocations(parca.metastore.v1alpha1.GetOrCreateLocationsRequest) returns (parca.metastore.v1alpha1.GetOrCreateLocationsResponse);
     */
    getOrCreateLocations(input: GetOrCreateLocationsRequest, options?: RpcOptions): UnaryCall<GetOrCreateLocationsRequest, GetOrCreateLocationsResponse> {
        const method = this.methods[2], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetOrCreateLocationsRequest, GetOrCreateLocationsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * GetOrCreateStacktraces checks if the stacktraces in the request are
     * already known and returns the known stacktrace (including its ID) if so.
     * If a stacktrace does not already exist, it is written to the backing
     * metastore.
     *
     * @generated from protobuf rpc: GetOrCreateStacktraces(parca.metastore.v1alpha1.GetOrCreateStacktracesRequest) returns (parca.metastore.v1alpha1.GetOrCreateStacktracesResponse);
     */
    getOrCreateStacktraces(input: GetOrCreateStacktracesRequest, options?: RpcOptions): UnaryCall<GetOrCreateStacktracesRequest, GetOrCreateStacktracesResponse> {
        const method = this.methods[3], opt = this._transport.mergeOptions(options);
        return stackIntercept<GetOrCreateStacktracesRequest, GetOrCreateStacktracesResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * UnsymbolizedLocations returns locations that can be symbolized but haven't
     * been asynchronously symbolized yet.
     *
     * @generated from protobuf rpc: UnsymbolizedLocations(parca.metastore.v1alpha1.UnsymbolizedLocationsRequest) returns (parca.metastore.v1alpha1.UnsymbolizedLocationsResponse);
     */
    unsymbolizedLocations(input: UnsymbolizedLocationsRequest, options?: RpcOptions): UnaryCall<UnsymbolizedLocationsRequest, UnsymbolizedLocationsResponse> {
        const method = this.methods[4], opt = this._transport.mergeOptions(options);
        return stackIntercept<UnsymbolizedLocationsRequest, UnsymbolizedLocationsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * CreateLocationLines creates the location lines contained in the provided
     * locations.
     *
     * @generated from protobuf rpc: CreateLocationLines(parca.metastore.v1alpha1.CreateLocationLinesRequest) returns (parca.metastore.v1alpha1.CreateLocationLinesResponse);
     */
    createLocationLines(input: CreateLocationLinesRequest, options?: RpcOptions): UnaryCall<CreateLocationLinesRequest, CreateLocationLinesResponse> {
        const method = this.methods[5], opt = this._transport.mergeOptions(options);
        return stackIntercept<CreateLocationLinesRequest, CreateLocationLinesResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Locations retrieves locations.
     *
     * @generated from protobuf rpc: Locations(parca.metastore.v1alpha1.LocationsRequest) returns (parca.metastore.v1alpha1.LocationsResponse);
     */
    locations(input: LocationsRequest, options?: RpcOptions): UnaryCall<LocationsRequest, LocationsResponse> {
        const method = this.methods[6], opt = this._transport.mergeOptions(options);
        return stackIntercept<LocationsRequest, LocationsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Functions retrieves functions.
     *
     * @generated from protobuf rpc: Functions(parca.metastore.v1alpha1.FunctionsRequest) returns (parca.metastore.v1alpha1.FunctionsResponse);
     */
    functions(input: FunctionsRequest, options?: RpcOptions): UnaryCall<FunctionsRequest, FunctionsResponse> {
        const method = this.methods[7], opt = this._transport.mergeOptions(options);
        return stackIntercept<FunctionsRequest, FunctionsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Mappings retrieves mappings.
     *
     * @generated from protobuf rpc: Mappings(parca.metastore.v1alpha1.MappingsRequest) returns (parca.metastore.v1alpha1.MappingsResponse);
     */
    mappings(input: MappingsRequest, options?: RpcOptions): UnaryCall<MappingsRequest, MappingsResponse> {
        const method = this.methods[8], opt = this._transport.mergeOptions(options);
        return stackIntercept<MappingsRequest, MappingsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Stacktraces retrieves mappings.
     *
     * @generated from protobuf rpc: Stacktraces(parca.metastore.v1alpha1.StacktracesRequest) returns (parca.metastore.v1alpha1.StacktracesResponse);
     */
    stacktraces(input: StacktracesRequest, options?: RpcOptions): UnaryCall<StacktracesRequest, StacktracesResponse> {
        const method = this.methods[9], opt = this._transport.mergeOptions(options);
        return stackIntercept<StacktracesRequest, StacktracesResponse>("unary", this._transport, method, opt, input);
    }
}
