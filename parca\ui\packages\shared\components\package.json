{"name": "@parca/components", "version": "0.16.349", "description": "A component library for Parca", "main": "dist/index.js", "scripts": {"test": "jest --coverage --config ../../../jest.config.js ./src/*", "prepublish": "pnpm run build", "build": "tsc && tailwindcss -o dist/styles.css --minify", "build-swc": "swc ./src -d dist --copy-files && tailwindcss -o dist/styles.css --minify", "watch": "tsc-watch --onCompilationComplete 'tailwindcss -o dist/styles.css'"}, "dependencies": {"@headlessui/react": "^1.7.19", "@iconify/react": "^4.0.0", "@parca/client": "workspace:*", "@parca/dynamicsize": "workspace:*", "@parca/hooks": "workspace:*", "@parca/icons": "workspace:*", "@parca/parser": "workspace:*", "@parca/store": "workspace:*", "@parca/utilities": "workspace:*", "@protobuf-ts/grpcweb-transport": "^2.5.0", "@protobuf-ts/runtime-rpc": "^2.5.0", "@tanstack/react-table": "^8.17.3", "@tanstack/react-virtual": "^3.5.0", "@tanstack/table-core": "^8.17.3", "@tanstack/virtual-core": "^3.5.0", "@types/d3-selection": "^3.0.10", "@types/lodash": "^4.17.0", "@types/react-datepicker": "^6.2.0", "classnames": "^2.3.1", "copy-to-clipboard": "^3.3.3", "d3": "7.9.0", "d3-selection": "3.0.0", "graphviz-wasm": "3.0.2", "lodash": "^4.17.21", "moment-timezone": "^0.6.0", "react-datepicker": "6.9.0", "react-popper": "^2.3.0", "react-textarea-autosize": "^8.4.0", "react-tooltip": "^5.26.3", "react-use": "^17.3.2", "tailwind-merge": "^1.10.0", "tailwindcss": "3.2.4", "tsc-watch": "6.3.1"}, "devDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}, "keywords": [], "author": "", "license": "ISC", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "f92c5502bce797d27d67f57a39f8af30d0d04e1e"}