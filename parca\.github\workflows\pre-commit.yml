name: pre-commit

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    branches:
      - main
      - release-*
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=pypi depName=pre-commit versioning=pep440
  PRE_COMMIT_VERSION: ==3.8.0

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - name: Check if pre-commit should run on all files
        id: run-all-files
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        continue-on-error: true
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              ".github/workflows/pre-commit.yml",
              ".go-version",
              ".pre-commit-config.yaml"
            ]
          skip_after_successful_duplicate: false

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version
          cache: false

      - name: Set golangci-lint cache key parts
        run: |
          echo "INTERVAL_KEY=$(( $(date +%s) / (7 * 86400) ))" >> $GITHUB_ENV
          # GH Actions hashFiles() function uses SHA512
          echo "GO_MOD_SHA1=$(sha1sum go.mod | cut -d' ' -f1)" >> $GITHUB_ENV

      # Same as https://github.com/golangci/golangci-lint-action#caching-internals
      - name: Set up golangci-lint cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            ~/.cache/golangci-lint
            ~/.cache/go-build
            ~/go/pkg
          # https://github.com/golangci/golangci-lint-action/blob/v3.2.0/src/cache.ts#L53-L69
          key: golangci-lint.cache-${{ env.INTERVAL_KEY }}-${{ env.GO_MOD_SHA1 }}
          restore-keys: |
            golangci-lint.cache-${{ env.INTERVAL_KEY }}-
            golangci-lint.cache-

      - name: Set up pre-commit
        run: pipx install "pre-commit${PRE_COMMIT_VERSION}"

      - name: Set pre-commit cache key parts
        run: |
          echo "GO_VERSION=$(<.go-version)" >> $GITHUB_ENV
          echo "PY_HASH=$(python -VV | sha256sum | cut -d' ' -f1)" >> $GITHUB_ENV

      - name: Set up pre-commit cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/.cache/pre-commit
          key: pre-commit-go${{ env.GO_VERSION }}-${{ env.PY_HASH }}-${{ hashFiles('.pre-commit-config.yaml') }}

      - name: Run pre-commit
        run: |
          declare -a EXTRA_ARGS=()
          if [[ '${{ steps.run-all-files.outputs.should_skip }}' != 'true' ]]; then
            EXTRA_ARGS=(--all-files)
          elif [[ "${GITHUB_EVENT_NAME}" == 'pull_request' ]]; then
            EXTRA_ARGS=(--from-ref "origin/${GITHUB_BASE_REF}" --to-ref "${GITHUB_SHA}")
          else
            EXTRA_ARGS=(--from-ref "${GITHUB_SHA}^" --to-ref "${GITHUB_SHA}")
          fi

          pre-commit run --show-diff-on-failure --color=always "${EXTRA_ARGS[@]}"

      - name: 'pre-commit-ci-lite: Apply automatic fixes'
        uses: pre-commit-ci/lite-action@5d6cc0eb514c891a40562a58a8e71576c5c7fb43 # v1.1.0
        if: always()
