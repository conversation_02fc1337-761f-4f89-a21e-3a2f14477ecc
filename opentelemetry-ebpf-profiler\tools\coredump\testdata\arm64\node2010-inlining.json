{"coredump-ref": "da4a92e5e012c96b2509583e4d9600096c3f8a9a1215270948ed045f4d397da0", "threads": [{"lwp": 8207, "frames": ["libc.so.6+0xd7d14", "node+0x17a6c1b", "node+0xd65c33", "node+0xd61d3b", "node+0xd61e4b", "V8::ExitFrame+0 in :0", "handleWriteReq+0 in node:internal/stream_base_commons:0", "writeGeneric+0 in node:internal/stream_base_commons:0", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+0 in node:internal/streams/writable:0", "_write+0 in node:internal/streams/writable:0", "Writable.write+0 in node:internal/streams/writable:0", "value+28 in node:internal/console/constructor:299", "warn+1 in node:internal/console/constructor:381", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xfba13f", "node+0xfbafbf", "node+0xe82e3f", "node+0xd8e33f", "V8::ExitFrame+0 in :0", "trace+6 in node:internal/console/constructor:427", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xfba13f", "node+0xfbafbf", "node+0xe82e3f", "node+0xd8e33f", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:3", "add3+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:8", "test+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:12", "submain+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:17", "main+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:23", "<anonymous>+26 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1267", "Module._extensions..js+45 in node:internal/modules/cjs/loader:1321", "Module.load+12 in node:internal/modules/cjs/loader:1125", "Module._load+71 in node:internal/modules/cjs/loader:965", "executeUserEntryPoint+8 in node:internal/modules/run_main:83", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xfba13f", "node+0xfbafbf", "node+0xe82e3f", "node+0xc4c9e7", "node+0xce6cef", "node+0xc285db", "node+0xc2aa07", "node+0xb9fd33", "node+0xcae117", "node+0xc29e03", "node+0xc2cceb", "libc.so.6+0x273fb", "libc.so.6+0x274cb", "node+0xb9a2ef"]}, {"lwp": 8208, "frames": ["libc.so.6+0xe5f3c", "node+0x17ab893", "node+0x179ab93", "node+0xcd9daf", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8209, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x17a855b", "node+0xcd5347", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8210, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x17a855b", "node+0xcd5347", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8211, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x17a855b", "node+0xcd5347", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8212, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x17a855b", "node+0xcd5347", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 8213, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x85a5b", "node+0x17a8387", "node+0xd80713", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}], "modules": [{"ref": "594545a9720b4a16973a823d18c71fbf070d3c07fb17b01df5376273e91644a1", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "22a0986a1047cd3c9a55368fdc6bb6e5a4455aceb53ec15dbe19112d95583642", "local-path": "/usr/lib/aarch64-linux-gnu/libpthread.so.0"}, {"ref": "f1935c0616a48e7ec471c26886df0411beffd4e56c92539d9a6a94641950badf", "local-path": "/usr/lib/aarch64-linux-gnu/libdl.so.2"}, {"ref": "acfe23cf0f5f4ac35b4c314415bb31d6f52a9bd39df8be04dfb2818a571533fd", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}, {"ref": "fd04b635d29b5cb3faaf502a6c5cd68a623d66a736f4d0561ff280c2fa411c79", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "779b30165fa16a24d8656af9d4c8ca9068bed57cab0e302331804dea568fd6ad", "local-path": "/home/<USER>/.nvm/versions/node/v20.1.0/bin/node"}, {"ref": "e647ee3042517f06cbebbbf0f66ba25486d0722222404f1d067683767a055566", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "c4834bd79254443665af96b6d5e71d124b7f92f2eea121e61f9fff5204fc594d", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30"}]}