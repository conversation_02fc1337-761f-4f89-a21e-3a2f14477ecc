// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package tpbase

import (
	"debug/elf"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExtractTSDInfo(t *testing.T) {
	testCases := map[string]struct {
		machine elf.Machine
		code    []byte
		info    TSDInfo
	}{
		"musl 1.2.3 / Alpine 3.16 / arm64": {
			machine: elf.EM_AARCH64,
			code: []byte{
				0x41, 0xd0, 0x3b, 0xd5, // mrs   x1, tpidr_el0
				0x21, 0x80, 0x5a, 0xf8, // ldur  x1, [x1, #-88]
				0x20, 0x58, 0x60, 0xf8, // ldr   x0, [x1, w0, uxtw #3]
				0xc0, 0x03, 0x5f, 0xd6, // ret
			},
			info: TSDInfo{
				Offset:     -88,
				Multiplier: 8,
				Indirect:   1,
			},
		},
		"glibc 2.35 / Fedora 36 / arm64": {
			machine: elf.EM_AARCH64,
			code: []byte{
				0x5f, 0x24, 0x03, 0xd5, // bti     c
				0xe1, 0x03, 0x00, 0x2a, // mov     w1, w0
				0x1f, 0x7c, 0x00, 0x71, // cmp     w0, #0x1f
				0x48, 0x02, 0x00, 0x54, // b.hi    85bb4 <__pthread_getspecific+0x54>
				0x20, 0x7c, 0x7c, 0xd3, // ubfiz   x0, x1, #4, #32
				0x42, 0xd0, 0x3b, 0xd5, // mrs     x2, tpidr_el0
				0x00, 0xc0, 0x1a, 0xd1, // sub     x0, x0, #0x6b0
				0x42, 0x00, 0x00, 0x8b, // add     x2, x2, x0
				0x40, 0x04, 0x40, 0xf9, // ldr     x0, [x2, #8]
				0x40, 0x01, 0x00, 0xb4, // cbz     x0, 85bac <__pthread_getspecific+0x4c>
				0x21, 0x7c, 0x7c, 0xd3, // ubfiz   x1, x1, #4, #32
				0xe3, 0x08, 0x00, 0xd0, // adrp    x3, 1a3000 <intr+0x60>
				0x63, 0x40, 0x0a, 0x91, // add     x3, x3, #0x290
				0x44, 0x00, 0x40, 0xf9, // ldr     x4, [x2]
				0x61, 0x68, 0x61, 0xf8, // ldr     x1, [x3, x1]
				0x3f, 0x00, 0x04, 0xeb, // cmp     x1, x4
				0x41, 0x00, 0x00, 0x54, // b.ne    85ba8 <__pthread_getspecific+0x48>
				0xc0, 0x03, 0x5f, 0xd6, // ret
				// code skipped handling keys >0x1f
			},
			info: TSDInfo{
				Offset:     -0x6b0 + 8,
				Multiplier: 0x10,
			},
		},
		"glibc 2.33 / Fedora 34 / arm64": {
			machine: elf.EM_AARCH64,
			code: []byte{
				0x5f, 0x24, 0x03, 0xd5, // bti     c
				0xe1, 0x03, 0x00, 0x2a, // mov     w1, w0
				0x1f, 0x7c, 0x00, 0x71, // cmp     w0, #0x1f
				0x48, 0x02, 0x00, 0x54, // b.hi    fb94 <__pthread_getspecific+0x54>  // b.pmore
				0x40, 0xd0, 0x3b, 0xd5, // mrs     x0, tpidr_el0
				0x22, 0x44, 0x00, 0x11, // add     w2, w1, #0x11
				0x00, 0x40, 0x1e, 0xd1, // sub     x0, x0, #0x790
				0x02, 0x10, 0x02, 0x8b, // add     x2, x0, x2, lsl #4
				0x40, 0x04, 0x40, 0xf9, // ldr     x0, [x2, #8]
				0x00, 0x01, 0x00, 0xb4, // cbz     x0, fb84 <__pthread_getspecific+0x44>
				0x21, 0x7c, 0x7c, 0xd3, // ubfiz   x1, x1, #4, #32
				0x03, 0x01, 0x00, 0xb0, // adrp    x3, 30000 <__nptl_nthreads>
				0x63, 0x80, 0x01, 0x91, // add     x3, x3, #0x60
				0x44, 0x00, 0x40, 0xf9, // ldr     x4, [x2]
				0x61, 0x68, 0x61, 0xf8, // ldr     x1, [x3, x1]
				0x3f, 0x00, 0x04, 0xeb, // cmp     x1, x4
				0x41, 0x00, 0x00, 0x54, // b.ne    fb88 <__pthread_getspecific+0x48>  // b.any
				0xc0, 0x03, 0x5f, 0xd6, // ret
				// code skipped handling keys >0x1f
			},
			info: TSDInfo{
				Offset:     -0x790 + (0x11 << 4) + 8,
				Multiplier: 0x10,
			},
		},
		"musl 1.2.3 / Alpine 3.16 / x86_64": {
			machine: elf.EM_X86_64,
			code: []byte{
				// mov    %fs:0x0,%rax
				// mov    0x80(%rax),%rax
				// mov    %edi,%edi
				// mov    (%rax,%rdi,8),%rax
				// ret
				0x64, 0x48, 0x8b, 0x04, 0x25, 0x00, 0x00, 0x00,
				0x00, 0x48, 0x8b, 0x80, 0x80, 0x00, 0x00, 0x00,
				0x89, 0xff, 0x48, 0x8b, 0x04, 0xf8, 0xc3,
			},
			info: TSDInfo{
				Offset:     0x80,
				Multiplier: 0x8,
				Indirect:   1,
			},
		},
		"musl 1.1.24 / Alpine 3.12 / x86_64": {
			machine: elf.EM_X86_64,
			code: []byte{
				// mov    %fs:0x0,%rax
				// mov    0x88(%rax),%rax
				// mov    %edi,%edi
				// mov    (%rax,%rdi,8),%rax
				// ret
				0x64, 0x48, 0x8b, 0x04, 0x25, 0x00, 0x00, 0x00,
				0x00, 0x48, 0x8b, 0x80, 0x88, 0x00, 0x00, 0x00,
				0x89, 0xff, 0x48, 0x8b, 0x04, 0xf8, 0xc3,
			},
			info: TSDInfo{
				Offset:     0x88,
				Multiplier: 0x8,
				Indirect:   1,
			},
		},
		"glibc 2.32 / Fedora 33 / x86_64": {
			machine: elf.EM_X86_64,
			code: []byte{
				// endbr64
				// cmp    $0x1f,%edi
				// ja     10bf0 <__pthread_getspecific+0x40>
				// lea    0x31(%rdi),%eax
				// shl    $0x4,%rax      # <- 0x31<<4 = 0x310, <<4 = *0x10
				// mov    %fs:0x10,%rdx
				// add    %rdx,%rax
				// mov    0x8(%rax),%r8  # <- +8
				// test   %r8,%r8
				// je     10beb <__pthread_getspecific+0x3b>
				// mov    %edi,%edi
				// lea    0xc4c2(%rip),%rdx # 1d0a0 <__GI___pthread_keys>
				// mov    (%rax),%rsi
				// shl    $0x4,%rdi
				// cmp    %rsi,(%rdx,%rdi,1)
				// jne    10c20 <__pthread_getspecific+0x70>
				// mov    %r8,%rax
				// retq
				// code skipped for handling keys >0x1f
				0xf3, 0x0f, 0x1e, 0xfa, 0x83, 0xff, 0x1f, 0x77,
				0x37, 0x8d, 0x47, 0x31, 0x48, 0xc1, 0xe0, 0x04,
				0x64, 0x48, 0x8b, 0x14, 0x25, 0x10, 0x00, 0x00,
				0x00, 0x48, 0x01, 0xd0, 0x4c, 0x8b, 0x40, 0x08,
				0x4d, 0x85, 0xc0, 0x74, 0x16, 0x89, 0xff, 0x48,
				0x8d, 0x15, 0xc2, 0xc4, 0x00, 0x00, 0x48, 0x8b,
				0x30, 0x48, 0xc1, 0xe7, 0x04, 0x48, 0x39, 0x34,
				0x3a, 0x75, 0x35, 0x4c, 0x89, 0xc0, 0xc3,
			},
			info: TSDInfo{
				Offset:     0x310 + 8,
				Multiplier: 0x10,
			},
		},
		"glibc 2.35  / Fedora 36 / x86_64": {
			machine: elf.EM_X86_64,
			code: []byte{
				// endbr64
				// cmp    $0x1f,%edi
				// ja     92a40 <__pthread_getspecific@GLIBC_2.2.5+0x40>
				// mov    %edi,%eax
				// add    $0x31,%rax
				// shl    $0x4,%rax
				// add    %fs:0x10,%rax
				// mov    0x8(%rax),%rdx
				// test   %rdx,%rdx
				// je     92a78 <__pthread_getspecific@GLIBC_2.2.5+0x78>
				// mov    %edi,%edi
				// lea    0x167b92(%rip),%rcx
				// mov    (%rax),%rsi
				// shl    $0x4,%rdi
				// cmp    %rsi,(%rcx,%rdi,1)
				// jne    92a70 <__pthread_getspecific@GLIBC_2.2.5+0x70>
				// mov    %rdx,%rax
				// ret
				// code skipped for handling keys >0x1f
				0xf3, 0x0f, 0x1e, 0xfa, 0x83, 0xff, 0x1f, 0x77,
				0x37, 0x89, 0xf8, 0x48, 0x83, 0xc0, 0x31, 0x48,
				0xc1, 0xe0, 0x04, 0x64, 0x48, 0x03, 0x04, 0x25,
				0x10, 0x00, 0x00, 0x00, 0x48, 0x8b, 0x50, 0x08,
				0x48, 0x85, 0xd2, 0x74, 0x53, 0x89, 0xff, 0x48,
				0x8d, 0x0d, 0x92, 0x7b, 0x16, 0x00, 0x48, 0x8b,
				0x30, 0x48, 0xc1, 0xe7, 0x04, 0x48, 0x39, 0x34,
				0x39, 0x75, 0x35, 0x48, 0x89, 0xd0, 0xc3,
			},
			info: TSDInfo{
				Offset:     0x310 + 8,
				Multiplier: 0x10,
			},
		},
		"glibc 2.38 / Fedora 39 / arm64": {
			machine: elf.EM_AARCH64,
			code: []byte{
				0x3f, 0x23, 0x03, 0xd5, // paciasp
				0xfd, 0x7b, 0xbf, 0xa9, // stp     x29, x30, [sp, #-16]!
				0xe1, 0x03, 0x00, 0x2a, // mov     w1, w0
				0xfd, 0x03, 0x00, 0x91, // mov     x29, sp
				0x1f, 0x7c, 0x00, 0x71, // cmp     w0, #0x1f
				// b.hi    91d98 <__pthread_getspecific@GLIBC_2.17+0x58>  // b.pmore
				0x28, 0x02, 0x00, 0x54,
				// mov     x0, #0xfffffffffffff9d0         // #-1584
				0xe0, 0xc5, 0x80, 0x92,
				0x42, 0xd0, 0x3b, 0xd5, // mrs     x2, tpidr_el0
				0x00, 0x50, 0x21, 0x8b, // add     x0, x0, w1, uxtw #4
				0x42, 0x00, 0x00, 0x8b, // add     x2, x2, x0
				0x40, 0x04, 0x40, 0xf9, // ldr     x0, [x2, #8]
				// cbz     x0, 91dcc <__pthread_getspecific@GLIBC_2.17+0x8c>
				0x00, 0x03, 0x00, 0xb4,
				0x21, 0x7c, 0x7c, 0xd3, // ubfiz   x1, x1, #4, #32
				0x83, 0x09, 0x00, 0xb0, // adrp    x3, 1c2000 <initial+0x198>
				0x63, 0x40, 0x17, 0x91, // add     x3, x3, #0x5d0
				0x44, 0x00, 0x40, 0xf9, // ldr     x4, [x2]
				0x61, 0x68, 0x61, 0xf8, // ldr     x1, [x3, x1]
				0x3f, 0x00, 0x04, 0xeb, // cmp     x1, x4
				// b.ne    91dc8 <__pthread_getspecific@GLIBC_2.17+0x88>  // b.any
				0x01, 0x02, 0x00, 0x54,
				0xfd, 0x7b, 0xc1, 0xa8, // ldp     x29, x30, [sp], #16
				0xbf, 0x23, 0x03, 0xd5, // autiasp
				0xc0, 0x03, 0x5f, 0xd6, // ret
				// code skipped handling keys >0x1f
			},
			info: TSDInfo{
				Offset:     -1584 + 8,
				Multiplier: 16,
			},
		},
		"booking coredump glibc": {
			machine: elf.EM_X86_64,
			code: []byte{
				0x83, 0xff, 0x1f, 0x77, 0x49, 0x89, 0xf8, 0x48, 0x83, 0xc0, 0x30, 0x48,
				0xc1, 0xe0, 0x04, 0x64, 0x48, 0x8b, 0x14, 0x25, 0x10, 0x00, 0x00, 0x00,
				0x48, 0x8d, 0x54, 0x02, 0x10, 0x48, 0x8b, 0x42, 0x08, 0x48, 0x85, 0xc0,
				0x74, 0x1a, 0x89, 0xff, 0x48, 0x8d, 0x0d, 0x61, 0xaa, 0x20, 0x00, 0x48,
				0xc1, 0xe7, 0x04, 0x48, 0x8b, 0x34, 0x39, 0x48, 0x39, 0x32, 0x75, 0x07,
				0xf3, 0xc3,
			},
			info: TSDInfo{
				Offset:     0x310 + 8,
				Multiplier: 0x10,
			},
		},
	}

	for name, test := range testCases {
		name := name
		test := test
		t.Run(name, func(t *testing.T) {
			var info TSDInfo
			var err error
			switch test.machine {
			case elf.EM_X86_64:
				info, err = ExtractTSDInfoX64_64(test.code)
			case elf.EM_AARCH64:
				info, err = ExtractTSDInfoARM64(test.code)
			}
			if errors.Is(err, errArchNotImplemented) {
				t.Skip("tests not available on this platform")
			}
			if assert.NoError(t, err) {
				assert.Equal(t, test.info, info, "Wrong TSD info extraction")
			}
		})
	}
}
