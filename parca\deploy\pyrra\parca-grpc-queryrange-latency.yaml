apiVersion: pyrra.dev/v1alpha1
kind: ServiceLevelObjective
metadata:
  name: parca-grpc-queryrange-latency
  namespace: parca
  labels:
    prometheus: k8s
    role: alert-rules
    pyrra.dev/team: parca
spec:
  target: '95'
  window: 2w
  indicator:
    latencyNative:
      latency: 3s
      total:
        metric: grpc_server_handling_seconds{grpc_service="parca.query.v1alpha1.QueryService",grpc_method="QueryRange"}
