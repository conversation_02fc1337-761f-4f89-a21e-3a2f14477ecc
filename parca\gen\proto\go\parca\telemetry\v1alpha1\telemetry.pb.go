// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/telemetry/v1alpha1/telemetry.proto

package telemetryv1alpha1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ReportPanicRequest contained the info about a panic.
type ReportPanicRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Stderr from the agent that exited with an error.
	Stderr string `protobuf:"bytes,1,opt,name=stderr,proto3" json:"stderr,omitempty"`
	// Agent metadata.
	Metadata      map[string]string `protobuf:"bytes,2,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportPanicRequest) Reset() {
	*x = ReportPanicRequest{}
	mi := &file_parca_telemetry_v1alpha1_telemetry_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportPanicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportPanicRequest) ProtoMessage() {}

func (x *ReportPanicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_telemetry_v1alpha1_telemetry_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportPanicRequest.ProtoReflect.Descriptor instead.
func (*ReportPanicRequest) Descriptor() ([]byte, []int) {
	return file_parca_telemetry_v1alpha1_telemetry_proto_rawDescGZIP(), []int{0}
}

func (x *ReportPanicRequest) GetStderr() string {
	if x != nil {
		return x.Stderr
	}
	return ""
}

func (x *ReportPanicRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// ReportPanicResponse contains the response for a ReportPanicRequest.
type ReportPanicResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportPanicResponse) Reset() {
	*x = ReportPanicResponse{}
	mi := &file_parca_telemetry_v1alpha1_telemetry_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportPanicResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportPanicResponse) ProtoMessage() {}

func (x *ReportPanicResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_telemetry_v1alpha1_telemetry_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportPanicResponse.ProtoReflect.Descriptor instead.
func (*ReportPanicResponse) Descriptor() ([]byte, []int) {
	return file_parca_telemetry_v1alpha1_telemetry_proto_rawDescGZIP(), []int{1}
}

var File_parca_telemetry_v1alpha1_telemetry_proto protoreflect.FileDescriptor

const file_parca_telemetry_v1alpha1_telemetry_proto_rawDesc = "" +
	"\n" +
	"(parca/telemetry/v1alpha1/telemetry.proto\x12\x18parca.telemetry.v1alpha1\x1a\x1cgoogle/api/annotations.proto\"\xc1\x01\n" +
	"\x12ReportPanicRequest\x12\x16\n" +
	"\x06stderr\x18\x01 \x01(\tR\x06stderr\x12V\n" +
	"\bmetadata\x18\x02 \x03(\v2:.parca.telemetry.v1alpha1.ReportPanicRequest.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x15\n" +
	"\x13ReportPanicResponse2\x9c\x01\n" +
	"\x10TelemetryService\x12\x87\x01\n" +
	"\vReportPanic\x12,.parca.telemetry.v1alpha1.ReportPanicRequest\x1a-.parca.telemetry.v1alpha1.ReportPanicResponse\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/telemetry/panicB\x84\x02\n" +
	"\x1ccom.parca.telemetry.v1alpha1B\x0eTelemetryProtoP\x01ZRgithub.com/parca-dev/parca/gen/proto/go/parca/telemetry/v1alpha1;telemetryv1alpha1\xa2\x02\x03PTX\xaa\x02\x18Parca.Telemetry.V1alpha1\xca\x02\x18Parca\\Telemetry\\V1alpha1\xe2\x02$Parca\\Telemetry\\V1alpha1\\GPBMetadata\xea\x02\x1aParca::Telemetry::V1alpha1b\x06proto3"

var (
	file_parca_telemetry_v1alpha1_telemetry_proto_rawDescOnce sync.Once
	file_parca_telemetry_v1alpha1_telemetry_proto_rawDescData []byte
)

func file_parca_telemetry_v1alpha1_telemetry_proto_rawDescGZIP() []byte {
	file_parca_telemetry_v1alpha1_telemetry_proto_rawDescOnce.Do(func() {
		file_parca_telemetry_v1alpha1_telemetry_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_telemetry_v1alpha1_telemetry_proto_rawDesc), len(file_parca_telemetry_v1alpha1_telemetry_proto_rawDesc)))
	})
	return file_parca_telemetry_v1alpha1_telemetry_proto_rawDescData
}

var file_parca_telemetry_v1alpha1_telemetry_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_parca_telemetry_v1alpha1_telemetry_proto_goTypes = []any{
	(*ReportPanicRequest)(nil),  // 0: parca.telemetry.v1alpha1.ReportPanicRequest
	(*ReportPanicResponse)(nil), // 1: parca.telemetry.v1alpha1.ReportPanicResponse
	nil,                         // 2: parca.telemetry.v1alpha1.ReportPanicRequest.MetadataEntry
}
var file_parca_telemetry_v1alpha1_telemetry_proto_depIdxs = []int32{
	2, // 0: parca.telemetry.v1alpha1.ReportPanicRequest.metadata:type_name -> parca.telemetry.v1alpha1.ReportPanicRequest.MetadataEntry
	0, // 1: parca.telemetry.v1alpha1.TelemetryService.ReportPanic:input_type -> parca.telemetry.v1alpha1.ReportPanicRequest
	1, // 2: parca.telemetry.v1alpha1.TelemetryService.ReportPanic:output_type -> parca.telemetry.v1alpha1.ReportPanicResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_parca_telemetry_v1alpha1_telemetry_proto_init() }
func file_parca_telemetry_v1alpha1_telemetry_proto_init() {
	if File_parca_telemetry_v1alpha1_telemetry_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_telemetry_v1alpha1_telemetry_proto_rawDesc), len(file_parca_telemetry_v1alpha1_telemetry_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parca_telemetry_v1alpha1_telemetry_proto_goTypes,
		DependencyIndexes: file_parca_telemetry_v1alpha1_telemetry_proto_depIdxs,
		MessageInfos:      file_parca_telemetry_v1alpha1_telemetry_proto_msgTypes,
	}.Build()
	File_parca_telemetry_v1alpha1_telemetry_proto = out.File
	file_parca_telemetry_v1alpha1_telemetry_proto_goTypes = nil
	file_parca_telemetry_v1alpha1_telemetry_proto_depIdxs = nil
}
