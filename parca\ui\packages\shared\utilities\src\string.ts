// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

export const startsWith = (str: string, prefix: string): boolean =>
  str.lastIndexOf(prefix, 0) === 0;

export const cutToMaxStringLength = (input: string, len: number): string => {
  if (input.length <= len) {
    return input;
  }
  return `${input.substring(0, len)}...`;
};
