syntax = "proto3";

package parca.share.v1alpha1;

import "parca/query/v1alpha1/query.proto";

option go_package = "github.com/parca-dev/parca/gen/go/share";

// Service that exposes APIs for sharing profiles.
service ShareService {
  // Uploads the profile and returns the link that can be used to access it.
  rpc Upload(UploadRequest) returns (UploadResponse) {}

  // Query performs a profile query
  rpc Query(QueryRequest) returns (QueryResponse) {}

  // ProfileTypes returns the list of available profile types.
  rpc ProfileTypes(ProfileTypesRequest) returns (ProfileTypesResponse) {}
}

// UploadRequest represents the request with profile bytes and description.
message UploadRequest {
  // pprof bytes of the profile to be uploaded.
  bytes profile = 1;

  // Description of the profile.
  string description = 2;
}

// UploadResponse represents the response with the link that can be used to access the profile.
message UploadResponse {
  // id of the uploaded profile.
  string id = 1;

  // link that can be used to access the profile.
  string link = 2;
}

// QueryRequest represents the request with the id of the profile to be queried.
message QueryRequest {
  // id of the profile to be queried.
  string id = 1;

  // Type of the profile to be queried.
  optional string profile_type = 2;

  // report_type is the type of report to return
  parca.query.v1alpha1.QueryRequest.ReportType report_type = 3;

  // filter_query is the query string to filter the profile samples
  optional string filter_query = 4 [deprecated = true];

  // node_trim_threshold is the threshold % where the nodes with Value less than this will be removed from the report
  optional float node_trim_threshold = 5;

  // which runtime frames to filter out, often interpreter frames like python or ruby are not super useful by default
  optional parca.query.v1alpha1.RuntimeFilter runtime_filter = 6 [deprecated = true];

  // group_by indicates the fields to group by
  optional parca.query.v1alpha1.GroupBy group_by = 7;

  // invert_call_stack inverts the call stacks in the flamegraph
  optional bool invert_call_stack = 8;

  // filter is a varying set of filter to apply to the query
  repeated parca.query.v1alpha1.Filter filter = 9;

  // sandwich_by_function is a function name to use for sandwich view functionality
  optional string sandwich_by_function = 10;
}

// ProfileTypesRequest represents the profile types request with the id of the profile to be queried.
message ProfileTypesRequest {
  // id of the profile's types to be queried.
  string id = 1;
}

// ProfileTypesResponse represents the response with the list of available profile types.
message ProfileTypesResponse {
  // list of available profile types.
  repeated parca.query.v1alpha1.ProfileType types = 1;

  // description of the profile uploaded.
  string description = 2;
}

// QueryResponse is the returned report for the given query.
message QueryResponse {
  // report is the generated report
  oneof report {
    // flamegraph is a flamegraph representation of the report
    parca.query.v1alpha1.Flamegraph flamegraph = 1;

    // pprof is a pprof profile as compressed bytes
    bytes pprof = 2;

    // top is a top list representation of the report
    parca.query.v1alpha1.Top top = 3;

    // callgraph is a callgraph nodes and edges representation of the report
    parca.query.v1alpha1.Callgraph callgraph = 4;

    // flamegraph_arrow is a flamegraph encoded as a arrow record
    parca.query.v1alpha1.FlamegraphArrow flamegraph_arrow = 7;

    // source is the source report type result
    parca.query.v1alpha1.Source source = 8;

    // table_arrow is a table encoded as a arrow record
    parca.query.v1alpha1.TableArrow table_arrow = 9;

    // profile_metadata contains metadata about the profile i.e. binaries, labels
    parca.query.v1alpha1.ProfileMetadata profile_metadata = 10;
  }

  // total is the total number of samples shown in the report.
  int64 total = 5;

  // filtered is the number of samples filtered out of the report.
  int64 filtered = 6;
}
