// Network byte order types that might be missing - define before includes
typedef unsigned short __be16;
typedef unsigned int __be32;
typedef unsigned int __wsum;

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_endian.h>

// BPF constants that might be missing from vmlinux.h
#ifndef BPF_MAP_TYPE_PERF_EVENT_ARRAY
#define BPF_MAP_TYPE_PERF_EVENT_ARRAY 4
#endif

#ifndef BPF_MAP_TYPE_HASH
#define BPF_MAP_TYPE_HASH 1
#endif

#ifndef BPF_F_USER_STACK
#define BPF_F_USER_STACK (1U << 8)
#endif

#ifndef BPF_ANY
#define BPF_ANY 0
#endif

#ifndef BPF_F_CURRENT_CPU
#define BPF_F_CURRENT_CPU 0xffffffffULL
#endif

// Process event types (following coroot pattern)
#define EVENT_TYPE_PROCESS_START 1
#define EVENT_TYPE_PROCESS_EXIT 2

// Clone flags for process vs thread detection
#ifndef CLONE_THREAD
#define CLONE_THREAD 0x00010000
#endif

// Event types for function tracing
#define EVENT_TYPE_PROCESS_START	    1
#define EVENT_TYPE_PROCESS_EXIT		    2
#define EVENT_TYPE_CONNECTION_OPEN	    3
#define EVENT_TYPE_CONNECTION_CLOSE	    4
#define EVENT_TYPE_CONNECTION_ERROR	    5
#define EVENT_TYPE_LISTEN_OPEN		    6
#define EVENT_TYPE_LISTEN_CLOSE 	    7
#define EVENT_TYPE_FILE_OPEN		    8
#define EVENT_TYPE_TCP_RETRANSMIT	    9
#define EVENT_TYPE_PYTHON_THREAD_LOCK	11

// New function tracing event types
#define EVENT_TYPE_FUNCTION_ENTRY       20
#define EVENT_TYPE_FUNCTION_EXIT        21
#define EVENT_TYPE_STACK_TRACE          22
#define EVENT_TYPE_FUNCTION_ERROR       23

#define EVENT_REASON_OOM_KILL		1

#define MIN(a,b) (((a)<(b))?(a):(b))

#define bpf_read(src, dst)                            \
({                                                    \
    if (bpf_probe_read(&dst, sizeof(dst), src) < 0) { \
        return 0;                                     \
    }                                                 \
})

// bpf_printk is already defined in bpf_helpers.h, so we don't redefine it

struct trace_event_raw_sys_exit__stub {
	__u64 unused;
	__u64 unused2;
	long int ret;
};

// Function event structure - following coroot pattern (SMALL AND SIMPLE)
struct function_event {
    __u32 type;
    __u32 pid;
    __u64 timestamp;
    __u64 function_addr;
    __u64 args[3];          // Only first 3 args to keep struct small
};

// BPF Maps
struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(__u32));
    __uint(value_size, sizeof(__u32));
} function_events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1000);
    __type(key, __u32);     // PID
    __type(value, __u8);    // Enabled flag
} traced_pids SEC(".maps");

// Helper functions
static __always_inline __u64 get_pid_tgid(void) {
    return bpf_get_current_pid_tgid();
}

static __always_inline __u32 get_pid(void) {
    return bpf_get_current_pid_tgid() >> 32;
}

static __always_inline __u32 get_tid(void) {
    return bpf_get_current_pid_tgid() & 0xffffffff;
}

static __always_inline int should_trace_pid(__u32 pid) {
    __u8 *enabled = bpf_map_lookup_elem(&traced_pids, &pid);
    return enabled && *enabled;
}

// Removed complex stack trace function - keeping it simple like coroot

// Function entry uprobe
SEC("uprobe/function_entry")
int trace_function_entry(struct pt_regs *ctx) {
    __u32 pid = get_pid();
    __u32 tid = get_tid();
    __u64 pid_tgid = get_pid_tgid();
    
    // Check if we should trace this PID
    if (!should_trace_pid(pid)) {
        return 0;
    }
    
    struct function_event event = {};
    event.type = EVENT_TYPE_FUNCTION_ENTRY;
    event.pid = pid;
    event.timestamp = bpf_ktime_get_ns();
    event.function_addr = PT_REGS_IP(ctx);
    
    // Capture function arguments from registers (x86_64 calling convention)
    event.args[0] = PT_REGS_PARM1(ctx);  // RDI
    event.args[1] = PT_REGS_PARM2(ctx);  // RSI
    event.args[2] = PT_REGS_PARM3(ctx);  // RDX
    
    // Send entry event
    bpf_perf_event_output(ctx, &function_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// Removed complex exit function - keeping it simple like coroot

// Process start/exit events (following coroot pattern exactly)
struct proc_event {
    __u32 type;
    __u32 pid;
    __u32 reason;
};

struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(__u32));
    __uint(value_size, sizeof(__u32));
} proc_events SEC(".maps");

// Tracepoint structure for task_newtask (from coroot)
struct trace_event_raw_task_newtask__stub {
    __u64 unused;
    __u32 clone_flags;
    __u32 pid;
};

SEC("tracepoint/task/task_newtask")
int trace_task_newtask(struct trace_event_raw_task_newtask__stub *args) {
    // Skip threads, only track processes (following coroot pattern)
    if (args->clone_flags & CLONE_THREAD) {
        return 0;
    }

    struct proc_event event = {};
    event.type = EVENT_TYPE_PROCESS_START;
    event.pid = args->pid;
    event.reason = 0;

    bpf_perf_event_output(args, &proc_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    return 0;
}

// Process exit detection (following coroot pattern)
struct trace_event_raw_sched_process_template__stub {
    __u64 unused;
    char comm[16]; // TASK_COMM_LEN
    __u32 pid;
};

SEC("tracepoint/sched/sched_process_exit")
int trace_sched_process_exit(struct trace_event_raw_sched_process_template__stub *args) {
    struct proc_event event = {};
    event.type = EVENT_TYPE_PROCESS_EXIT;
    event.pid = args->pid;
    event.reason = 0;

    bpf_perf_event_output(args, &proc_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    return 0;
}

char _license[] SEC("license") = "GPL";
