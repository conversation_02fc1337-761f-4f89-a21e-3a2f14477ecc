// Define target architecture for BPF
#define __TARGET_ARCH_x86

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_endian.h>

// BPF constants that might be missing
#ifndef BPF_MAP_TYPE_PERF_EVENT_ARRAY
#define BPF_MAP_TYPE_PERF_EVENT_ARRAY 4
#endif

#ifndef BPF_MAP_TYPE_HASH
#define BPF_MAP_TYPE_HASH 1
#endif

#ifndef BPF_F_USER_STACK
#define BPF_F_USER_STACK (1U << 8)
#endif

#ifndef BPF_ANY
#define BPF_ANY 0
#endif

#ifndef BPF_F_CURRENT_CPU
#define BPF_F_CURRENT_CPU 0xffffffffULL
#endif

// Event types for function tracing
#define EVENT_TYPE_PROCESS_START	    1
#define EVENT_TYPE_PROCESS_EXIT		    2
#define EVENT_TYPE_CONNECTION_OPEN	    3
#define EVENT_TYPE_CONNECTION_CLOSE	    4
#define EVENT_TYPE_CONNECTION_ERROR	    5
#define EVENT_TYPE_LISTEN_OPEN		    6
#define EVENT_TYPE_LISTEN_CLOSE 	    7
#define EVENT_TYPE_FILE_OPEN		    8
#define EVENT_TYPE_TCP_RETRANSMIT	    9
#define EVENT_TYPE_PYTHON_THREAD_LOCK	11

// New function tracing event types
#define EVENT_TYPE_FUNCTION_ENTRY       20
#define EVENT_TYPE_FUNCTION_EXIT        21
#define EVENT_TYPE_STACK_TRACE          22
#define EVENT_TYPE_FUNCTION_ERROR       23

#define EVENT_REASON_OOM_KILL		1

#define MIN(a,b) (((a)<(b))?(a):(b))

#define bpf_read(src, dst)                            \
({                                                    \
    if (bpf_probe_read(&dst, sizeof(dst), src) < 0) { \
        return 0;                                     \
    }                                                 \
})

#define bpf_printk(fmt, ...)                                   \
({                                                             \
    char ____fmt[] = fmt;                                      \
    bpf_trace_printk(____fmt, sizeof(____fmt), ##__VA_ARGS__); \
})

struct trace_event_raw_sys_exit__stub {
	__u64 unused;
	__u64 unused2;
	long int ret;
};

// Function event structure
struct function_event {
    __u32 type;
    __u32 pid;
    __u64 timestamp;
    __u64 duration;
    __u64 function_addr;
    __u64 return_addr;
    __u64 args[6];          // RDI, RSI, RDX, RCX, R8, R9
    __u64 return_value;
    __u32 stack_depth;
    __u64 stack_trace[64];
};

// BPF Maps
struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(__u32));
    __uint(value_size, sizeof(__u32));
} function_events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 10000);
    __type(key, __u64);     // PID + TID
    __type(value, struct function_event);
} active_function_calls SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1000);
    __type(key, __u32);     // PID
    __type(value, __u8);    // Enabled flag
} traced_pids SEC(".maps");

// Helper functions
static __always_inline __u64 get_pid_tgid(void) {
    return bpf_get_current_pid_tgid();
}

static __always_inline __u32 get_pid(void) {
    return bpf_get_current_pid_tgid() >> 32;
}

static __always_inline __u32 get_tid(void) {
    return bpf_get_current_pid_tgid() & 0xffffffff;
}

static __always_inline int should_trace_pid(__u32 pid) {
    __u8 *enabled = bpf_map_lookup_elem(&traced_pids, &pid);
    return enabled && *enabled;
}

static __always_inline int capture_stack_trace(struct function_event *event) {
    // Capture user stack trace
    int stack_size = bpf_get_stack(bpf_get_current_task(), 
                                   event->stack_trace, 
                                   sizeof(event->stack_trace), 
                                   BPF_F_USER_STACK);
    
    if (stack_size > 0) {
        event->stack_depth = stack_size / sizeof(__u64);
        if (event->stack_depth > 64) {
            event->stack_depth = 64;
        }
    } else {
        event->stack_depth = 0;
    }
    
    return stack_size > 0 ? 0 : -1;
}

// Function entry uprobe
SEC("uprobe/function_entry")
int trace_function_entry(struct pt_regs *ctx) {
    __u32 pid = get_pid();
    __u32 tid = get_tid();
    __u64 pid_tgid = get_pid_tgid();
    
    // Check if we should trace this PID
    if (!should_trace_pid(pid)) {
        return 0;
    }
    
    struct function_event event = {};
    event.type = EVENT_TYPE_FUNCTION_ENTRY;
    event.pid = pid;
    event.timestamp = bpf_ktime_get_ns();
    event.function_addr = PT_REGS_IP(ctx);
    
    // Capture function arguments from registers (x86_64 calling convention)
    event.args[0] = PT_REGS_PARM1(ctx);  // RDI
    event.args[1] = PT_REGS_PARM2(ctx);  // RSI
    event.args[2] = PT_REGS_PARM3(ctx);  // RDX
    event.args[3] = PT_REGS_PARM4(ctx);  // RCX
    event.args[4] = PT_REGS_PARM5(ctx);  // R8
    event.args[5] = PT_REGS_PARM6(ctx);  // R9
    
    // Capture stack trace
    capture_stack_trace(&event);
    
    // Store entry event for matching with exit
    bpf_map_update_elem(&active_function_calls, &pid_tgid, &event, BPF_ANY);
    
    // Send entry event
    bpf_perf_event_output(ctx, &function_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// Function exit uretprobe
SEC("uretprobe/function_exit")
int trace_function_exit(struct pt_regs *ctx) {
    __u32 pid = get_pid();
    __u32 tid = get_tid();
    __u64 pid_tgid = get_pid_tgid();
    
    // Check if we should trace this PID
    if (!should_trace_pid(pid)) {
        return 0;
    }
    
    // Look up the entry event
    struct function_event *entry_event = bpf_map_lookup_elem(&active_function_calls, &pid_tgid);
    if (!entry_event) {
        return 0; // No matching entry event
    }
    
    struct function_event exit_event = {};
    exit_event.type = EVENT_TYPE_FUNCTION_EXIT;
    exit_event.pid = pid;
    exit_event.timestamp = bpf_ktime_get_ns();
    exit_event.function_addr = entry_event->function_addr;
    exit_event.return_addr = PT_REGS_IP(ctx);
    
    // Calculate duration
    exit_event.duration = exit_event.timestamp - entry_event->timestamp;
    
    // Get return value
    exit_event.return_value = PT_REGS_RC(ctx);
    
    // Copy arguments from entry event
    for (int i = 0; i < 6; i++) {
        exit_event.args[i] = entry_event->args[i];
    }
    
    // Capture stack trace for exit
    capture_stack_trace(&exit_event);
    
    // Send exit event
    bpf_perf_event_output(ctx, &function_events, BPF_F_CURRENT_CPU, &exit_event, sizeof(exit_event));
    
    // Clean up entry event
    bpf_map_delete_elem(&active_function_calls, &pid_tgid);
    
    return 0;
}

// Process start/exit events (from original coroot)
struct proc_event {
    __u32 type;
    __u32 pid;
    __u32 reason;
};

struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(__u32));
    __uint(value_size, sizeof(__u32));
} proc_events SEC(".maps");

SEC("tracepoint/task/task_newtask")
int trace_task_newtask(void *ctx) {
    struct proc_event event = {};
    event.type = EVENT_TYPE_PROCESS_START;
    event.pid = get_pid();
    event.reason = 0;
    
    bpf_perf_event_output(ctx, &proc_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    return 0;
}

SEC("tracepoint/sched/sched_process_exit")
int trace_sched_process_exit(void *ctx) {
    struct proc_event event = {};
    event.type = EVENT_TYPE_PROCESS_EXIT;
    event.pid = get_pid();
    event.reason = 0;
    
    bpf_perf_event_output(ctx, &proc_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    return 0;
}

char _license[] SEC("license") = "GPL";
