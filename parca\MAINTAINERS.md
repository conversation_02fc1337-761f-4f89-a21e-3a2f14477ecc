Document that refers to a specific project within the Parca ecosystem
is maintained by the maintainers of the respective project. For example, refer
to the maintainers specified in Parca Agent's
[MAINTAINERS.md](https://github.com/parca/parca-agent/blob/main/MAINTAINERS.md)
file for the Parca Agent.

Note that the documentation for the Parca server is located in the [parca.dev website](https://www.parca.dev/docs/overview).

Refer to the following maintainers with their focus areas:

- <PERSON> <https://github.com/brancz> @brancz: Everything related to the Parca project (storage, backend, frontend, agent).
- <PERSON> <https://github.com/thorfour> @thorfour: Storage and backend. (Please do not disturb him with questions about the frontend).
- <PERSON> <https://github.com/metalmatze> @metalmatze: Everything related to the Parca server (storage, backend, frontend).
- <PERSON><PERSON> <https://github.com/yomete> @yomete: Everything related to frontend (website, design).
- <PERSON> <https://github.com/monicawoj> @monicawoj: Everything related to frontend (website, design).
- <PERSON><PERSON><PERSON> <https://github.com/manojVivek> @manojvivek: Everything related to frontend (website, design).

### Emeritus Maintainers

- Kemal Akkoyun <https://github.com/kakkoyun> @kakkoyun: Backend, especially symbolization and debug information related components.
