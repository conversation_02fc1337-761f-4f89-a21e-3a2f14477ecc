name: Release (Dry Run)

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    branches:
      - main
      - release-*
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=go depName=github.com/goreleaser/goreleaser
  GORELEASER_VERSION: v1.26.2
  # renovate: datasource=npm depName=pnpm versioning=npm
  PNPM_VERSION: '10.13.1'

permissions:
  contents: write

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              "**.go",
              ".github/workflows/release-dry-run.yml",
              ".goreleaser.yml",
              ".go-version",
              ".node-version",
              "cmd/**",
              "gen/**",
              "go.mod",
              "go.sum",
              "pkg/**",
              "ui/**"
            ]
          skip_after_successful_duplicate: false

  goreleaser:
    name: Release (Dry Run)
    needs: skip-check
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 40
    permissions:
      contents: read
    env:
      DOCKER_CLI_EXPERIMENTAL: "enabled"
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Free up disk space
        run: |
          ./scripts/free_disk_space.sh

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version

      - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version-file: .node-version
          cache: 'pnpm'
          cache-dependency-path: ui/pnpm-lock.yaml

      - name: Set up Snapcraft
        run: |
          sudo snap install snapcraft --channel=7.x/stable --classic
          # See https://github.com/goreleaser/goreleaser/issues/1715
          mkdir -p "$HOME/.cache/snapcraft/download"
          mkdir -p "$HOME/.cache/snapcraft/stage-packages"

      - name: Set up environment
        run: |
          ./env.sh

      - name: Validate
        uses: goreleaser/goreleaser-action@5fdedb94abba051217030cc86d4523cf3f02243d # v4.6.0
        with:
          distribution: goreleaser
          version: ${{ env.GORELEASER_VERSION }}
          args: check

      - name: Set Tag
        run: |
          echo "goreleaser_current_tag=`git describe --match 'v*' --tags`" >> $GITHUB_ENV

      - name: Dry Run
        uses: goreleaser/goreleaser-action@5fdedb94abba051217030cc86d4523cf3f02243d # v4.6.0
        with:
          distribution: goreleaser
          version: ${{ env.GORELEASER_VERSION }}
          args: release --clean --skip=validate --skip-publish --timeout=60m
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GORELEASER_CURRENT_TAG: "${{ env.goreleaser_current_tag }}"
