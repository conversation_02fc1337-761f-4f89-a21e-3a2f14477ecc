package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v2"
	"k8s.io/klog/v2"
)

// Config represents the main configuration structure (following Parca/coroot pattern)
type Config struct {
	General   GeneralConfig   `yaml:"general"`
	Filtering FilteringConfig `yaml:"filtering"`
	Sampling  SamplingConfig  `yaml:"sampling"`
	Output    OutputConfig    `yaml:"output"`
}

// GeneralConfig contains general tracer settings
type GeneralConfig struct {
	Enabled           bool   `yaml:"enabled"`
	LogLevel          string `yaml:"log_level"`
	ProcessName       string `yaml:"process_name"`
	GracefulShutdown  bool   `yaml:"graceful_shutdown"`
	ConfigFile        string `yaml:"config_file"`
}

// FilteringConfig contains process and path filtering settings (following Parca/coroot pattern)
type FilteringConfig struct {
	Enabled         bool            `yaml:"enabled"`
	ProcessFilters  ProcessFilters  `yaml:"process_filters"`
	PathFilters     PathFilters     `yaml:"path_filters"`
	SystemFilters   SystemFilters   `yaml:"system_filters"`
}

// ProcessFilters contains process-based filtering (following coroot-node-agent pattern)
type ProcessFilters struct {
	// Include only these PIDs (empty = all)
	IncludePIDs []uint32 `yaml:"include_pids"`
	
	// Exclude these PIDs
	ExcludePIDs []uint32 `yaml:"exclude_pids"`
	
	// Include only these process names (empty = all)
	IncludeProcessNames []string `yaml:"include_process_names"`
	
	// Exclude these process names (supports glob patterns)
	ExcludeProcessNames []string `yaml:"exclude_process_names"`
	
	// Minimum PID to consider (filter out kernel threads)
	MinPID uint32 `yaml:"min_pid"`
	
	// Maximum PID to consider
	MaxPID uint32 `yaml:"max_pid"`
}

// PathFilters contains path-based filtering (following Parca/coroot pattern)
type PathFilters struct {
	// Include only these binary paths (empty = all, supports glob patterns)
	IncludePaths []string `yaml:"include_paths"`
	
	// Exclude these binary paths (supports glob patterns)
	ExcludePaths []string `yaml:"exclude_paths"`
	
	// Exclude these directory patterns
	ExcludeDirectories []string `yaml:"exclude_directories"`
}

// SystemFilters contains system-level filtering
type SystemFilters struct {
	// Skip kernel threads
	SkipKernelThreads bool `yaml:"skip_kernel_threads"`
	
	// Skip system processes
	SkipSystemProcesses bool `yaml:"skip_system_processes"`
	
	// Skip container runtime processes
	SkipContainerRuntime bool `yaml:"skip_container_runtime"`
	
	// Skip self and parent processes
	SkipSelfProcesses bool `yaml:"skip_self_processes"`
}

// SamplingConfig contains sampling settings
type SamplingConfig struct {
	Enabled              bool    `yaml:"enabled"`
	Rate                 float64 `yaml:"rate"`
	MaxEventsPerSecond   int     `yaml:"max_events_per_second"`
	AdaptiveSampling     bool    `yaml:"adaptive_sampling"`
}

// OutputConfig contains output settings
type OutputConfig struct {
	Format      string `yaml:"format"`
	Destination string `yaml:"destination"`
	BufferSize  int    `yaml:"buffer_size"`
	FlushInterval string `yaml:"flush_interval"`
}

// Global configuration instance
var GlobalConfig *Config

// DefaultConfig returns the default configuration (following Parca/coroot pattern)
func DefaultConfig() *Config {
	return &Config{
		General: GeneralConfig{
			Enabled:          true,
			LogLevel:         "info",
			ProcessName:      "deep-ebpf-node",
			GracefulShutdown: true,
			ConfigFile:       "config.yaml",
		},
		Filtering: FilteringConfig{
			Enabled: true,
			ProcessFilters: ProcessFilters{
				IncludePIDs:         []uint32{},
				ExcludePIDs:         []uint32{},
				IncludeProcessNames: []string{},
				ExcludeProcessNames: []string{
					"systemd*",
					"kthreadd",
					"ksoftirqd*",
					"migration*",
					"rcu_*",
					"watchdog*",
					"deep-ebpf-node*",
					"redis*",
				},
				MinPID: 1,
				MaxPID: 65535,
			},
			PathFilters: PathFilters{
				IncludePaths: []string{},
				ExcludePaths: []string{
					"/init",
					"/usr/lib/*",
					"/usr/bin/*",
					"/usr/sbin/*",
					"/usr/libexec/*",
					"/lib/*",
					"/lib64/*",
					"/sbin/*",
				},
				ExcludeDirectories: []string{
					"/init",
					"/usr/lib",
					"/usr/bin",
					"/usr/sbin",
					"/usr/libexec",
					"/lib",
					"/lib64",
					"/sbin",
				},
			},
			SystemFilters: SystemFilters{
				SkipKernelThreads:    true,
				SkipSystemProcesses:  true,
				SkipContainerRuntime: true,
				SkipSelfProcesses:    true,
			},
		},
		Sampling: SamplingConfig{
			Enabled:            false,
			Rate:               1.0,
			MaxEventsPerSecond: 0,
			AdaptiveSampling:   false,
		},
		Output: OutputConfig{
			Format:        "human",
			Destination:   "stdout",
			BufferSize:    1024,
			FlushInterval: "5s",
		},
	}
}

// LoadConfig loads configuration from file (following Parca/coroot pattern)
func LoadConfig(configFile string) (*Config, error) {
	cfg := DefaultConfig()
	
	// If config file doesn't exist, use defaults
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		klog.Infof("📋 CONFIG: Using default configuration (no config file found: %s)", configFile)
		return cfg, nil
	}
	
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %v", configFile, err)
	}
	
	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file %s: %v", configFile, err)
	}
	
	klog.Infof("📋 CONFIG: Loaded configuration from %s", configFile)
	return cfg, nil
}

// InitConfig initializes the global configuration
func InitConfig(configFile string) error {
	var err error
	GlobalConfig, err = LoadConfig(configFile)
	if err != nil {
		return err
	}
	
	klog.Infof("📋 CONFIG: Filtering enabled: %v", GlobalConfig.Filtering.Enabled)
	klog.Infof("📋 CONFIG: Process exclusions: %v", GlobalConfig.Filtering.ProcessFilters.ExcludeProcessNames)
	klog.Infof("📋 CONFIG: Path exclusions: %v", GlobalConfig.Filtering.PathFilters.ExcludePaths)
	
	return nil
}

// ShouldSkipProcess checks if a process should be skipped based on configuration (following coroot pattern)
func (c *Config) ShouldSkipProcess(pid uint32, processName, binaryPath string) bool {
	if !c.Filtering.Enabled {
		return false
	}
	
	// Check PID range
	if pid < c.Filtering.ProcessFilters.MinPID || pid > c.Filtering.ProcessFilters.MaxPID {
		return true
	}
	
	// Check PID exclusions
	for _, excludePID := range c.Filtering.ProcessFilters.ExcludePIDs {
		if pid == excludePID {
			return true
		}
	}
	
	// Check PID inclusions (if specified, only include these PIDs)
	if len(c.Filtering.ProcessFilters.IncludePIDs) > 0 {
		found := false
		for _, includePID := range c.Filtering.ProcessFilters.IncludePIDs {
			if pid == includePID {
				found = true
				break
			}
		}
		if !found {
			return true
		}
	}
	
	// Check process name exclusions (with glob pattern support)
	for _, excludePattern := range c.Filtering.ProcessFilters.ExcludeProcessNames {
		if matched, _ := filepath.Match(excludePattern, processName); matched {
			return true
		}
	}
	
	// Check process name inclusions (if specified, only include these processes)
	if len(c.Filtering.ProcessFilters.IncludeProcessNames) > 0 {
		found := false
		for _, includePattern := range c.Filtering.ProcessFilters.IncludeProcessNames {
			if matched, _ := filepath.Match(includePattern, processName); matched {
				found = true
				break
			}
		}
		if !found {
			return true
		}
	}
	
	// Check binary path exclusions (with glob pattern support)
	for _, excludePattern := range c.Filtering.PathFilters.ExcludePaths {
		if matched, _ := filepath.Match(excludePattern, binaryPath); matched {
			return true
		}
	}
	
	// Check directory exclusions
	for _, excludeDir := range c.Filtering.PathFilters.ExcludeDirectories {
		if strings.HasPrefix(binaryPath, excludeDir) {
			return true
		}
	}
	
	// Check binary path inclusions (if specified, only include these paths)
	if len(c.Filtering.PathFilters.IncludePaths) > 0 {
		found := false
		for _, includePattern := range c.Filtering.PathFilters.IncludePaths {
			if matched, _ := filepath.Match(includePattern, binaryPath); matched {
				found = true
				break
			}
		}
		if !found {
			return true
		}
	}
	
	return false
}

// IsSystemProcess checks if a process is a system process (following coroot pattern)
func (c *Config) IsSystemProcess(processName, binaryPath string) bool {
	if !c.Filtering.SystemFilters.SkipSystemProcesses {
		return false
	}
	
	systemPatterns := []string{
		"systemd*",
		"kthreadd",
		"ksoftirqd*",
		"migration*",
		"rcu_*",
		"watchdog*",
		"init",
		"kernel*",
	}
	
	for _, pattern := range systemPatterns {
		if matched, _ := filepath.Match(pattern, processName); matched {
			return true
		}
	}
	
	systemPaths := []string{
		"/usr/lib/systemd",
		"/lib/systemd",
		"/sbin",
		"/usr/sbin",
	}
	
	for _, path := range systemPaths {
		if strings.HasPrefix(binaryPath, path) {
			return true
		}
	}
	
	return false
}
