{"coredump-ref": "4f775111099ba567307d199529fa17eb3ae961e6d44c7f5fee2b1ee074f03628", "threads": [{"lwp": 7887, "frames": ["libc.so.6+0xd7d14", "node+0x14c5e37", "node+0xbc6457", "node+0xbc16cf", "node+0xbc1ad3", "V8::ExitFrame+0 in :0", "handleWriteReq+16 in node:internal/stream_base_commons:61", "writeGeneric+2 in node:internal/stream_base_commons:153", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+24 in node:internal/streams/writable:389", "_write+47 in node:internal/streams/writable:330", "Writable.write+1 in node:internal/streams/writable:334", "value+28 in node:internal/console/constructor:285", "warn+1 in node:internal/console/constructor:364", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xdee9d7", "node+0xdef51b", "node+0xce14d3", "node+0xbf128b", "V8::ExitFrame+0 in :0", "trace+6 in node:internal/console/constructor:410", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xdee9d7", "node+0xdef51b", "node+0xce14d3", "node+0xbf128b", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:3", "add3+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:8", "test+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:12", "submain+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:17", "main+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:23", "<anonymous>+26 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1101", "Module._extensions..js+43 in node:internal/modules/cjs/loader:1153", "Module.load+12 in node:internal/modules/cjs/loader:981", "Module._load+65 in node:internal/modules/cjs/loader:822", "executeUserEntryPoint+0 in node:internal/modules/run_main:0", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xdee9d7", "node+0xdef51b", "node+0xce14d3", "node+0xab8f17", "node+0xab9137", "node+0xaba40f", "node+0xa46457", "node+0xb2e987", "node+0xabc237", "libc.so.6+0x273fb", "libc.so.6+0x274cb", "node+0xa41c3b"]}, {"lwp": 7888, "frames": ["libc.so.6+0xe5f3c", "node+0x14cc1d3", "node+0x14bb6bf", "node+0xb5a0b7", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7889, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14c8bfb", "node+0xb55947", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7890, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14c8bfb", "node+0xb55947", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7891, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14c8bfb", "node+0xb55947", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7892, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14c8bfb", "node+0xb55947", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7893, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x85a5b", "node+0x14c89e3", "node+0xbe4f6f", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}], "modules": [{"ref": "f1935c0616a48e7ec471c26886df0411beffd4e56c92539d9a6a94641950badf", "local-path": "/usr/lib/aarch64-linux-gnu/libdl.so.2"}, {"ref": "594545a9720b4a16973a823d18c71fbf070d3c07fb17b01df5376273e91644a1", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "fd04b635d29b5cb3faaf502a6c5cd68a623d66a736f4d0561ff280c2fa411c79", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "c4834bd79254443665af96b6d5e71d124b7f92f2eea121e61f9fff5204fc594d", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30"}, {"ref": "e647ee3042517f06cbebbbf0f66ba25486d0722222404f1d067683767a055566", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "8fe9e4297ec52bd22f6e31a227a7a15d7bdff7a01429c37011a173c8d5386669", "local-path": "/home/<USER>/.nvm/versions/node/v16.11.0/bin/node"}, {"ref": "22a0986a1047cd3c9a55368fdc6bb6e5a4455aceb53ec15dbe19112d95583642", "local-path": "/usr/lib/aarch64-linux-gnu/libpthread.so.0"}, {"ref": "acfe23cf0f5f4ac35b4c314415bb31d6f52a9bd39df8be04dfb2818a571533fd", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}]}