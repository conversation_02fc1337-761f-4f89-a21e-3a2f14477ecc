{"coredump-ref": "071e1a04783da57c0a534e6502c519d7202141dad947a46b99a9eb31dd4174e2", "threads": [{"lwp": 8255, "frames": ["node+0x9a939f", "node+0x9ad13e", "node+0x9ad4fd", "node+0x9dfbc1", "node+0x9fa065", "node+0x9fa5ce", "node+0x9fdd6f", "node+0x8f569c", "V8::BuiltinExitFrame+0 in :0", "trace+5 in node:internal/console/constructor:427", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0x9d5a90", "node+0x9d6a9c", "node+0x8884b6", "node+0x79c955", "V8::ExitFrame+0 in :0", "<anonymous>+2 in /home/<USER>/work/elastic/node/async.js:29", "Promise+0 in <unknown>:0", "V8::ConstructFrame+0 in :0", "V8::StubFrame+0 in :0", "serveCustomer+1 in /home/<USER>/work/elastic/node/async.js:27", "runRestaurant+4 in /home/<USER>/work/elastic/node/async.js:43", "<anonymous>+0 in <unknown>:0", "V8::StubFrame+0 in :0", "V8::StubFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0x9d5bde", "node+0x9d6d1f", "node+0x9d6eb4", "node+0xa0affe", "node+0xa0b34d", "node+0x8cd006", "node+0x8ce178", "V8::BuiltinExitFrame+0 in :0", "processTicksAndRejections+29 in node:internal/process/task_queues:96", "runNextTicks+6 in node:internal/process/task_queues:64", "listOnTimeout+21 in node:internal/timers:538", "processTimers+6 in node:internal/timers:503", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0x9d5a90", "node+0x9d6a9c", "node+0x8884b6", "node+0x602d09", "node+0x1205e54", "node+0x1209de0", "node+0x5b7500", "node+0x6c51f6", "node+0x6c5fcb", "node+0x638d16", "node+0x63c01b", "ld-musl-x86_64.so.1+0x1baac", "node+0x5879d9"]}, {"lwp": 8256, "frames": ["ld-musl-x86_64.so.1+0x57fac", "ld-musl-x86_64.so.1+0x55136", "ld-musl-x86_64.so.1+0x1eed3", "node+0x121c3ba", "node+0x1209e2a", "node+0x6f0a1c", "ld-musl-x86_64.so.1+0x55c0a", "ld-musl-x86_64.so.1+0x57f78"]}, {"lwp": 8257, "frames": ["ld-musl-x86_64.so.1+0x57fac", "ld-musl-x86_64.so.1+0x55136", "ld-musl-x86_64.so.1+0x54701", "ld-musl-x86_64.so.1+0x5553f", "node+0x12186c8", "node+0x6ed672", "ld-musl-x86_64.so.1+0x55c0a", "ld-musl-x86_64.so.1+0x57f78"]}, {"lwp": 8258, "frames": ["ld-musl-x86_64.so.1+0x57fac", "ld-musl-x86_64.so.1+0x55136", "ld-musl-x86_64.so.1+0x54701", "ld-musl-x86_64.so.1+0x5553f", "node+0x12186c8", "node+0x6ed672", "ld-musl-x86_64.so.1+0x55c0a", "ld-musl-x86_64.so.1+0x57f78"]}, {"lwp": 8259, "frames": ["ld-musl-x86_64.so.1+0x57fac", "ld-musl-x86_64.so.1+0x55136", "ld-musl-x86_64.so.1+0x54701", "ld-musl-x86_64.so.1+0x5553f", "node+0x12186c8", "node+0x6ed672", "ld-musl-x86_64.so.1+0x55c0a", "ld-musl-x86_64.so.1+0x57f78"]}, {"lwp": 8260, "frames": ["ld-musl-x86_64.so.1+0x57fac", "ld-musl-x86_64.so.1+0x55136", "ld-musl-x86_64.so.1+0x54701", "ld-musl-x86_64.so.1+0x5553f", "node+0x12186c8", "node+0x6ed672", "ld-musl-x86_64.so.1+0x55c0a", "ld-musl-x86_64.so.1+0x57f78"]}, {"lwp": 8261, "frames": ["ld-musl-x86_64.so.1+0x57fac", "ld-musl-x86_64.so.1+0x55136", "ld-musl-x86_64.so.1+0x54701", "ld-musl-x86_64.so.1+0x57b15", "node+0x1218551", "node+0x78d161", "ld-musl-x86_64.so.1+0x55c0a", "ld-musl-x86_64.so.1+0x57f78"]}], "modules": [{"ref": "c6b3288ba48945a21ede7ccf6f7a257d41bacf2c061236726ff9b5def383a766", "local-path": "/lib/ld-musl-x86_64.so.1"}, {"ref": "d73e165c5a4c95589dd8e86e7cc131e471eea28b252011b750ab87fbc9739016", "local-path": "/usr/lib/debug/lib/ld-musl-x86_64.so.1.debug"}, {"ref": "c91aeb888636f562fe74f9133abd4eac5259c8ab43fc7d21e3dde5a337fba8c6", "local-path": "/usr/bin/node"}, {"ref": "2f15e5cf60c63c296e982052e49d9c44d6f52431eb41537ba4f71f842d518ded", "local-path": "/usr/lib/libstdc++.so.6.0.30"}, {"ref": "995cc8e85cd6b9769a83efdab853b4d05ba86ed1bdb39b569f7d63f97b5e27ab", "local-path": "/usr/lib/libbrotlicommon.so.1.0.9"}, {"ref": "fd2fd9f296ea16052681d3192e7356f28e2fc916c5b5220610b86af03688ef17", "local-path": "/usr/lib/libgcc_s.so.1"}, {"ref": "b865c43fe862c6c04fce726f7f20f8eca6a4e34c5d4b2bd271e9372168fc6bcc", "local-path": "/lib/libz.so.1.2.13"}, {"ref": "d8aaa6af79c0a0c4d0f4687422e4cc330f04e480535f380977c7be553a8e36ef", "local-path": "/usr/lib/libicui18n.so.73.1"}, {"ref": "4b4c10a7d32f55bb68d671d3ac53dc29442d7326863d1a0fefb877e585ebf71d", "local-path": "/usr/lib/libcares.so.2.6.0"}, {"ref": "226cb34f3cf708521c167f998c95fbc4ce7cd92c30e3ce0553dbc7f5cdd29171", "local-path": "/usr/lib/libbrotlienc.so.1.0.9"}, {"ref": "24d5c76824bdc72821678492e6350830d2fca6069d19cbe589ef4f0187781b0e", "local-path": "/usr/lib/libicuuc.so.73.1"}, {"ref": "9ec9a27c455af033247e18781e33907a84446281f0b51f4b9b3b1b43cb01dd56", "local-path": "/lib/libcrypto.so.3"}, {"ref": "72bbcf719bd93c00701f9b86e240f86d771b49f3ab00f2436275e58d11940292", "local-path": "/lib/libssl.so.3"}, {"ref": "17d79c05629b778fbddeba441356d8dafec8c6c02d44454e18f4a399f361b1b4", "local-path": "/usr/lib/libbrotlidec.so.1.0.9"}]}