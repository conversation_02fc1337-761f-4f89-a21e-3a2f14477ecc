package ebpftracer

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/cilium/ebpf"
	"k8s.io/klog/v2"
)

// loadEBPFProgram loads the eBPF program from the compiled object file
func loadEBPFProgram() (*ebpf.CollectionSpec, error) {
	// Try to load the real eBPF object file first
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		klog.Warningf("Failed to get current file path, using embedded program")
		return loadEmbeddedProgram()
	}

	dir := filepath.Dir(filename)
	ebpfObjPath := filepath.Join(dir, "ebpf", "ebpf.o")

	// Check if the eBPF object file exists
	if _, err := os.Stat(ebpfObjPath); os.IsNotExist(err) {
		klog.Warningf("eBPF object file not found at %s, using embedded program", ebpfObjPath)
		return loadEmbeddedProgram()
	}

	// Load the eBPF program from object file
	klog.Infof("Loading eBPF program from %s", ebpfObjPath)
	spec, err := ebpf.LoadCollectionSpec(ebpfObjPath)
	if err != nil {
		klog.Warningf("Failed to load eBPF object file: %v, falling back to embedded program", err)
		return loadEmbeddedProgram()
	}

	klog.Infof("Successfully loaded real eBPF program with %d maps and %d programs", len(spec.Maps), len(spec.Programs))
	return spec, nil

	// TODO: Enable this when eBPF compilation is working
	/*
	// Get the directory of this source file
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return nil, fmt.Errorf("failed to get current file path")
	}

	// Construct path to eBPF object file
	dir := filepath.Dir(filename)
	ebpfObjPath := filepath.Join(dir, "ebpf", "ebpf.o")

	// Check if the eBPF object file exists
	if _, err := os.Stat(ebpfObjPath); os.IsNotExist(err) {
		klog.Warningf("eBPF object file not found at %s, using embedded program", ebpfObjPath)
		return loadEmbeddedProgram()
	}

	// Load the eBPF program from object file
	klog.Infof("Loading eBPF program from %s", ebpfObjPath)
	spec, err := ebpf.LoadCollectionSpec(ebpfObjPath)
	if err != nil {
		klog.Warningf("Failed to load eBPF object file: %v, falling back to embedded program", err)
		return loadEmbeddedProgram()
	}

	return spec, nil
	*/
}

// loadEmbeddedProgram loads a minimal embedded eBPF program for testing
func loadEmbeddedProgram() (*ebpf.CollectionSpec, error) {
	// This is a minimal eBPF program for testing purposes
	// In a real implementation, this would be the compiled bytecode
	
	klog.Warningf("Using minimal embedded eBPF program - limited functionality")
	
	// Create a minimal collection spec for testing
	spec := &ebpf.CollectionSpec{
		Maps: map[string]*ebpf.MapSpec{
			"function_events": {
				Type:       ebpf.PerfEventArray,
				KeySize:    4,
				ValueSize:  4,
				MaxEntries: 0,
			},
			"proc_events": {
				Type:       ebpf.PerfEventArray,
				KeySize:    4,
				ValueSize:  4,
				MaxEntries: 0,
			},
			"traced_pids": {
				Type:       ebpf.Hash,
				KeySize:    4,
				ValueSize:  1,
				MaxEntries: 1000,
			},
		},
		Programs: map[string]*ebpf.ProgramSpec{
			// Note: We'll create minimal programs without complex instructions
			// This is just for testing the framework
		},
	}
	
	return spec, nil
}

// initializeEBPFMaps initializes the eBPF maps with required data
func (t *Tracer) initializeEBPFMaps() error {
	// Initialize traced PIDs map if we have target PIDs
	if len(t.targetPIDs) > 0 {
		tracedPidsMap := t.collection.Maps["traced_pids"]
		if tracedPidsMap == nil {
			return fmt.Errorf("traced_pids map not found")
		}
		
		// Add target PIDs to the map
		enabled := uint8(1)
		for pid := range t.targetPIDs {
			if err := tracedPidsMap.Put(pid, enabled); err != nil {
				klog.Warningf("Failed to add PID %d to traced_pids map: %v", pid, err)
			} else {
				klog.Infof("Added PID %d to eBPF traced_pids map", pid)
			}
		}
	}
	
	return nil
}

// validateEBPFSupport checks if the system supports eBPF
func validateEBPFSupport() error {
	// Check if BPF filesystem is mounted
	if _, err := os.Stat("/sys/fs/bpf"); os.IsNotExist(err) {
		return fmt.Errorf("BPF filesystem not mounted at /sys/fs/bpf")
	}
	
	// Check if we can create a simple map (requires privileges)
	testSpec := &ebpf.MapSpec{
		Type:       ebpf.Hash,
		KeySize:    4,
		ValueSize:  4,
		MaxEntries: 1,
	}
	
	testMap, err := ebpf.NewMap(testSpec)
	if err != nil {
		return fmt.Errorf("failed to create test eBPF map (check privileges): %w", err)
	}
	testMap.Close()
	
	klog.Infof("eBPF support validated successfully")
	return nil
}

// getEBPFCapabilities returns information about eBPF capabilities
func getEBPFCapabilities() map[string]interface{} {
	caps := map[string]interface{}{
		"bpf_fs_mounted": false,
		"btf_support":    false,
		"uprobe_support": false,
	}
	
	// Check BPF filesystem
	if _, err := os.Stat("/sys/fs/bpf"); err == nil {
		caps["bpf_fs_mounted"] = true
	}
	
	// Check BTF support
	if _, err := os.Stat("/sys/kernel/btf/vmlinux"); err == nil {
		caps["btf_support"] = true
	}
	
	// Check uprobe support (simplified check)
	if _, err := os.Stat("/sys/kernel/debug/tracing/uprobe_events"); err == nil {
		caps["uprobe_support"] = true
	} else if _, err := os.Stat("/sys/kernel/tracing/uprobe_events"); err == nil {
		caps["uprobe_support"] = true
	}
	
	return caps
}

// logEBPFInfo logs information about the eBPF environment
func logEBPFInfo() {
	caps := getEBPFCapabilities()
	
	klog.Infof("eBPF Environment:")
	klog.Infof("  BPF filesystem mounted: %v", caps["bpf_fs_mounted"])
	klog.Infof("  BTF support available: %v", caps["btf_support"])
	klog.Infof("  Uprobe support available: %v", caps["uprobe_support"])
	
	// Log kernel version
	if data, err := os.ReadFile("/proc/version"); err == nil {
		klog.Infof("  Kernel: %s", string(data)[:min(len(data), 100)])
	}
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
