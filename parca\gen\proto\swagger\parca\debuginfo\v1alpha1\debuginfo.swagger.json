{"swagger": "2.0", "info": {"title": "parca/debuginfo/v1alpha1/debuginfo.proto", "version": "version not set"}, "tags": [{"name": "DebuginfoService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/initiateupload": {"post": {"summary": "InitiateUpload returns a strategy and information to upload debug info for a given build_id.", "operationId": "DebuginfoService_InitiateUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1alpha1InitiateUploadResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "InitiateUploadRequest is the request to initiate an upload.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1InitiateUploadRequest"}}], "tags": ["DebuginfoService"]}}, "/markuploadfinished": {"post": {"summary": "MarkUploadFinished marks the upload as finished for a given build_id.", "operationId": "DebuginfoService_MarkUploadFinished", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1alpha1MarkUploadFinishedResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "MarkUploadFinishedRequest is the request to mark an upload as finished.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1MarkUploadFinishedRequest"}}], "tags": ["DebuginfoService"]}}, "/shouldinitiateupload": {"post": {"summary": "ShouldInitiateUpload returns whether an upload for a given build_id should be initiated or not.", "operationId": "DebuginfoService_ShouldInitiateUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1alpha1ShouldInitiateUploadResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "ShouldInitiateUploadRequest is the request for ShouldInitiateUpload.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1alpha1ShouldInitiateUploadRequest"}}], "tags": ["DebuginfoService"]}}, "/upload": {"post": {"summary": "Upload ingests debug info for a given build_id", "operationId": "DebuginfoService_Upload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/parcadebuginfov1alpha1UploadResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": " (streaming inputs)", "in": "body", "required": true, "schema": {"$ref": "#/definitions/parcadebuginfov1alpha1UploadRequest"}}], "tags": ["DebuginfoService"]}}}, "definitions": {"UploadInstructionsUploadStrategy": {"type": "string", "enum": ["UPLOAD_STRATEGY_UNSPECIFIED", "UPLOAD_STRATEGY_GRPC", "UPLOAD_STRATEGY_SIGNED_URL"], "default": "UPLOAD_STRATEGY_UNSPECIFIED", "description": "The strategy to use for uploading.\n\n - UPLOAD_STRATEGY_UNSPECIFIED: The upload is not allowed.\n - UPLOAD_STRATEGY_GRPC: The upload is allowed and should be done via the Upload RPC.\n - UPLOAD_STRATEGY_SIGNED_URL: The upload is allowed and should be done via a returned signed URL."}, "parcadebuginfov1alpha1UploadRequest": {"type": "object", "properties": {"info": {"$ref": "#/definitions/v1alpha1UploadInfo", "title": "info is the metadata for the debug info"}, "chunkData": {"type": "string", "format": "byte", "title": "chunk_data is the raw bytes of the debug info"}}, "title": "UploadRequest upload debug info"}, "parcadebuginfov1alpha1UploadResponse": {"type": "object", "properties": {"buildId": {"type": "string", "title": "build_id is a unique identifier for the debug data"}, "size": {"type": "string", "format": "uint64", "title": "size is the number of bytes of the debug info"}}, "title": "UploadResponse returns the build_id and the size of the uploaded debug info"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1alpha1BuildIDType": {"type": "string", "enum": ["BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED", "BUILD_ID_TYPE_GNU", "BUILD_ID_TYPE_HASH", "BUILD_ID_TYPE_GO"], "default": "BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED", "description": "BuildIDType is the type of build ID.\n\n - BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED: The build ID is unknown.\n - BUILD_ID_TYPE_GNU: The build ID is a GNU build ID.\n - BUILD_ID_TYPE_HASH: The build ID is an opaque hash.\n - BUILD_ID_TYPE_GO: The build ID is a Go build ID."}, "v1alpha1DebuginfoType": {"type": "string", "enum": ["DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED", "DEBUGINFO_TYPE_EXECUTABLE", "DEBUGINFO_TYPE_SOURCES"], "default": "DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED", "description": "Types of debuginfo.\n\n - DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED: The default type that the API always supported. This type is expected to\ncontain debuginfos for symbolizaton purposes.\n - DEBUGINFO_TYPE_EXECUTABLE: The type to identify executables. This is meant to be used for\ndisassembling so it is expected to contain executable `.text` section.\n - DEBUGINFO_TYPE_SOURCES: The type to identify a source tarball. This is expected to contain\nmultiple source files that debuginfo references. It is meant to show code\nwith profiling data inline."}, "v1alpha1InitiateUploadRequest": {"type": "object", "properties": {"buildId": {"type": "string", "description": "The build_id of the debug info to upload."}, "size": {"type": "string", "format": "int64", "description": "The size of the debug info to upload."}, "hash": {"type": "string", "description": "Hash of the debuginfo to upload."}, "force": {"type": "boolean", "description": "Force uploading even if valid debuginfos are already available."}, "type": {"$ref": "#/definitions/v1alpha1DebuginfoType", "description": "Type of debuginfo to propose uploading."}, "buildIdType": {"$ref": "#/definitions/v1alpha1BuildIDType", "description": "Type of build ID."}}, "description": "InitiateUploadRequest is the request to initiate an upload."}, "v1alpha1InitiateUploadResponse": {"type": "object", "properties": {"uploadInstructions": {"$ref": "#/definitions/v1alpha1UploadInstructions", "description": "UploadInstructions contains the instructions for the client to upload the debuginfo."}}, "description": "InitiateUploadResponse is the response to an InitiateUploadRequest."}, "v1alpha1MarkUploadFinishedRequest": {"type": "object", "properties": {"buildId": {"type": "string", "description": "The build_id of the debug info to mark as finished."}, "uploadId": {"type": "string", "description": "The upload_id of the debug info to mark as finished."}, "type": {"$ref": "#/definitions/v1alpha1DebuginfoType", "description": "The type of debuginfo upload to mark as finished."}}, "description": "MarkUploadFinishedRequest is the request to mark an upload as finished."}, "v1alpha1MarkUploadFinishedResponse": {"type": "object", "description": "MarkUploadFinishedResponse is the response to a MarkUploadFinishedRequest."}, "v1alpha1ShouldInitiateUploadRequest": {"type": "object", "properties": {"buildId": {"type": "string", "description": "The build_id of the debuginfo."}, "hash": {"type": "string", "description": "Hash of the debuginfo to upload."}, "force": {"type": "boolean", "description": "Force uploading even if valid debuginfos are already available."}, "type": {"$ref": "#/definitions/v1alpha1DebuginfoType", "description": "Type of debuginfo to propose uploading."}, "buildIdType": {"$ref": "#/definitions/v1alpha1BuildIDType", "description": "Type of build ID."}}, "description": "ShouldInitiateUploadRequest is the request for ShouldInitiateUpload."}, "v1alpha1ShouldInitiateUploadResponse": {"type": "object", "properties": {"shouldInitiateUpload": {"type": "boolean", "description": "Whether an upload should be initiated or not."}, "reason": {"type": "string", "description": "Reason for why an upload should be initiated or not."}}, "description": "ShouldInitiateUploadResponse is the response for ShouldInitiateUpload."}, "v1alpha1UploadInfo": {"type": "object", "properties": {"buildId": {"type": "string", "title": "build_id is a unique identifier for the debug data"}, "uploadId": {"type": "string", "title": "upload_id is a unique identifier for the upload"}, "type": {"$ref": "#/definitions/v1alpha1DebuginfoType", "title": "the type of debuginfo that's being uploaded"}}, "title": "UploadInfo contains the build_id and other metadata for the debug data"}, "v1alpha1UploadInstructions": {"type": "object", "properties": {"buildId": {"type": "string", "description": "The build ID of the debuginfo to upload."}, "uploadId": {"type": "string", "description": "The upload_id to use for uploading."}, "uploadStrategy": {"$ref": "#/definitions/UploadInstructionsUploadStrategy", "description": "The strategy to use for uploading."}, "signedUrl": {"type": "string", "description": "The signed url to use for uploading using a PUT request when the upload\nstrategy is SIGNED_STRATEGY_URL."}, "type": {"$ref": "#/definitions/v1alpha1DebuginfoType", "description": "Type of debuginfo the upload instructions are for."}}, "description": "UploadInstructions contains the instructions for the client to upload debuginfo."}}}