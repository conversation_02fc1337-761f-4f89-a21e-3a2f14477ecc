# Deep-eBPF Server

Central data collection and processing server for the Deep-eBPF universal function tracing system. Based on the proven coroot server architecture.

## Overview

Deep-eBPF Server collects function tracing data from deep-ebpf-node agents, processes and correlates function calls, and provides APIs for accessing trace data. It serves as the central hub for the distributed function tracing system.

## Features

### Data Collection
- **Agent Management**: Automatic discovery and management of deep-ebpf-node agents
- **Real-time Ingestion**: High-throughput collection of function trace events
- **Data Validation**: Ensures data integrity and consistency
- **Buffering**: Handles burst traffic and network interruptions

### Function Trace Processing
- **Call Correlation**: Matches function entry/exit events
- **Stack Reconstruction**: Builds complete call stacks from trace data
- **Performance Analysis**: Calculates latency percentiles and performance metrics
- **Error Detection**: Identifies failed function calls and error patterns

### APIs and Interfaces
- **REST API**: Query function traces, performance metrics, and system status
- **WebSocket**: Real-time streaming of function trace events
- **Web UI**: Interactive dashboard for trace visualization
- **Export APIs**: Integration with external monitoring systems

### Storage and Retention
- **Time-series Storage**: Efficient storage of function trace data
- **Configurable Retention**: Flexible data retention policies
- **Compression**: Optimized storage with data compression
- **Indexing**: Fast queries on function names, processes, and time ranges

## Architecture

```
deep-ebpf-server/
├── main.go                    # Server entry point (from coroot)
├── api/                       # REST API handlers (from coroot/api)
│   ├── handlers.go            # Function trace API endpoints
│   ├── middleware.go          # Authentication and logging
│   └── routes.go              # API route definitions
├── collector/                 # Data collection from agents (from coroot/collector)
│   ├── collector.go           # Main collector implementation
│   ├── agent_manager.go       # Agent discovery and management
│   └── ingestion.go           # Data ingestion pipeline
├── processor/                 # Function trace processing
│   ├── correlator.go          # Function call correlation
│   ├── analyzer.go            # Performance analysis
│   └── aggregator.go          # Data aggregation
├── storage/                   # Data storage layer
│   ├── timeseries.go          # Time-series database interface
│   ├── memory.go              # In-memory storage for development
│   └── persistent.go          # Persistent storage implementation
├── web/                       # Web UI (from coroot/static)
│   ├── static/                # Static assets
│   └── templates/             # HTML templates
├── config/                    # Configuration management (from coroot/config)
└── common/                    # Shared utilities
```

## Quick Start

### Prerequisites
- Go 1.19+
- Deep-eBPF Node agents deployed on target systems
- Network connectivity between server and agents

### Build and Run
```bash
# Build the server
make all

# Run with default configuration
./deep-ebpf-server

# Run with custom configuration
./deep-ebpf-server --config /path/to/config.yaml
```

### Configuration
```yaml
# config.yaml
server:
  listen_address: ":8080"
  data_dir: "/var/lib/deep-ebpf"
  
collector:
  agent_discovery: true
  ingestion_buffer_size: 10000
  
storage:
  retention_days: 30
  compression: true
  
api:
  enable_cors: true
  rate_limit: 1000
```

## API Endpoints

### Function Traces
- `GET /api/v1/traces` - Query function traces
- `GET /api/v1/traces/{trace_id}` - Get specific trace
- `GET /api/v1/functions` - List traced functions
- `GET /api/v1/processes` - List traced processes

### Performance Metrics
- `GET /api/v1/metrics/latency` - Function latency metrics
- `GET /api/v1/metrics/throughput` - Function call throughput
- `GET /api/v1/metrics/errors` - Error rates and patterns

### System Status
- `GET /api/v1/status` - Server health status
- `GET /api/v1/agents` - Connected agent status
- `GET /api/v1/stats` - Collection statistics

### Real-time Streaming
- `WS /api/v1/stream/traces` - Real-time trace events
- `WS /api/v1/stream/metrics` - Real-time metrics

## Web Interface

Access the web interface at `http://localhost:8080` to:
- View real-time function traces
- Analyze performance metrics
- Monitor system health
- Configure collection settings

## Integration

### With Monitoring Systems
```bash
# Prometheus metrics
curl http://localhost:8080/metrics

# Grafana dashboard
# Import dashboard from web/grafana/dashboard.json

# Jaeger integration
# Configure OTLP export in agents
```

### With CI/CD Pipelines
```bash
# Health check
curl http://localhost:8080/api/v1/status

# Query specific function performance
curl "http://localhost:8080/api/v1/metrics/latency?function=main&timerange=1h"
```

## Deployment

### Docker
```bash
docker build -t deep-ebpf-server .
docker run -p 8080:8080 -v /data:/var/lib/deep-ebpf deep-ebpf-server
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deep-ebpf-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: deep-ebpf-server
  template:
    metadata:
      labels:
        app: deep-ebpf-server
    spec:
      containers:
      - name: server
        image: deep-ebpf-server:latest
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: data
          mountPath: /var/lib/deep-ebpf
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: deep-ebpf-data
```

### Systemd Service
```ini
[Unit]
Description=Deep-eBPF Server
After=network.target

[Service]
Type=simple
User=deep-ebpf
ExecStart=/usr/local/bin/deep-ebpf-server --config /etc/deep-ebpf/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

## Performance

- **Throughput**: Handles 100K+ function traces per second
- **Latency**: Sub-millisecond query response times
- **Storage**: Efficient compression reduces storage by 80%
- **Memory**: Configurable memory usage based on retention settings

## Security

- **Authentication**: API key and JWT token support
- **Authorization**: Role-based access control
- **Encryption**: TLS encryption for all communications
- **Audit Logging**: Complete audit trail of all operations

## Monitoring

The server exposes metrics for monitoring its own performance:
- Collection rates and latencies
- Storage utilization
- API response times
- Agent connectivity status

## Contributing

This project follows the coroot architecture patterns. When contributing:

1. Maintain compatibility with coroot design principles
2. Focus on function tracing specific features
3. Ensure high performance and reliability
4. Add comprehensive tests for new functionality

## License

Apache License 2.0 - Based on coroot

## Acknowledgments

- **Coroot Team**: For the foundational server architecture
- **Deep-eBPF Community**: For function tracing requirements and feedback
