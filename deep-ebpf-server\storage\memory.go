package storage

import (
	"fmt"
	"sort"
	"sync"
	"time"
)

// MemoryStorage implements in-memory storage for development and testing
type MemoryStorage struct {
	functionCalls map[string]*FunctionCall
	callsLock     sync.RWMutex

	functionMetrics map[string][]*FunctionMetrics
	metricsLock     sync.RWMutex

	// Indexes for faster queries
	callsByTime     []*FunctionCall
	callsByFunction map[string][]*FunctionCall
	callsByProcess  map[string][]*FunctionCall
	indexLock       sync.RWMutex

	stats struct {
		totalCalls   uint64
		totalMetrics uint64
		startTime    time.Time
	}
}

// NewMemoryStorage creates a new in-memory storage instance
func NewMemoryStorage() *MemoryStorage {
	return &MemoryStorage{
		functionCalls:   make(map[string]*FunctionCall),
		functionMetrics: make(map[string][]*FunctionMetrics),
		callsByFunction: make(map[string][]*FunctionCall),
		callsByProcess:  make(map[string][]*FunctionCall),
		stats: struct {
			totalCalls   uint64
			totalMetrics uint64
			startTime    time.Time
		}{
			startTime: time.Now(),
		},
	}
}

// StoreFunctionCall stores a function call
func (m *MemoryStorage) StoreFunctionCall(call *FunctionCall) error {
	m.callsLock.Lock()
	defer m.callsLock.Unlock()

	m.functionCalls[call.ID] = call
	m.stats.totalCalls++

	// Update indexes
	m.indexLock.Lock()
	defer m.indexLock.Unlock()

	// Add to time-ordered index
	m.callsByTime = append(m.callsByTime, call)
	sort.Slice(m.callsByTime, func(i, j int) bool {
		return m.callsByTime[i].StartTime.Before(m.callsByTime[j].StartTime)
	})

	// Add to function index
	m.callsByFunction[call.FunctionName] = append(m.callsByFunction[call.FunctionName], call)

	// Add to process index
	m.callsByProcess[call.ProcessName] = append(m.callsByProcess[call.ProcessName], call)

	return nil
}

// GetFunctionCall retrieves a function call by ID
func (m *MemoryStorage) GetFunctionCall(id string) (*FunctionCall, error) {
	m.callsLock.RLock()
	defer m.callsLock.RUnlock()

	call, exists := m.functionCalls[id]
	if !exists {
		return nil, fmt.Errorf("function call not found: %s", id)
	}

	return call, nil
}

// QueryFunctionCalls queries function calls with options
func (m *MemoryStorage) QueryFunctionCalls(opts QueryOptions) ([]*FunctionCall, error) {
	m.indexLock.RLock()
	defer m.indexLock.RUnlock()

	var results []*FunctionCall

	// Start with all calls or filter by function/process
	if opts.FunctionName != "" {
		results = append(results, m.callsByFunction[opts.FunctionName]...)
	} else if opts.ProcessName != "" {
		results = append(results, m.callsByProcess[opts.ProcessName]...)
	} else {
		results = append(results, m.callsByTime...)
	}

	// Apply filters
	filtered := make([]*FunctionCall, 0, len(results))
	for _, call := range results {
		if m.matchesFilters(call, opts) {
			filtered = append(filtered, call)
		}
	}

	// Sort results
	if opts.OrderBy == "start_time" || opts.OrderBy == "" {
		sort.Slice(filtered, func(i, j int) bool {
			if opts.OrderDesc {
				return filtered[i].StartTime.After(filtered[j].StartTime)
			}
			return filtered[i].StartTime.Before(filtered[j].StartTime)
		})
	} else if opts.OrderBy == "duration" {
		sort.Slice(filtered, func(i, j int) bool {
			if opts.OrderDesc {
				return filtered[i].Duration > filtered[j].Duration
			}
			return filtered[i].Duration < filtered[j].Duration
		})
	}

	// Apply pagination
	start := opts.Offset
	if start > len(filtered) {
		start = len(filtered)
	}

	end := start + opts.Limit
	if opts.Limit == 0 || end > len(filtered) {
		end = len(filtered)
	}

	return filtered[start:end], nil
}

// DeleteFunctionCalls deletes function calls before a given time
func (m *MemoryStorage) DeleteFunctionCalls(before time.Time) error {
	m.callsLock.Lock()
	defer m.callsLock.Unlock()

	// Remove from main map
	for id, call := range m.functionCalls {
		if call.StartTime.Before(before) {
			delete(m.functionCalls, id)
		}
	}

	// Rebuild indexes
	m.indexLock.Lock()
	defer m.indexLock.Unlock()

	m.callsByTime = make([]*FunctionCall, 0)
	m.callsByFunction = make(map[string][]*FunctionCall)
	m.callsByProcess = make(map[string][]*FunctionCall)

	for _, call := range m.functionCalls {
		m.callsByTime = append(m.callsByTime, call)
		m.callsByFunction[call.FunctionName] = append(m.callsByFunction[call.FunctionName], call)
		m.callsByProcess[call.ProcessName] = append(m.callsByProcess[call.ProcessName], call)
	}

	sort.Slice(m.callsByTime, func(i, j int) bool {
		return m.callsByTime[i].StartTime.Before(m.callsByTime[j].StartTime)
	})

	return nil
}

// StoreFunctionMetrics stores function metrics
func (m *MemoryStorage) StoreFunctionMetrics(metrics *FunctionMetrics) error {
	m.metricsLock.Lock()
	defer m.metricsLock.Unlock()

	m.functionMetrics[metrics.FunctionName] = append(m.functionMetrics[metrics.FunctionName], metrics)
	m.stats.totalMetrics++

	return nil
}

// GetFunctionMetrics retrieves function metrics for a time range
func (m *MemoryStorage) GetFunctionMetrics(functionName string, start, end time.Time) ([]*FunctionMetrics, error) {
	m.metricsLock.RLock()
	defer m.metricsLock.RUnlock()

	allMetrics := m.functionMetrics[functionName]
	var results []*FunctionMetrics

	for _, metric := range allMetrics {
		if (metric.WindowStart.After(start) || metric.WindowStart.Equal(start)) &&
			(metric.WindowEnd.Before(end) || metric.WindowEnd.Equal(end)) {
			results = append(results, metric)
		}
	}

	return results, nil
}

// QueryFunctionMetrics queries function metrics with options
func (m *MemoryStorage) QueryFunctionMetrics(opts QueryOptions) ([]*FunctionMetrics, error) {
	m.metricsLock.RLock()
	defer m.metricsLock.RUnlock()

	var results []*FunctionMetrics

	if opts.FunctionName != "" {
		results = append(results, m.functionMetrics[opts.FunctionName]...)
	} else {
		for _, metrics := range m.functionMetrics {
			results = append(results, metrics...)
		}
	}

	// Apply time filters
	if opts.StartTime != nil || opts.EndTime != nil {
		filtered := make([]*FunctionMetrics, 0, len(results))
		for _, metric := range results {
			if opts.StartTime != nil && metric.WindowStart.Before(*opts.StartTime) {
				continue
			}
			if opts.EndTime != nil && metric.WindowEnd.After(*opts.EndTime) {
				continue
			}
			filtered = append(filtered, metric)
		}
		results = filtered
	}

	return results, nil
}

// GetFunctionList returns a list of all function names
func (m *MemoryStorage) GetFunctionList() ([]string, error) {
	m.indexLock.RLock()
	defer m.indexLock.RUnlock()

	functions := make([]string, 0, len(m.callsByFunction))
	for funcName := range m.callsByFunction {
		functions = append(functions, funcName)
	}

	sort.Strings(functions)
	return functions, nil
}

// GetProcessList returns a list of all process names
func (m *MemoryStorage) GetProcessList() ([]string, error) {
	m.indexLock.RLock()
	defer m.indexLock.RUnlock()

	processes := make([]string, 0, len(m.callsByProcess))
	for procName := range m.callsByProcess {
		processes = append(processes, procName)
	}

	sort.Strings(processes)
	return processes, nil
}

// GetLatencyMetrics calculates latency metrics for a function
func (m *MemoryStorage) GetLatencyMetrics(functionName string, start, end time.Time) (*LatencyMetrics, error) {
	calls, err := m.QueryFunctionCalls(QueryOptions{
		FunctionName: functionName,
		StartTime:    &start,
		EndTime:      &end,
	})
	if err != nil {
		return nil, err
	}

	if len(calls) == 0 {
		return &LatencyMetrics{
			FunctionName: functionName,
			StartTime:    start,
			EndTime:      end,
		}, nil
	}

	// Calculate latency statistics
	durations := make([]time.Duration, len(calls))
	var total time.Duration
	min := calls[0].Duration
	max := calls[0].Duration

	for i, call := range calls {
		durations[i] = call.Duration
		total += call.Duration
		if call.Duration < min {
			min = call.Duration
		}
		if call.Duration > max {
			max = call.Duration
		}
	}

	sort.Slice(durations, func(i, j int) bool {
		return durations[i] < durations[j]
	})

	avg := total / time.Duration(len(calls))
	p50 := durations[len(durations)*50/100]
	p95 := durations[len(durations)*95/100]
	p99 := durations[len(durations)*99/100]

	return &LatencyMetrics{
		FunctionName: functionName,
		StartTime:    start,
		EndTime:      end,
		CallCount:    uint64(len(calls)),
		MinLatency:   min,
		MaxLatency:   max,
		AvgLatency:   avg,
		Percentiles: map[string]time.Duration{
			"p50": p50,
			"p95": p95,
			"p99": p99,
		},
	}, nil
}

// GetThroughputMetrics calculates throughput metrics for a function
func (m *MemoryStorage) GetThroughputMetrics(functionName string, start, end time.Time) (*ThroughputMetrics, error) {
	calls, err := m.QueryFunctionCalls(QueryOptions{
		FunctionName: functionName,
		StartTime:    &start,
		EndTime:      &end,
	})
	if err != nil {
		return nil, err
	}

	duration := end.Sub(start)
	callsPerSec := float64(len(calls)) / duration.Seconds()

	return &ThroughputMetrics{
		FunctionName: functionName,
		StartTime:    start,
		EndTime:      end,
		TotalCalls:   uint64(len(calls)),
		CallsPerSec:  callsPerSec,
	}, nil
}

// GetErrorMetrics calculates error metrics for a function
func (m *MemoryStorage) GetErrorMetrics(functionName string, start, end time.Time) (*ErrorMetrics, error) {
	calls, err := m.QueryFunctionCalls(QueryOptions{
		FunctionName: functionName,
		StartTime:    &start,
		EndTime:      &end,
	})
	if err != nil {
		return nil, err
	}

	var errorCount uint64
	for _, call := range calls {
		if call.Error != "" {
			errorCount++
		}
	}

	var errorRate float64
	if len(calls) > 0 {
		errorRate = float64(errorCount) / float64(len(calls))
	}

	return &ErrorMetrics{
		FunctionName: functionName,
		StartTime:    start,
		EndTime:      end,
		TotalCalls:   uint64(len(calls)),
		ErrorCalls:   errorCount,
		ErrorRate:    errorRate,
	}, nil
}

// GetStats returns storage statistics
func (m *MemoryStorage) GetStats() map[string]interface{} {
	m.callsLock.RLock()
	callCount := len(m.functionCalls)
	m.callsLock.RUnlock()

	m.metricsLock.RLock()
	metricCount := len(m.functionMetrics)
	m.metricsLock.RUnlock()

	return map[string]interface{}{
		"type":           "memory",
		"function_calls": callCount,
		"metrics":        metricCount,
		"total_calls":    m.stats.totalCalls,
		"total_metrics":  m.stats.totalMetrics,
		"uptime":         time.Since(m.stats.startTime).String(),
	}
}

// Close closes the storage (no-op for memory storage)
func (m *MemoryStorage) Close() error {
	return nil
}

// matchesFilters checks if a function call matches the query filters
func (m *MemoryStorage) matchesFilters(call *FunctionCall, opts QueryOptions) bool {
	if opts.StartTime != nil && call.StartTime.Before(*opts.StartTime) {
		return false
	}
	if opts.EndTime != nil && call.StartTime.After(*opts.EndTime) {
		return false
	}
	if opts.PID != nil && call.PID != *opts.PID {
		return false
	}
	if opts.AgentID != "" && call.AgentID != opts.AgentID {
		return false
	}
	return true
}
