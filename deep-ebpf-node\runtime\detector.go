package runtime

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"k8s.io/klog/v2"
)

// RuntimeType represents different runtime environments
type RuntimeType string

const (
	RuntimeC       RuntimeType = "C/native"
	RuntimeGo      RuntimeType = "Go"
	RuntimePython  RuntimeType = "Python"
	RuntimeJava    RuntimeType = "Java"
	RuntimeNodeJS  RuntimeType = "Node.js"
	RuntimeRuby    RuntimeType = "Ruby"
	RuntimeDotNet  RuntimeType = ".NET"
	RuntimeRust    RuntimeType = "Rust"
	RuntimePHP     RuntimeType = "PHP"
	RuntimeUnknown RuntimeType = "Unknown"
)

// ProcessInfo contains runtime information about a process
type ProcessInfo struct {
	PID         uint32
	Runtime     RuntimeType
	Version     string
	Interpreter string
	MainModule  string
}

// Detector provides runtime detection capabilities
type Detector struct {
	cache map[uint32]*ProcessInfo
	mutex sync.RWMutex
}

// NewDetector creates a new runtime detector
func NewDetector() *Detector {
	return &Detector{
		cache: make(map[uint32]*ProcessInfo),
	}
}

// DetectRuntime detects the runtime for a given process ID
func (d *Detector) DetectRuntime(pid uint32) *ProcessInfo {
	d.mutex.RLock()
	if info, exists := d.cache[pid]; exists {
		d.mutex.RUnlock()
		return info
	}
	d.mutex.RUnlock()

	// Perform detection
	info := d.analyzeProcess(pid)
	
	// Cache the result
	d.mutex.Lock()
	d.cache[pid] = info
	d.mutex.Unlock()

	return info
}

// analyzeProcess performs comprehensive process analysis
func (d *Detector) analyzeProcess(pid uint32) *ProcessInfo {
	info := &ProcessInfo{
		PID:     pid,
		Runtime: RuntimeUnknown,
	}

	// Get executable path
	exePath, err := d.getExecutablePath(pid)
	if err != nil {
		klog.V(2).Infof("Failed to get executable path for PID %d: %v", pid, err)
		return info
	}

	// Get command line
	cmdline, err := d.getCommandLine(pid)
	if err != nil {
		klog.V(2).Infof("Failed to get command line for PID %d: %v", pid, err)
		return info
	}

	// Get environment variables
	environ, err := d.getEnvironment(pid)
	if err != nil {
		klog.V(2).Infof("Failed to get environment for PID %d: %v", pid, err)
		environ = make(map[string]string)
	}

	// Analyze runtime based on multiple signals
	d.detectPython(info, exePath, cmdline, environ)
	d.detectJava(info, exePath, cmdline, environ)
	d.detectNodeJS(info, exePath, cmdline, environ)
	d.detectGo(info, exePath, cmdline, environ)
	d.detectRuby(info, exePath, cmdline, environ)
	d.detectDotNet(info, exePath, cmdline, environ)
	d.detectRust(info, exePath, cmdline, environ)
	d.detectPHP(info, exePath, cmdline, environ)

	// Default to C/native if no specific runtime detected
	if info.Runtime == RuntimeUnknown {
		info.Runtime = RuntimeC
	}

	klog.V(2).Infof("Detected runtime for PID %d: %s", pid, info.Runtime)
	return info
}

// detectPython detects Python runtime
func (d *Detector) detectPython(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	baseName := filepath.Base(exePath)
	
	// Check executable name
	if strings.Contains(baseName, "python") {
		info.Runtime = RuntimePython
		info.Interpreter = exePath
		
		// Extract version from executable name
		if strings.Contains(baseName, "python3") {
			info.Version = "3.x"
		} else if strings.Contains(baseName, "python2") {
			info.Version = "2.x"
		}
		
		// Get main module from command line
		if len(cmdline) > 1 {
			info.MainModule = cmdline[1]
		}
		
		// Check for virtual environment
		if venv, exists := environ["VIRTUAL_ENV"]; exists {
			info.Version += fmt.Sprintf(" (venv: %s)", filepath.Base(venv))
		}
		return
	}

	// Check for Python in command line
	if len(cmdline) > 0 && strings.Contains(cmdline[0], "python") {
		info.Runtime = RuntimePython
		info.Interpreter = cmdline[0]
		if len(cmdline) > 1 {
			info.MainModule = cmdline[1]
		}
		return
	}
}

// detectJava detects Java runtime
func (d *Detector) detectJava(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	baseName := filepath.Base(exePath)
	
	// Check executable name
	if strings.Contains(baseName, "java") || d.isJVMProcess(cmdline) {
		info.Runtime = RuntimeJava
		info.Interpreter = exePath
		
		// Extract Java version from environment
		if javaHome, exists := environ["JAVA_HOME"]; exists {
			info.Version = fmt.Sprintf("JAVA_HOME: %s", javaHome)
		}
		
		// Get main class from command line
		for i, arg := range cmdline {
			if strings.Contains(arg, ".jar") {
				info.MainModule = arg
				break
			}
			if i > 0 && !strings.HasPrefix(arg, "-") && strings.Contains(arg, ".") {
				info.MainModule = arg
				break
			}
		}
		return
	}
}

// detectNodeJS detects Node.js runtime
func (d *Detector) detectNodeJS(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	baseName := filepath.Base(exePath)
	
	if strings.Contains(baseName, "node") {
		info.Runtime = RuntimeNodeJS
		info.Interpreter = exePath
		
		// Get main script from command line
		if len(cmdline) > 1 {
			for _, arg := range cmdline[1:] {
				if !strings.HasPrefix(arg, "-") && strings.HasSuffix(arg, ".js") {
					info.MainModule = arg
					break
				}
			}
		}
		
		// Check for Node.js version
		if nodeVersion, exists := environ["NODE_VERSION"]; exists {
			info.Version = nodeVersion
		}
		return
	}
}

// detectGo detects Go runtime
func (d *Detector) detectGo(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	// Check if binary was built with Go
	if d.isGoBinary(exePath) {
		info.Runtime = RuntimeGo
		info.Interpreter = "go"
		info.MainModule = exePath
		
		// Try to extract Go version from binary
		if version := d.extractGoVersion(exePath); version != "" {
			info.Version = version
		}
		return
	}
}

// detectRuby detects Ruby runtime
func (d *Detector) detectRuby(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	baseName := filepath.Base(exePath)
	
	if strings.Contains(baseName, "ruby") {
		info.Runtime = RuntimeRuby
		info.Interpreter = exePath
		
		// Get main script from command line
		if len(cmdline) > 1 {
			info.MainModule = cmdline[1]
		}
		
		// Check for Ruby version
		if rubyVersion, exists := environ["RUBY_VERSION"]; exists {
			info.Version = rubyVersion
		}
		return
	}
}

// detectDotNet detects .NET runtime
func (d *Detector) detectDotNet(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	baseName := filepath.Base(exePath)
	
	if strings.Contains(baseName, "dotnet") || d.isDotNetProcess(cmdline) {
		info.Runtime = RuntimeDotNet
		info.Interpreter = exePath
		
		// Get assembly from command line
		if len(cmdline) > 1 {
			for _, arg := range cmdline[1:] {
				if strings.HasSuffix(arg, ".dll") || strings.HasSuffix(arg, ".exe") {
					info.MainModule = arg
					break
				}
			}
		}
		
		// Check for .NET version
		if dotnetVersion, exists := environ["DOTNET_VERSION"]; exists {
			info.Version = dotnetVersion
		}
		return
	}
}

// detectRust detects Rust runtime
func (d *Detector) detectRust(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	// Rust produces native binaries, so we need to check for Rust-specific patterns
	if d.isRustBinary(exePath) {
		info.Runtime = RuntimeRust
		info.MainModule = exePath
		return
	}
}

// detectPHP detects PHP runtime
func (d *Detector) detectPHP(info *ProcessInfo, exePath string, cmdline []string, environ map[string]string) {
	baseName := filepath.Base(exePath)
	
	if strings.Contains(baseName, "php") {
		info.Runtime = RuntimePHP
		info.Interpreter = exePath
		
		// Get main script from command line
		if len(cmdline) > 1 {
			for _, arg := range cmdline[1:] {
				if strings.HasSuffix(arg, ".php") {
					info.MainModule = arg
					break
				}
			}
		}
		return
	}
}

// Helper methods for process analysis
func (d *Detector) getExecutablePath(pid uint32) (string, error) {
	exePath := fmt.Sprintf("/proc/%d/exe", pid)
	return os.Readlink(exePath)
}

func (d *Detector) getCommandLine(pid uint32) ([]string, error) {
	cmdlinePath := fmt.Sprintf("/proc/%d/cmdline", pid)
	data, err := os.ReadFile(cmdlinePath)
	if err != nil {
		return nil, err
	}
	
	// Split by null bytes
	parts := strings.Split(string(data), "\x00")
	// Remove empty last element
	if len(parts) > 0 && parts[len(parts)-1] == "" {
		parts = parts[:len(parts)-1]
	}
	
	return parts, nil
}

func (d *Detector) getEnvironment(pid uint32) (map[string]string, error) {
	environPath := fmt.Sprintf("/proc/%d/environ", pid)
	data, err := os.ReadFile(environPath)
	if err != nil {
		return nil, err
	}
	
	environ := make(map[string]string)
	parts := strings.Split(string(data), "\x00")
	
	for _, part := range parts {
		if part == "" {
			continue
		}
		
		if idx := strings.Index(part, "="); idx > 0 {
			key := part[:idx]
			value := part[idx+1:]
			environ[key] = value
		}
	}
	
	return environ, nil
}

// isJVMProcess checks if the process is a JVM process
func (d *Detector) isJVMProcess(cmdline []string) bool {
	if len(cmdline) == 0 {
		return false
	}
	
	// Check for JVM-specific arguments
	for _, arg := range cmdline {
		if strings.HasPrefix(arg, "-Xmx") || strings.HasPrefix(arg, "-Xms") ||
		   strings.HasPrefix(arg, "-XX:") || strings.Contains(arg, "java.") {
			return true
		}
	}
	
	return false
}

// isDotNetProcess checks if the process is a .NET process
func (d *Detector) isDotNetProcess(cmdline []string) bool {
	if len(cmdline) == 0 {
		return false
	}
	
	for _, arg := range cmdline {
		if strings.HasSuffix(arg, ".dll") && strings.Contains(arg, ".") {
			return true
		}
	}
	
	return false
}

// isGoBinary checks if the binary was compiled with Go
func (d *Detector) isGoBinary(exePath string) bool {
	// Try to read Go build info from the binary
	file, err := os.Open(exePath)
	if err != nil {
		return false
	}
	defer file.Close()
	
	// Read first 1KB to look for Go signatures
	buffer := make([]byte, 1024)
	n, err := file.Read(buffer)
	if err != nil {
		return false
	}
	
	content := string(buffer[:n])
	return strings.Contains(content, "go1.") || strings.Contains(content, "runtime.") ||
		   strings.Contains(content, "Go build ID:")
}

// isRustBinary checks if the binary was compiled with Rust
func (d *Detector) isRustBinary(exePath string) bool {
	file, err := os.Open(exePath)
	if err != nil {
		return false
	}
	defer file.Close()
	
	// Read first 1KB to look for Rust signatures
	buffer := make([]byte, 1024)
	n, err := file.Read(buffer)
	if err != nil {
		return false
	}
	
	content := string(buffer[:n])
	return strings.Contains(content, "rustc") || strings.Contains(content, "rust_")
}

// extractGoVersion attempts to extract Go version from binary
func (d *Detector) extractGoVersion(exePath string) string {
	// This is a simplified version - in production, use debug/buildinfo
	file, err := os.Open(exePath)
	if err != nil {
		return ""
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, "go1.") {
			// Extract version pattern
			parts := strings.Fields(line)
			for _, part := range parts {
				if strings.HasPrefix(part, "go1.") {
					return part
				}
			}
		}
	}
	
	return ""
}

// ClearCache clears the runtime detection cache
func (d *Detector) ClearCache() {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	d.cache = make(map[uint32]*ProcessInfo)
}

// GetCacheSize returns the current cache size
func (d *Detector) GetCacheSize() int {
	d.mutex.RLock()
	defer d.mutex.RUnlock()
	return len(d.cache)
}
