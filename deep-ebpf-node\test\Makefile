# Test programs Makefile

CC := gcc
CFLAGS := -Wall -Wextra -g -O0 -fno-omit-frame-pointer

.PHONY: all clean test

all: simple_test

simple_test: simple_test.c
	$(CC) $(CFLAGS) -o simple_test simple_test.c

clean:
	rm -f simple_test

test: simple_test
	@echo "Running test program..."
	@echo "In another terminal, run:"
	@echo "sudo ../deep-ebpf-node --target-binary ./simple_test --format human"
	@echo ""
	./simple_test
