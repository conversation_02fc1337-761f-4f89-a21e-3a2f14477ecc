{"name": "@parca/icons", "version": "0.16.72", "description": "Parca commonly used icons", "main": "dist/index.js", "scripts": {"test": "jest --coverage --config ../../../jest.config.js ./src/*", "watch": "tsc-watch --onCompilationComplete 'pnpm run compileStaticFiles'", "build-swc": "swc ./src -d dist --copy-files && pnpm run compileStaticFiles", "build": "tsc && pnpm run compileStaticFiles", "compileStaticFiles": "tailwindcss -o dist/styles.css && mkdir -p ./dist/assets && cp ./src/assets/* ./dist/assets/"}, "keywords": [], "author": "", "license": "ISC", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "feaaafad44b53ecc44f1d9e35905cd7d1243c4a3", "dependencies": {"react": "18.3.1", "react-dom": "18.3.1", "tailwindcss": "3.2.4", "tsc-watch": "6.3.1", "vite-plugin-svgr": "^4.2.0"}}