name: Goreleaser

on:
  push:
    tags:
      - v*

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=go depName=github.com/goreleaser/goreleaser
  GORELEASER_VERSION: v1.26.2
  # renovate: datasource=npm depName=pnpm versioning=npm
  PNPM_VERSION: '10.13.1'

permissions:
  contents: write

jobs:
  release:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    env:
      DOCKER_CLI_EXPERIMENTAL: "enabled"
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Free up disk space
        run: |
          ./scripts/free_disk_space.sh

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version

      - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version-file: .node-version
          cache: 'pnpm'
          cache-dependency-path: ui/pnpm-lock.yaml

      - name: Set up Snapcraft
        run: |
          sudo snap install snapcraft --channel=7.x/stable --classic
          # See https://github.com/goreleaser/goreleaser/issues/1715
          mkdir -p "$HOME/.cache/snapcraft/download"
          mkdir -p "$HOME/.cache/snapcraft/stage-packages"

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@5fdedb94abba051217030cc86d4523cf3f02243d # v4.6.0
        if: startsWith(github.ref, 'refs/tags/')
        with:
          distribution: goreleaser
          version: ${{ env.GORELEASER_VERSION }}
          args: release --clean --timeout=60m
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Archive generated artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: parca-dist-release
          if-no-files-found: error
          path: |
            dist
            !dist/*.txt

  manifests:
    name: Generate and release Kubernetes Manifests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: release
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version

      - name: Set up Jsonnet
        run: ./env-jsonnet.sh

      - name: Generate
        run: cd deploy && make --always-make vendor manifests

      - name: Prepare
        run: |
          tar -zcvf deploy/manifests.tar.gz deploy/manifests
          cp deploy/manifests/kubernetes/manifest.yaml deploy/kubernetes-manifest.yaml
          cp deploy/manifests/openshift/manifest.yaml deploy/openshift-manifest.yaml

      - name: Release
        uses: softprops/action-gh-release@de2c0eb89ae2a093876385947365aca7b0e5f844 # v0.1.15
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            deploy/manifests.tar.gz
            deploy/kubernetes-manifest.yaml
            deploy/openshift-manifest.yaml

  docs:
    name: Publish Docs
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: manifests
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Publish Netlify
        uses: netlify/actions/build@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

      - name: Publish Vercel
        run: |
          curl -X POST "https://api.vercel.com/v1/integrations/deploy/${{ secrets.VERCEL_WEBHOOK }}"

  container:
    name: Build and release container images
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: release
    container:
      # https://github.com/containers/podman/tree/main/contrib/podmanimage
      image: quay.io/containers/podman:v4.9.4
      options: >-
        --device /dev/fuse:rw
        --privileged
        --security-opt label=disable
        --security-opt seccomp=unconfined
    permissions:
      id-token: write
      packages: write
      contents: read

    steps:
      - name: Install dependencies
        run: dnf install --assumeyes --repo fedora git make jq

      - name: Check out code into the Go module directory
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      # The checkout action is supposed to take care of it, but it is not enough :/
      - name: Add repository directory to the git global config as a safe directory
        run: git config --global --add safe.directory /__w/parca/parca

      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: parca-dist-release
          path: dist

      - name: Get branch name
        shell: bash
        run: echo "GITHUB_BRANCH_NAME=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - name: Build container
        run: make container

      - name: Check images created
        run: podman images | grep 'ghcr.io/parca-dev/parca'

      - name: Login to registry
        run: |
          echo "${{ secrets.PERSONAL_ACCESS_TOKEN }}" | podman login -u parca-dev --password-stdin ghcr.io

      - name: Install cosign
        uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1

      - name: Install crane
        uses: imjasonh/setup-crane@00c9e93efa4e1138c9a7a5c594acd6c75a2fbf0c # v0.3

      - name: Push container
        run: |
          make push-container

      - name: Sign container
        run: |
          make sign-container

  snap:
    runs-on: ubuntu-latest
    needs: release
    timeout-minutes: 30
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: parca-dist-release
          path: dist

      - name: Install snapcraft
        run: |
          sudo snap install snapcraft --classic --channel=7.x/stable

      - name: Release to latest/edge
        env:
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_STORE_CREDENTIALS }}
        run: |
          snapcraft upload dist/*_amd64.snap --release stable
          snapcraft upload dist/*_arm64.snap --release stable
