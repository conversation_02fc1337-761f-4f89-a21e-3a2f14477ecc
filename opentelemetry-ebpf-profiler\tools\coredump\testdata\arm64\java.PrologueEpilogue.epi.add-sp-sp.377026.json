{"coredump-ref": "c901e34f8c64796d785b8e5ef0a2cec545426ed66a91b396df46721ebeb695a5", "threads": [{"lwp": 377038, "frames": ["int PrologueEpilogue.b()+0 in PrologueEpilogue.java:17", "int PrologueEpilogue.a()+0 in PrologueEpilogue.java:13", "void PrologueEpilogue.main(java.lang.String[])+2 in PrologueEpilogue.java:27", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7d1f3b", "libjvm.so+0x86368b", "libjvm.so+0x865b6f", "libjli.so+0x40cb", "libjli.so+0x723b", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377045, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd547", "libjvm.so+0xb6df57", "libjvm.so+0xb20b87", "libjvm.so+0x894b8b", "void java.lang.ref.Reference.waitForReferencePendingList()+0 in Reference.java:0", "void java.lang.ref.Reference.processPendingReferences()+0 in Reference.java:253", "void java.lang.ref.Reference$ReferenceHandler.run()+0 in Reference.java:215", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7d1f3b", "libjvm.so+0x7d3233", "libjvm.so+0x88c65f", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377049, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6df03", "libjvm.so+0xb20ae7", "libjvm.so+0xb14543", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377026, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0x82a7", "libjli.so+0x7d8b", "libjli.so+0x5293", "libjli.so+0x66ff", "java+0xacf", "libc-2.33.so+0x21613", "java+0xb77"]}, {"lwp": 377050, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6df03", "libjvm.so+0xb20b87", "libjvm.so+0x59963b", "libjvm.so+0x59bdfb", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377047, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0x101fb", "libjvm.so+0xc09c1b", "libjvm.so+0xcf4303", "libjvm.so+0xb569d3", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377044, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6df03", "libjvm.so+0xb20ae7", "libjvm.so+0xe3ed57", "libjvm.so+0xe3f93b", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377039, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0x101fb", "libjvm.so+0xc09c1b", "libjvm.so+0xe597f7", "libjvm.so+0xe5988f", "<unwinding aborted due to error native_lr_unwinding_mid_trace>"]}, {"lwp": 377040, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd547", "libjvm.so+0xb6df57", "libjvm.so+0xb20ae7", "libjvm.so+0x6ad5d7", "libjvm.so+0x6ae643", "libjvm.so+0x5b1f97", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377041, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0x101fb", "libjvm.so+0xc09c1b", "libjvm.so+0xe597f7", "libjvm.so+0xe5988f", "<unwinding aborted due to error native_lr_unwinding_mid_trace>"]}, {"lwp": 377042, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0x101fb", "libjvm.so+0xc09c1b", "libjvm.so+0x6afe5f", "libjvm.so+0x5b1f97", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377053, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6df03", "libjvm.so+0xb20ae7", "libjvm.so+0xb353fb", "libjvm.so+0xb354e7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377043, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6df03", "libjvm.so+0xb20ae7", "libjvm.so+0x706fd7", "libjvm.so+0x7071f3", "libjvm.so+0x5b1f97", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377054, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6d75f", "libjvm.so+0xb413b3", "libjvm.so+0xd7dc2b", "libjvm.so+0x88d7b7", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "void jdk.internal.ref.CleanerImpl.run()+12 in CleanerImpl.java:140", "void java.lang.Thread.run()+1 in Thread.java:833", "void jdk.internal.misc.InnocuousThread.run()+2 in InnocuousThread.java:162", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7d1f3b", "libjvm.so+0x7d3233", "libjvm.so+0x88c65f", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377052, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd547", "libjvm.so+0xb6df57", "libjvm.so+0xb20ae7", "libjvm.so+0xb35973", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377051, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd85b", "libjvm.so+0xb6df03", "libjvm.so+0xb20ae7", "libjvm.so+0xd74027", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377046, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd547", "libjvm.so+0xb6d573", "libjvm.so+0xb41453", "libjvm.so+0xd7dc2b", "libjvm.so+0x88d7b7", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove()+0 in ReferenceQueue.java:176", "void java.lang.ref.Finalizer$FinalizerThread.run()+17 in Finalizer.java:172", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7d1f3b", "libjvm.so+0x7d3233", "libjvm.so+0x88c65f", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}, {"lwp": 377048, "frames": ["libpthread-2.33.so+0x143c4", "libpthread-2.33.so+0xd547", "libjvm.so+0xb6df57", "libjvm.so+0xb20ae7", "libjvm.so+0xc0a5bb", "libjvm.so+0xdc07f7", "libjvm.so+0xdc5633", "libjvm.so+0xb63fbb", "libpthread-2.33.so+0x6f3b", "libc-2.33.so+0xd2cdb"]}], "modules": null}