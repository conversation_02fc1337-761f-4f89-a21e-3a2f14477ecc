apiVersion: pyrra.dev/v1alpha1
kind: ServiceLevelObjective
metadata:
  name: parca-grpc-profilestore-latency
  namespace: parca
  labels:
    prometheus: k8s
    role: alert-rules
    pyrra.dev/team: parca
spec:
  target: '95'
  window: 4w
  indicator:
    latencyNative:
      latency: 1s
      total:
        metric: grpc_server_handling_seconds{grpc_service="parca.profilestore.v1alpha1.ProfileStoreService",grpc_method="WriteRaw"}
