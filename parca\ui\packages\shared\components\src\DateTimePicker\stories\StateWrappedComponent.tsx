// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useState} from 'react';

import {AbsoluteDate} from '../../DateTimeRangePicker/utils';
import {AbsoluteDateValue, DateTimePicker} from '../index';

const StateWrappedComponent = (props: {value: AbsoluteDateValue}): JSX.Element => {
  const [time, setTime] = useState<AbsoluteDate>(new AbsoluteDate(props.value ?? new Date()));

  return (
    <div className="flex flex-col gap-4">
      <DateTimePicker selected={time} onChange={val => setTime(val)} />
      <span>Evaluated value: {time.getTime().toISOString()}</span>
    </div>
  );
};

export default StateWrappedComponent;
