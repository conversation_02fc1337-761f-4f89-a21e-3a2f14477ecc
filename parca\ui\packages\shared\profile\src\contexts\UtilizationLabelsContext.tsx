// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {ReactNode, createContext, useContext} from 'react';

export interface UtilizationLabels {
  utilizationLabelNames?: string[];
  utilizationFetchLabelValues?: (key: string) => Promise<string[]>;
  utilizationLabelValues?: string[];
  utilizationLabelNamesLoading?: boolean;
}

interface UtilizationLabelsProviderProps {
  children: ReactNode;
  value: UtilizationLabels | undefined;
}

// The UtilizationLabelsContext is used to store the utilization label names and values. It also
// contains the function utilizationFetchLabelValues to fetch the utilization label values.
// This context was created so as to avoid props drilling.
const UtilizationLabelsContext = createContext<UtilizationLabels | undefined>(undefined);

export function UtilizationLabelsProvider({
  children,
  value,
}: UtilizationLabelsProviderProps): JSX.Element {
  return (
    <UtilizationLabelsContext.Provider value={value}>{children}</UtilizationLabelsContext.Provider>
  );
}

export function useUtilizationLabels(): UtilizationLabels | undefined {
  const context = useContext(UtilizationLabelsContext);
  return context;
}
