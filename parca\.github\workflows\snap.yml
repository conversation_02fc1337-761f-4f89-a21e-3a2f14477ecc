name: Snap

on:
  push:
    branches:
      - main
      - release-*
  pull_request:
    branches:
      - main
      - release-*
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=go depName=github.com/goreleaser/goreleaser
  GORELEASER_VERSION: v1.26.2
  # renovate: datasource=npm depName=pnpm versioning=npm
  PNPM_VERSION: '10.13.1'

permissions:
  contents: write

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              "**.go",
              ".github/workflows/snap.yml",
              ".go-version",
              ".node-version",
              "cmd/**",
              "gen/**",
              "go.mod",
              "go.sum",
              "pkg/**",
              "ui/**"
            ]
          skip_after_successful_duplicate: false

  build:
    name: Build Snap (goreleaser)
    needs: skip-check
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 45
    permissions:
      contents: read
    env:
      DOCKER_CLI_EXPERIMENTAL: "enabled"
    steps:
      - name: Remove unnecessary files
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /opt/ghc
          sudo rm -rf "/usr/local/share/boost"
          sudo rm -rf "$AGENT_TOOLSDIRECTORY"
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version
          check-latest: true
          cache: true

      - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version-file: .node-version
          cache: 'pnpm'
          cache-dependency-path: ui/pnpm-lock.yaml

      - name: Set up Snapcraft
        run: |
          sudo snap install snapcraft --channel=7.x/stable --classic
          # See https://github.com/goreleaser/goreleaser/issues/1715
          mkdir -p "$HOME/.cache/snapcraft/download"
          mkdir -p "$HOME/.cache/snapcraft/stage-packages"

      - name: Set up environment
        run: |
          ./env.sh

      - name: Validate
        uses: goreleaser/goreleaser-action@5fdedb94abba051217030cc86d4523cf3f02243d # v4.6.0
        with:
          distribution: goreleaser
          version: ${{ env.GORELEASER_VERSION }}
          args: check

      - name: Set Tag
        run: |
          echo "goreleaser_current_tag=`git describe --match 'v*' --tags`" >> $GITHUB_ENV

      - name: Dry Run
        uses: goreleaser/goreleaser-action@5fdedb94abba051217030cc86d4523cf3f02243d # v4.6.0
        with:
          distribution: goreleaser
          version: ${{ env.GORELEASER_VERSION }}
          args: release --clean --skip=validate --skip-publish --timeout=60m
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GORELEASER_CURRENT_TAG: "${{ env.goreleaser_current_tag }}"

      - name: Archive generated artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: parca-dist-release
          if-no-files-found: error
          path: |
            dist/*.snap

  test:
    name: Test Snap
    needs: build
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: parca-dist-release
          path: dist

      - name: Install snapcraft
        run: |
          sudo snap install snapcraft --classic --channel=7.x/stable

      - name: Install snap & invoke parca
        run: |
          sudo snap install --dangerous dist/parca*_linux_amd64.snap
          sudo snap set parca log-level=debug
          parca --version

      - name: Start Parca - default config
        run: |
          sudo snap start parca

          # Set some options to allow retries while Parca comes back up
          CURL_OPTS=(--max-time 20 --retry 5 --retry-delay 3 --retry-connrefused)

          curl ${CURL_OPTS[@]} http://localhost:7070/
          curl ${CURL_OPTS[@]} http://localhost:7070/metrics

      - name: Configure snap - enable-persistence
        run: |
          sudo snap set parca enable-persistence=true
          sudo snap restart parca

          # Set some options to allow retries while Parca comes back up
          CURL_OPTS=(--max-time 20 --retry 5 --retry-delay 3 --retry-connrefused)

          curl ${CURL_OPTS[@]} http://localhost:7070/
          curl ${CURL_OPTS[@]} http://localhost:7070/metrics

      - name: Configure snap - http-address
        run: |
          sudo snap set parca http-address=":8080"
          sudo snap restart parca

          # Set some options to allow retries while Parca comes back up
          CURL_OPTS=(--max-time 20 --retry 5 --retry-delay 3 --retry-connrefused)

          curl ${CURL_OPTS[@]} http://localhost:8080/
          curl ${CURL_OPTS[@]} http://localhost:8080/metrics

      # In case the above tests fail, dump the logs for inspection
      - name: Dump snap service logs
        if: failure()
        run: |
          sudo snap logs parca -n=all

  release-edge:
    name: Release Snap (latest/edge)
    needs: test
    if: ${{ github.event_name != 'pull_request' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: parca-dist-release
          path: dist

      - name: Install snapcraft
        run: |
          sudo snap install snapcraft --classic --channel=7.x/stable

      - name: Release to latest/edge
        env:
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_STORE_CREDENTIALS }}
        run: |
          snapcraft upload dist/*_amd64.snap --release edge
          snapcraft upload dist/*_arm64.snap --release edge
