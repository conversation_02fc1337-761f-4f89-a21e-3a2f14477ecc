// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/debuginfo/v1alpha1/debuginfo.proto

package debuginfov1alpha1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Types of debuginfo.
type DebuginfoType int32

const (
	// The default type that the API always supported. This type is expected to
	// contain debuginfos for symbolizaton purposes.
	DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED DebuginfoType = 0
	// The type to identify executables. This is meant to be used for
	// disassembling so it is expected to contain executable `.text` section.
	DebuginfoType_DEBUGINFO_TYPE_EXECUTABLE DebuginfoType = 1
	// The type to identify a source tarball. This is expected to contain
	// multiple source files that debuginfo references. It is meant to show code
	// with profiling data inline.
	DebuginfoType_DEBUGINFO_TYPE_SOURCES DebuginfoType = 2
)

// Enum value maps for DebuginfoType.
var (
	DebuginfoType_name = map[int32]string{
		0: "DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED",
		1: "DEBUGINFO_TYPE_EXECUTABLE",
		2: "DEBUGINFO_TYPE_SOURCES",
	}
	DebuginfoType_value = map[string]int32{
		"DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED": 0,
		"DEBUGINFO_TYPE_EXECUTABLE":            1,
		"DEBUGINFO_TYPE_SOURCES":               2,
	}
)

func (x DebuginfoType) Enum() *DebuginfoType {
	p := new(DebuginfoType)
	*p = x
	return p
}

func (x DebuginfoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DebuginfoType) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[0].Descriptor()
}

func (DebuginfoType) Type() protoreflect.EnumType {
	return &file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[0]
}

func (x DebuginfoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DebuginfoType.Descriptor instead.
func (DebuginfoType) EnumDescriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{0}
}

// BuildIDType is the type of build ID.
type BuildIDType int32

const (
	// The build ID is unknown.
	BuildIDType_BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED BuildIDType = 0
	// The build ID is a GNU build ID.
	BuildIDType_BUILD_ID_TYPE_GNU BuildIDType = 1
	// The build ID is an opaque hash.
	BuildIDType_BUILD_ID_TYPE_HASH BuildIDType = 2
	// The build ID is a Go build ID.
	BuildIDType_BUILD_ID_TYPE_GO BuildIDType = 3
)

// Enum value maps for BuildIDType.
var (
	BuildIDType_name = map[int32]string{
		0: "BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED",
		1: "BUILD_ID_TYPE_GNU",
		2: "BUILD_ID_TYPE_HASH",
		3: "BUILD_ID_TYPE_GO",
	}
	BuildIDType_value = map[string]int32{
		"BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED": 0,
		"BUILD_ID_TYPE_GNU":                 1,
		"BUILD_ID_TYPE_HASH":                2,
		"BUILD_ID_TYPE_GO":                  3,
	}
)

func (x BuildIDType) Enum() *BuildIDType {
	p := new(BuildIDType)
	*p = x
	return p
}

func (x BuildIDType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BuildIDType) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[1].Descriptor()
}

func (BuildIDType) Type() protoreflect.EnumType {
	return &file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[1]
}

func (x BuildIDType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BuildIDType.Descriptor instead.
func (BuildIDType) EnumDescriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{1}
}

// The strategy to use for uploading.
type UploadInstructions_UploadStrategy int32

const (
	// The upload is not allowed.
	UploadInstructions_UPLOAD_STRATEGY_UNSPECIFIED UploadInstructions_UploadStrategy = 0
	// The upload is allowed and should be done via the Upload RPC.
	UploadInstructions_UPLOAD_STRATEGY_GRPC UploadInstructions_UploadStrategy = 1
	// The upload is allowed and should be done via a returned signed URL.
	UploadInstructions_UPLOAD_STRATEGY_SIGNED_URL UploadInstructions_UploadStrategy = 2
)

// Enum value maps for UploadInstructions_UploadStrategy.
var (
	UploadInstructions_UploadStrategy_name = map[int32]string{
		0: "UPLOAD_STRATEGY_UNSPECIFIED",
		1: "UPLOAD_STRATEGY_GRPC",
		2: "UPLOAD_STRATEGY_SIGNED_URL",
	}
	UploadInstructions_UploadStrategy_value = map[string]int32{
		"UPLOAD_STRATEGY_UNSPECIFIED": 0,
		"UPLOAD_STRATEGY_GRPC":        1,
		"UPLOAD_STRATEGY_SIGNED_URL":  2,
	}
)

func (x UploadInstructions_UploadStrategy) Enum() *UploadInstructions_UploadStrategy {
	p := new(UploadInstructions_UploadStrategy)
	*p = x
	return p
}

func (x UploadInstructions_UploadStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadInstructions_UploadStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[2].Descriptor()
}

func (UploadInstructions_UploadStrategy) Type() protoreflect.EnumType {
	return &file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[2]
}

func (x UploadInstructions_UploadStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadInstructions_UploadStrategy.Descriptor instead.
func (UploadInstructions_UploadStrategy) EnumDescriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{4, 0}
}

// Source is the source of the debuginfo.
type Debuginfo_Source int32

const (
	// To understand when no source is set we have the unknown source.
	Debuginfo_SOURCE_UNKNOWN_UNSPECIFIED Debuginfo_Source = 0
	// The debuginfo was uploaded by a user/agent.
	Debuginfo_SOURCE_UPLOAD Debuginfo_Source = 1
	// The debuginfo is available from the configured debuginfod server(s).
	Debuginfo_SOURCE_DEBUGINFOD Debuginfo_Source = 2
)

// Enum value maps for Debuginfo_Source.
var (
	Debuginfo_Source_name = map[int32]string{
		0: "SOURCE_UNKNOWN_UNSPECIFIED",
		1: "SOURCE_UPLOAD",
		2: "SOURCE_DEBUGINFOD",
	}
	Debuginfo_Source_value = map[string]int32{
		"SOURCE_UNKNOWN_UNSPECIFIED": 0,
		"SOURCE_UPLOAD":              1,
		"SOURCE_DEBUGINFOD":          2,
	}
)

func (x Debuginfo_Source) Enum() *Debuginfo_Source {
	p := new(Debuginfo_Source)
	*p = x
	return p
}

func (x Debuginfo_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Debuginfo_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[3].Descriptor()
}

func (Debuginfo_Source) Type() protoreflect.EnumType {
	return &file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[3]
}

func (x Debuginfo_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Debuginfo_Source.Descriptor instead.
func (Debuginfo_Source) EnumDescriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{10, 0}
}

// The state of the debuginfo upload.
type DebuginfoUpload_State int32

const (
	// To understand when no upload state is set we have the unknown state.
	DebuginfoUpload_STATE_UNKNOWN_UNSPECIFIED DebuginfoUpload_State = 0
	// The debuginfo is currently being uploaded.
	DebuginfoUpload_STATE_UPLOADING DebuginfoUpload_State = 1
	// The debuginfo has been uploaded successfully.
	DebuginfoUpload_STATE_UPLOADED DebuginfoUpload_State = 2
)

// Enum value maps for DebuginfoUpload_State.
var (
	DebuginfoUpload_State_name = map[int32]string{
		0: "STATE_UNKNOWN_UNSPECIFIED",
		1: "STATE_UPLOADING",
		2: "STATE_UPLOADED",
	}
	DebuginfoUpload_State_value = map[string]int32{
		"STATE_UNKNOWN_UNSPECIFIED": 0,
		"STATE_UPLOADING":           1,
		"STATE_UPLOADED":            2,
	}
)

func (x DebuginfoUpload_State) Enum() *DebuginfoUpload_State {
	p := new(DebuginfoUpload_State)
	*p = x
	return p
}

func (x DebuginfoUpload_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DebuginfoUpload_State) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[4].Descriptor()
}

func (DebuginfoUpload_State) Type() protoreflect.EnumType {
	return &file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes[4]
}

func (x DebuginfoUpload_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DebuginfoUpload_State.Descriptor instead.
func (DebuginfoUpload_State) EnumDescriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{11, 0}
}

// ShouldInitiateUploadRequest is the request for ShouldInitiateUpload.
type ShouldInitiateUploadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The build_id of the debuginfo.
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// Hash of the debuginfo to upload.
	Hash string `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	// Force uploading even if valid debuginfos are already available.
	Force bool `protobuf:"varint,3,opt,name=force,proto3" json:"force,omitempty"`
	// Type of debuginfo to propose uploading.
	Type DebuginfoType `protobuf:"varint,4,opt,name=type,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoType" json:"type,omitempty"`
	// Type of build ID.
	BuildIdType   BuildIDType `protobuf:"varint,5,opt,name=build_id_type,json=buildIdType,proto3,enum=parca.debuginfo.v1alpha1.BuildIDType" json:"build_id_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShouldInitiateUploadRequest) Reset() {
	*x = ShouldInitiateUploadRequest{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShouldInitiateUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShouldInitiateUploadRequest) ProtoMessage() {}

func (x *ShouldInitiateUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShouldInitiateUploadRequest.ProtoReflect.Descriptor instead.
func (*ShouldInitiateUploadRequest) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{0}
}

func (x *ShouldInitiateUploadRequest) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *ShouldInitiateUploadRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ShouldInitiateUploadRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

func (x *ShouldInitiateUploadRequest) GetType() DebuginfoType {
	if x != nil {
		return x.Type
	}
	return DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED
}

func (x *ShouldInitiateUploadRequest) GetBuildIdType() BuildIDType {
	if x != nil {
		return x.BuildIdType
	}
	return BuildIDType_BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED
}

// ShouldInitiateUploadResponse is the response for ShouldInitiateUpload.
type ShouldInitiateUploadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether an upload should be initiated or not.
	ShouldInitiateUpload bool `protobuf:"varint,1,opt,name=should_initiate_upload,json=shouldInitiateUpload,proto3" json:"should_initiate_upload,omitempty"`
	// Reason for why an upload should be initiated or not.
	Reason        string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShouldInitiateUploadResponse) Reset() {
	*x = ShouldInitiateUploadResponse{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShouldInitiateUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShouldInitiateUploadResponse) ProtoMessage() {}

func (x *ShouldInitiateUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShouldInitiateUploadResponse.ProtoReflect.Descriptor instead.
func (*ShouldInitiateUploadResponse) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{1}
}

func (x *ShouldInitiateUploadResponse) GetShouldInitiateUpload() bool {
	if x != nil {
		return x.ShouldInitiateUpload
	}
	return false
}

func (x *ShouldInitiateUploadResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// InitiateUploadRequest is the request to initiate an upload.
type InitiateUploadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The build_id of the debug info to upload.
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// The size of the debug info to upload.
	Size int64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	// Hash of the debuginfo to upload.
	Hash string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	// Force uploading even if valid debuginfos are already available.
	Force bool `protobuf:"varint,4,opt,name=force,proto3" json:"force,omitempty"`
	// Type of debuginfo to propose uploading.
	Type DebuginfoType `protobuf:"varint,5,opt,name=type,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoType" json:"type,omitempty"`
	// Type of build ID.
	BuildIdType   BuildIDType `protobuf:"varint,6,opt,name=build_id_type,json=buildIdType,proto3,enum=parca.debuginfo.v1alpha1.BuildIDType" json:"build_id_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitiateUploadRequest) Reset() {
	*x = InitiateUploadRequest{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitiateUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateUploadRequest) ProtoMessage() {}

func (x *InitiateUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateUploadRequest.ProtoReflect.Descriptor instead.
func (*InitiateUploadRequest) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{2}
}

func (x *InitiateUploadRequest) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *InitiateUploadRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *InitiateUploadRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *InitiateUploadRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

func (x *InitiateUploadRequest) GetType() DebuginfoType {
	if x != nil {
		return x.Type
	}
	return DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED
}

func (x *InitiateUploadRequest) GetBuildIdType() BuildIDType {
	if x != nil {
		return x.BuildIdType
	}
	return BuildIDType_BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED
}

// InitiateUploadResponse is the response to an InitiateUploadRequest.
type InitiateUploadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// UploadInstructions contains the instructions for the client to upload the debuginfo.
	UploadInstructions *UploadInstructions `protobuf:"bytes,1,opt,name=upload_instructions,json=uploadInstructions,proto3" json:"upload_instructions,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *InitiateUploadResponse) Reset() {
	*x = InitiateUploadResponse{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitiateUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateUploadResponse) ProtoMessage() {}

func (x *InitiateUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateUploadResponse.ProtoReflect.Descriptor instead.
func (*InitiateUploadResponse) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{3}
}

func (x *InitiateUploadResponse) GetUploadInstructions() *UploadInstructions {
	if x != nil {
		return x.UploadInstructions
	}
	return nil
}

// UploadInstructions contains the instructions for the client to upload debuginfo.
type UploadInstructions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The build ID of the debuginfo to upload.
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// The upload_id to use for uploading.
	UploadId string `protobuf:"bytes,2,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	// The strategy to use for uploading.
	UploadStrategy UploadInstructions_UploadStrategy `protobuf:"varint,3,opt,name=upload_strategy,json=uploadStrategy,proto3,enum=parca.debuginfo.v1alpha1.UploadInstructions_UploadStrategy" json:"upload_strategy,omitempty"`
	// The signed url to use for uploading using a PUT request when the upload
	// strategy is SIGNED_STRATEGY_URL.
	SignedUrl string `protobuf:"bytes,4,opt,name=signed_url,json=signedUrl,proto3" json:"signed_url,omitempty"`
	// Type of debuginfo the upload instructions are for.
	Type          DebuginfoType `protobuf:"varint,5,opt,name=type,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadInstructions) Reset() {
	*x = UploadInstructions{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadInstructions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadInstructions) ProtoMessage() {}

func (x *UploadInstructions) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadInstructions.ProtoReflect.Descriptor instead.
func (*UploadInstructions) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{4}
}

func (x *UploadInstructions) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *UploadInstructions) GetUploadId() string {
	if x != nil {
		return x.UploadId
	}
	return ""
}

func (x *UploadInstructions) GetUploadStrategy() UploadInstructions_UploadStrategy {
	if x != nil {
		return x.UploadStrategy
	}
	return UploadInstructions_UPLOAD_STRATEGY_UNSPECIFIED
}

func (x *UploadInstructions) GetSignedUrl() string {
	if x != nil {
		return x.SignedUrl
	}
	return ""
}

func (x *UploadInstructions) GetType() DebuginfoType {
	if x != nil {
		return x.Type
	}
	return DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED
}

// MarkUploadFinishedRequest is the request to mark an upload as finished.
type MarkUploadFinishedRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The build_id of the debug info to mark as finished.
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// The upload_id of the debug info to mark as finished.
	UploadId string `protobuf:"bytes,2,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	// The type of debuginfo upload to mark as finished.
	Type          DebuginfoType `protobuf:"varint,3,opt,name=type,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkUploadFinishedRequest) Reset() {
	*x = MarkUploadFinishedRequest{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkUploadFinishedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkUploadFinishedRequest) ProtoMessage() {}

func (x *MarkUploadFinishedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkUploadFinishedRequest.ProtoReflect.Descriptor instead.
func (*MarkUploadFinishedRequest) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{5}
}

func (x *MarkUploadFinishedRequest) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *MarkUploadFinishedRequest) GetUploadId() string {
	if x != nil {
		return x.UploadId
	}
	return ""
}

func (x *MarkUploadFinishedRequest) GetType() DebuginfoType {
	if x != nil {
		return x.Type
	}
	return DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED
}

// MarkUploadFinishedResponse is the response to a MarkUploadFinishedRequest.
type MarkUploadFinishedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkUploadFinishedResponse) Reset() {
	*x = MarkUploadFinishedResponse{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkUploadFinishedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkUploadFinishedResponse) ProtoMessage() {}

func (x *MarkUploadFinishedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkUploadFinishedResponse.ProtoReflect.Descriptor instead.
func (*MarkUploadFinishedResponse) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{6}
}

// UploadRequest upload debug info
type UploadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// data contains either the upload info metadata or the debug info
	//
	// Types that are valid to be assigned to Data:
	//
	//	*UploadRequest_Info
	//	*UploadRequest_ChunkData
	Data          isUploadRequest_Data `protobuf_oneof:"data"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadRequest) Reset() {
	*x = UploadRequest{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadRequest) ProtoMessage() {}

func (x *UploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadRequest.ProtoReflect.Descriptor instead.
func (*UploadRequest) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{7}
}

func (x *UploadRequest) GetData() isUploadRequest_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UploadRequest) GetInfo() *UploadInfo {
	if x != nil {
		if x, ok := x.Data.(*UploadRequest_Info); ok {
			return x.Info
		}
	}
	return nil
}

func (x *UploadRequest) GetChunkData() []byte {
	if x != nil {
		if x, ok := x.Data.(*UploadRequest_ChunkData); ok {
			return x.ChunkData
		}
	}
	return nil
}

type isUploadRequest_Data interface {
	isUploadRequest_Data()
}

type UploadRequest_Info struct {
	// info is the metadata for the debug info
	Info *UploadInfo `protobuf:"bytes,1,opt,name=info,proto3,oneof"`
}

type UploadRequest_ChunkData struct {
	// chunk_data is the raw bytes of the debug info
	ChunkData []byte `protobuf:"bytes,2,opt,name=chunk_data,json=chunkData,proto3,oneof"`
}

func (*UploadRequest_Info) isUploadRequest_Data() {}

func (*UploadRequest_ChunkData) isUploadRequest_Data() {}

// UploadInfo contains the build_id and other metadata for the debug data
type UploadInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// build_id is a unique identifier for the debug data
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// upload_id is a unique identifier for the upload
	UploadId string `protobuf:"bytes,2,opt,name=upload_id,json=uploadId,proto3" json:"upload_id,omitempty"`
	// the type of debuginfo that's being uploaded
	Type          DebuginfoType `protobuf:"varint,3,opt,name=type,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadInfo) Reset() {
	*x = UploadInfo{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadInfo) ProtoMessage() {}

func (x *UploadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadInfo.ProtoReflect.Descriptor instead.
func (*UploadInfo) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{8}
}

func (x *UploadInfo) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *UploadInfo) GetUploadId() string {
	if x != nil {
		return x.UploadId
	}
	return ""
}

func (x *UploadInfo) GetType() DebuginfoType {
	if x != nil {
		return x.Type
	}
	return DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED
}

// UploadResponse returns the build_id and the size of the uploaded debug info
type UploadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// build_id is a unique identifier for the debug data
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// size is the number of bytes of the debug info
	Size          uint64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadResponse) Reset() {
	*x = UploadResponse{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadResponse) ProtoMessage() {}

func (x *UploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadResponse.ProtoReflect.Descriptor instead.
func (*UploadResponse) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{9}
}

func (x *UploadResponse) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *UploadResponse) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

// Debuginfo contains metadata about a debuginfo file.
type Debuginfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// BuildID is the build ID of the debuginfo.
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// Source is the source of the debuginfo.
	Source Debuginfo_Source `protobuf:"varint,2,opt,name=source,proto3,enum=parca.debuginfo.v1alpha1.Debuginfo_Source" json:"source,omitempty"`
	// DebuginfoUpload is the debuginfo upload metadata.
	Upload *DebuginfoUpload `protobuf:"bytes,3,opt,name=upload,proto3" json:"upload,omitempty"`
	// Quality is the quality of the debuginfo. This is set asynchonously by the
	// symbolizer when the debuginfo is actually used.
	Quality *DebuginfoQuality `protobuf:"bytes,4,opt,name=quality,proto3" json:"quality,omitempty"`
	// The debuginfod servers this piece of debuginfo is available at.
	DebuginfodServers []string `protobuf:"bytes,5,rep,name=debuginfod_servers,json=debuginfodServers,proto3" json:"debuginfod_servers,omitempty"`
	// The type of debuginfo.
	Type          DebuginfoType `protobuf:"varint,6,opt,name=type,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Debuginfo) Reset() {
	*x = Debuginfo{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Debuginfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Debuginfo) ProtoMessage() {}

func (x *Debuginfo) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Debuginfo.ProtoReflect.Descriptor instead.
func (*Debuginfo) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{10}
}

func (x *Debuginfo) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *Debuginfo) GetSource() Debuginfo_Source {
	if x != nil {
		return x.Source
	}
	return Debuginfo_SOURCE_UNKNOWN_UNSPECIFIED
}

func (x *Debuginfo) GetUpload() *DebuginfoUpload {
	if x != nil {
		return x.Upload
	}
	return nil
}

func (x *Debuginfo) GetQuality() *DebuginfoQuality {
	if x != nil {
		return x.Quality
	}
	return nil
}

func (x *Debuginfo) GetDebuginfodServers() []string {
	if x != nil {
		return x.DebuginfodServers
	}
	return nil
}

func (x *Debuginfo) GetType() DebuginfoType {
	if x != nil {
		return x.Type
	}
	return DebuginfoType_DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED
}

// DebuginfoUpload contains metadata about a debuginfo upload.
type DebuginfoUpload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// UploadID is the ID of the debuginfo upload.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Hash is the hash of the debuginfo.
	Hash string `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	// State is the current state of the debuginfo upload.
	State DebuginfoUpload_State `protobuf:"varint,3,opt,name=state,proto3,enum=parca.debuginfo.v1alpha1.DebuginfoUpload_State" json:"state,omitempty"`
	// StartedAt is the time the debuginfo upload was started.
	StartedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	// FinishedAt is the time the debuginfo upload was finished.
	FinishedAt    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DebuginfoUpload) Reset() {
	*x = DebuginfoUpload{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DebuginfoUpload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebuginfoUpload) ProtoMessage() {}

func (x *DebuginfoUpload) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebuginfoUpload.ProtoReflect.Descriptor instead.
func (*DebuginfoUpload) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{11}
}

func (x *DebuginfoUpload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DebuginfoUpload) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *DebuginfoUpload) GetState() DebuginfoUpload_State {
	if x != nil {
		return x.State
	}
	return DebuginfoUpload_STATE_UNKNOWN_UNSPECIFIED
}

func (x *DebuginfoUpload) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *DebuginfoUpload) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

// DebuginfoQuality is the quality of the debuginfo.
type DebuginfoQuality struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The debuginfo file is not a valid ELF file.
	NotValidElf bool `protobuf:"varint,1,opt,name=not_valid_elf,json=notValidElf,proto3" json:"not_valid_elf,omitempty"`
	// Whether the debuginfo contains dwarf information.
	HasDwarf bool `protobuf:"varint,2,opt,name=has_dwarf,json=hasDwarf,proto3" json:"has_dwarf,omitempty"`
	// Whether the debuginfo contains Go's pclntab.
	HasGoPclntab bool `protobuf:"varint,3,opt,name=has_go_pclntab,json=hasGoPclntab,proto3" json:"has_go_pclntab,omitempty"`
	// Whether the debuginfo contains symtab.
	HasSymtab bool `protobuf:"varint,4,opt,name=has_symtab,json=hasSymtab,proto3" json:"has_symtab,omitempty"`
	// Whether the debuginfo contains dynsym.
	HasDynsym     bool `protobuf:"varint,5,opt,name=has_dynsym,json=hasDynsym,proto3" json:"has_dynsym,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DebuginfoQuality) Reset() {
	*x = DebuginfoQuality{}
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DebuginfoQuality) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebuginfoQuality) ProtoMessage() {}

func (x *DebuginfoQuality) ProtoReflect() protoreflect.Message {
	mi := &file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebuginfoQuality.ProtoReflect.Descriptor instead.
func (*DebuginfoQuality) Descriptor() ([]byte, []int) {
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP(), []int{12}
}

func (x *DebuginfoQuality) GetNotValidElf() bool {
	if x != nil {
		return x.NotValidElf
	}
	return false
}

func (x *DebuginfoQuality) GetHasDwarf() bool {
	if x != nil {
		return x.HasDwarf
	}
	return false
}

func (x *DebuginfoQuality) GetHasGoPclntab() bool {
	if x != nil {
		return x.HasGoPclntab
	}
	return false
}

func (x *DebuginfoQuality) GetHasSymtab() bool {
	if x != nil {
		return x.HasSymtab
	}
	return false
}

func (x *DebuginfoQuality) GetHasDynsym() bool {
	if x != nil {
		return x.HasDynsym
	}
	return false
}

var File_parca_debuginfo_v1alpha1_debuginfo_proto protoreflect.FileDescriptor

const file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDesc = "" +
	"\n" +
	"(parca/debuginfo/v1alpha1/debuginfo.proto\x12\x18parca.debuginfo.v1alpha1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xea\x01\n" +
	"\x1bShouldInitiateUploadRequest\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x12\n" +
	"\x04hash\x18\x02 \x01(\tR\x04hash\x12\x14\n" +
	"\x05force\x18\x03 \x01(\bR\x05force\x12;\n" +
	"\x04type\x18\x04 \x01(\x0e2'.parca.debuginfo.v1alpha1.DebuginfoTypeR\x04type\x12I\n" +
	"\rbuild_id_type\x18\x05 \x01(\x0e2%.parca.debuginfo.v1alpha1.BuildIDTypeR\vbuildIdType\"l\n" +
	"\x1cShouldInitiateUploadResponse\x124\n" +
	"\x16should_initiate_upload\x18\x01 \x01(\bR\x14shouldInitiateUpload\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"\xf8\x01\n" +
	"\x15InitiateUploadRequest\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x12\n" +
	"\x04size\x18\x02 \x01(\x03R\x04size\x12\x12\n" +
	"\x04hash\x18\x03 \x01(\tR\x04hash\x12\x14\n" +
	"\x05force\x18\x04 \x01(\bR\x05force\x12;\n" +
	"\x04type\x18\x05 \x01(\x0e2'.parca.debuginfo.v1alpha1.DebuginfoTypeR\x04type\x12I\n" +
	"\rbuild_id_type\x18\x06 \x01(\x0e2%.parca.debuginfo.v1alpha1.BuildIDTypeR\vbuildIdType\"w\n" +
	"\x16InitiateUploadResponse\x12]\n" +
	"\x13upload_instructions\x18\x01 \x01(\v2,.parca.debuginfo.v1alpha1.UploadInstructionsR\x12uploadInstructions\"\xfb\x02\n" +
	"\x12UploadInstructions\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x1b\n" +
	"\tupload_id\x18\x02 \x01(\tR\buploadId\x12d\n" +
	"\x0fupload_strategy\x18\x03 \x01(\x0e2;.parca.debuginfo.v1alpha1.UploadInstructions.UploadStrategyR\x0euploadStrategy\x12\x1d\n" +
	"\n" +
	"signed_url\x18\x04 \x01(\tR\tsignedUrl\x12;\n" +
	"\x04type\x18\x05 \x01(\x0e2'.parca.debuginfo.v1alpha1.DebuginfoTypeR\x04type\"k\n" +
	"\x0eUploadStrategy\x12\x1f\n" +
	"\x1bUPLOAD_STRATEGY_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14UPLOAD_STRATEGY_GRPC\x10\x01\x12\x1e\n" +
	"\x1aUPLOAD_STRATEGY_SIGNED_URL\x10\x02\"\x90\x01\n" +
	"\x19MarkUploadFinishedRequest\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x1b\n" +
	"\tupload_id\x18\x02 \x01(\tR\buploadId\x12;\n" +
	"\x04type\x18\x03 \x01(\x0e2'.parca.debuginfo.v1alpha1.DebuginfoTypeR\x04type\"\x1c\n" +
	"\x1aMarkUploadFinishedResponse\"t\n" +
	"\rUploadRequest\x12:\n" +
	"\x04info\x18\x01 \x01(\v2$.parca.debuginfo.v1alpha1.UploadInfoH\x00R\x04info\x12\x1f\n" +
	"\n" +
	"chunk_data\x18\x02 \x01(\fH\x00R\tchunkDataB\x06\n" +
	"\x04data\"\x81\x01\n" +
	"\n" +
	"UploadInfo\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x1b\n" +
	"\tupload_id\x18\x02 \x01(\tR\buploadId\x12;\n" +
	"\x04type\x18\x03 \x01(\x0e2'.parca.debuginfo.v1alpha1.DebuginfoTypeR\x04type\"?\n" +
	"\x0eUploadResponse\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x12\n" +
	"\x04size\x18\x02 \x01(\x04R\x04size\"\xb3\x03\n" +
	"\tDebuginfo\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12B\n" +
	"\x06source\x18\x02 \x01(\x0e2*.parca.debuginfo.v1alpha1.Debuginfo.SourceR\x06source\x12A\n" +
	"\x06upload\x18\x03 \x01(\v2).parca.debuginfo.v1alpha1.DebuginfoUploadR\x06upload\x12D\n" +
	"\aquality\x18\x04 \x01(\v2*.parca.debuginfo.v1alpha1.DebuginfoQualityR\aquality\x12-\n" +
	"\x12debuginfod_servers\x18\x05 \x03(\tR\x11debuginfodServers\x12;\n" +
	"\x04type\x18\x06 \x01(\x0e2'.parca.debuginfo.v1alpha1.DebuginfoTypeR\x04type\"R\n" +
	"\x06Source\x12\x1e\n" +
	"\x1aSOURCE_UNKNOWN_UNSPECIFIED\x10\x00\x12\x11\n" +
	"\rSOURCE_UPLOAD\x10\x01\x12\x15\n" +
	"\x11SOURCE_DEBUGINFOD\x10\x02\"\xc5\x02\n" +
	"\x0fDebuginfoUpload\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04hash\x18\x02 \x01(\tR\x04hash\x12E\n" +
	"\x05state\x18\x03 \x01(\x0e2/.parca.debuginfo.v1alpha1.DebuginfoUpload.StateR\x05state\x129\n" +
	"\n" +
	"started_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12;\n" +
	"\vfinished_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"finishedAt\"O\n" +
	"\x05State\x12\x1d\n" +
	"\x19STATE_UNKNOWN_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fSTATE_UPLOADING\x10\x01\x12\x12\n" +
	"\x0eSTATE_UPLOADED\x10\x02\"\xb7\x01\n" +
	"\x10DebuginfoQuality\x12\"\n" +
	"\rnot_valid_elf\x18\x01 \x01(\bR\vnotValidElf\x12\x1b\n" +
	"\thas_dwarf\x18\x02 \x01(\bR\bhasDwarf\x12$\n" +
	"\x0ehas_go_pclntab\x18\x03 \x01(\bR\fhasGoPclntab\x12\x1d\n" +
	"\n" +
	"has_symtab\x18\x04 \x01(\bR\thasSymtab\x12\x1d\n" +
	"\n" +
	"has_dynsym\x18\x05 \x01(\bR\thasDynsym*t\n" +
	"\rDebuginfoType\x12(\n" +
	"$DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19DEBUGINFO_TYPE_EXECUTABLE\x10\x01\x12\x1a\n" +
	"\x16DEBUGINFO_TYPE_SOURCES\x10\x02*y\n" +
	"\vBuildIDType\x12%\n" +
	"!BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11BUILD_ID_TYPE_GNU\x10\x01\x12\x16\n" +
	"\x12BUILD_ID_TYPE_HASH\x10\x02\x12\x14\n" +
	"\x10BUILD_ID_TYPE_GO\x10\x032\xe3\x04\n" +
	"\x10DebuginfoService\x12q\n" +
	"\x06Upload\x12'.parca.debuginfo.v1alpha1.UploadRequest\x1a(.parca.debuginfo.v1alpha1.UploadResponse\"\x12\x82\xd3\xe4\x93\x02\f:\x01*\"\a/upload(\x01\x12\xa7\x01\n" +
	"\x14ShouldInitiateUpload\x125.parca.debuginfo.v1alpha1.ShouldInitiateUploadRequest\x1a6.parca.debuginfo.v1alpha1.ShouldInitiateUploadResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/shouldinitiateupload\x12\x8f\x01\n" +
	"\x0eInitiateUpload\x12/.parca.debuginfo.v1alpha1.InitiateUploadRequest\x1a0.parca.debuginfo.v1alpha1.InitiateUploadResponse\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/initiateupload\x12\x9f\x01\n" +
	"\x12MarkUploadFinished\x123.parca.debuginfo.v1alpha1.MarkUploadFinishedRequest\x1a4.parca.debuginfo.v1alpha1.MarkUploadFinishedResponse\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/markuploadfinishedB\x84\x02\n" +
	"\x1ccom.parca.debuginfo.v1alpha1B\x0eDebuginfoProtoP\x01ZRgithub.com/parca-dev/parca/gen/proto/go/parca/debuginfo/v1alpha1;debuginfov1alpha1\xa2\x02\x03PDX\xaa\x02\x18Parca.Debuginfo.V1alpha1\xca\x02\x18Parca\\Debuginfo\\V1alpha1\xe2\x02$Parca\\Debuginfo\\V1alpha1\\GPBMetadata\xea\x02\x1aParca::Debuginfo::V1alpha1b\x06proto3"

var (
	file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescOnce sync.Once
	file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescData []byte
)

func file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescGZIP() []byte {
	file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescOnce.Do(func() {
		file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDesc), len(file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDesc)))
	})
	return file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDescData
}

var file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_parca_debuginfo_v1alpha1_debuginfo_proto_goTypes = []any{
	(DebuginfoType)(0),                     // 0: parca.debuginfo.v1alpha1.DebuginfoType
	(BuildIDType)(0),                       // 1: parca.debuginfo.v1alpha1.BuildIDType
	(UploadInstructions_UploadStrategy)(0), // 2: parca.debuginfo.v1alpha1.UploadInstructions.UploadStrategy
	(Debuginfo_Source)(0),                  // 3: parca.debuginfo.v1alpha1.Debuginfo.Source
	(DebuginfoUpload_State)(0),             // 4: parca.debuginfo.v1alpha1.DebuginfoUpload.State
	(*ShouldInitiateUploadRequest)(nil),    // 5: parca.debuginfo.v1alpha1.ShouldInitiateUploadRequest
	(*ShouldInitiateUploadResponse)(nil),   // 6: parca.debuginfo.v1alpha1.ShouldInitiateUploadResponse
	(*InitiateUploadRequest)(nil),          // 7: parca.debuginfo.v1alpha1.InitiateUploadRequest
	(*InitiateUploadResponse)(nil),         // 8: parca.debuginfo.v1alpha1.InitiateUploadResponse
	(*UploadInstructions)(nil),             // 9: parca.debuginfo.v1alpha1.UploadInstructions
	(*MarkUploadFinishedRequest)(nil),      // 10: parca.debuginfo.v1alpha1.MarkUploadFinishedRequest
	(*MarkUploadFinishedResponse)(nil),     // 11: parca.debuginfo.v1alpha1.MarkUploadFinishedResponse
	(*UploadRequest)(nil),                  // 12: parca.debuginfo.v1alpha1.UploadRequest
	(*UploadInfo)(nil),                     // 13: parca.debuginfo.v1alpha1.UploadInfo
	(*UploadResponse)(nil),                 // 14: parca.debuginfo.v1alpha1.UploadResponse
	(*Debuginfo)(nil),                      // 15: parca.debuginfo.v1alpha1.Debuginfo
	(*DebuginfoUpload)(nil),                // 16: parca.debuginfo.v1alpha1.DebuginfoUpload
	(*DebuginfoQuality)(nil),               // 17: parca.debuginfo.v1alpha1.DebuginfoQuality
	(*timestamppb.Timestamp)(nil),          // 18: google.protobuf.Timestamp
}
var file_parca_debuginfo_v1alpha1_debuginfo_proto_depIdxs = []int32{
	0,  // 0: parca.debuginfo.v1alpha1.ShouldInitiateUploadRequest.type:type_name -> parca.debuginfo.v1alpha1.DebuginfoType
	1,  // 1: parca.debuginfo.v1alpha1.ShouldInitiateUploadRequest.build_id_type:type_name -> parca.debuginfo.v1alpha1.BuildIDType
	0,  // 2: parca.debuginfo.v1alpha1.InitiateUploadRequest.type:type_name -> parca.debuginfo.v1alpha1.DebuginfoType
	1,  // 3: parca.debuginfo.v1alpha1.InitiateUploadRequest.build_id_type:type_name -> parca.debuginfo.v1alpha1.BuildIDType
	9,  // 4: parca.debuginfo.v1alpha1.InitiateUploadResponse.upload_instructions:type_name -> parca.debuginfo.v1alpha1.UploadInstructions
	2,  // 5: parca.debuginfo.v1alpha1.UploadInstructions.upload_strategy:type_name -> parca.debuginfo.v1alpha1.UploadInstructions.UploadStrategy
	0,  // 6: parca.debuginfo.v1alpha1.UploadInstructions.type:type_name -> parca.debuginfo.v1alpha1.DebuginfoType
	0,  // 7: parca.debuginfo.v1alpha1.MarkUploadFinishedRequest.type:type_name -> parca.debuginfo.v1alpha1.DebuginfoType
	13, // 8: parca.debuginfo.v1alpha1.UploadRequest.info:type_name -> parca.debuginfo.v1alpha1.UploadInfo
	0,  // 9: parca.debuginfo.v1alpha1.UploadInfo.type:type_name -> parca.debuginfo.v1alpha1.DebuginfoType
	3,  // 10: parca.debuginfo.v1alpha1.Debuginfo.source:type_name -> parca.debuginfo.v1alpha1.Debuginfo.Source
	16, // 11: parca.debuginfo.v1alpha1.Debuginfo.upload:type_name -> parca.debuginfo.v1alpha1.DebuginfoUpload
	17, // 12: parca.debuginfo.v1alpha1.Debuginfo.quality:type_name -> parca.debuginfo.v1alpha1.DebuginfoQuality
	0,  // 13: parca.debuginfo.v1alpha1.Debuginfo.type:type_name -> parca.debuginfo.v1alpha1.DebuginfoType
	4,  // 14: parca.debuginfo.v1alpha1.DebuginfoUpload.state:type_name -> parca.debuginfo.v1alpha1.DebuginfoUpload.State
	18, // 15: parca.debuginfo.v1alpha1.DebuginfoUpload.started_at:type_name -> google.protobuf.Timestamp
	18, // 16: parca.debuginfo.v1alpha1.DebuginfoUpload.finished_at:type_name -> google.protobuf.Timestamp
	12, // 17: parca.debuginfo.v1alpha1.DebuginfoService.Upload:input_type -> parca.debuginfo.v1alpha1.UploadRequest
	5,  // 18: parca.debuginfo.v1alpha1.DebuginfoService.ShouldInitiateUpload:input_type -> parca.debuginfo.v1alpha1.ShouldInitiateUploadRequest
	7,  // 19: parca.debuginfo.v1alpha1.DebuginfoService.InitiateUpload:input_type -> parca.debuginfo.v1alpha1.InitiateUploadRequest
	10, // 20: parca.debuginfo.v1alpha1.DebuginfoService.MarkUploadFinished:input_type -> parca.debuginfo.v1alpha1.MarkUploadFinishedRequest
	14, // 21: parca.debuginfo.v1alpha1.DebuginfoService.Upload:output_type -> parca.debuginfo.v1alpha1.UploadResponse
	6,  // 22: parca.debuginfo.v1alpha1.DebuginfoService.ShouldInitiateUpload:output_type -> parca.debuginfo.v1alpha1.ShouldInitiateUploadResponse
	8,  // 23: parca.debuginfo.v1alpha1.DebuginfoService.InitiateUpload:output_type -> parca.debuginfo.v1alpha1.InitiateUploadResponse
	11, // 24: parca.debuginfo.v1alpha1.DebuginfoService.MarkUploadFinished:output_type -> parca.debuginfo.v1alpha1.MarkUploadFinishedResponse
	21, // [21:25] is the sub-list for method output_type
	17, // [17:21] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_parca_debuginfo_v1alpha1_debuginfo_proto_init() }
func file_parca_debuginfo_v1alpha1_debuginfo_proto_init() {
	if File_parca_debuginfo_v1alpha1_debuginfo_proto != nil {
		return
	}
	file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes[7].OneofWrappers = []any{
		(*UploadRequest_Info)(nil),
		(*UploadRequest_ChunkData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDesc), len(file_parca_debuginfo_v1alpha1_debuginfo_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parca_debuginfo_v1alpha1_debuginfo_proto_goTypes,
		DependencyIndexes: file_parca_debuginfo_v1alpha1_debuginfo_proto_depIdxs,
		EnumInfos:         file_parca_debuginfo_v1alpha1_debuginfo_proto_enumTypes,
		MessageInfos:      file_parca_debuginfo_v1alpha1_debuginfo_proto_msgTypes,
	}.Build()
	File_parca_debuginfo_v1alpha1_debuginfo_proto = out.File
	file_parca_debuginfo_v1alpha1_debuginfo_proto_goTypes = nil
	file_parca_debuginfo_v1alpha1_debuginfo_proto_depIdxs = nil
}
