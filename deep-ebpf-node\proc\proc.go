package proc

import (
	"bytes"
	"errors"
	"os"
	"path"
	"strconv"
	"strings"
)

var root = "/proc"

func Path(pid uint32, subpath ...string) string {
	return path.Join(append([]string{root, strconv.Itoa(int(pid))}, subpath...)...)
}

func HostPath(p string) string {
	return Path(1, "root", p)
}

func GetCmdline(pid uint32) []byte {
	cmdline, err := os.ReadFile(Path(pid, "cmdline"))
	if err != nil {
		return nil
	}
	return bytes.TrimSuffix(cmdline, []byte{0})
}

func GetNsPid(pid uint32) (uint32, error) {
	data, err := os.ReadFile(Path(pid, "status"))
	if err != nil {
		return 0, err
	}
	for _, line := range strings.Split(string(data), "\n") {
		fields := strings.Fields(line)
		if len(fields) < 2 {
			continue
		}
		if fields[0] == "NSpid:" {
			var f string
			switch len(fields) {
			case 2:
				f = fields[1]
			case 3:
				f = fields[2]
			default:
				continue
			}
			nsPid, err := strconv.ParseUint(f, 10, 32)
			if err != nil {
				return 0, err
			}
			return uint32(nsPid), nil
		}
	}
	return 0, errors.New("NSpid not found")
}

func ExecuteInNetNs(hostNetNs, selfNetNs interface{}, fn func() error) error {
	// Simplified implementation for function tracing
	// In the original coroot, this switches network namespaces
	// For function tracing, we can just execute the function directly
	return fn()
}
