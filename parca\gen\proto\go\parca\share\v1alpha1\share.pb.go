// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/share/v1alpha1/share.proto

package sharev1alpha1

import (
	v1alpha1 "github.com/parca-dev/parca/gen/proto/go/parca/query/v1alpha1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UploadRequest represents the request with profile bytes and description.
type UploadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pprof bytes of the profile to be uploaded.
	Profile []byte `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	// Description of the profile.
	Description   string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadRequest) Reset() {
	*x = UploadRequest{}
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadRequest) ProtoMessage() {}

func (x *UploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadRequest.ProtoReflect.Descriptor instead.
func (*UploadRequest) Descriptor() ([]byte, []int) {
	return file_parca_share_v1alpha1_share_proto_rawDescGZIP(), []int{0}
}

func (x *UploadRequest) GetProfile() []byte {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *UploadRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// UploadResponse represents the response with the link that can be used to access the profile.
type UploadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id of the uploaded profile.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// link that can be used to access the profile.
	Link          string `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadResponse) Reset() {
	*x = UploadResponse{}
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadResponse) ProtoMessage() {}

func (x *UploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadResponse.ProtoReflect.Descriptor instead.
func (*UploadResponse) Descriptor() ([]byte, []int) {
	return file_parca_share_v1alpha1_share_proto_rawDescGZIP(), []int{1}
}

func (x *UploadResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UploadResponse) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

// QueryRequest represents the request with the id of the profile to be queried.
type QueryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id of the profile to be queried.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Type of the profile to be queried.
	ProfileType *string `protobuf:"bytes,2,opt,name=profile_type,json=profileType,proto3,oneof" json:"profile_type,omitempty"`
	// report_type is the type of report to return
	ReportType v1alpha1.QueryRequest_ReportType `protobuf:"varint,3,opt,name=report_type,json=reportType,proto3,enum=parca.query.v1alpha1.QueryRequest_ReportType" json:"report_type,omitempty"`
	// filter_query is the query string to filter the profile samples
	//
	// Deprecated: Marked as deprecated in parca/share/v1alpha1/share.proto.
	FilterQuery *string `protobuf:"bytes,4,opt,name=filter_query,json=filterQuery,proto3,oneof" json:"filter_query,omitempty"`
	// node_trim_threshold is the threshold % where the nodes with Value less than this will be removed from the report
	NodeTrimThreshold *float32 `protobuf:"fixed32,5,opt,name=node_trim_threshold,json=nodeTrimThreshold,proto3,oneof" json:"node_trim_threshold,omitempty"`
	// which runtime frames to filter out, often interpreter frames like python or ruby are not super useful by default
	//
	// Deprecated: Marked as deprecated in parca/share/v1alpha1/share.proto.
	RuntimeFilter *v1alpha1.RuntimeFilter `protobuf:"bytes,6,opt,name=runtime_filter,json=runtimeFilter,proto3,oneof" json:"runtime_filter,omitempty"`
	// group_by indicates the fields to group by
	GroupBy *v1alpha1.GroupBy `protobuf:"bytes,7,opt,name=group_by,json=groupBy,proto3,oneof" json:"group_by,omitempty"`
	// invert_call_stack inverts the call stacks in the flamegraph
	InvertCallStack *bool `protobuf:"varint,8,opt,name=invert_call_stack,json=invertCallStack,proto3,oneof" json:"invert_call_stack,omitempty"`
	// filter is a varying set of filter to apply to the query
	Filter []*v1alpha1.Filter `protobuf:"bytes,9,rep,name=filter,proto3" json:"filter,omitempty"`
	// sandwich_by_function is a function name to use for sandwich view functionality
	SandwichByFunction *string `protobuf:"bytes,10,opt,name=sandwich_by_function,json=sandwichByFunction,proto3,oneof" json:"sandwich_by_function,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *QueryRequest) Reset() {
	*x = QueryRequest{}
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRequest) ProtoMessage() {}

func (x *QueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRequest.ProtoReflect.Descriptor instead.
func (*QueryRequest) Descriptor() ([]byte, []int) {
	return file_parca_share_v1alpha1_share_proto_rawDescGZIP(), []int{2}
}

func (x *QueryRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *QueryRequest) GetProfileType() string {
	if x != nil && x.ProfileType != nil {
		return *x.ProfileType
	}
	return ""
}

func (x *QueryRequest) GetReportType() v1alpha1.QueryRequest_ReportType {
	if x != nil {
		return x.ReportType
	}
	return v1alpha1.QueryRequest_ReportType(0)
}

// Deprecated: Marked as deprecated in parca/share/v1alpha1/share.proto.
func (x *QueryRequest) GetFilterQuery() string {
	if x != nil && x.FilterQuery != nil {
		return *x.FilterQuery
	}
	return ""
}

func (x *QueryRequest) GetNodeTrimThreshold() float32 {
	if x != nil && x.NodeTrimThreshold != nil {
		return *x.NodeTrimThreshold
	}
	return 0
}

// Deprecated: Marked as deprecated in parca/share/v1alpha1/share.proto.
func (x *QueryRequest) GetRuntimeFilter() *v1alpha1.RuntimeFilter {
	if x != nil {
		return x.RuntimeFilter
	}
	return nil
}

func (x *QueryRequest) GetGroupBy() *v1alpha1.GroupBy {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *QueryRequest) GetInvertCallStack() bool {
	if x != nil && x.InvertCallStack != nil {
		return *x.InvertCallStack
	}
	return false
}

func (x *QueryRequest) GetFilter() []*v1alpha1.Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *QueryRequest) GetSandwichByFunction() string {
	if x != nil && x.SandwichByFunction != nil {
		return *x.SandwichByFunction
	}
	return ""
}

// ProfileTypesRequest represents the profile types request with the id of the profile to be queried.
type ProfileTypesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id of the profile's types to be queried.
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileTypesRequest) Reset() {
	*x = ProfileTypesRequest{}
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileTypesRequest) ProtoMessage() {}

func (x *ProfileTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileTypesRequest.ProtoReflect.Descriptor instead.
func (*ProfileTypesRequest) Descriptor() ([]byte, []int) {
	return file_parca_share_v1alpha1_share_proto_rawDescGZIP(), []int{3}
}

func (x *ProfileTypesRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ProfileTypesResponse represents the response with the list of available profile types.
type ProfileTypesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of available profile types.
	Types []*v1alpha1.ProfileType `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"`
	// description of the profile uploaded.
	Description   string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileTypesResponse) Reset() {
	*x = ProfileTypesResponse{}
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileTypesResponse) ProtoMessage() {}

func (x *ProfileTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileTypesResponse.ProtoReflect.Descriptor instead.
func (*ProfileTypesResponse) Descriptor() ([]byte, []int) {
	return file_parca_share_v1alpha1_share_proto_rawDescGZIP(), []int{4}
}

func (x *ProfileTypesResponse) GetTypes() []*v1alpha1.ProfileType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ProfileTypesResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// QueryResponse is the returned report for the given query.
type QueryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// report is the generated report
	//
	// Types that are valid to be assigned to Report:
	//
	//	*QueryResponse_Flamegraph
	//	*QueryResponse_Pprof
	//	*QueryResponse_Top
	//	*QueryResponse_Callgraph
	//	*QueryResponse_FlamegraphArrow
	//	*QueryResponse_Source
	//	*QueryResponse_TableArrow
	//	*QueryResponse_ProfileMetadata
	Report isQueryResponse_Report `protobuf_oneof:"report"`
	// total is the total number of samples shown in the report.
	Total int64 `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
	// filtered is the number of samples filtered out of the report.
	Filtered      int64 `protobuf:"varint,6,opt,name=filtered,proto3" json:"filtered,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryResponse) Reset() {
	*x = QueryResponse{}
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryResponse) ProtoMessage() {}

func (x *QueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_share_v1alpha1_share_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryResponse.ProtoReflect.Descriptor instead.
func (*QueryResponse) Descriptor() ([]byte, []int) {
	return file_parca_share_v1alpha1_share_proto_rawDescGZIP(), []int{5}
}

func (x *QueryResponse) GetReport() isQueryResponse_Report {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *QueryResponse) GetFlamegraph() *v1alpha1.Flamegraph {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Flamegraph); ok {
			return x.Flamegraph
		}
	}
	return nil
}

func (x *QueryResponse) GetPprof() []byte {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Pprof); ok {
			return x.Pprof
		}
	}
	return nil
}

func (x *QueryResponse) GetTop() *v1alpha1.Top {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Top); ok {
			return x.Top
		}
	}
	return nil
}

func (x *QueryResponse) GetCallgraph() *v1alpha1.Callgraph {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Callgraph); ok {
			return x.Callgraph
		}
	}
	return nil
}

func (x *QueryResponse) GetFlamegraphArrow() *v1alpha1.FlamegraphArrow {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_FlamegraphArrow); ok {
			return x.FlamegraphArrow
		}
	}
	return nil
}

func (x *QueryResponse) GetSource() *v1alpha1.Source {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Source); ok {
			return x.Source
		}
	}
	return nil
}

func (x *QueryResponse) GetTableArrow() *v1alpha1.TableArrow {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_TableArrow); ok {
			return x.TableArrow
		}
	}
	return nil
}

func (x *QueryResponse) GetProfileMetadata() *v1alpha1.ProfileMetadata {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_ProfileMetadata); ok {
			return x.ProfileMetadata
		}
	}
	return nil
}

func (x *QueryResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *QueryResponse) GetFiltered() int64 {
	if x != nil {
		return x.Filtered
	}
	return 0
}

type isQueryResponse_Report interface {
	isQueryResponse_Report()
}

type QueryResponse_Flamegraph struct {
	// flamegraph is a flamegraph representation of the report
	Flamegraph *v1alpha1.Flamegraph `protobuf:"bytes,1,opt,name=flamegraph,proto3,oneof"`
}

type QueryResponse_Pprof struct {
	// pprof is a pprof profile as compressed bytes
	Pprof []byte `protobuf:"bytes,2,opt,name=pprof,proto3,oneof"`
}

type QueryResponse_Top struct {
	// top is a top list representation of the report
	Top *v1alpha1.Top `protobuf:"bytes,3,opt,name=top,proto3,oneof"`
}

type QueryResponse_Callgraph struct {
	// callgraph is a callgraph nodes and edges representation of the report
	Callgraph *v1alpha1.Callgraph `protobuf:"bytes,4,opt,name=callgraph,proto3,oneof"`
}

type QueryResponse_FlamegraphArrow struct {
	// flamegraph_arrow is a flamegraph encoded as a arrow record
	FlamegraphArrow *v1alpha1.FlamegraphArrow `protobuf:"bytes,7,opt,name=flamegraph_arrow,json=flamegraphArrow,proto3,oneof"`
}

type QueryResponse_Source struct {
	// source is the source report type result
	Source *v1alpha1.Source `protobuf:"bytes,8,opt,name=source,proto3,oneof"`
}

type QueryResponse_TableArrow struct {
	// table_arrow is a table encoded as a arrow record
	TableArrow *v1alpha1.TableArrow `protobuf:"bytes,9,opt,name=table_arrow,json=tableArrow,proto3,oneof"`
}

type QueryResponse_ProfileMetadata struct {
	// profile_metadata contains metadata about the profile i.e. binaries, labels
	ProfileMetadata *v1alpha1.ProfileMetadata `protobuf:"bytes,10,opt,name=profile_metadata,json=profileMetadata,proto3,oneof"`
}

func (*QueryResponse_Flamegraph) isQueryResponse_Report() {}

func (*QueryResponse_Pprof) isQueryResponse_Report() {}

func (*QueryResponse_Top) isQueryResponse_Report() {}

func (*QueryResponse_Callgraph) isQueryResponse_Report() {}

func (*QueryResponse_FlamegraphArrow) isQueryResponse_Report() {}

func (*QueryResponse_Source) isQueryResponse_Report() {}

func (*QueryResponse_TableArrow) isQueryResponse_Report() {}

func (*QueryResponse_ProfileMetadata) isQueryResponse_Report() {}

var File_parca_share_v1alpha1_share_proto protoreflect.FileDescriptor

const file_parca_share_v1alpha1_share_proto_rawDesc = "" +
	"\n" +
	" parca/share/v1alpha1/share.proto\x12\x14parca.share.v1alpha1\x1a parca/query/v1alpha1/query.proto\"K\n" +
	"\rUploadRequest\x12\x18\n" +
	"\aprofile\x18\x01 \x01(\fR\aprofile\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"4\n" +
	"\x0eUploadResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04link\x18\x02 \x01(\tR\x04link\"\xb2\x05\n" +
	"\fQueryRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12&\n" +
	"\fprofile_type\x18\x02 \x01(\tH\x00R\vprofileType\x88\x01\x01\x12N\n" +
	"\vreport_type\x18\x03 \x01(\x0e2-.parca.query.v1alpha1.QueryRequest.ReportTypeR\n" +
	"reportType\x12*\n" +
	"\ffilter_query\x18\x04 \x01(\tB\x02\x18\x01H\x01R\vfilterQuery\x88\x01\x01\x123\n" +
	"\x13node_trim_threshold\x18\x05 \x01(\x02H\x02R\x11nodeTrimThreshold\x88\x01\x01\x12S\n" +
	"\x0eruntime_filter\x18\x06 \x01(\v2#.parca.query.v1alpha1.RuntimeFilterB\x02\x18\x01H\x03R\rruntimeFilter\x88\x01\x01\x12=\n" +
	"\bgroup_by\x18\a \x01(\v2\x1d.parca.query.v1alpha1.GroupByH\x04R\agroupBy\x88\x01\x01\x12/\n" +
	"\x11invert_call_stack\x18\b \x01(\bH\x05R\x0finvertCallStack\x88\x01\x01\x124\n" +
	"\x06filter\x18\t \x03(\v2\x1c.parca.query.v1alpha1.FilterR\x06filter\x125\n" +
	"\x14sandwich_by_function\x18\n" +
	" \x01(\tH\x06R\x12sandwichByFunction\x88\x01\x01B\x0f\n" +
	"\r_profile_typeB\x0f\n" +
	"\r_filter_queryB\x16\n" +
	"\x14_node_trim_thresholdB\x11\n" +
	"\x0f_runtime_filterB\v\n" +
	"\t_group_byB\x14\n" +
	"\x12_invert_call_stackB\x17\n" +
	"\x15_sandwich_by_function\"%\n" +
	"\x13ProfileTypesRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"q\n" +
	"\x14ProfileTypesResponse\x127\n" +
	"\x05types\x18\x01 \x03(\v2!.parca.query.v1alpha1.ProfileTypeR\x05types\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"\xbc\x04\n" +
	"\rQueryResponse\x12B\n" +
	"\n" +
	"flamegraph\x18\x01 \x01(\v2 .parca.query.v1alpha1.FlamegraphH\x00R\n" +
	"flamegraph\x12\x16\n" +
	"\x05pprof\x18\x02 \x01(\fH\x00R\x05pprof\x12-\n" +
	"\x03top\x18\x03 \x01(\v2\x19.parca.query.v1alpha1.TopH\x00R\x03top\x12?\n" +
	"\tcallgraph\x18\x04 \x01(\v2\x1f.parca.query.v1alpha1.CallgraphH\x00R\tcallgraph\x12R\n" +
	"\x10flamegraph_arrow\x18\a \x01(\v2%.parca.query.v1alpha1.FlamegraphArrowH\x00R\x0fflamegraphArrow\x126\n" +
	"\x06source\x18\b \x01(\v2\x1c.parca.query.v1alpha1.SourceH\x00R\x06source\x12C\n" +
	"\vtable_arrow\x18\t \x01(\v2 .parca.query.v1alpha1.TableArrowH\x00R\n" +
	"tableArrow\x12R\n" +
	"\x10profile_metadata\x18\n" +
	" \x01(\v2%.parca.query.v1alpha1.ProfileMetadataH\x00R\x0fprofileMetadata\x12\x14\n" +
	"\x05total\x18\x05 \x01(\x03R\x05total\x12\x1a\n" +
	"\bfiltered\x18\x06 \x01(\x03R\bfilteredB\b\n" +
	"\x06report2\xa2\x02\n" +
	"\fShareService\x12U\n" +
	"\x06Upload\x12#.parca.share.v1alpha1.UploadRequest\x1a$.parca.share.v1alpha1.UploadResponse\"\x00\x12R\n" +
	"\x05Query\x12\".parca.share.v1alpha1.QueryRequest\x1a#.parca.share.v1alpha1.QueryResponse\"\x00\x12g\n" +
	"\fProfileTypes\x12).parca.share.v1alpha1.ProfileTypesRequest\x1a*.parca.share.v1alpha1.ProfileTypesResponse\"\x00B\xe4\x01\n" +
	"\x18com.parca.share.v1alpha1B\n" +
	"ShareProtoP\x01ZJgithub.com/parca-dev/parca/gen/proto/go/parca/share/v1alpha1;sharev1alpha1\xa2\x02\x03PSX\xaa\x02\x14Parca.Share.V1alpha1\xca\x02\x14Parca\\Share\\V1alpha1\xe2\x02 Parca\\Share\\V1alpha1\\GPBMetadata\xea\x02\x16Parca::Share::V1alpha1b\x06proto3"

var (
	file_parca_share_v1alpha1_share_proto_rawDescOnce sync.Once
	file_parca_share_v1alpha1_share_proto_rawDescData []byte
)

func file_parca_share_v1alpha1_share_proto_rawDescGZIP() []byte {
	file_parca_share_v1alpha1_share_proto_rawDescOnce.Do(func() {
		file_parca_share_v1alpha1_share_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_share_v1alpha1_share_proto_rawDesc), len(file_parca_share_v1alpha1_share_proto_rawDesc)))
	})
	return file_parca_share_v1alpha1_share_proto_rawDescData
}

var file_parca_share_v1alpha1_share_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_parca_share_v1alpha1_share_proto_goTypes = []any{
	(*UploadRequest)(nil),                 // 0: parca.share.v1alpha1.UploadRequest
	(*UploadResponse)(nil),                // 1: parca.share.v1alpha1.UploadResponse
	(*QueryRequest)(nil),                  // 2: parca.share.v1alpha1.QueryRequest
	(*ProfileTypesRequest)(nil),           // 3: parca.share.v1alpha1.ProfileTypesRequest
	(*ProfileTypesResponse)(nil),          // 4: parca.share.v1alpha1.ProfileTypesResponse
	(*QueryResponse)(nil),                 // 5: parca.share.v1alpha1.QueryResponse
	(v1alpha1.QueryRequest_ReportType)(0), // 6: parca.query.v1alpha1.QueryRequest.ReportType
	(*v1alpha1.RuntimeFilter)(nil),        // 7: parca.query.v1alpha1.RuntimeFilter
	(*v1alpha1.GroupBy)(nil),              // 8: parca.query.v1alpha1.GroupBy
	(*v1alpha1.Filter)(nil),               // 9: parca.query.v1alpha1.Filter
	(*v1alpha1.ProfileType)(nil),          // 10: parca.query.v1alpha1.ProfileType
	(*v1alpha1.Flamegraph)(nil),           // 11: parca.query.v1alpha1.Flamegraph
	(*v1alpha1.Top)(nil),                  // 12: parca.query.v1alpha1.Top
	(*v1alpha1.Callgraph)(nil),            // 13: parca.query.v1alpha1.Callgraph
	(*v1alpha1.FlamegraphArrow)(nil),      // 14: parca.query.v1alpha1.FlamegraphArrow
	(*v1alpha1.Source)(nil),               // 15: parca.query.v1alpha1.Source
	(*v1alpha1.TableArrow)(nil),           // 16: parca.query.v1alpha1.TableArrow
	(*v1alpha1.ProfileMetadata)(nil),      // 17: parca.query.v1alpha1.ProfileMetadata
}
var file_parca_share_v1alpha1_share_proto_depIdxs = []int32{
	6,  // 0: parca.share.v1alpha1.QueryRequest.report_type:type_name -> parca.query.v1alpha1.QueryRequest.ReportType
	7,  // 1: parca.share.v1alpha1.QueryRequest.runtime_filter:type_name -> parca.query.v1alpha1.RuntimeFilter
	8,  // 2: parca.share.v1alpha1.QueryRequest.group_by:type_name -> parca.query.v1alpha1.GroupBy
	9,  // 3: parca.share.v1alpha1.QueryRequest.filter:type_name -> parca.query.v1alpha1.Filter
	10, // 4: parca.share.v1alpha1.ProfileTypesResponse.types:type_name -> parca.query.v1alpha1.ProfileType
	11, // 5: parca.share.v1alpha1.QueryResponse.flamegraph:type_name -> parca.query.v1alpha1.Flamegraph
	12, // 6: parca.share.v1alpha1.QueryResponse.top:type_name -> parca.query.v1alpha1.Top
	13, // 7: parca.share.v1alpha1.QueryResponse.callgraph:type_name -> parca.query.v1alpha1.Callgraph
	14, // 8: parca.share.v1alpha1.QueryResponse.flamegraph_arrow:type_name -> parca.query.v1alpha1.FlamegraphArrow
	15, // 9: parca.share.v1alpha1.QueryResponse.source:type_name -> parca.query.v1alpha1.Source
	16, // 10: parca.share.v1alpha1.QueryResponse.table_arrow:type_name -> parca.query.v1alpha1.TableArrow
	17, // 11: parca.share.v1alpha1.QueryResponse.profile_metadata:type_name -> parca.query.v1alpha1.ProfileMetadata
	0,  // 12: parca.share.v1alpha1.ShareService.Upload:input_type -> parca.share.v1alpha1.UploadRequest
	2,  // 13: parca.share.v1alpha1.ShareService.Query:input_type -> parca.share.v1alpha1.QueryRequest
	3,  // 14: parca.share.v1alpha1.ShareService.ProfileTypes:input_type -> parca.share.v1alpha1.ProfileTypesRequest
	1,  // 15: parca.share.v1alpha1.ShareService.Upload:output_type -> parca.share.v1alpha1.UploadResponse
	5,  // 16: parca.share.v1alpha1.ShareService.Query:output_type -> parca.share.v1alpha1.QueryResponse
	4,  // 17: parca.share.v1alpha1.ShareService.ProfileTypes:output_type -> parca.share.v1alpha1.ProfileTypesResponse
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_parca_share_v1alpha1_share_proto_init() }
func file_parca_share_v1alpha1_share_proto_init() {
	if File_parca_share_v1alpha1_share_proto != nil {
		return
	}
	file_parca_share_v1alpha1_share_proto_msgTypes[2].OneofWrappers = []any{}
	file_parca_share_v1alpha1_share_proto_msgTypes[5].OneofWrappers = []any{
		(*QueryResponse_Flamegraph)(nil),
		(*QueryResponse_Pprof)(nil),
		(*QueryResponse_Top)(nil),
		(*QueryResponse_Callgraph)(nil),
		(*QueryResponse_FlamegraphArrow)(nil),
		(*QueryResponse_Source)(nil),
		(*QueryResponse_TableArrow)(nil),
		(*QueryResponse_ProfileMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_share_v1alpha1_share_proto_rawDesc), len(file_parca_share_v1alpha1_share_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parca_share_v1alpha1_share_proto_goTypes,
		DependencyIndexes: file_parca_share_v1alpha1_share_proto_depIdxs,
		MessageInfos:      file_parca_share_v1alpha1_share_proto_msgTypes,
	}.Build()
	File_parca_share_v1alpha1_share_proto = out.File
	file_parca_share_v1alpha1_share_proto_goTypes = nil
	file_parca_share_v1alpha1_share_proto_depIdxs = nil
}
