// @generated by protobuf-ts 2.7.0 with parameter long_type_string,generate_dependencies
// @generated from protobuf file "parca/share/share.proto" (package "polarsignals.share", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { Share } from "./share";
import type { QueryResponse } from "../query/v1alpha1/query";
import type { ProfileRequest } from "./share";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { UploadResponse } from "./share";
import type { UploadRequest } from "./share";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * Service that exposes APIs for sharing profiles.
 *
 * @generated from protobuf service polarsignals.share.Share
 */
export interface IShareClient {
    /**
     * Uploads the profile and returns the link that can be used to access it.
     *
     * @generated from protobuf rpc: Upload(polarsignals.share.UploadRequest) returns (polarsignals.share.UploadResponse);
     */
    upload(input: UploadRequest, options?: RpcOptions): UnaryCall<UploadRequest, UploadResponse>;
    /**
     * Query performs a profile query
     *
     * @generated from protobuf rpc: Query(polarsignals.share.ProfileRequest) returns (parca.query.v1alpha1.QueryResponse);
     */
    query(input: ProfileRequest, options?: RpcOptions): UnaryCall<ProfileRequest, QueryResponse>;
}
/**
 * Service that exposes APIs for sharing profiles.
 *
 * @generated from protobuf service polarsignals.share.Share
 */
export class ShareClient implements IShareClient, ServiceInfo {
    typeName = Share.typeName;
    methods = Share.methods;
    options = Share.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * Uploads the profile and returns the link that can be used to access it.
     *
     * @generated from protobuf rpc: Upload(polarsignals.share.UploadRequest) returns (polarsignals.share.UploadResponse);
     */
    upload(input: UploadRequest, options?: RpcOptions): UnaryCall<UploadRequest, UploadResponse> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<UploadRequest, UploadResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Query performs a profile query
     *
     * @generated from protobuf rpc: Query(polarsignals.share.ProfileRequest) returns (parca.query.v1alpha1.QueryResponse);
     */
    query(input: ProfileRequest, options?: RpcOptions): UnaryCall<ProfileRequest, QueryResponse> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<ProfileRequest, QueryResponse>("unary", this._transport, method, opt, input);
    }
}
