// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/scrape/v1alpha1/scrape.proto

package scrapev1alpha1

import (
	v1alpha1 "github.com/parca-dev/parca/gen/proto/go/parca/profilestore/v1alpha1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// State represents the current state of a target
type TargetsRequest_State int32

const (
	// STATE_ANY_UNSPECIFIED unspecified
	TargetsRequest_STATE_ANY_UNSPECIFIED TargetsRequest_State = 0
	// STATE_ACTIVE target active state
	TargetsRequest_STATE_ACTIVE TargetsRequest_State = 1
	// STATE_DROPPED target dropped state
	TargetsRequest_STATE_DROPPED TargetsRequest_State = 2
)

// Enum value maps for TargetsRequest_State.
var (
	TargetsRequest_State_name = map[int32]string{
		0: "STATE_ANY_UNSPECIFIED",
		1: "STATE_ACTIVE",
		2: "STATE_DROPPED",
	}
	TargetsRequest_State_value = map[string]int32{
		"STATE_ANY_UNSPECIFIED": 0,
		"STATE_ACTIVE":          1,
		"STATE_DROPPED":         2,
	}
)

func (x TargetsRequest_State) Enum() *TargetsRequest_State {
	p := new(TargetsRequest_State)
	*p = x
	return p
}

func (x TargetsRequest_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetsRequest_State) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_scrape_v1alpha1_scrape_proto_enumTypes[0].Descriptor()
}

func (TargetsRequest_State) Type() protoreflect.EnumType {
	return &file_parca_scrape_v1alpha1_scrape_proto_enumTypes[0]
}

func (x TargetsRequest_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetsRequest_State.Descriptor instead.
func (TargetsRequest_State) EnumDescriptor() ([]byte, []int) {
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP(), []int{0, 0}
}

// Health are the possible health values of a target
type Target_Health int32

const (
	// HEALTH_UNKNOWN_UNSPECIFIED unspecified
	Target_HEALTH_UNKNOWN_UNSPECIFIED Target_Health = 0
	// HEALTH_GOOD healthy target
	Target_HEALTH_GOOD Target_Health = 1
	// HEALTH_BAD unhealthy target
	Target_HEALTH_BAD Target_Health = 2
)

// Enum value maps for Target_Health.
var (
	Target_Health_name = map[int32]string{
		0: "HEALTH_UNKNOWN_UNSPECIFIED",
		1: "HEALTH_GOOD",
		2: "HEALTH_BAD",
	}
	Target_Health_value = map[string]int32{
		"HEALTH_UNKNOWN_UNSPECIFIED": 0,
		"HEALTH_GOOD":                1,
		"HEALTH_BAD":                 2,
	}
)

func (x Target_Health) Enum() *Target_Health {
	p := new(Target_Health)
	*p = x
	return p
}

func (x Target_Health) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Target_Health) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_scrape_v1alpha1_scrape_proto_enumTypes[1].Descriptor()
}

func (Target_Health) Type() protoreflect.EnumType {
	return &file_parca_scrape_v1alpha1_scrape_proto_enumTypes[1]
}

func (x Target_Health) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Target_Health.Descriptor instead.
func (Target_Health) EnumDescriptor() ([]byte, []int) {
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP(), []int{3, 0}
}

// TargetsRequest contains the parameters for the set of targets to return
type TargetsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// state is the state of targets to returns
	State         TargetsRequest_State `protobuf:"varint,1,opt,name=state,proto3,enum=parca.scrape.v1alpha1.TargetsRequest_State" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TargetsRequest) Reset() {
	*x = TargetsRequest{}
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TargetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetsRequest) ProtoMessage() {}

func (x *TargetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetsRequest.ProtoReflect.Descriptor instead.
func (*TargetsRequest) Descriptor() ([]byte, []int) {
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP(), []int{0}
}

func (x *TargetsRequest) GetState() TargetsRequest_State {
	if x != nil {
		return x.State
	}
	return TargetsRequest_STATE_ANY_UNSPECIFIED
}

// TargetsResponse is the set of targets for the given requested state
type TargetsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// targets is the mapping of targets
	Targets       map[string]*Targets `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TargetsResponse) Reset() {
	*x = TargetsResponse{}
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TargetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetsResponse) ProtoMessage() {}

func (x *TargetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetsResponse.ProtoReflect.Descriptor instead.
func (*TargetsResponse) Descriptor() ([]byte, []int) {
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP(), []int{1}
}

func (x *TargetsResponse) GetTargets() map[string]*Targets {
	if x != nil {
		return x.Targets
	}
	return nil
}

// Targets is a list of targets
type Targets struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// targets is a list of targets
	Targets       []*Target `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Targets) Reset() {
	*x = Targets{}
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Targets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Targets) ProtoMessage() {}

func (x *Targets) ProtoReflect() protoreflect.Message {
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Targets.ProtoReflect.Descriptor instead.
func (*Targets) Descriptor() ([]byte, []int) {
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP(), []int{2}
}

func (x *Targets) GetTargets() []*Target {
	if x != nil {
		return x.Targets
	}
	return nil
}

// Target is the scrape target representation
type Target struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// discovered_labels are the set of labels for the target that have been discovered
	DiscoveredLabels *v1alpha1.LabelSet `protobuf:"bytes,1,opt,name=discovered_labels,json=discoveredLabels,proto3" json:"discovered_labels,omitempty"`
	// labels are the set of labels given for the target
	Labels *v1alpha1.LabelSet `protobuf:"bytes,2,opt,name=labels,proto3" json:"labels,omitempty"`
	// last_error is the error message most recently received from a scrape attempt
	LastError string `protobuf:"bytes,3,opt,name=last_error,json=lastError,proto3" json:"last_error,omitempty"`
	// last_scrape is the time stamp the last scrape request was performed
	LastScrape *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_scrape,json=lastScrape,proto3" json:"last_scrape,omitempty"`
	// last_scrape_duration is the duration of the last scrape request
	LastScrapeDuration *durationpb.Duration `protobuf:"bytes,5,opt,name=last_scrape_duration,json=lastScrapeDuration,proto3" json:"last_scrape_duration,omitempty"`
	// url is the url of the target
	Url string `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	// health indicates the current health of the target
	Health        Target_Health `protobuf:"varint,7,opt,name=health,proto3,enum=parca.scrape.v1alpha1.Target_Health" json:"health,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Target) Reset() {
	*x = Target{}
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Target) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Target) ProtoMessage() {}

func (x *Target) ProtoReflect() protoreflect.Message {
	mi := &file_parca_scrape_v1alpha1_scrape_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Target.ProtoReflect.Descriptor instead.
func (*Target) Descriptor() ([]byte, []int) {
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP(), []int{3}
}

func (x *Target) GetDiscoveredLabels() *v1alpha1.LabelSet {
	if x != nil {
		return x.DiscoveredLabels
	}
	return nil
}

func (x *Target) GetLabels() *v1alpha1.LabelSet {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Target) GetLastError() string {
	if x != nil {
		return x.LastError
	}
	return ""
}

func (x *Target) GetLastScrape() *timestamppb.Timestamp {
	if x != nil {
		return x.LastScrape
	}
	return nil
}

func (x *Target) GetLastScrapeDuration() *durationpb.Duration {
	if x != nil {
		return x.LastScrapeDuration
	}
	return nil
}

func (x *Target) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Target) GetHealth() Target_Health {
	if x != nil {
		return x.Health
	}
	return Target_HEALTH_UNKNOWN_UNSPECIFIED
}

var File_parca_scrape_v1alpha1_scrape_proto protoreflect.FileDescriptor

const file_parca_scrape_v1alpha1_scrape_proto_rawDesc = "" +
	"\n" +
	"\"parca/scrape/v1alpha1/scrape.proto\x12\x15parca.scrape.v1alpha1\x1a\x1cgoogle/api/annotations.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a.parca/profilestore/v1alpha1/profilestore.proto\"\x9c\x01\n" +
	"\x0eTargetsRequest\x12A\n" +
	"\x05state\x18\x01 \x01(\x0e2+.parca.scrape.v1alpha1.TargetsRequest.StateR\x05state\"G\n" +
	"\x05State\x12\x19\n" +
	"\x15STATE_ANY_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fSTATE_ACTIVE\x10\x01\x12\x11\n" +
	"\rSTATE_DROPPED\x10\x02\"\xbc\x01\n" +
	"\x0fTargetsResponse\x12M\n" +
	"\atargets\x18\x01 \x03(\v23.parca.scrape.v1alpha1.TargetsResponse.TargetsEntryR\atargets\x1aZ\n" +
	"\fTargetsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x124\n" +
	"\x05value\x18\x02 \x01(\v2\x1e.parca.scrape.v1alpha1.TargetsR\x05value:\x028\x01\"B\n" +
	"\aTargets\x127\n" +
	"\atargets\x18\x01 \x03(\v2\x1d.parca.scrape.v1alpha1.TargetR\atargets\"\xdf\x03\n" +
	"\x06Target\x12R\n" +
	"\x11discovered_labels\x18\x01 \x01(\v2%.parca.profilestore.v1alpha1.LabelSetR\x10discoveredLabels\x12=\n" +
	"\x06labels\x18\x02 \x01(\v2%.parca.profilestore.v1alpha1.LabelSetR\x06labels\x12\x1d\n" +
	"\n" +
	"last_error\x18\x03 \x01(\tR\tlastError\x12;\n" +
	"\vlast_scrape\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastScrape\x12K\n" +
	"\x14last_scrape_duration\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\x12lastScrapeDuration\x12\x10\n" +
	"\x03url\x18\x06 \x01(\tR\x03url\x12<\n" +
	"\x06health\x18\a \x01(\x0e2$.parca.scrape.v1alpha1.Target.HealthR\x06health\"I\n" +
	"\x06Health\x12\x1e\n" +
	"\x1aHEALTH_UNKNOWN_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vHEALTH_GOOD\x10\x01\x12\x0e\n" +
	"\n" +
	"HEALTH_BAD\x10\x022{\n" +
	"\rScrapeService\x12j\n" +
	"\aTargets\x12%.parca.scrape.v1alpha1.TargetsRequest\x1a&.parca.scrape.v1alpha1.TargetsResponse\"\x10\x82\xd3\xe4\x93\x02\n" +
	"\x12\b/targetsB\xec\x01\n" +
	"\x19com.parca.scrape.v1alpha1B\vScrapeProtoP\x01ZLgithub.com/parca-dev/parca/gen/proto/go/parca/scrape/v1alpha1;scrapev1alpha1\xa2\x02\x03PSX\xaa\x02\x15Parca.Scrape.V1alpha1\xca\x02\x15Parca\\Scrape\\V1alpha1\xe2\x02!Parca\\Scrape\\V1alpha1\\GPBMetadata\xea\x02\x17Parca::Scrape::V1alpha1b\x06proto3"

var (
	file_parca_scrape_v1alpha1_scrape_proto_rawDescOnce sync.Once
	file_parca_scrape_v1alpha1_scrape_proto_rawDescData []byte
)

func file_parca_scrape_v1alpha1_scrape_proto_rawDescGZIP() []byte {
	file_parca_scrape_v1alpha1_scrape_proto_rawDescOnce.Do(func() {
		file_parca_scrape_v1alpha1_scrape_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_scrape_v1alpha1_scrape_proto_rawDesc), len(file_parca_scrape_v1alpha1_scrape_proto_rawDesc)))
	})
	return file_parca_scrape_v1alpha1_scrape_proto_rawDescData
}

var file_parca_scrape_v1alpha1_scrape_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_parca_scrape_v1alpha1_scrape_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_parca_scrape_v1alpha1_scrape_proto_goTypes = []any{
	(TargetsRequest_State)(0),     // 0: parca.scrape.v1alpha1.TargetsRequest.State
	(Target_Health)(0),            // 1: parca.scrape.v1alpha1.Target.Health
	(*TargetsRequest)(nil),        // 2: parca.scrape.v1alpha1.TargetsRequest
	(*TargetsResponse)(nil),       // 3: parca.scrape.v1alpha1.TargetsResponse
	(*Targets)(nil),               // 4: parca.scrape.v1alpha1.Targets
	(*Target)(nil),                // 5: parca.scrape.v1alpha1.Target
	nil,                           // 6: parca.scrape.v1alpha1.TargetsResponse.TargetsEntry
	(*v1alpha1.LabelSet)(nil),     // 7: parca.profilestore.v1alpha1.LabelSet
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 9: google.protobuf.Duration
}
var file_parca_scrape_v1alpha1_scrape_proto_depIdxs = []int32{
	0,  // 0: parca.scrape.v1alpha1.TargetsRequest.state:type_name -> parca.scrape.v1alpha1.TargetsRequest.State
	6,  // 1: parca.scrape.v1alpha1.TargetsResponse.targets:type_name -> parca.scrape.v1alpha1.TargetsResponse.TargetsEntry
	5,  // 2: parca.scrape.v1alpha1.Targets.targets:type_name -> parca.scrape.v1alpha1.Target
	7,  // 3: parca.scrape.v1alpha1.Target.discovered_labels:type_name -> parca.profilestore.v1alpha1.LabelSet
	7,  // 4: parca.scrape.v1alpha1.Target.labels:type_name -> parca.profilestore.v1alpha1.LabelSet
	8,  // 5: parca.scrape.v1alpha1.Target.last_scrape:type_name -> google.protobuf.Timestamp
	9,  // 6: parca.scrape.v1alpha1.Target.last_scrape_duration:type_name -> google.protobuf.Duration
	1,  // 7: parca.scrape.v1alpha1.Target.health:type_name -> parca.scrape.v1alpha1.Target.Health
	4,  // 8: parca.scrape.v1alpha1.TargetsResponse.TargetsEntry.value:type_name -> parca.scrape.v1alpha1.Targets
	2,  // 9: parca.scrape.v1alpha1.ScrapeService.Targets:input_type -> parca.scrape.v1alpha1.TargetsRequest
	3,  // 10: parca.scrape.v1alpha1.ScrapeService.Targets:output_type -> parca.scrape.v1alpha1.TargetsResponse
	10, // [10:11] is the sub-list for method output_type
	9,  // [9:10] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_parca_scrape_v1alpha1_scrape_proto_init() }
func file_parca_scrape_v1alpha1_scrape_proto_init() {
	if File_parca_scrape_v1alpha1_scrape_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_scrape_v1alpha1_scrape_proto_rawDesc), len(file_parca_scrape_v1alpha1_scrape_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parca_scrape_v1alpha1_scrape_proto_goTypes,
		DependencyIndexes: file_parca_scrape_v1alpha1_scrape_proto_depIdxs,
		EnumInfos:         file_parca_scrape_v1alpha1_scrape_proto_enumTypes,
		MessageInfos:      file_parca_scrape_v1alpha1_scrape_proto_msgTypes,
	}.Build()
	File_parca_scrape_v1alpha1_scrape_proto = out.File
	file_parca_scrape_v1alpha1_scrape_proto_goTypes = nil
	file_parca_scrape_v1alpha1_scrape_proto_depIdxs = nil
}
