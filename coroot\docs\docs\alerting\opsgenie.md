---
sidebar_position: 6
---

# Opsgenie

## Configure Opsgenie

To configure a **Rest HTTP API** integration in your Opsgenie account:
* Navigate to the **Teams** page
* Choose a team (or create a new one)
* Go to **Integrations** → **Add integration** and create an **API** integration
  <img alt="Opsgenie Add Integration" src="/img/docs/opsgenie-integration-step1.png" class="card w-800"/>
* Change the **Name**, if necessary (e.g. Coroot)
* Adjust permissions (Coroot requires only **Create and Update Access**)
* Press **Save Integration**
* Copy the **API Key**
  <img alt="Opsgenie API Key" src="/img/docs/opsgenie-integration-step2.png" class="card w-800"/>

## Configure Coroot

* Go to the **Project Settings** → **Integrations**
* Create an Opsgenie integration
* Paste the API Key to the form
  <img alt="Coroot Opsgenie Integration" src="/img/docs/opsgenie-integration.png" class="card w-800"/>
* You can also send a test alert to check the integration
  <img alt="Coroot Opsgenie Test Alert" src="/img/docs/opsgenie-integration-test.png" class="card w-800"/>
