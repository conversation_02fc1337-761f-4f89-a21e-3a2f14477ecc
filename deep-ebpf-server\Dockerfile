# Deep-eBPF Server Dockerfile

# Build stage
FROM golang:1.19-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git make

# Set working directory
WORKDIR /src

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the server
RUN make server

# Runtime stage
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1000 deep-ebpf && \
    adduser -D -s /bin/sh -u 1000 -G deep-ebpf deep-ebpf

# Create data directory
RUN mkdir -p /data && chown deep-ebpf:deep-ebpf /data

# Copy binary from builder
COPY --from=builder /src/deep-ebpf-server /usr/local/bin/deep-ebpf-server

# Copy static files if they exist
COPY --from=builder /src/static /static

# Set permissions
RUN chmod +x /usr/local/bin/deep-ebpf-server

# Switch to non-root user
USER deep-ebpf

# Set working directory
WORKDIR /data

# Expose ports
EXPOSE 8080 8081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Default command
CMD ["deep-ebpf-server", "--listen", ":8080", "--data-dir", "/data"]
