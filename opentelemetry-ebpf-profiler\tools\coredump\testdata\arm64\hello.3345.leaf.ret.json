{"coredump-ref": "6505deb8d72f6a1751c36d092d62f785289b03854ffe1638522b657d6504106e", "threads": [{"lwp": 156629, "frames": ["main.leaf+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:6", "main.hello5+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:43", "main.hello4+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:36", "main.hello3+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:30", "main.hello2+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:25", "main.hello1+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:17", "main.hello+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:12", "main.main+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:47", "runtime.main+0 in /usr/local/go/src/runtime/proc.go:259", "runtime.goexit+0 in /usr/local/go/src/runtime/asm_arm64.s:1166"]}, {"lwp": 156634, "frames": ["runtime.usleep+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:142", "runtime.sysmon+0 in /usr/local/go/src/runtime/proc.go:5162", "runtime.mstart1+0 in /usr/local/go/src/runtime/proc.go:1428", "runtime.mstart0+0 in /usr/local/go/src/runtime/proc.go:1359", "runtime.mstart+0 in /usr/local/go/src/runtime/asm_arm64.s:129"]}, {"lwp": 156635, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.startlockedm+0 in /usr/local/go/src/runtime/proc.go:2471", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3241", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 156636, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2867", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 156637, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2867", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}], "modules": [{"ref": "bb87b9bba238372aad8a65bacbf088583cf7a580daa23abfed2467a19307c707", "local-path": "/media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.3345"}]}