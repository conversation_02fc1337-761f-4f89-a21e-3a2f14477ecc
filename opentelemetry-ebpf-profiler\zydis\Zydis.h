// DO NOT EDIT. This file is auto-generated by `amalgamate.py`.


//
// Header: <PERSON>yd<PERSON>/<PERSON>yd<PERSON>.h
//

/***************************************************************************************************

  <PERSON><PERSON> Disassembler Library (Zydis)

  Original Author : <PERSON><PERSON><PERSON> Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Master include file. Includes everything else.
 */

#ifndef ZYDIS_H
#define ZYDIS_H


//
// Header: Zycore/Defines.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Core Library (Zycore-C)

  Original Author : Florian Bernd, Joel Hoener

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * General helper and platform detection macros.
 */

#ifndef ZYCORE_DEFINES_H
#define ZYCORE_DEFINES_H

/* ============================================================================================== */
/* Meta macros                                                                                    */
/* ============================================================================================== */

/**
 * Concatenates two values using the stringify operator (`##`).
 *
 * @param   x   The first value.
 * @param   y   The second value.
 *
 * @return  The combined string of the given values.
 */
#define ZYAN_MACRO_CONCAT(x, y) x ## y

/**
 * Concatenates two values using the stringify operator (`##`) and expands the value to
 *          be used in another macro.
 *
 * @param   x   The first value.
 * @param   y   The second value.
 *
 * @return  The combined string of the given values.
 */
#define ZYAN_MACRO_CONCAT_EXPAND(x, y) ZYAN_MACRO_CONCAT(x, y)

/* ============================================================================================== */
/* Compiler detection                                                                             */
/* ============================================================================================== */

#if defined(__clang__)
#   define ZYAN_CLANG
#   define ZYAN_GNUC
#elif defined(__ICC) || defined(__INTEL_COMPILER)
#   define ZYAN_ICC
#elif defined(__GNUC__) || defined(__GNUG__)
#   define ZYAN_GCC
#   define ZYAN_GNUC
#elif defined(_MSC_VER)
#   define ZYAN_MSVC
#elif defined(__BORLANDC__)
#   define ZYAN_BORLAND
#else
#   define ZYAN_UNKNOWN_COMPILER
#endif

/* ============================================================================================== */
/* Platform detection                                                                             */
/* ============================================================================================== */

#if defined(_WIN32)
#   define ZYAN_WINDOWS
#elif defined(__EMSCRIPTEN__)
#   define ZYAN_EMSCRIPTEN
#elif defined(__wasi__) || defined(__WASI__)
// via: https://reviews.llvm.org/D57155
#   define ZYAN_WASI
#elif defined(__APPLE__)
#   define ZYAN_APPLE
#   define ZYAN_POSIX
#elif defined(__linux)
#   define ZYAN_LINUX
#   define ZYAN_POSIX
#elif defined(__FreeBSD__)
#   define ZYAN_FREEBSD
#   define ZYAN_POSIX
#elif defined(__NetBSD__)
#   define ZYAN_NETBSD
#   define ZYAN_POSIX
#elif defined(sun) || defined(__sun)
#   define ZYAN_SOLARIS
#   define ZYAN_POSIX
#elif defined(__unix) || defined(__unix__)
#   define ZYAN_UNIX
#   define ZYAN_POSIX
#elif defined(__posix)
#   define ZYAN_POSIX
#else
#   define ZYAN_UNKNOWN_PLATFORM
#endif

/* ============================================================================================== */
/* Kernel mode detection                                                                          */
/* ============================================================================================== */

#if (defined(ZYAN_WINDOWS) && defined(_KERNEL_MODE)) || \
    (defined(ZYAN_APPLE) && defined(KERNEL)) || \
    (defined(ZYAN_LINUX) && defined(__KERNEL__)) || \
    (defined(__FreeBSD_kernel__))
#   define ZYAN_KERNEL
#else
#   define ZYAN_USER
#endif

/* ============================================================================================== */
/* Architecture detection                                                                         */
/* ============================================================================================== */

#if defined(_M_AMD64) || defined(__x86_64__)
#   define ZYAN_X64
#elif defined(_M_IX86) || defined(__i386__)
#   define ZYAN_X86
#elif defined(_M_ARM64) || defined(__aarch64__)
#   define ZYAN_AARCH64
#elif defined(_M_ARM) || defined(_M_ARMT) || defined(__arm__) || defined(__thumb__)
#   define ZYAN_ARM
#elif defined(__EMSCRIPTEN__) || defined(__wasm__) || defined(__WASM__)
#   define ZYAN_WASM
#elif defined(__loongarch__)
#   define ZYAN_LOONGARCH
#elif defined(__powerpc64__)
#   define ZYAN_PPC64
#elif defined(__powerpc__)
#   define ZYAN_PPC
#elif defined(__riscv) && __riscv_xlen == 64
#   define ZYAN_RISCV64
#else
#   error "Unsupported architecture detected"
#endif

/* ============================================================================================== */
/* Debug/Release detection                                                                        */
/* ============================================================================================== */

#if defined(ZYAN_MSVC) || defined(ZYAN_BORLAND)
#   ifdef _DEBUG
#       define ZYAN_DEBUG
#   else
#       define ZYAN_RELEASE
#   endif
#elif defined(ZYAN_GNUC) || defined(ZYAN_ICC)
#   ifdef NDEBUG
#       define ZYAN_RELEASE
#   else
#       define ZYAN_DEBUG
#   endif
#else
#   define ZYAN_RELEASE
#endif

/* ============================================================================================== */
/* Deprecation hint                                                                               */
/* ============================================================================================== */

#if defined(ZYAN_GCC) || defined(ZYAN_CLANG)
#   define ZYAN_DEPRECATED __attribute__((__deprecated__))
#elif defined(ZYAN_MSVC)
#   define ZYAN_DEPRECATED __declspec(deprecated)
#else
#   define ZYAN_DEPRECATED
#endif

/* ============================================================================================== */
/* Generic DLL import/export helpers                                                              */
/* ============================================================================================== */

#if defined(ZYAN_MSVC)
#   define ZYAN_DLLEXPORT __declspec(dllexport)
#   define ZYAN_DLLIMPORT __declspec(dllimport)
#else
#   define ZYAN_DLLEXPORT
#   define ZYAN_DLLIMPORT
#endif

/* ============================================================================================== */
/* Zycore dll{export,import}                                                                      */
/* ============================================================================================== */

// This is a cut-down version of what CMake's `GenerateExportHeader` would usually generate. To
// simplify builds without CMake, we define these things manually instead of relying on CMake
// to generate the header.
//
// For static builds, our CMakeList will define `ZYCORE_STATIC_BUILD`. For shared library builds,
// our CMake will define `ZYCORE_SHOULD_EXPORT` depending on whether the target is being imported or
// exported. If CMake isn't used, users can manually define these to fit their use-case.

// Backward compatibility: CMake would previously generate these variables names. However, because
// they have pretty cryptic names, we renamed them when we got rid of `GenerateExportHeader`. For
// backward compatibility for users that don't use CMake and previously manually defined these, we
// translate the old defines here and print a warning.
#if defined(ZYCORE_STATIC_DEFINE)
#   pragma message("ZYCORE_STATIC_DEFINE was renamed to ZYCORE_STATIC_BUILD.")
#   define ZYCORE_STATIC_BUILD
#endif
#if defined(Zycore_EXPORTS)
#   pragma message("Zycore_EXPORTS was renamed to ZYCORE_SHOULD_EXPORT.")
#   define ZYCORE_SHOULD_EXPORT
#endif

/**
 * Symbol is exported in shared library builds.
 */
#if defined(ZYCORE_STATIC_BUILD)
#   define ZYCORE_EXPORT
#else
#   if defined(ZYCORE_SHOULD_EXPORT)
#       define ZYCORE_EXPORT ZYAN_DLLEXPORT
#   else
#       define ZYCORE_EXPORT ZYAN_DLLIMPORT
#   endif
#endif

/**
 * Symbol is not exported and for internal use only.
 */
#define ZYCORE_NO_EXPORT

/* ============================================================================================== */
/* Misc compatibility macros                                                                      */
/* ============================================================================================== */

#if defined(ZYAN_CLANG)
#   define ZYAN_NO_SANITIZE(what) __attribute__((no_sanitize(what)))
#else
#   define ZYAN_NO_SANITIZE(what)
#endif

#if defined(ZYAN_MSVC) || defined(ZYAN_BORLAND)
#   define ZYAN_INLINE __inline
#else
#   define ZYAN_INLINE static inline
#endif

#if defined(ZYAN_MSVC)
#   define ZYAN_NOINLINE __declspec(noinline)
#elif defined(ZYAN_GCC) || defined(ZYAN_CLANG)
#   define ZYAN_NOINLINE __attribute__((noinline))
#else
#   define ZYAN_NOINLINE
#endif

/* ============================================================================================== */
/* Debugging and optimization macros                                                              */
/* ============================================================================================== */

/**
 * Runtime debug assertion.
 */
#if defined(ZYAN_NO_LIBC)
#   define ZYAN_ASSERT(condition) (void)(condition)
#elif defined(ZYAN_WINDOWS) && defined(ZYAN_KERNEL)
#   include <wdm.h>
#   define ZYAN_ASSERT(condition) NT_ASSERT(condition)
#else
#   include <assert.h>
#   define ZYAN_ASSERT(condition) assert(condition)
#endif

/**
 * Compiler-time assertion.
 */
#if defined(__STDC_VERSION__) && __STDC_VERSION__ >= 201112L && !defined(__cplusplus)
#   define ZYAN_STATIC_ASSERT(x) _Static_assert(x, #x)
#elif (defined(__cplusplus) && __cplusplus >= 201103L) || \
      (defined(__cplusplus) && defined (_MSC_VER) && (_MSC_VER >= 1600)) || \
      (defined (_MSC_VER) && (_MSC_VER >= 1800))
#   define ZYAN_STATIC_ASSERT(x) static_assert(x, #x)
#else
#   define ZYAN_STATIC_ASSERT(x) \
        typedef int ZYAN_MACRO_CONCAT_EXPAND(ZYAN_SASSERT_, __COUNTER__) [(x) ? 1 : -1]
#endif

/**
 * Marks the current code path as unreachable.
 */
#if defined(ZYAN_RELEASE)
#   if defined(ZYAN_CLANG) // GCC eagerly evals && RHS, we have to use nested ifs.
#       if __has_builtin(__builtin_unreachable)
#           define ZYAN_UNREACHABLE __builtin_unreachable()
#       else
#           define ZYAN_UNREACHABLE for(;;)
#       endif
#   elif defined(ZYAN_GCC) && ((__GNUC__ == 4 && __GNUC_MINOR__ > 4) || __GNUC__ > 4)
#       define ZYAN_UNREACHABLE __builtin_unreachable()
#   elif defined(ZYAN_ICC)
#       ifdef ZYAN_WINDOWS
#           include <stdlib.h> // "missing return statement" workaround
#           define ZYAN_UNREACHABLE __assume(0); (void)abort()
#       else
#           define ZYAN_UNREACHABLE __builtin_unreachable()
#       endif
#   elif defined(ZYAN_MSVC)
#       define ZYAN_UNREACHABLE __assume(0)
#   else
#       define ZYAN_UNREACHABLE for(;;)
#   endif
#elif defined(ZYAN_NO_LIBC)
#   define ZYAN_UNREACHABLE for(;;)
#elif defined(ZYAN_WINDOWS) && defined(ZYAN_KERNEL)
#   define ZYAN_UNREACHABLE { __fastfail(0); for(;;){} }
#else
#   include <stdlib.h>
#   define ZYAN_UNREACHABLE { assert(0); abort(); }
#endif

/* ============================================================================================== */
/* Utils                                                                                          */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* General purpose                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Marks the specified parameter as unused.
 *
 * @param   x   The name of the unused parameter.
 */
#define ZYAN_UNUSED(x) (void)(x)

/**
 * Intentional fallthrough.
 */
#if defined(ZYAN_GCC) && __GNUC__ >= 7
#   define ZYAN_FALLTHROUGH ; __attribute__((__fallthrough__))
#else
#   define ZYAN_FALLTHROUGH
#endif

/**
 * Declares a bitfield.
 *
 * @param   x   The size (in bits) of the bitfield.
 */
#define ZYAN_BITFIELD(x) : x

/**
 * Marks functions that require libc (cannot be used with `ZYAN_NO_LIBC`).
 */
#define ZYAN_REQUIRES_LIBC

/**
 * Decorator for `printf`-style functions.
 *
 * @param   format_index    The 1-based index of the format string parameter.
 * @param   first_to_check  The 1-based index of the format arguments parameter.
 */
#if defined(__RESHARPER__)
#   define ZYAN_PRINTF_ATTR(format_index, first_to_check) \
        [[gnu::format(printf, format_index, first_to_check)]]
#elif defined(ZYAN_GCC)
#   define ZYAN_PRINTF_ATTR(format_index, first_to_check) \
        __attribute__((format(printf, format_index, first_to_check)))
#else
#   define ZYAN_PRINTF_ATTR(format_index, first_to_check)
#endif

/**
 * Decorator for `wprintf`-style functions.
 *
 * @param   format_index    The 1-based index of the format string parameter.
 * @param   first_to_check  The 1-based index of the format arguments parameter.
 */
#if defined(__RESHARPER__)
#   define ZYAN_WPRINTF_ATTR(format_index, first_to_check) \
        [[rscpp::format(wprintf, format_index, first_to_check)]]
#else
#   define ZYAN_WPRINTF_ATTR(format_index, first_to_check)
#endif

/* ---------------------------------------------------------------------------------------------- */
/* Arrays                                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the length (number of elements) of an array.
 *
 * @param   a   The name of the array.
 *
 * @return  The number of elements of the given array.
 */
#define ZYAN_ARRAY_LENGTH(a) (sizeof(a) / sizeof((a)[0]))

/* ---------------------------------------------------------------------------------------------- */
/* Arithmetic                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the smaller value of `a` or `b`.
 *
 * @param   a   The first value.
 * @param   b   The second value.
 *
 * @return  The smaller value of `a` or `b`.
 */
#define ZYAN_MIN(a, b) (((a) < (b)) ? (a) : (b))

/**
 * Returns the bigger value of `a` or `b`.
 *
 * @param   a   The first value.
 * @param   b   The second value.
 *
 * @return  The bigger value of `a` or `b`.
 */
#define ZYAN_MAX(a, b) (((a) > (b)) ? (a) : (b))

/**
 * Returns the absolute value of `a`.
 *
 * @param   a   The value.
 *
 * @return  The absolute value of `a`.
 */
#define ZYAN_ABS(a) (((a) < 0) ? -(a) : (a))

/**
 * Checks, if the given value is a power of 2.
 *
 * @param   x   The value.
 *
 * @return  `ZYAN_TRUE`, if the given value is a power of 2 or `ZYAN_FALSE`, if not.
 *
 * Note that this macro always returns `ZYAN_TRUE` for `x == 0`.
 */
#define ZYAN_IS_POWER_OF_2(x) (((x) & ((x) - 1)) == 0)

/**
 * Checks, if the given value is properly aligned.
 *
 * Note that this macro only works for powers of 2.
 */
#define ZYAN_IS_ALIGNED_TO(x, align) (((x) & ((align) - 1)) == 0)

/**
 * Aligns the value to the nearest given alignment boundary (by rounding it up).
 *
 * @param   x       The value.
 * @param   align   The desired alignment.
 *
 * @return  The aligned value.
 *
 * Note that this macro only works for powers of 2.
 */
#define ZYAN_ALIGN_UP(x, align) (((x) + (align) - 1) & ~((align) - 1))

/**
 * Aligns the value to the nearest given alignment boundary (by rounding it down).
 *
 * @param   x       The value.
 * @param   align   The desired alignment.
 *
 * @return  The aligned value.
 *
 * Note that this macro only works for powers of 2.
 */
#define ZYAN_ALIGN_DOWN(x, align) (((x) - 1) & ~((align) - 1))

/**
 * Divide the 64bit integer value by the given divisor.
 *
 * @param   n       Variable containing the dividend that will be updated with the result of the
 *                  division.
 * @param   divisor The divisor.
 */
#if defined(ZYAN_LINUX) && defined(ZYAN_KERNEL)
#   include <asm/div64.h> /* do_div */
#   define ZYAN_DIV64(n, divisor) do_div(n, divisor)
#else
#   define ZYAN_DIV64(n, divisor) (n /= divisor)
#endif

/* ---------------------------------------------------------------------------------------------- */
/* Bit operations                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/*
 * Checks, if the bit at index `b` is required to present the ordinal value `n`.
 *
 * @param   n   The ordinal value.
 * @param   b   The bit index.
 *
 * @return  `ZYAN_TRUE`, if the bit at index `b` is required to present the ordinal value `n` or
 *          `ZYAN_FALSE`, if not.
 *
 * Note that this macro always returns `ZYAN_FALSE` for `n == 0`.
 */
#define ZYAN_NEEDS_BIT(n, b) (((unsigned long)(n) >> (b)) > 0)

/*
 * Returns the number of bits required to represent the ordinal value `n`.
 *
 * @param   n   The ordinal value.
 *
 * @return  The number of bits required to represent the ordinal value `n`.
 *
 * Note that this macro returns `0` for `n == 0`.
 */
#define ZYAN_BITS_TO_REPRESENT(n) \
    ( \
        ZYAN_NEEDS_BIT(n,  0) + ZYAN_NEEDS_BIT(n,  1) + \
        ZYAN_NEEDS_BIT(n,  2) + ZYAN_NEEDS_BIT(n,  3) + \
        ZYAN_NEEDS_BIT(n,  4) + ZYAN_NEEDS_BIT(n,  5) + \
        ZYAN_NEEDS_BIT(n,  6) + ZYAN_NEEDS_BIT(n,  7) + \
        ZYAN_NEEDS_BIT(n,  8) + ZYAN_NEEDS_BIT(n,  9) + \
        ZYAN_NEEDS_BIT(n, 10) + ZYAN_NEEDS_BIT(n, 11) + \
        ZYAN_NEEDS_BIT(n, 12) + ZYAN_NEEDS_BIT(n, 13) + \
        ZYAN_NEEDS_BIT(n, 14) + ZYAN_NEEDS_BIT(n, 15) + \
        ZYAN_NEEDS_BIT(n, 16) + ZYAN_NEEDS_BIT(n, 17) + \
        ZYAN_NEEDS_BIT(n, 18) + ZYAN_NEEDS_BIT(n, 19) + \
        ZYAN_NEEDS_BIT(n, 20) + ZYAN_NEEDS_BIT(n, 21) + \
        ZYAN_NEEDS_BIT(n, 22) + ZYAN_NEEDS_BIT(n, 23) + \
        ZYAN_NEEDS_BIT(n, 24) + ZYAN_NEEDS_BIT(n, 25) + \
        ZYAN_NEEDS_BIT(n, 26) + ZYAN_NEEDS_BIT(n, 27) + \
        ZYAN_NEEDS_BIT(n, 28) + ZYAN_NEEDS_BIT(n, 29) + \
        ZYAN_NEEDS_BIT(n, 30) + ZYAN_NEEDS_BIT(n, 31)   \
    )

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#endif /* ZYCORE_DEFINES_H */

//
// Header: Zycore/Types.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Core Library (Zyan-C)

  Original Author : Florian Bernd, Joel Hoener

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Includes and defines some default data types.
 */

#ifndef ZYCORE_TYPES_H
#define ZYCORE_TYPES_H


/* ============================================================================================== */
/* Integer types                                                                                  */
/* ============================================================================================== */

#if defined(ZYAN_NO_LIBC) || \
    (defined(ZYAN_MSVC) && defined(ZYAN_KERNEL)) // The WDK LibC lacks stdint.h.
    // No LibC mode, use compiler built-in types / macros.
#   if defined(ZYAN_MSVC) || defined(ZYAN_ICC)
        typedef unsigned __int8                 ZyanU8;
        typedef unsigned __int16                ZyanU16;
        typedef unsigned __int32                ZyanU32;
        typedef unsigned __int64                ZyanU64;
        typedef   signed __int8                 ZyanI8;
        typedef   signed __int16                ZyanI16;
        typedef   signed __int32                ZyanI32;
        typedef   signed __int64                ZyanI64;
#       if _WIN64
           typedef ZyanU64                      ZyanUSize;
           typedef ZyanI64                      ZyanISize;
           typedef ZyanU64                      ZyanUPointer;
           typedef ZyanI64                      ZyanIPointer;
#       else
           typedef ZyanU32                      ZyanUSize;
           typedef ZyanI32                      ZyanISize;
           typedef ZyanU32                      ZyanUPointer;
           typedef ZyanI32                      ZyanIPointer;
#       endif
#   elif defined(ZYAN_GNUC)
#       ifdef __UINT8_TYPE__
            typedef __UINT8_TYPE__              ZyanU8;
#       else
            typedef unsigned char               ZyanU8;
#       endif
#       ifdef __UINT16_TYPE__
            typedef __UINT16_TYPE__             ZyanU16;
#       else
            typedef unsigned short int          ZyanU16;
#       endif
#       ifdef __UINT32_TYPE__
            typedef __UINT32_TYPE__             ZyanU32;
#       else
            typedef unsigned int                ZyanU32;
#       endif
#       ifdef __UINT64_TYPE__
            typedef __UINT64_TYPE__             ZyanU64;
#       else
#           if defined(__x86_64__) && !defined(__ILP32__)
                typedef unsigned long int       ZyanU64;
#           else
                typedef unsigned long long int  ZyanU64;
#           endif
#       endif
#       ifdef __INT8_TYPE__
            typedef __INT8_TYPE__               ZyanI8;
#       else
            typedef signed char                 ZyanI8;
#       endif
#       ifdef __INT16_TYPE__
            typedef __INT16_TYPE__              ZyanI16;
#       else
            typedef signed short int            ZyanI16;
#       endif
#       ifdef __INT32_TYPE__
            typedef __INT32_TYPE__              ZyanI32;
#       else
            typedef signed int                  ZyanI32;
#       endif
#       ifdef __INT64_TYPE__
            typedef __INT64_TYPE__              ZyanI64;
#       else
#           if defined(__x86_64__) && !defined( __ILP32__)
                typedef signed long int         ZyanI64;
#           else
                typedef signed long long int    ZyanI64;
#           endif
#       endif
#       ifdef __SIZE_TYPE__
            typedef __SIZE_TYPE__               ZyanUSize;
#       else
            typedef long unsigned int           ZyanUSize;
#       endif
#       ifdef __PTRDIFF_TYPE__
            typedef __PTRDIFF_TYPE__            ZyanISize;
#       else
            typedef long int                    ZyanISize;
#       endif
#       ifdef __UINTPTR_TYPE__
            typedef __UINTPTR_TYPE__            ZyanUPointer;
#       else
#           if defined(__x86_64__) && !defined( __ILP32__)
                typedef unsigned long int       ZyanUPointer;
#           else
                typedef unsigned int            ZyanUPointer;
#           endif
#       endif
#       ifdef __INTPTR_TYPE__
            typedef __INTPTR_TYPE__             ZyanIPointer;
#       else
#           if defined(__x86_64__) && !defined( __ILP32__)
                typedef long int                ZyanIPointer;
#           else
                typedef int                     ZyanIPointer;
#           endif
#       endif
#   else
#       error "Unsupported compiler for no-libc mode."
#   endif

#   if defined(ZYAN_MSVC)
#       define ZYAN_INT8_MIN            (-127i8 - 1)
#       define ZYAN_INT16_MIN           (-32767i16 - 1)
#       define ZYAN_INT32_MIN           (-2147483647i32 - 1)
#       define ZYAN_INT64_MIN           (-9223372036854775807i64 - 1)
#       define ZYAN_INT8_MAX            127i8
#       define ZYAN_INT16_MAX           32767i16
#       define ZYAN_INT32_MAX           2147483647i32
#       define ZYAN_INT64_MAX           9223372036854775807i64
#       define ZYAN_UINT8_MAX           0xffui8
#       define ZYAN_UINT16_MAX          0xffffui16
#       define ZYAN_UINT32_MAX          0xffffffffui32
#       define ZYAN_UINT64_MAX          0xffffffffffffffffui64
#   else
#       ifdef __INT8_MAX__
#           define ZYAN_INT8_MAX        __INT8_MAX__
#       else
#           define ZYAN_INT8_MAX        (127)
#       endif
#       define ZYAN_INT8_MIN            (-ZYAN_INT8_MAX - 1)
#       ifdef __INT16_MAX__
#           define ZYAN_INT16_MAX       __INT16_MAX__
#       else
#           define ZYAN_INT16_MAX       (32767)
#       endif
#       define ZYAN_INT16_MIN           (-ZYAN_INT16_MAX - 1)
#       ifdef __INT32_MAX__
#           define ZYAN_INT32_MAX       __INT32_MAX__
#       else
#           define ZYAN_INT32_MAX       (2147483647)
#       endif
#       define ZYAN_INT32_MIN           (-ZYAN_INT32_MAX - 1)
#       ifdef __INT64_MAX__
#           define ZYAN_INT64_MAX       __INT64_MAX__
#       else
#           if defined(__x86_64__) && !defined( __ILP32__)
#               define ZYAN_INT64_MAX   (9223372036854775807L)
#           else
#               define ZYAN_INT64_MAX   (9223372036854775807LL)
#           endif
#       endif
#       define ZYAN_INT64_MIN           (-ZYAN_INT64_MAX - 1)
#       ifdef __UINT8_MAX__
#           define ZYAN_UINT8_MAX       __UINT8_MAX__
#       else
#           define ZYAN_UINT8_MAX       (255)
#       endif
#       ifdef __UINT16_MAX__
#           define ZYAN_UINT16_MAX      __UINT16_MAX__
#       else
#           define ZYAN_UINT16_MAX      (65535)
#       endif
#       ifdef __UINT32_MAX__
#           define ZYAN_UINT32_MAX      __UINT32_MAX__
#       else
#           define ZYAN_UINT32_MAX      (4294967295U)
#       endif
#       ifdef __UINT64_MAX__
#           define ZYAN_UINT64_MAX      __UINT64_MAX__
#       else
#           if defined(__x86_64__) && !defined( __ILP32__)
#               define ZYAN_UINT64_MAX  (18446744073709551615UL)
#           else
#               define ZYAN_UINT64_MAX  (18446744073709551615ULL)
#           endif
#       endif
#   endif
#else
    // If is LibC present, we use stdint types.
#   include <stdint.h>
#   include <stddef.h>
    typedef uint8_t   ZyanU8;
    typedef uint16_t  ZyanU16;
    typedef uint32_t  ZyanU32;
    typedef uint64_t  ZyanU64;
    typedef int8_t    ZyanI8;
    typedef int16_t   ZyanI16;
    typedef int32_t   ZyanI32;
    typedef int64_t   ZyanI64;
    typedef size_t    ZyanUSize;
    typedef ptrdiff_t ZyanISize;
    typedef uintptr_t ZyanUPointer;
    typedef intptr_t  ZyanIPointer;

#   define ZYAN_INT8_MIN         INT8_MIN
#   define ZYAN_INT16_MIN        INT16_MIN
#   define ZYAN_INT32_MIN        INT32_MIN
#   define ZYAN_INT64_MIN        INT64_MIN
#   define ZYAN_INT8_MAX         INT8_MAX
#   define ZYAN_INT16_MAX        INT16_MAX
#   define ZYAN_INT32_MAX        INT32_MAX
#   define ZYAN_INT64_MAX        INT64_MAX
#   define ZYAN_UINT8_MAX        UINT8_MAX
#   define ZYAN_UINT16_MAX       UINT16_MAX
#   define ZYAN_UINT32_MAX       UINT32_MAX
#   define ZYAN_UINT64_MAX       UINT64_MAX
#endif

// Verify size assumptions.
ZYAN_STATIC_ASSERT(sizeof(ZyanU8      ) == 1            );
ZYAN_STATIC_ASSERT(sizeof(ZyanU16     ) == 2            );
ZYAN_STATIC_ASSERT(sizeof(ZyanU32     ) == 4            );
ZYAN_STATIC_ASSERT(sizeof(ZyanU64     ) == 8            );
ZYAN_STATIC_ASSERT(sizeof(ZyanI8      ) == 1            );
ZYAN_STATIC_ASSERT(sizeof(ZyanI16     ) == 2            );
ZYAN_STATIC_ASSERT(sizeof(ZyanI32     ) == 4            );
ZYAN_STATIC_ASSERT(sizeof(ZyanI64     ) == 8            );
ZYAN_STATIC_ASSERT(sizeof(ZyanUSize   ) == sizeof(void*)); // TODO: This one is incorrect!
ZYAN_STATIC_ASSERT(sizeof(ZyanISize   ) == sizeof(void*)); // TODO: This one is incorrect!
ZYAN_STATIC_ASSERT(sizeof(ZyanUPointer) == sizeof(void*));
ZYAN_STATIC_ASSERT(sizeof(ZyanIPointer) == sizeof(void*));

// Verify signedness assumptions (relies on size checks above).
ZYAN_STATIC_ASSERT((ZyanI8 )-1 >> 1 < (ZyanI8 )((ZyanU8 )-1 >> 1));
ZYAN_STATIC_ASSERT((ZyanI16)-1 >> 1 < (ZyanI16)((ZyanU16)-1 >> 1));
ZYAN_STATIC_ASSERT((ZyanI32)-1 >> 1 < (ZyanI32)((ZyanU32)-1 >> 1));
ZYAN_STATIC_ASSERT((ZyanI64)-1 >> 1 < (ZyanI64)((ZyanU64)-1 >> 1));

/* ============================================================================================== */
/* Pointer                                                                                        */
/* ============================================================================================== */

/**
 * Defines the `ZyanVoidPointer` data-type.
 */
typedef void* ZyanVoidPointer;

/**
 * Defines the `ZyanConstVoidPointer` data-type.
 */
typedef const void* ZyanConstVoidPointer;

#define ZYAN_NULL ((void*)0)

/* ============================================================================================== */
/* Logic types                                                                                    */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Boolean                                                                                        */
/* ---------------------------------------------------------------------------------------------- */

#define ZYAN_FALSE 0u
#define ZYAN_TRUE  1u

/**
 * Defines the `ZyanBool` data-type.
 *
 * Represents a default boolean data-type where `0` is interpreted as `false` and all other values
 * as `true`.
 */
typedef ZyanU8 ZyanBool;

/* ---------------------------------------------------------------------------------------------- */
/* Ternary                                                                                        */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZyanTernary` data-type.
 *
 * The `ZyanTernary` is a balanced ternary type that uses three truth values indicating `true`,
 * `false` and an indeterminate third value.
 */
typedef ZyanI8 ZyanTernary;

#define ZYAN_TERNARY_FALSE    (-1)
#define ZYAN_TERNARY_UNKNOWN  0x00
#define ZYAN_TERNARY_TRUE     0x01

/* ============================================================================================== */
/* String types                                                                                   */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* C-style strings                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZyanCharPointer` data-type.
 *
 * This type is most often used to represent null-terminated strings aka. C-style strings.
 */
typedef char* ZyanCharPointer;

/**
 * Defines the `ZyanConstCharPointer` data-type.
 *
 * This type is most often used to represent null-terminated strings aka. C-style strings.
 */
typedef const char* ZyanConstCharPointer;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#endif /* ZYCORE_TYPES_H */

#if !defined(ZYDIS_DISABLE_DECODER)

//
// Header: Zydis/Decoder.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Functions for decoding instructions.
 */

#ifndef ZYDIS_DECODER_H
#define ZYDIS_DECODER_H


//
// Header: Zydis/DecoderTypes.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Defines the basic `ZydisDecodedInstruction` and `ZydisDecodedOperand` structs.
 */

#ifndef ZYDIS_INSTRUCTIONINFO_H
#define ZYDIS_INSTRUCTIONINFO_H


//
// Header: Zydis/MetaInfo.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * @brief
 */

#ifndef ZYDIS_METAINFO_H
#define ZYDIS_METAINFO_H


//
// Header: Zydis/Defines.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/MetaInfo.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Joel Hoener

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Import/export defines for MSVC builds.
 */

#ifndef ZYDIS_DEFINES_H
#define ZYDIS_DEFINES_H


// This is a cut-down version of what CMake's `GenerateExportHeader` would usually generate. To
// simplify builds without CMake, we define these things manually instead of relying on CMake
// to generate the header.
//
// For static builds, our CMakeList will define `ZYDIS_STATIC_BUILD`. For shared library builds,
// our CMake will define `ZYDIS_SHOULD_EXPORT` depending on whether the target is being imported or
// exported. If CMake isn't used, users can manually define these to fit their use-case.

// Backward compatibility: CMake would previously generate these variables names. However, because
// they have pretty cryptic names, we renamed them when we got rid of `GenerateExportHeader`. For
// backward compatibility for users that don't use CMake and previously manually defined these, we
// translate the old defines here and print a warning.
#if defined(ZYDIS_STATIC_DEFINE)
#   pragma message("ZYDIS_STATIC_DEFINE was renamed to ZYDIS_STATIC_BUILD.")
#   define ZYDIS_STATIC_BUILD
#endif
#if defined(Zydis_EXPORTS)
#   pragma message("Zydis_EXPORTS was renamed to ZYDIS_SHOULD_EXPORT.")
#   define ZYDIS_SHOULD_EXPORT
#endif

/**
 * Symbol is exported in shared library builds.
 */
#if defined(ZYDIS_STATIC_BUILD)
#   define ZYDIS_EXPORT
#else
#   if defined(ZYDIS_SHOULD_EXPORT)
#       define ZYDIS_EXPORT ZYAN_DLLEXPORT
#   else
#       define ZYDIS_EXPORT ZYAN_DLLIMPORT
#   endif
#endif

/**
 * Symbol is not exported and for internal use only.
 */
#define ZYDIS_NO_EXPORT

#endif // ZYDIS_DEFINES_H

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */


//
// Header: Zydis/Generated/EnumInstructionCategory.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/MetaInfo.h
//

/**
 * Defines the `ZydisInstructionCategory` enum.
 */
typedef enum ZydisInstructionCategory_
{
    ZYDIS_CATEGORY_INVALID,
    ZYDIS_CATEGORY_ADOX_ADCX,
    ZYDIS_CATEGORY_AES,
    ZYDIS_CATEGORY_AMD3DNOW,
    ZYDIS_CATEGORY_AMX_TILE,
    ZYDIS_CATEGORY_AVX,
    ZYDIS_CATEGORY_AVX2,
    ZYDIS_CATEGORY_AVX2GATHER,
    ZYDIS_CATEGORY_AVX512,
    ZYDIS_CATEGORY_AVX512_4FMAPS,
    ZYDIS_CATEGORY_AVX512_4VNNIW,
    ZYDIS_CATEGORY_AVX512_BITALG,
    ZYDIS_CATEGORY_AVX512_VBMI,
    ZYDIS_CATEGORY_AVX512_VP2INTERSECT,
    ZYDIS_CATEGORY_AVX_IFMA,
    ZYDIS_CATEGORY_BINARY,
    ZYDIS_CATEGORY_BITBYTE,
    ZYDIS_CATEGORY_BLEND,
    ZYDIS_CATEGORY_BMI1,
    ZYDIS_CATEGORY_BMI2,
    ZYDIS_CATEGORY_BROADCAST,
    ZYDIS_CATEGORY_CALL,
    ZYDIS_CATEGORY_CET,
    ZYDIS_CATEGORY_CLDEMOTE,
    ZYDIS_CATEGORY_CLFLUSHOPT,
    ZYDIS_CATEGORY_CLWB,
    ZYDIS_CATEGORY_CLZERO,
    ZYDIS_CATEGORY_CMOV,
    ZYDIS_CATEGORY_COMPRESS,
    ZYDIS_CATEGORY_COND_BR,
    ZYDIS_CATEGORY_CONFLICT,
    ZYDIS_CATEGORY_CONVERT,
    ZYDIS_CATEGORY_DATAXFER,
    ZYDIS_CATEGORY_DECIMAL,
    ZYDIS_CATEGORY_ENQCMD,
    ZYDIS_CATEGORY_EXPAND,
    ZYDIS_CATEGORY_FCMOV,
    ZYDIS_CATEGORY_FLAGOP,
    ZYDIS_CATEGORY_FMA4,
    ZYDIS_CATEGORY_FP16,
    ZYDIS_CATEGORY_GATHER,
    ZYDIS_CATEGORY_GFNI,
    ZYDIS_CATEGORY_HRESET,
    ZYDIS_CATEGORY_IFMA,
    ZYDIS_CATEGORY_INTERRUPT,
    ZYDIS_CATEGORY_IO,
    ZYDIS_CATEGORY_IOSTRINGOP,
    ZYDIS_CATEGORY_KEYLOCKER,
    ZYDIS_CATEGORY_KEYLOCKER_WIDE,
    ZYDIS_CATEGORY_KMASK,
    ZYDIS_CATEGORY_KNC,
    ZYDIS_CATEGORY_KNCMASK,
    ZYDIS_CATEGORY_KNCSCALAR,
    ZYDIS_CATEGORY_LEGACY,
    ZYDIS_CATEGORY_LOGICAL,
    ZYDIS_CATEGORY_LOGICAL_FP,
    ZYDIS_CATEGORY_LZCNT,
    ZYDIS_CATEGORY_MISC,
    ZYDIS_CATEGORY_MMX,
    ZYDIS_CATEGORY_MOVDIR,
    ZYDIS_CATEGORY_MPX,
    ZYDIS_CATEGORY_MSRLIST,
    ZYDIS_CATEGORY_NOP,
    ZYDIS_CATEGORY_PADLOCK,
    ZYDIS_CATEGORY_PBNDKB,
    ZYDIS_CATEGORY_PCLMULQDQ,
    ZYDIS_CATEGORY_PCOMMIT,
    ZYDIS_CATEGORY_PCONFIG,
    ZYDIS_CATEGORY_PKU,
    ZYDIS_CATEGORY_POP,
    ZYDIS_CATEGORY_PREFETCH,
    ZYDIS_CATEGORY_PREFETCHWT1,
    ZYDIS_CATEGORY_PT,
    ZYDIS_CATEGORY_PUSH,
    ZYDIS_CATEGORY_RDPID,
    ZYDIS_CATEGORY_RDPRU,
    ZYDIS_CATEGORY_RDRAND,
    ZYDIS_CATEGORY_RDSEED,
    ZYDIS_CATEGORY_RDWRFSGS,
    ZYDIS_CATEGORY_RET,
    ZYDIS_CATEGORY_ROTATE,
    ZYDIS_CATEGORY_SCATTER,
    ZYDIS_CATEGORY_SEGOP,
    ZYDIS_CATEGORY_SEMAPHORE,
    ZYDIS_CATEGORY_SERIALIZE,
    ZYDIS_CATEGORY_SETCC,
    ZYDIS_CATEGORY_SGX,
    ZYDIS_CATEGORY_SHA,
    ZYDIS_CATEGORY_SHA512,
    ZYDIS_CATEGORY_SHIFT,
    ZYDIS_CATEGORY_SMAP,
    ZYDIS_CATEGORY_SSE,
    ZYDIS_CATEGORY_STRINGOP,
    ZYDIS_CATEGORY_STTNI,
    ZYDIS_CATEGORY_SYSCALL,
    ZYDIS_CATEGORY_SYSRET,
    ZYDIS_CATEGORY_SYSTEM,
    ZYDIS_CATEGORY_TBM,
    ZYDIS_CATEGORY_TSX_LDTRK,
    ZYDIS_CATEGORY_UFMA,
    ZYDIS_CATEGORY_UINTR,
    ZYDIS_CATEGORY_UNCOND_BR,
    ZYDIS_CATEGORY_VAES,
    ZYDIS_CATEGORY_VBMI2,
    ZYDIS_CATEGORY_VEX,
    ZYDIS_CATEGORY_VFMA,
    ZYDIS_CATEGORY_VPCLMULQDQ,
    ZYDIS_CATEGORY_VTX,
    ZYDIS_CATEGORY_WAITPKG,
    ZYDIS_CATEGORY_WIDENOP,
    ZYDIS_CATEGORY_WRMSRNS,
    ZYDIS_CATEGORY_X87_ALU,
    ZYDIS_CATEGORY_XOP,
    ZYDIS_CATEGORY_XSAVE,
    ZYDIS_CATEGORY_XSAVEOPT,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_CATEGORY_MAX_VALUE = ZYDIS_CATEGORY_XSAVEOPT,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_CATEGORY_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_CATEGORY_MAX_VALUE)
} ZydisInstructionCategory;

//
// Header: Zydis/Generated/EnumISASet.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/MetaInfo.h
//

/**
 * Defines the `ZydisISASet` enum.
 */
typedef enum ZydisISASet_
{
    ZYDIS_ISA_SET_INVALID,
    ZYDIS_ISA_SET_ADOX_ADCX,
    ZYDIS_ISA_SET_AES,
    ZYDIS_ISA_SET_AMD,
    ZYDIS_ISA_SET_AMD3DNOW,
    ZYDIS_ISA_SET_AMD_INVLPGB,
    ZYDIS_ISA_SET_AMX_BF16,
    ZYDIS_ISA_SET_AMX_FP16,
    ZYDIS_ISA_SET_AMX_INT8,
    ZYDIS_ISA_SET_AMX_TILE,
    ZYDIS_ISA_SET_AVX,
    ZYDIS_ISA_SET_AVX2,
    ZYDIS_ISA_SET_AVX2GATHER,
    ZYDIS_ISA_SET_AVX512BW_128,
    ZYDIS_ISA_SET_AVX512BW_128N,
    ZYDIS_ISA_SET_AVX512BW_256,
    ZYDIS_ISA_SET_AVX512BW_512,
    ZYDIS_ISA_SET_AVX512BW_KOP,
    ZYDIS_ISA_SET_AVX512CD_128,
    ZYDIS_ISA_SET_AVX512CD_256,
    ZYDIS_ISA_SET_AVX512CD_512,
    ZYDIS_ISA_SET_AVX512DQ_128,
    ZYDIS_ISA_SET_AVX512DQ_128N,
    ZYDIS_ISA_SET_AVX512DQ_256,
    ZYDIS_ISA_SET_AVX512DQ_512,
    ZYDIS_ISA_SET_AVX512DQ_KOP,
    ZYDIS_ISA_SET_AVX512DQ_SCALAR,
    ZYDIS_ISA_SET_AVX512ER_512,
    ZYDIS_ISA_SET_AVX512ER_SCALAR,
    ZYDIS_ISA_SET_AVX512F_128,
    ZYDIS_ISA_SET_AVX512F_128N,
    ZYDIS_ISA_SET_AVX512F_256,
    ZYDIS_ISA_SET_AVX512F_512,
    ZYDIS_ISA_SET_AVX512F_KOP,
    ZYDIS_ISA_SET_AVX512F_SCALAR,
    ZYDIS_ISA_SET_AVX512PF_512,
    ZYDIS_ISA_SET_AVX512_4FMAPS_512,
    ZYDIS_ISA_SET_AVX512_4FMAPS_SCALAR,
    ZYDIS_ISA_SET_AVX512_4VNNIW_512,
    ZYDIS_ISA_SET_AVX512_BF16_128,
    ZYDIS_ISA_SET_AVX512_BF16_256,
    ZYDIS_ISA_SET_AVX512_BF16_512,
    ZYDIS_ISA_SET_AVX512_BITALG_128,
    ZYDIS_ISA_SET_AVX512_BITALG_256,
    ZYDIS_ISA_SET_AVX512_BITALG_512,
    ZYDIS_ISA_SET_AVX512_FP16_128,
    ZYDIS_ISA_SET_AVX512_FP16_128N,
    ZYDIS_ISA_SET_AVX512_FP16_256,
    ZYDIS_ISA_SET_AVX512_FP16_512,
    ZYDIS_ISA_SET_AVX512_FP16_SCALAR,
    ZYDIS_ISA_SET_AVX512_GFNI_128,
    ZYDIS_ISA_SET_AVX512_GFNI_256,
    ZYDIS_ISA_SET_AVX512_GFNI_512,
    ZYDIS_ISA_SET_AVX512_IFMA_128,
    ZYDIS_ISA_SET_AVX512_IFMA_256,
    ZYDIS_ISA_SET_AVX512_IFMA_512,
    ZYDIS_ISA_SET_AVX512_VAES_128,
    ZYDIS_ISA_SET_AVX512_VAES_256,
    ZYDIS_ISA_SET_AVX512_VAES_512,
    ZYDIS_ISA_SET_AVX512_VBMI2_128,
    ZYDIS_ISA_SET_AVX512_VBMI2_256,
    ZYDIS_ISA_SET_AVX512_VBMI2_512,
    ZYDIS_ISA_SET_AVX512_VBMI_128,
    ZYDIS_ISA_SET_AVX512_VBMI_256,
    ZYDIS_ISA_SET_AVX512_VBMI_512,
    ZYDIS_ISA_SET_AVX512_VNNI_128,
    ZYDIS_ISA_SET_AVX512_VNNI_256,
    ZYDIS_ISA_SET_AVX512_VNNI_512,
    ZYDIS_ISA_SET_AVX512_VP2INTERSECT_128,
    ZYDIS_ISA_SET_AVX512_VP2INTERSECT_256,
    ZYDIS_ISA_SET_AVX512_VP2INTERSECT_512,
    ZYDIS_ISA_SET_AVX512_VPCLMULQDQ_128,
    ZYDIS_ISA_SET_AVX512_VPCLMULQDQ_256,
    ZYDIS_ISA_SET_AVX512_VPCLMULQDQ_512,
    ZYDIS_ISA_SET_AVX512_VPOPCNTDQ_128,
    ZYDIS_ISA_SET_AVX512_VPOPCNTDQ_256,
    ZYDIS_ISA_SET_AVX512_VPOPCNTDQ_512,
    ZYDIS_ISA_SET_AVXAES,
    ZYDIS_ISA_SET_AVX_GFNI,
    ZYDIS_ISA_SET_AVX_IFMA,
    ZYDIS_ISA_SET_AVX_NE_CONVERT,
    ZYDIS_ISA_SET_AVX_VNNI,
    ZYDIS_ISA_SET_AVX_VNNI_INT16,
    ZYDIS_ISA_SET_AVX_VNNI_INT8,
    ZYDIS_ISA_SET_BMI1,
    ZYDIS_ISA_SET_BMI2,
    ZYDIS_ISA_SET_CET,
    ZYDIS_ISA_SET_CLDEMOTE,
    ZYDIS_ISA_SET_CLFLUSHOPT,
    ZYDIS_ISA_SET_CLFSH,
    ZYDIS_ISA_SET_CLWB,
    ZYDIS_ISA_SET_CLZERO,
    ZYDIS_ISA_SET_CMOV,
    ZYDIS_ISA_SET_CMPXCHG16B,
    ZYDIS_ISA_SET_ENQCMD,
    ZYDIS_ISA_SET_F16C,
    ZYDIS_ISA_SET_FAT_NOP,
    ZYDIS_ISA_SET_FCMOV,
    ZYDIS_ISA_SET_FCOMI,
    ZYDIS_ISA_SET_FMA,
    ZYDIS_ISA_SET_FMA4,
    ZYDIS_ISA_SET_FXSAVE,
    ZYDIS_ISA_SET_FXSAVE64,
    ZYDIS_ISA_SET_GFNI,
    ZYDIS_ISA_SET_HRESET,
    ZYDIS_ISA_SET_I186,
    ZYDIS_ISA_SET_I286PROTECTED,
    ZYDIS_ISA_SET_I286REAL,
    ZYDIS_ISA_SET_I386,
    ZYDIS_ISA_SET_I486,
    ZYDIS_ISA_SET_I486REAL,
    ZYDIS_ISA_SET_I86,
    ZYDIS_ISA_SET_ICACHE_PREFETCH,
    ZYDIS_ISA_SET_INVPCID,
    ZYDIS_ISA_SET_KEYLOCKER,
    ZYDIS_ISA_SET_KEYLOCKER_WIDE,
    ZYDIS_ISA_SET_KNCE,
    ZYDIS_ISA_SET_KNCJKBR,
    ZYDIS_ISA_SET_KNCSTREAM,
    ZYDIS_ISA_SET_KNCV,
    ZYDIS_ISA_SET_KNC_MISC,
    ZYDIS_ISA_SET_KNC_PF_HINT,
    ZYDIS_ISA_SET_LAHF,
    ZYDIS_ISA_SET_LONGMODE,
    ZYDIS_ISA_SET_LWP,
    ZYDIS_ISA_SET_LZCNT,
    ZYDIS_ISA_SET_MCOMMIT,
    ZYDIS_ISA_SET_MONITOR,
    ZYDIS_ISA_SET_MONITORX,
    ZYDIS_ISA_SET_MOVBE,
    ZYDIS_ISA_SET_MOVDIR,
    ZYDIS_ISA_SET_MPX,
    ZYDIS_ISA_SET_MSRLIST,
    ZYDIS_ISA_SET_PADLOCK_ACE,
    ZYDIS_ISA_SET_PADLOCK_PHE,
    ZYDIS_ISA_SET_PADLOCK_PMM,
    ZYDIS_ISA_SET_PADLOCK_RNG,
    ZYDIS_ISA_SET_PAUSE,
    ZYDIS_ISA_SET_PBNDKB,
    ZYDIS_ISA_SET_PCLMULQDQ,
    ZYDIS_ISA_SET_PCOMMIT,
    ZYDIS_ISA_SET_PCONFIG,
    ZYDIS_ISA_SET_PENTIUMMMX,
    ZYDIS_ISA_SET_PENTIUMREAL,
    ZYDIS_ISA_SET_PKU,
    ZYDIS_ISA_SET_POPCNT,
    ZYDIS_ISA_SET_PPRO,
    ZYDIS_ISA_SET_PREFETCHWT1,
    ZYDIS_ISA_SET_PREFETCH_NOP,
    ZYDIS_ISA_SET_PT,
    ZYDIS_ISA_SET_RAO_INT,
    ZYDIS_ISA_SET_RDPID,
    ZYDIS_ISA_SET_RDPMC,
    ZYDIS_ISA_SET_RDPRU,
    ZYDIS_ISA_SET_RDRAND,
    ZYDIS_ISA_SET_RDSEED,
    ZYDIS_ISA_SET_RDTSCP,
    ZYDIS_ISA_SET_RDWRFSGS,
    ZYDIS_ISA_SET_RTM,
    ZYDIS_ISA_SET_SERIALIZE,
    ZYDIS_ISA_SET_SGX,
    ZYDIS_ISA_SET_SGX_ENCLV,
    ZYDIS_ISA_SET_SHA,
    ZYDIS_ISA_SET_SHA512,
    ZYDIS_ISA_SET_SM3,
    ZYDIS_ISA_SET_SM4,
    ZYDIS_ISA_SET_SMAP,
    ZYDIS_ISA_SET_SMX,
    ZYDIS_ISA_SET_SNP,
    ZYDIS_ISA_SET_SSE,
    ZYDIS_ISA_SET_SSE2,
    ZYDIS_ISA_SET_SSE2MMX,
    ZYDIS_ISA_SET_SSE3,
    ZYDIS_ISA_SET_SSE3X87,
    ZYDIS_ISA_SET_SSE4,
    ZYDIS_ISA_SET_SSE42,
    ZYDIS_ISA_SET_SSE4A,
    ZYDIS_ISA_SET_SSEMXCSR,
    ZYDIS_ISA_SET_SSE_PREFETCH,
    ZYDIS_ISA_SET_SSSE3,
    ZYDIS_ISA_SET_SSSE3MMX,
    ZYDIS_ISA_SET_SVM,
    ZYDIS_ISA_SET_TBM,
    ZYDIS_ISA_SET_TDX,
    ZYDIS_ISA_SET_TSX_LDTRK,
    ZYDIS_ISA_SET_UINTR,
    ZYDIS_ISA_SET_VAES,
    ZYDIS_ISA_SET_VMFUNC,
    ZYDIS_ISA_SET_VPCLMULQDQ,
    ZYDIS_ISA_SET_VTX,
    ZYDIS_ISA_SET_WAITPKG,
    ZYDIS_ISA_SET_WRMSRNS,
    ZYDIS_ISA_SET_X87,
    ZYDIS_ISA_SET_XOP,
    ZYDIS_ISA_SET_XSAVE,
    ZYDIS_ISA_SET_XSAVEC,
    ZYDIS_ISA_SET_XSAVEOPT,
    ZYDIS_ISA_SET_XSAVES,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_ISA_SET_MAX_VALUE = ZYDIS_ISA_SET_XSAVES,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_ISA_SET_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_ISA_SET_MAX_VALUE)
} ZydisISASet;

//
// Header: Zydis/Generated/EnumISAExt.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/MetaInfo.h
//

/**
 * Defines the `ZydisISAExt` enum.
 */
typedef enum ZydisISAExt_
{
    ZYDIS_ISA_EXT_INVALID,
    ZYDIS_ISA_EXT_ADOX_ADCX,
    ZYDIS_ISA_EXT_AES,
    ZYDIS_ISA_EXT_AMD3DNOW,
    ZYDIS_ISA_EXT_AMD3DNOW_PREFETCH,
    ZYDIS_ISA_EXT_AMD_INVLPGB,
    ZYDIS_ISA_EXT_AMX_BF16,
    ZYDIS_ISA_EXT_AMX_FP16,
    ZYDIS_ISA_EXT_AMX_INT8,
    ZYDIS_ISA_EXT_AMX_TILE,
    ZYDIS_ISA_EXT_AVX,
    ZYDIS_ISA_EXT_AVX2,
    ZYDIS_ISA_EXT_AVX2GATHER,
    ZYDIS_ISA_EXT_AVX512EVEX,
    ZYDIS_ISA_EXT_AVX512VEX,
    ZYDIS_ISA_EXT_AVXAES,
    ZYDIS_ISA_EXT_AVX_IFMA,
    ZYDIS_ISA_EXT_AVX_NE_CONVERT,
    ZYDIS_ISA_EXT_AVX_VNNI,
    ZYDIS_ISA_EXT_AVX_VNNI_INT16,
    ZYDIS_ISA_EXT_AVX_VNNI_INT8,
    ZYDIS_ISA_EXT_BASE,
    ZYDIS_ISA_EXT_BMI1,
    ZYDIS_ISA_EXT_BMI2,
    ZYDIS_ISA_EXT_CET,
    ZYDIS_ISA_EXT_CLDEMOTE,
    ZYDIS_ISA_EXT_CLFLUSHOPT,
    ZYDIS_ISA_EXT_CLFSH,
    ZYDIS_ISA_EXT_CLWB,
    ZYDIS_ISA_EXT_CLZERO,
    ZYDIS_ISA_EXT_ENQCMD,
    ZYDIS_ISA_EXT_F16C,
    ZYDIS_ISA_EXT_FMA,
    ZYDIS_ISA_EXT_FMA4,
    ZYDIS_ISA_EXT_GFNI,
    ZYDIS_ISA_EXT_HRESET,
    ZYDIS_ISA_EXT_ICACHE_PREFETCH,
    ZYDIS_ISA_EXT_INVPCID,
    ZYDIS_ISA_EXT_KEYLOCKER,
    ZYDIS_ISA_EXT_KEYLOCKER_WIDE,
    ZYDIS_ISA_EXT_KNC,
    ZYDIS_ISA_EXT_KNCE,
    ZYDIS_ISA_EXT_KNCV,
    ZYDIS_ISA_EXT_LONGMODE,
    ZYDIS_ISA_EXT_LZCNT,
    ZYDIS_ISA_EXT_MCOMMIT,
    ZYDIS_ISA_EXT_MMX,
    ZYDIS_ISA_EXT_MONITOR,
    ZYDIS_ISA_EXT_MONITORX,
    ZYDIS_ISA_EXT_MOVBE,
    ZYDIS_ISA_EXT_MOVDIR,
    ZYDIS_ISA_EXT_MPX,
    ZYDIS_ISA_EXT_MSRLIST,
    ZYDIS_ISA_EXT_PADLOCK,
    ZYDIS_ISA_EXT_PAUSE,
    ZYDIS_ISA_EXT_PBNDKB,
    ZYDIS_ISA_EXT_PCLMULQDQ,
    ZYDIS_ISA_EXT_PCOMMIT,
    ZYDIS_ISA_EXT_PCONFIG,
    ZYDIS_ISA_EXT_PKU,
    ZYDIS_ISA_EXT_PREFETCHWT1,
    ZYDIS_ISA_EXT_PT,
    ZYDIS_ISA_EXT_RAO_INT,
    ZYDIS_ISA_EXT_RDPID,
    ZYDIS_ISA_EXT_RDPRU,
    ZYDIS_ISA_EXT_RDRAND,
    ZYDIS_ISA_EXT_RDSEED,
    ZYDIS_ISA_EXT_RDTSCP,
    ZYDIS_ISA_EXT_RDWRFSGS,
    ZYDIS_ISA_EXT_RTM,
    ZYDIS_ISA_EXT_SERIALIZE,
    ZYDIS_ISA_EXT_SGX,
    ZYDIS_ISA_EXT_SGX_ENCLV,
    ZYDIS_ISA_EXT_SHA,
    ZYDIS_ISA_EXT_SHA512,
    ZYDIS_ISA_EXT_SM3,
    ZYDIS_ISA_EXT_SM4,
    ZYDIS_ISA_EXT_SMAP,
    ZYDIS_ISA_EXT_SMX,
    ZYDIS_ISA_EXT_SNP,
    ZYDIS_ISA_EXT_SSE,
    ZYDIS_ISA_EXT_SSE2,
    ZYDIS_ISA_EXT_SSE3,
    ZYDIS_ISA_EXT_SSE4,
    ZYDIS_ISA_EXT_SSE4A,
    ZYDIS_ISA_EXT_SSSE3,
    ZYDIS_ISA_EXT_SVM,
    ZYDIS_ISA_EXT_TBM,
    ZYDIS_ISA_EXT_TDX,
    ZYDIS_ISA_EXT_TSX_LDTRK,
    ZYDIS_ISA_EXT_UINTR,
    ZYDIS_ISA_EXT_VAES,
    ZYDIS_ISA_EXT_VMFUNC,
    ZYDIS_ISA_EXT_VPCLMULQDQ,
    ZYDIS_ISA_EXT_VTX,
    ZYDIS_ISA_EXT_WAITPKG,
    ZYDIS_ISA_EXT_WRMSRNS,
    ZYDIS_ISA_EXT_X87,
    ZYDIS_ISA_EXT_XOP,
    ZYDIS_ISA_EXT_XSAVE,
    ZYDIS_ISA_EXT_XSAVEC,
    ZYDIS_ISA_EXT_XSAVEOPT,
    ZYDIS_ISA_EXT_XSAVES,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_ISA_EXT_MAX_VALUE = ZYDIS_ISA_EXT_XSAVES,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_ISA_EXT_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_ISA_EXT_MAX_VALUE)
} ZydisISAExt;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

 /**
 * Returns the specified instruction category string.
 *
 * @param   category    The instruction category.
 *
 * @return  The instruction category string or `ZYAN_NULL`, if an invalid category was passed.
 */
ZYDIS_EXPORT const char* ZydisCategoryGetString(ZydisInstructionCategory category);

/**
 * Returns the specified isa-set string.
 *
 * @param   isa_set The isa-set.
 *
 * @return  The isa-set string or `ZYAN_NULL`, if an invalid isa-set was passed.
 */
ZYDIS_EXPORT const char* ZydisISASetGetString(ZydisISASet isa_set);

/**
 * Returns the specified isa-extension string.
 *
 * @param   isa_ext The isa-extension.
 *
 * @return  The isa-extension string or `ZYAN_NULL`, if an invalid isa-extension was passed.
 */
ZYDIS_EXPORT const char* ZydisISAExtGetString(ZydisISAExt isa_ext);

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_METAINFO_H */

//
// Header: Zydis/Mnemonic.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Mnemonic constant definitions and helper functions.
 */

#ifndef ZYDIS_MNEMONIC_H
#define ZYDIS_MNEMONIC_H


//
// Header: Zydis/ShortString.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/Mnemonic.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Defines the immutable and storage-efficient `ZydisShortString` struct, which
 * is used to store strings in the generated tables.
 */

#ifndef ZYDIS_SHORTSTRING_H
#define ZYDIS_SHORTSTRING_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

#if !defined(ZYAN_APPLE)
#   pragma pack(push, 1)
#endif

/**
 * Defines the `ZydisShortString` struct.
 *
 * This compact struct is mainly used for internal string-tables to save up some bytes.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZydisShortString_
{
    /**
     * The buffer that contains the actual (null-terminated) string.
    */
    const char* data;
    /**
     * The length (number of characters) of the string (without 0-termination).
    */
    ZyanU8 size;
} ZydisShortString;

#if !defined(ZYAN_APPLE)
#   pragma pack(pop)
#endif

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/**
 * Declares a `ZydisShortString` from a static C-style string.
 *
 * @param   string  The C-string constant.
 */
#define ZYDIS_MAKE_SHORTSTRING(string) \
    { string, sizeof(string) - 1 }

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_SHORTSTRING_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */


//
// Header: Zydis/Generated/EnumMnemonic.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/Mnemonic.h
//

/**
 * Defines the `ZydisMnemonic` enum.
 */
typedef enum ZydisMnemonic_
{
    ZYDIS_MNEMONIC_INVALID,
    ZYDIS_MNEMONIC_AAA,
    ZYDIS_MNEMONIC_AAD,
    ZYDIS_MNEMONIC_AADD,
    ZYDIS_MNEMONIC_AAM,
    ZYDIS_MNEMONIC_AAND,
    ZYDIS_MNEMONIC_AAS,
    ZYDIS_MNEMONIC_ADC,
    ZYDIS_MNEMONIC_ADCX,
    ZYDIS_MNEMONIC_ADD,
    ZYDIS_MNEMONIC_ADDPD,
    ZYDIS_MNEMONIC_ADDPS,
    ZYDIS_MNEMONIC_ADDSD,
    ZYDIS_MNEMONIC_ADDSS,
    ZYDIS_MNEMONIC_ADDSUBPD,
    ZYDIS_MNEMONIC_ADDSUBPS,
    ZYDIS_MNEMONIC_ADOX,
    ZYDIS_MNEMONIC_AESDEC,
    ZYDIS_MNEMONIC_AESDEC128KL,
    ZYDIS_MNEMONIC_AESDEC256KL,
    ZYDIS_MNEMONIC_AESDECLAST,
    ZYDIS_MNEMONIC_AESDECWIDE128KL,
    ZYDIS_MNEMONIC_AESDECWIDE256KL,
    ZYDIS_MNEMONIC_AESENC,
    ZYDIS_MNEMONIC_AESENC128KL,
    ZYDIS_MNEMONIC_AESENC256KL,
    ZYDIS_MNEMONIC_AESENCLAST,
    ZYDIS_MNEMONIC_AESENCWIDE128KL,
    ZYDIS_MNEMONIC_AESENCWIDE256KL,
    ZYDIS_MNEMONIC_AESIMC,
    ZYDIS_MNEMONIC_AESKEYGENASSIST,
    ZYDIS_MNEMONIC_AND,
    ZYDIS_MNEMONIC_ANDN,
    ZYDIS_MNEMONIC_ANDNPD,
    ZYDIS_MNEMONIC_ANDNPS,
    ZYDIS_MNEMONIC_ANDPD,
    ZYDIS_MNEMONIC_ANDPS,
    ZYDIS_MNEMONIC_AOR,
    ZYDIS_MNEMONIC_ARPL,
    ZYDIS_MNEMONIC_AXOR,
    ZYDIS_MNEMONIC_BEXTR,
    ZYDIS_MNEMONIC_BLCFILL,
    ZYDIS_MNEMONIC_BLCI,
    ZYDIS_MNEMONIC_BLCIC,
    ZYDIS_MNEMONIC_BLCMSK,
    ZYDIS_MNEMONIC_BLCS,
    ZYDIS_MNEMONIC_BLENDPD,
    ZYDIS_MNEMONIC_BLENDPS,
    ZYDIS_MNEMONIC_BLENDVPD,
    ZYDIS_MNEMONIC_BLENDVPS,
    ZYDIS_MNEMONIC_BLSFILL,
    ZYDIS_MNEMONIC_BLSI,
    ZYDIS_MNEMONIC_BLSIC,
    ZYDIS_MNEMONIC_BLSMSK,
    ZYDIS_MNEMONIC_BLSR,
    ZYDIS_MNEMONIC_BNDCL,
    ZYDIS_MNEMONIC_BNDCN,
    ZYDIS_MNEMONIC_BNDCU,
    ZYDIS_MNEMONIC_BNDLDX,
    ZYDIS_MNEMONIC_BNDMK,
    ZYDIS_MNEMONIC_BNDMOV,
    ZYDIS_MNEMONIC_BNDSTX,
    ZYDIS_MNEMONIC_BOUND,
    ZYDIS_MNEMONIC_BSF,
    ZYDIS_MNEMONIC_BSR,
    ZYDIS_MNEMONIC_BSWAP,
    ZYDIS_MNEMONIC_BT,
    ZYDIS_MNEMONIC_BTC,
    ZYDIS_MNEMONIC_BTR,
    ZYDIS_MNEMONIC_BTS,
    ZYDIS_MNEMONIC_BZHI,
    ZYDIS_MNEMONIC_CALL,
    ZYDIS_MNEMONIC_CBW,
    ZYDIS_MNEMONIC_CDQ,
    ZYDIS_MNEMONIC_CDQE,
    ZYDIS_MNEMONIC_CLAC,
    ZYDIS_MNEMONIC_CLC,
    ZYDIS_MNEMONIC_CLD,
    ZYDIS_MNEMONIC_CLDEMOTE,
    ZYDIS_MNEMONIC_CLEVICT0,
    ZYDIS_MNEMONIC_CLEVICT1,
    ZYDIS_MNEMONIC_CLFLUSH,
    ZYDIS_MNEMONIC_CLFLUSHOPT,
    ZYDIS_MNEMONIC_CLGI,
    ZYDIS_MNEMONIC_CLI,
    ZYDIS_MNEMONIC_CLRSSBSY,
    ZYDIS_MNEMONIC_CLTS,
    ZYDIS_MNEMONIC_CLUI,
    ZYDIS_MNEMONIC_CLWB,
    ZYDIS_MNEMONIC_CLZERO,
    ZYDIS_MNEMONIC_CMC,
    ZYDIS_MNEMONIC_CMOVB,
    ZYDIS_MNEMONIC_CMOVBE,
    ZYDIS_MNEMONIC_CMOVL,
    ZYDIS_MNEMONIC_CMOVLE,
    ZYDIS_MNEMONIC_CMOVNB,
    ZYDIS_MNEMONIC_CMOVNBE,
    ZYDIS_MNEMONIC_CMOVNL,
    ZYDIS_MNEMONIC_CMOVNLE,
    ZYDIS_MNEMONIC_CMOVNO,
    ZYDIS_MNEMONIC_CMOVNP,
    ZYDIS_MNEMONIC_CMOVNS,
    ZYDIS_MNEMONIC_CMOVNZ,
    ZYDIS_MNEMONIC_CMOVO,
    ZYDIS_MNEMONIC_CMOVP,
    ZYDIS_MNEMONIC_CMOVS,
    ZYDIS_MNEMONIC_CMOVZ,
    ZYDIS_MNEMONIC_CMP,
    ZYDIS_MNEMONIC_CMPPD,
    ZYDIS_MNEMONIC_CMPPS,
    ZYDIS_MNEMONIC_CMPSB,
    ZYDIS_MNEMONIC_CMPSD,
    ZYDIS_MNEMONIC_CMPSQ,
    ZYDIS_MNEMONIC_CMPSS,
    ZYDIS_MNEMONIC_CMPSW,
    ZYDIS_MNEMONIC_CMPXCHG,
    ZYDIS_MNEMONIC_CMPXCHG16B,
    ZYDIS_MNEMONIC_CMPXCHG8B,
    ZYDIS_MNEMONIC_COMISD,
    ZYDIS_MNEMONIC_COMISS,
    ZYDIS_MNEMONIC_CPUID,
    ZYDIS_MNEMONIC_CQO,
    ZYDIS_MNEMONIC_CRC32,
    ZYDIS_MNEMONIC_CVTDQ2PD,
    ZYDIS_MNEMONIC_CVTDQ2PS,
    ZYDIS_MNEMONIC_CVTPD2DQ,
    ZYDIS_MNEMONIC_CVTPD2PI,
    ZYDIS_MNEMONIC_CVTPD2PS,
    ZYDIS_MNEMONIC_CVTPI2PD,
    ZYDIS_MNEMONIC_CVTPI2PS,
    ZYDIS_MNEMONIC_CVTPS2DQ,
    ZYDIS_MNEMONIC_CVTPS2PD,
    ZYDIS_MNEMONIC_CVTPS2PI,
    ZYDIS_MNEMONIC_CVTSD2SI,
    ZYDIS_MNEMONIC_CVTSD2SS,
    ZYDIS_MNEMONIC_CVTSI2SD,
    ZYDIS_MNEMONIC_CVTSI2SS,
    ZYDIS_MNEMONIC_CVTSS2SD,
    ZYDIS_MNEMONIC_CVTSS2SI,
    ZYDIS_MNEMONIC_CVTTPD2DQ,
    ZYDIS_MNEMONIC_CVTTPD2PI,
    ZYDIS_MNEMONIC_CVTTPS2DQ,
    ZYDIS_MNEMONIC_CVTTPS2PI,
    ZYDIS_MNEMONIC_CVTTSD2SI,
    ZYDIS_MNEMONIC_CVTTSS2SI,
    ZYDIS_MNEMONIC_CWD,
    ZYDIS_MNEMONIC_CWDE,
    ZYDIS_MNEMONIC_DAA,
    ZYDIS_MNEMONIC_DAS,
    ZYDIS_MNEMONIC_DEC,
    ZYDIS_MNEMONIC_DELAY,
    ZYDIS_MNEMONIC_DIV,
    ZYDIS_MNEMONIC_DIVPD,
    ZYDIS_MNEMONIC_DIVPS,
    ZYDIS_MNEMONIC_DIVSD,
    ZYDIS_MNEMONIC_DIVSS,
    ZYDIS_MNEMONIC_DPPD,
    ZYDIS_MNEMONIC_DPPS,
    ZYDIS_MNEMONIC_EMMS,
    ZYDIS_MNEMONIC_ENCLS,
    ZYDIS_MNEMONIC_ENCLU,
    ZYDIS_MNEMONIC_ENCLV,
    ZYDIS_MNEMONIC_ENCODEKEY128,
    ZYDIS_MNEMONIC_ENCODEKEY256,
    ZYDIS_MNEMONIC_ENDBR32,
    ZYDIS_MNEMONIC_ENDBR64,
    ZYDIS_MNEMONIC_ENQCMD,
    ZYDIS_MNEMONIC_ENQCMDS,
    ZYDIS_MNEMONIC_ENTER,
    ZYDIS_MNEMONIC_EXTRACTPS,
    ZYDIS_MNEMONIC_EXTRQ,
    ZYDIS_MNEMONIC_F2XM1,
    ZYDIS_MNEMONIC_FABS,
    ZYDIS_MNEMONIC_FADD,
    ZYDIS_MNEMONIC_FADDP,
    ZYDIS_MNEMONIC_FBLD,
    ZYDIS_MNEMONIC_FBSTP,
    ZYDIS_MNEMONIC_FCHS,
    ZYDIS_MNEMONIC_FCMOVB,
    ZYDIS_MNEMONIC_FCMOVBE,
    ZYDIS_MNEMONIC_FCMOVE,
    ZYDIS_MNEMONIC_FCMOVNB,
    ZYDIS_MNEMONIC_FCMOVNBE,
    ZYDIS_MNEMONIC_FCMOVNE,
    ZYDIS_MNEMONIC_FCMOVNU,
    ZYDIS_MNEMONIC_FCMOVU,
    ZYDIS_MNEMONIC_FCOM,
    ZYDIS_MNEMONIC_FCOMI,
    ZYDIS_MNEMONIC_FCOMIP,
    ZYDIS_MNEMONIC_FCOMP,
    ZYDIS_MNEMONIC_FCOMPP,
    ZYDIS_MNEMONIC_FCOS,
    ZYDIS_MNEMONIC_FDECSTP,
    ZYDIS_MNEMONIC_FDISI8087_NOP,
    ZYDIS_MNEMONIC_FDIV,
    ZYDIS_MNEMONIC_FDIVP,
    ZYDIS_MNEMONIC_FDIVR,
    ZYDIS_MNEMONIC_FDIVRP,
    ZYDIS_MNEMONIC_FEMMS,
    ZYDIS_MNEMONIC_FENI8087_NOP,
    ZYDIS_MNEMONIC_FFREE,
    ZYDIS_MNEMONIC_FFREEP,
    ZYDIS_MNEMONIC_FIADD,
    ZYDIS_MNEMONIC_FICOM,
    ZYDIS_MNEMONIC_FICOMP,
    ZYDIS_MNEMONIC_FIDIV,
    ZYDIS_MNEMONIC_FIDIVR,
    ZYDIS_MNEMONIC_FILD,
    ZYDIS_MNEMONIC_FIMUL,
    ZYDIS_MNEMONIC_FINCSTP,
    ZYDIS_MNEMONIC_FIST,
    ZYDIS_MNEMONIC_FISTP,
    ZYDIS_MNEMONIC_FISTTP,
    ZYDIS_MNEMONIC_FISUB,
    ZYDIS_MNEMONIC_FISUBR,
    ZYDIS_MNEMONIC_FLD,
    ZYDIS_MNEMONIC_FLD1,
    ZYDIS_MNEMONIC_FLDCW,
    ZYDIS_MNEMONIC_FLDENV,
    ZYDIS_MNEMONIC_FLDL2E,
    ZYDIS_MNEMONIC_FLDL2T,
    ZYDIS_MNEMONIC_FLDLG2,
    ZYDIS_MNEMONIC_FLDLN2,
    ZYDIS_MNEMONIC_FLDPI,
    ZYDIS_MNEMONIC_FLDZ,
    ZYDIS_MNEMONIC_FMUL,
    ZYDIS_MNEMONIC_FMULP,
    ZYDIS_MNEMONIC_FNCLEX,
    ZYDIS_MNEMONIC_FNINIT,
    ZYDIS_MNEMONIC_FNOP,
    ZYDIS_MNEMONIC_FNSAVE,
    ZYDIS_MNEMONIC_FNSTCW,
    ZYDIS_MNEMONIC_FNSTENV,
    ZYDIS_MNEMONIC_FNSTSW,
    ZYDIS_MNEMONIC_FPATAN,
    ZYDIS_MNEMONIC_FPREM,
    ZYDIS_MNEMONIC_FPREM1,
    ZYDIS_MNEMONIC_FPTAN,
    ZYDIS_MNEMONIC_FRNDINT,
    ZYDIS_MNEMONIC_FRSTOR,
    ZYDIS_MNEMONIC_FSCALE,
    ZYDIS_MNEMONIC_FSETPM287_NOP,
    ZYDIS_MNEMONIC_FSIN,
    ZYDIS_MNEMONIC_FSINCOS,
    ZYDIS_MNEMONIC_FSQRT,
    ZYDIS_MNEMONIC_FST,
    ZYDIS_MNEMONIC_FSTP,
    ZYDIS_MNEMONIC_FSTPNCE,
    ZYDIS_MNEMONIC_FSUB,
    ZYDIS_MNEMONIC_FSUBP,
    ZYDIS_MNEMONIC_FSUBR,
    ZYDIS_MNEMONIC_FSUBRP,
    ZYDIS_MNEMONIC_FTST,
    ZYDIS_MNEMONIC_FUCOM,
    ZYDIS_MNEMONIC_FUCOMI,
    ZYDIS_MNEMONIC_FUCOMIP,
    ZYDIS_MNEMONIC_FUCOMP,
    ZYDIS_MNEMONIC_FUCOMPP,
    ZYDIS_MNEMONIC_FWAIT,
    ZYDIS_MNEMONIC_FXAM,
    ZYDIS_MNEMONIC_FXCH,
    ZYDIS_MNEMONIC_FXRSTOR,
    ZYDIS_MNEMONIC_FXRSTOR64,
    ZYDIS_MNEMONIC_FXSAVE,
    ZYDIS_MNEMONIC_FXSAVE64,
    ZYDIS_MNEMONIC_FXTRACT,
    ZYDIS_MNEMONIC_FYL2X,
    ZYDIS_MNEMONIC_FYL2XP1,
    ZYDIS_MNEMONIC_GETSEC,
    ZYDIS_MNEMONIC_GF2P8AFFINEINVQB,
    ZYDIS_MNEMONIC_GF2P8AFFINEQB,
    ZYDIS_MNEMONIC_GF2P8MULB,
    ZYDIS_MNEMONIC_HADDPD,
    ZYDIS_MNEMONIC_HADDPS,
    ZYDIS_MNEMONIC_HLT,
    ZYDIS_MNEMONIC_HRESET,
    ZYDIS_MNEMONIC_HSUBPD,
    ZYDIS_MNEMONIC_HSUBPS,
    ZYDIS_MNEMONIC_IDIV,
    ZYDIS_MNEMONIC_IMUL,
    ZYDIS_MNEMONIC_IN,
    ZYDIS_MNEMONIC_INC,
    ZYDIS_MNEMONIC_INCSSPD,
    ZYDIS_MNEMONIC_INCSSPQ,
    ZYDIS_MNEMONIC_INSB,
    ZYDIS_MNEMONIC_INSD,
    ZYDIS_MNEMONIC_INSERTPS,
    ZYDIS_MNEMONIC_INSERTQ,
    ZYDIS_MNEMONIC_INSW,
    ZYDIS_MNEMONIC_INT,
    ZYDIS_MNEMONIC_INT1,
    ZYDIS_MNEMONIC_INT3,
    ZYDIS_MNEMONIC_INTO,
    ZYDIS_MNEMONIC_INVD,
    ZYDIS_MNEMONIC_INVEPT,
    ZYDIS_MNEMONIC_INVLPG,
    ZYDIS_MNEMONIC_INVLPGA,
    ZYDIS_MNEMONIC_INVLPGB,
    ZYDIS_MNEMONIC_INVPCID,
    ZYDIS_MNEMONIC_INVVPID,
    ZYDIS_MNEMONIC_IRET,
    ZYDIS_MNEMONIC_IRETD,
    ZYDIS_MNEMONIC_IRETQ,
    ZYDIS_MNEMONIC_JB,
    ZYDIS_MNEMONIC_JBE,
    ZYDIS_MNEMONIC_JCXZ,
    ZYDIS_MNEMONIC_JECXZ,
    ZYDIS_MNEMONIC_JKNZD,
    ZYDIS_MNEMONIC_JKZD,
    ZYDIS_MNEMONIC_JL,
    ZYDIS_MNEMONIC_JLE,
    ZYDIS_MNEMONIC_JMP,
    ZYDIS_MNEMONIC_JNB,
    ZYDIS_MNEMONIC_JNBE,
    ZYDIS_MNEMONIC_JNL,
    ZYDIS_MNEMONIC_JNLE,
    ZYDIS_MNEMONIC_JNO,
    ZYDIS_MNEMONIC_JNP,
    ZYDIS_MNEMONIC_JNS,
    ZYDIS_MNEMONIC_JNZ,
    ZYDIS_MNEMONIC_JO,
    ZYDIS_MNEMONIC_JP,
    ZYDIS_MNEMONIC_JRCXZ,
    ZYDIS_MNEMONIC_JS,
    ZYDIS_MNEMONIC_JZ,
    ZYDIS_MNEMONIC_KADDB,
    ZYDIS_MNEMONIC_KADDD,
    ZYDIS_MNEMONIC_KADDQ,
    ZYDIS_MNEMONIC_KADDW,
    ZYDIS_MNEMONIC_KAND,
    ZYDIS_MNEMONIC_KANDB,
    ZYDIS_MNEMONIC_KANDD,
    ZYDIS_MNEMONIC_KANDN,
    ZYDIS_MNEMONIC_KANDNB,
    ZYDIS_MNEMONIC_KANDND,
    ZYDIS_MNEMONIC_KANDNQ,
    ZYDIS_MNEMONIC_KANDNR,
    ZYDIS_MNEMONIC_KANDNW,
    ZYDIS_MNEMONIC_KANDQ,
    ZYDIS_MNEMONIC_KANDW,
    ZYDIS_MNEMONIC_KCONCATH,
    ZYDIS_MNEMONIC_KCONCATL,
    ZYDIS_MNEMONIC_KEXTRACT,
    ZYDIS_MNEMONIC_KMERGE2L1H,
    ZYDIS_MNEMONIC_KMERGE2L1L,
    ZYDIS_MNEMONIC_KMOV,
    ZYDIS_MNEMONIC_KMOVB,
    ZYDIS_MNEMONIC_KMOVD,
    ZYDIS_MNEMONIC_KMOVQ,
    ZYDIS_MNEMONIC_KMOVW,
    ZYDIS_MNEMONIC_KNOT,
    ZYDIS_MNEMONIC_KNOTB,
    ZYDIS_MNEMONIC_KNOTD,
    ZYDIS_MNEMONIC_KNOTQ,
    ZYDIS_MNEMONIC_KNOTW,
    ZYDIS_MNEMONIC_KOR,
    ZYDIS_MNEMONIC_KORB,
    ZYDIS_MNEMONIC_KORD,
    ZYDIS_MNEMONIC_KORQ,
    ZYDIS_MNEMONIC_KORTEST,
    ZYDIS_MNEMONIC_KORTESTB,
    ZYDIS_MNEMONIC_KORTESTD,
    ZYDIS_MNEMONIC_KORTESTQ,
    ZYDIS_MNEMONIC_KORTESTW,
    ZYDIS_MNEMONIC_KORW,
    ZYDIS_MNEMONIC_KSHIFTLB,
    ZYDIS_MNEMONIC_KSHIFTLD,
    ZYDIS_MNEMONIC_KSHIFTLQ,
    ZYDIS_MNEMONIC_KSHIFTLW,
    ZYDIS_MNEMONIC_KSHIFTRB,
    ZYDIS_MNEMONIC_KSHIFTRD,
    ZYDIS_MNEMONIC_KSHIFTRQ,
    ZYDIS_MNEMONIC_KSHIFTRW,
    ZYDIS_MNEMONIC_KTESTB,
    ZYDIS_MNEMONIC_KTESTD,
    ZYDIS_MNEMONIC_KTESTQ,
    ZYDIS_MNEMONIC_KTESTW,
    ZYDIS_MNEMONIC_KUNPCKBW,
    ZYDIS_MNEMONIC_KUNPCKDQ,
    ZYDIS_MNEMONIC_KUNPCKWD,
    ZYDIS_MNEMONIC_KXNOR,
    ZYDIS_MNEMONIC_KXNORB,
    ZYDIS_MNEMONIC_KXNORD,
    ZYDIS_MNEMONIC_KXNORQ,
    ZYDIS_MNEMONIC_KXNORW,
    ZYDIS_MNEMONIC_KXOR,
    ZYDIS_MNEMONIC_KXORB,
    ZYDIS_MNEMONIC_KXORD,
    ZYDIS_MNEMONIC_KXORQ,
    ZYDIS_MNEMONIC_KXORW,
    ZYDIS_MNEMONIC_LAHF,
    ZYDIS_MNEMONIC_LAR,
    ZYDIS_MNEMONIC_LDDQU,
    ZYDIS_MNEMONIC_LDMXCSR,
    ZYDIS_MNEMONIC_LDS,
    ZYDIS_MNEMONIC_LDTILECFG,
    ZYDIS_MNEMONIC_LEA,
    ZYDIS_MNEMONIC_LEAVE,
    ZYDIS_MNEMONIC_LES,
    ZYDIS_MNEMONIC_LFENCE,
    ZYDIS_MNEMONIC_LFS,
    ZYDIS_MNEMONIC_LGDT,
    ZYDIS_MNEMONIC_LGS,
    ZYDIS_MNEMONIC_LIDT,
    ZYDIS_MNEMONIC_LLDT,
    ZYDIS_MNEMONIC_LLWPCB,
    ZYDIS_MNEMONIC_LMSW,
    ZYDIS_MNEMONIC_LOADIWKEY,
    ZYDIS_MNEMONIC_LODSB,
    ZYDIS_MNEMONIC_LODSD,
    ZYDIS_MNEMONIC_LODSQ,
    ZYDIS_MNEMONIC_LODSW,
    ZYDIS_MNEMONIC_LOOP,
    ZYDIS_MNEMONIC_LOOPE,
    ZYDIS_MNEMONIC_LOOPNE,
    ZYDIS_MNEMONIC_LSL,
    ZYDIS_MNEMONIC_LSS,
    ZYDIS_MNEMONIC_LTR,
    ZYDIS_MNEMONIC_LWPINS,
    ZYDIS_MNEMONIC_LWPVAL,
    ZYDIS_MNEMONIC_LZCNT,
    ZYDIS_MNEMONIC_MASKMOVDQU,
    ZYDIS_MNEMONIC_MASKMOVQ,
    ZYDIS_MNEMONIC_MAXPD,
    ZYDIS_MNEMONIC_MAXPS,
    ZYDIS_MNEMONIC_MAXSD,
    ZYDIS_MNEMONIC_MAXSS,
    ZYDIS_MNEMONIC_MCOMMIT,
    ZYDIS_MNEMONIC_MFENCE,
    ZYDIS_MNEMONIC_MINPD,
    ZYDIS_MNEMONIC_MINPS,
    ZYDIS_MNEMONIC_MINSD,
    ZYDIS_MNEMONIC_MINSS,
    ZYDIS_MNEMONIC_MONITOR,
    ZYDIS_MNEMONIC_MONITORX,
    ZYDIS_MNEMONIC_MONTMUL,
    ZYDIS_MNEMONIC_MOV,
    ZYDIS_MNEMONIC_MOVAPD,
    ZYDIS_MNEMONIC_MOVAPS,
    ZYDIS_MNEMONIC_MOVBE,
    ZYDIS_MNEMONIC_MOVD,
    ZYDIS_MNEMONIC_MOVDDUP,
    ZYDIS_MNEMONIC_MOVDIR64B,
    ZYDIS_MNEMONIC_MOVDIRI,
    ZYDIS_MNEMONIC_MOVDQ2Q,
    ZYDIS_MNEMONIC_MOVDQA,
    ZYDIS_MNEMONIC_MOVDQU,
    ZYDIS_MNEMONIC_MOVHLPS,
    ZYDIS_MNEMONIC_MOVHPD,
    ZYDIS_MNEMONIC_MOVHPS,
    ZYDIS_MNEMONIC_MOVLHPS,
    ZYDIS_MNEMONIC_MOVLPD,
    ZYDIS_MNEMONIC_MOVLPS,
    ZYDIS_MNEMONIC_MOVMSKPD,
    ZYDIS_MNEMONIC_MOVMSKPS,
    ZYDIS_MNEMONIC_MOVNTDQ,
    ZYDIS_MNEMONIC_MOVNTDQA,
    ZYDIS_MNEMONIC_MOVNTI,
    ZYDIS_MNEMONIC_MOVNTPD,
    ZYDIS_MNEMONIC_MOVNTPS,
    ZYDIS_MNEMONIC_MOVNTQ,
    ZYDIS_MNEMONIC_MOVNTSD,
    ZYDIS_MNEMONIC_MOVNTSS,
    ZYDIS_MNEMONIC_MOVQ,
    ZYDIS_MNEMONIC_MOVQ2DQ,
    ZYDIS_MNEMONIC_MOVSB,
    ZYDIS_MNEMONIC_MOVSD,
    ZYDIS_MNEMONIC_MOVSHDUP,
    ZYDIS_MNEMONIC_MOVSLDUP,
    ZYDIS_MNEMONIC_MOVSQ,
    ZYDIS_MNEMONIC_MOVSS,
    ZYDIS_MNEMONIC_MOVSW,
    ZYDIS_MNEMONIC_MOVSX,
    ZYDIS_MNEMONIC_MOVSXD,
    ZYDIS_MNEMONIC_MOVUPD,
    ZYDIS_MNEMONIC_MOVUPS,
    ZYDIS_MNEMONIC_MOVZX,
    ZYDIS_MNEMONIC_MPSADBW,
    ZYDIS_MNEMONIC_MUL,
    ZYDIS_MNEMONIC_MULPD,
    ZYDIS_MNEMONIC_MULPS,
    ZYDIS_MNEMONIC_MULSD,
    ZYDIS_MNEMONIC_MULSS,
    ZYDIS_MNEMONIC_MULX,
    ZYDIS_MNEMONIC_MWAIT,
    ZYDIS_MNEMONIC_MWAITX,
    ZYDIS_MNEMONIC_NEG,
    ZYDIS_MNEMONIC_NOP,
    ZYDIS_MNEMONIC_NOT,
    ZYDIS_MNEMONIC_OR,
    ZYDIS_MNEMONIC_ORPD,
    ZYDIS_MNEMONIC_ORPS,
    ZYDIS_MNEMONIC_OUT,
    ZYDIS_MNEMONIC_OUTSB,
    ZYDIS_MNEMONIC_OUTSD,
    ZYDIS_MNEMONIC_OUTSW,
    ZYDIS_MNEMONIC_PABSB,
    ZYDIS_MNEMONIC_PABSD,
    ZYDIS_MNEMONIC_PABSW,
    ZYDIS_MNEMONIC_PACKSSDW,
    ZYDIS_MNEMONIC_PACKSSWB,
    ZYDIS_MNEMONIC_PACKUSDW,
    ZYDIS_MNEMONIC_PACKUSWB,
    ZYDIS_MNEMONIC_PADDB,
    ZYDIS_MNEMONIC_PADDD,
    ZYDIS_MNEMONIC_PADDQ,
    ZYDIS_MNEMONIC_PADDSB,
    ZYDIS_MNEMONIC_PADDSW,
    ZYDIS_MNEMONIC_PADDUSB,
    ZYDIS_MNEMONIC_PADDUSW,
    ZYDIS_MNEMONIC_PADDW,
    ZYDIS_MNEMONIC_PALIGNR,
    ZYDIS_MNEMONIC_PAND,
    ZYDIS_MNEMONIC_PANDN,
    ZYDIS_MNEMONIC_PAUSE,
    ZYDIS_MNEMONIC_PAVGB,
    ZYDIS_MNEMONIC_PAVGUSB,
    ZYDIS_MNEMONIC_PAVGW,
    ZYDIS_MNEMONIC_PBLENDVB,
    ZYDIS_MNEMONIC_PBLENDW,
    ZYDIS_MNEMONIC_PBNDKB,
    ZYDIS_MNEMONIC_PCLMULQDQ,
    ZYDIS_MNEMONIC_PCMPEQB,
    ZYDIS_MNEMONIC_PCMPEQD,
    ZYDIS_MNEMONIC_PCMPEQQ,
    ZYDIS_MNEMONIC_PCMPEQW,
    ZYDIS_MNEMONIC_PCMPESTRI,
    ZYDIS_MNEMONIC_PCMPESTRM,
    ZYDIS_MNEMONIC_PCMPGTB,
    ZYDIS_MNEMONIC_PCMPGTD,
    ZYDIS_MNEMONIC_PCMPGTQ,
    ZYDIS_MNEMONIC_PCMPGTW,
    ZYDIS_MNEMONIC_PCMPISTRI,
    ZYDIS_MNEMONIC_PCMPISTRM,
    ZYDIS_MNEMONIC_PCOMMIT,
    ZYDIS_MNEMONIC_PCONFIG,
    ZYDIS_MNEMONIC_PDEP,
    ZYDIS_MNEMONIC_PEXT,
    ZYDIS_MNEMONIC_PEXTRB,
    ZYDIS_MNEMONIC_PEXTRD,
    ZYDIS_MNEMONIC_PEXTRQ,
    ZYDIS_MNEMONIC_PEXTRW,
    ZYDIS_MNEMONIC_PF2ID,
    ZYDIS_MNEMONIC_PF2IW,
    ZYDIS_MNEMONIC_PFACC,
    ZYDIS_MNEMONIC_PFADD,
    ZYDIS_MNEMONIC_PFCMPEQ,
    ZYDIS_MNEMONIC_PFCMPGE,
    ZYDIS_MNEMONIC_PFCMPGT,
    ZYDIS_MNEMONIC_PFCPIT1,
    ZYDIS_MNEMONIC_PFMAX,
    ZYDIS_MNEMONIC_PFMIN,
    ZYDIS_MNEMONIC_PFMUL,
    ZYDIS_MNEMONIC_PFNACC,
    ZYDIS_MNEMONIC_PFPNACC,
    ZYDIS_MNEMONIC_PFRCP,
    ZYDIS_MNEMONIC_PFRCPIT2,
    ZYDIS_MNEMONIC_PFRSQIT1,
    ZYDIS_MNEMONIC_PFSQRT,
    ZYDIS_MNEMONIC_PFSUB,
    ZYDIS_MNEMONIC_PFSUBR,
    ZYDIS_MNEMONIC_PHADDD,
    ZYDIS_MNEMONIC_PHADDSW,
    ZYDIS_MNEMONIC_PHADDW,
    ZYDIS_MNEMONIC_PHMINPOSUW,
    ZYDIS_MNEMONIC_PHSUBD,
    ZYDIS_MNEMONIC_PHSUBSW,
    ZYDIS_MNEMONIC_PHSUBW,
    ZYDIS_MNEMONIC_PI2FD,
    ZYDIS_MNEMONIC_PI2FW,
    ZYDIS_MNEMONIC_PINSRB,
    ZYDIS_MNEMONIC_PINSRD,
    ZYDIS_MNEMONIC_PINSRQ,
    ZYDIS_MNEMONIC_PINSRW,
    ZYDIS_MNEMONIC_PMADDUBSW,
    ZYDIS_MNEMONIC_PMADDWD,
    ZYDIS_MNEMONIC_PMAXSB,
    ZYDIS_MNEMONIC_PMAXSD,
    ZYDIS_MNEMONIC_PMAXSW,
    ZYDIS_MNEMONIC_PMAXUB,
    ZYDIS_MNEMONIC_PMAXUD,
    ZYDIS_MNEMONIC_PMAXUW,
    ZYDIS_MNEMONIC_PMINSB,
    ZYDIS_MNEMONIC_PMINSD,
    ZYDIS_MNEMONIC_PMINSW,
    ZYDIS_MNEMONIC_PMINUB,
    ZYDIS_MNEMONIC_PMINUD,
    ZYDIS_MNEMONIC_PMINUW,
    ZYDIS_MNEMONIC_PMOVMSKB,
    ZYDIS_MNEMONIC_PMOVSXBD,
    ZYDIS_MNEMONIC_PMOVSXBQ,
    ZYDIS_MNEMONIC_PMOVSXBW,
    ZYDIS_MNEMONIC_PMOVSXDQ,
    ZYDIS_MNEMONIC_PMOVSXWD,
    ZYDIS_MNEMONIC_PMOVSXWQ,
    ZYDIS_MNEMONIC_PMOVZXBD,
    ZYDIS_MNEMONIC_PMOVZXBQ,
    ZYDIS_MNEMONIC_PMOVZXBW,
    ZYDIS_MNEMONIC_PMOVZXDQ,
    ZYDIS_MNEMONIC_PMOVZXWD,
    ZYDIS_MNEMONIC_PMOVZXWQ,
    ZYDIS_MNEMONIC_PMULDQ,
    ZYDIS_MNEMONIC_PMULHRSW,
    ZYDIS_MNEMONIC_PMULHRW,
    ZYDIS_MNEMONIC_PMULHUW,
    ZYDIS_MNEMONIC_PMULHW,
    ZYDIS_MNEMONIC_PMULLD,
    ZYDIS_MNEMONIC_PMULLW,
    ZYDIS_MNEMONIC_PMULUDQ,
    ZYDIS_MNEMONIC_POP,
    ZYDIS_MNEMONIC_POPA,
    ZYDIS_MNEMONIC_POPAD,
    ZYDIS_MNEMONIC_POPCNT,
    ZYDIS_MNEMONIC_POPF,
    ZYDIS_MNEMONIC_POPFD,
    ZYDIS_MNEMONIC_POPFQ,
    ZYDIS_MNEMONIC_POR,
    ZYDIS_MNEMONIC_PREFETCH,
    ZYDIS_MNEMONIC_PREFETCHIT0,
    ZYDIS_MNEMONIC_PREFETCHIT1,
    ZYDIS_MNEMONIC_PREFETCHNTA,
    ZYDIS_MNEMONIC_PREFETCHT0,
    ZYDIS_MNEMONIC_PREFETCHT1,
    ZYDIS_MNEMONIC_PREFETCHT2,
    ZYDIS_MNEMONIC_PREFETCHW,
    ZYDIS_MNEMONIC_PREFETCHWT1,
    ZYDIS_MNEMONIC_PSADBW,
    ZYDIS_MNEMONIC_PSHUFB,
    ZYDIS_MNEMONIC_PSHUFD,
    ZYDIS_MNEMONIC_PSHUFHW,
    ZYDIS_MNEMONIC_PSHUFLW,
    ZYDIS_MNEMONIC_PSHUFW,
    ZYDIS_MNEMONIC_PSIGNB,
    ZYDIS_MNEMONIC_PSIGND,
    ZYDIS_MNEMONIC_PSIGNW,
    ZYDIS_MNEMONIC_PSLLD,
    ZYDIS_MNEMONIC_PSLLDQ,
    ZYDIS_MNEMONIC_PSLLQ,
    ZYDIS_MNEMONIC_PSLLW,
    ZYDIS_MNEMONIC_PSMASH,
    ZYDIS_MNEMONIC_PSRAD,
    ZYDIS_MNEMONIC_PSRAW,
    ZYDIS_MNEMONIC_PSRLD,
    ZYDIS_MNEMONIC_PSRLDQ,
    ZYDIS_MNEMONIC_PSRLQ,
    ZYDIS_MNEMONIC_PSRLW,
    ZYDIS_MNEMONIC_PSUBB,
    ZYDIS_MNEMONIC_PSUBD,
    ZYDIS_MNEMONIC_PSUBQ,
    ZYDIS_MNEMONIC_PSUBSB,
    ZYDIS_MNEMONIC_PSUBSW,
    ZYDIS_MNEMONIC_PSUBUSB,
    ZYDIS_MNEMONIC_PSUBUSW,
    ZYDIS_MNEMONIC_PSUBW,
    ZYDIS_MNEMONIC_PSWAPD,
    ZYDIS_MNEMONIC_PTEST,
    ZYDIS_MNEMONIC_PTWRITE,
    ZYDIS_MNEMONIC_PUNPCKHBW,
    ZYDIS_MNEMONIC_PUNPCKHDQ,
    ZYDIS_MNEMONIC_PUNPCKHQDQ,
    ZYDIS_MNEMONIC_PUNPCKHWD,
    ZYDIS_MNEMONIC_PUNPCKLBW,
    ZYDIS_MNEMONIC_PUNPCKLDQ,
    ZYDIS_MNEMONIC_PUNPCKLQDQ,
    ZYDIS_MNEMONIC_PUNPCKLWD,
    ZYDIS_MNEMONIC_PUSH,
    ZYDIS_MNEMONIC_PUSHA,
    ZYDIS_MNEMONIC_PUSHAD,
    ZYDIS_MNEMONIC_PUSHF,
    ZYDIS_MNEMONIC_PUSHFD,
    ZYDIS_MNEMONIC_PUSHFQ,
    ZYDIS_MNEMONIC_PVALIDATE,
    ZYDIS_MNEMONIC_PXOR,
    ZYDIS_MNEMONIC_RCL,
    ZYDIS_MNEMONIC_RCPPS,
    ZYDIS_MNEMONIC_RCPSS,
    ZYDIS_MNEMONIC_RCR,
    ZYDIS_MNEMONIC_RDFSBASE,
    ZYDIS_MNEMONIC_RDGSBASE,
    ZYDIS_MNEMONIC_RDMSR,
    ZYDIS_MNEMONIC_RDMSRLIST,
    ZYDIS_MNEMONIC_RDPID,
    ZYDIS_MNEMONIC_RDPKRU,
    ZYDIS_MNEMONIC_RDPMC,
    ZYDIS_MNEMONIC_RDPRU,
    ZYDIS_MNEMONIC_RDRAND,
    ZYDIS_MNEMONIC_RDSEED,
    ZYDIS_MNEMONIC_RDSSPD,
    ZYDIS_MNEMONIC_RDSSPQ,
    ZYDIS_MNEMONIC_RDTSC,
    ZYDIS_MNEMONIC_RDTSCP,
    ZYDIS_MNEMONIC_RET,
    ZYDIS_MNEMONIC_RMPADJUST,
    ZYDIS_MNEMONIC_RMPUPDATE,
    ZYDIS_MNEMONIC_ROL,
    ZYDIS_MNEMONIC_ROR,
    ZYDIS_MNEMONIC_RORX,
    ZYDIS_MNEMONIC_ROUNDPD,
    ZYDIS_MNEMONIC_ROUNDPS,
    ZYDIS_MNEMONIC_ROUNDSD,
    ZYDIS_MNEMONIC_ROUNDSS,
    ZYDIS_MNEMONIC_RSM,
    ZYDIS_MNEMONIC_RSQRTPS,
    ZYDIS_MNEMONIC_RSQRTSS,
    ZYDIS_MNEMONIC_RSTORSSP,
    ZYDIS_MNEMONIC_SAHF,
    ZYDIS_MNEMONIC_SALC,
    ZYDIS_MNEMONIC_SAR,
    ZYDIS_MNEMONIC_SARX,
    ZYDIS_MNEMONIC_SAVEPREVSSP,
    ZYDIS_MNEMONIC_SBB,
    ZYDIS_MNEMONIC_SCASB,
    ZYDIS_MNEMONIC_SCASD,
    ZYDIS_MNEMONIC_SCASQ,
    ZYDIS_MNEMONIC_SCASW,
    ZYDIS_MNEMONIC_SEAMCALL,
    ZYDIS_MNEMONIC_SEAMOPS,
    ZYDIS_MNEMONIC_SEAMRET,
    ZYDIS_MNEMONIC_SENDUIPI,
    ZYDIS_MNEMONIC_SERIALIZE,
    ZYDIS_MNEMONIC_SETB,
    ZYDIS_MNEMONIC_SETBE,
    ZYDIS_MNEMONIC_SETL,
    ZYDIS_MNEMONIC_SETLE,
    ZYDIS_MNEMONIC_SETNB,
    ZYDIS_MNEMONIC_SETNBE,
    ZYDIS_MNEMONIC_SETNL,
    ZYDIS_MNEMONIC_SETNLE,
    ZYDIS_MNEMONIC_SETNO,
    ZYDIS_MNEMONIC_SETNP,
    ZYDIS_MNEMONIC_SETNS,
    ZYDIS_MNEMONIC_SETNZ,
    ZYDIS_MNEMONIC_SETO,
    ZYDIS_MNEMONIC_SETP,
    ZYDIS_MNEMONIC_SETS,
    ZYDIS_MNEMONIC_SETSSBSY,
    ZYDIS_MNEMONIC_SETZ,
    ZYDIS_MNEMONIC_SFENCE,
    ZYDIS_MNEMONIC_SGDT,
    ZYDIS_MNEMONIC_SHA1MSG1,
    ZYDIS_MNEMONIC_SHA1MSG2,
    ZYDIS_MNEMONIC_SHA1NEXTE,
    ZYDIS_MNEMONIC_SHA1RNDS4,
    ZYDIS_MNEMONIC_SHA256MSG1,
    ZYDIS_MNEMONIC_SHA256MSG2,
    ZYDIS_MNEMONIC_SHA256RNDS2,
    ZYDIS_MNEMONIC_SHL,
    ZYDIS_MNEMONIC_SHLD,
    ZYDIS_MNEMONIC_SHLX,
    ZYDIS_MNEMONIC_SHR,
    ZYDIS_MNEMONIC_SHRD,
    ZYDIS_MNEMONIC_SHRX,
    ZYDIS_MNEMONIC_SHUFPD,
    ZYDIS_MNEMONIC_SHUFPS,
    ZYDIS_MNEMONIC_SIDT,
    ZYDIS_MNEMONIC_SKINIT,
    ZYDIS_MNEMONIC_SLDT,
    ZYDIS_MNEMONIC_SLWPCB,
    ZYDIS_MNEMONIC_SMSW,
    ZYDIS_MNEMONIC_SPFLT,
    ZYDIS_MNEMONIC_SQRTPD,
    ZYDIS_MNEMONIC_SQRTPS,
    ZYDIS_MNEMONIC_SQRTSD,
    ZYDIS_MNEMONIC_SQRTSS,
    ZYDIS_MNEMONIC_STAC,
    ZYDIS_MNEMONIC_STC,
    ZYDIS_MNEMONIC_STD,
    ZYDIS_MNEMONIC_STGI,
    ZYDIS_MNEMONIC_STI,
    ZYDIS_MNEMONIC_STMXCSR,
    ZYDIS_MNEMONIC_STOSB,
    ZYDIS_MNEMONIC_STOSD,
    ZYDIS_MNEMONIC_STOSQ,
    ZYDIS_MNEMONIC_STOSW,
    ZYDIS_MNEMONIC_STR,
    ZYDIS_MNEMONIC_STTILECFG,
    ZYDIS_MNEMONIC_STUI,
    ZYDIS_MNEMONIC_SUB,
    ZYDIS_MNEMONIC_SUBPD,
    ZYDIS_MNEMONIC_SUBPS,
    ZYDIS_MNEMONIC_SUBSD,
    ZYDIS_MNEMONIC_SUBSS,
    ZYDIS_MNEMONIC_SWAPGS,
    ZYDIS_MNEMONIC_SYSCALL,
    ZYDIS_MNEMONIC_SYSENTER,
    ZYDIS_MNEMONIC_SYSEXIT,
    ZYDIS_MNEMONIC_SYSRET,
    ZYDIS_MNEMONIC_T1MSKC,
    ZYDIS_MNEMONIC_TDCALL,
    ZYDIS_MNEMONIC_TDPBF16PS,
    ZYDIS_MNEMONIC_TDPBSSD,
    ZYDIS_MNEMONIC_TDPBSUD,
    ZYDIS_MNEMONIC_TDPBUSD,
    ZYDIS_MNEMONIC_TDPBUUD,
    ZYDIS_MNEMONIC_TDPFP16PS,
    ZYDIS_MNEMONIC_TEST,
    ZYDIS_MNEMONIC_TESTUI,
    ZYDIS_MNEMONIC_TILELOADD,
    ZYDIS_MNEMONIC_TILELOADDT1,
    ZYDIS_MNEMONIC_TILERELEASE,
    ZYDIS_MNEMONIC_TILESTORED,
    ZYDIS_MNEMONIC_TILEZERO,
    ZYDIS_MNEMONIC_TLBSYNC,
    ZYDIS_MNEMONIC_TPAUSE,
    ZYDIS_MNEMONIC_TZCNT,
    ZYDIS_MNEMONIC_TZCNTI,
    ZYDIS_MNEMONIC_TZMSK,
    ZYDIS_MNEMONIC_UCOMISD,
    ZYDIS_MNEMONIC_UCOMISS,
    ZYDIS_MNEMONIC_UD0,
    ZYDIS_MNEMONIC_UD1,
    ZYDIS_MNEMONIC_UD2,
    ZYDIS_MNEMONIC_UIRET,
    ZYDIS_MNEMONIC_UMONITOR,
    ZYDIS_MNEMONIC_UMWAIT,
    ZYDIS_MNEMONIC_UNPCKHPD,
    ZYDIS_MNEMONIC_UNPCKHPS,
    ZYDIS_MNEMONIC_UNPCKLPD,
    ZYDIS_MNEMONIC_UNPCKLPS,
    ZYDIS_MNEMONIC_V4FMADDPS,
    ZYDIS_MNEMONIC_V4FMADDSS,
    ZYDIS_MNEMONIC_V4FNMADDPS,
    ZYDIS_MNEMONIC_V4FNMADDSS,
    ZYDIS_MNEMONIC_VADDNPD,
    ZYDIS_MNEMONIC_VADDNPS,
    ZYDIS_MNEMONIC_VADDPD,
    ZYDIS_MNEMONIC_VADDPH,
    ZYDIS_MNEMONIC_VADDPS,
    ZYDIS_MNEMONIC_VADDSD,
    ZYDIS_MNEMONIC_VADDSETSPS,
    ZYDIS_MNEMONIC_VADDSH,
    ZYDIS_MNEMONIC_VADDSS,
    ZYDIS_MNEMONIC_VADDSUBPD,
    ZYDIS_MNEMONIC_VADDSUBPS,
    ZYDIS_MNEMONIC_VAESDEC,
    ZYDIS_MNEMONIC_VAESDECLAST,
    ZYDIS_MNEMONIC_VAESENC,
    ZYDIS_MNEMONIC_VAESENCLAST,
    ZYDIS_MNEMONIC_VAESIMC,
    ZYDIS_MNEMONIC_VAESKEYGENASSIST,
    ZYDIS_MNEMONIC_VALIGND,
    ZYDIS_MNEMONIC_VALIGNQ,
    ZYDIS_MNEMONIC_VANDNPD,
    ZYDIS_MNEMONIC_VANDNPS,
    ZYDIS_MNEMONIC_VANDPD,
    ZYDIS_MNEMONIC_VANDPS,
    ZYDIS_MNEMONIC_VBCSTNEBF162PS,
    ZYDIS_MNEMONIC_VBCSTNESH2PS,
    ZYDIS_MNEMONIC_VBLENDMPD,
    ZYDIS_MNEMONIC_VBLENDMPS,
    ZYDIS_MNEMONIC_VBLENDPD,
    ZYDIS_MNEMONIC_VBLENDPS,
    ZYDIS_MNEMONIC_VBLENDVPD,
    ZYDIS_MNEMONIC_VBLENDVPS,
    ZYDIS_MNEMONIC_VBROADCASTF128,
    ZYDIS_MNEMONIC_VBROADCASTF32X2,
    ZYDIS_MNEMONIC_VBROADCASTF32X4,
    ZYDIS_MNEMONIC_VBROADCASTF32X8,
    ZYDIS_MNEMONIC_VBROADCASTF64X2,
    ZYDIS_MNEMONIC_VBROADCASTF64X4,
    ZYDIS_MNEMONIC_VBROADCASTI128,
    ZYDIS_MNEMONIC_VBROADCASTI32X2,
    ZYDIS_MNEMONIC_VBROADCASTI32X4,
    ZYDIS_MNEMONIC_VBROADCASTI32X8,
    ZYDIS_MNEMONIC_VBROADCASTI64X2,
    ZYDIS_MNEMONIC_VBROADCASTI64X4,
    ZYDIS_MNEMONIC_VBROADCASTSD,
    ZYDIS_MNEMONIC_VBROADCASTSS,
    ZYDIS_MNEMONIC_VCMPPD,
    ZYDIS_MNEMONIC_VCMPPH,
    ZYDIS_MNEMONIC_VCMPPS,
    ZYDIS_MNEMONIC_VCMPSD,
    ZYDIS_MNEMONIC_VCMPSH,
    ZYDIS_MNEMONIC_VCMPSS,
    ZYDIS_MNEMONIC_VCOMISD,
    ZYDIS_MNEMONIC_VCOMISH,
    ZYDIS_MNEMONIC_VCOMISS,
    ZYDIS_MNEMONIC_VCOMPRESSPD,
    ZYDIS_MNEMONIC_VCOMPRESSPS,
    ZYDIS_MNEMONIC_VCVTDQ2PD,
    ZYDIS_MNEMONIC_VCVTDQ2PH,
    ZYDIS_MNEMONIC_VCVTDQ2PS,
    ZYDIS_MNEMONIC_VCVTFXPNTDQ2PS,
    ZYDIS_MNEMONIC_VCVTFXPNTPD2DQ,
    ZYDIS_MNEMONIC_VCVTFXPNTPD2UDQ,
    ZYDIS_MNEMONIC_VCVTFXPNTPS2DQ,
    ZYDIS_MNEMONIC_VCVTFXPNTPS2UDQ,
    ZYDIS_MNEMONIC_VCVTFXPNTUDQ2PS,
    ZYDIS_MNEMONIC_VCVTNE2PS2BF16,
    ZYDIS_MNEMONIC_VCVTNEEBF162PS,
    ZYDIS_MNEMONIC_VCVTNEEPH2PS,
    ZYDIS_MNEMONIC_VCVTNEOBF162PS,
    ZYDIS_MNEMONIC_VCVTNEOPH2PS,
    ZYDIS_MNEMONIC_VCVTNEPS2BF16,
    ZYDIS_MNEMONIC_VCVTPD2DQ,
    ZYDIS_MNEMONIC_VCVTPD2PH,
    ZYDIS_MNEMONIC_VCVTPD2PS,
    ZYDIS_MNEMONIC_VCVTPD2QQ,
    ZYDIS_MNEMONIC_VCVTPD2UDQ,
    ZYDIS_MNEMONIC_VCVTPD2UQQ,
    ZYDIS_MNEMONIC_VCVTPH2DQ,
    ZYDIS_MNEMONIC_VCVTPH2PD,
    ZYDIS_MNEMONIC_VCVTPH2PS,
    ZYDIS_MNEMONIC_VCVTPH2PSX,
    ZYDIS_MNEMONIC_VCVTPH2QQ,
    ZYDIS_MNEMONIC_VCVTPH2UDQ,
    ZYDIS_MNEMONIC_VCVTPH2UQQ,
    ZYDIS_MNEMONIC_VCVTPH2UW,
    ZYDIS_MNEMONIC_VCVTPH2W,
    ZYDIS_MNEMONIC_VCVTPS2DQ,
    ZYDIS_MNEMONIC_VCVTPS2PD,
    ZYDIS_MNEMONIC_VCVTPS2PH,
    ZYDIS_MNEMONIC_VCVTPS2PHX,
    ZYDIS_MNEMONIC_VCVTPS2QQ,
    ZYDIS_MNEMONIC_VCVTPS2UDQ,
    ZYDIS_MNEMONIC_VCVTPS2UQQ,
    ZYDIS_MNEMONIC_VCVTQQ2PD,
    ZYDIS_MNEMONIC_VCVTQQ2PH,
    ZYDIS_MNEMONIC_VCVTQQ2PS,
    ZYDIS_MNEMONIC_VCVTSD2SH,
    ZYDIS_MNEMONIC_VCVTSD2SI,
    ZYDIS_MNEMONIC_VCVTSD2SS,
    ZYDIS_MNEMONIC_VCVTSD2USI,
    ZYDIS_MNEMONIC_VCVTSH2SD,
    ZYDIS_MNEMONIC_VCVTSH2SI,
    ZYDIS_MNEMONIC_VCVTSH2SS,
    ZYDIS_MNEMONIC_VCVTSH2USI,
    ZYDIS_MNEMONIC_VCVTSI2SD,
    ZYDIS_MNEMONIC_VCVTSI2SH,
    ZYDIS_MNEMONIC_VCVTSI2SS,
    ZYDIS_MNEMONIC_VCVTSS2SD,
    ZYDIS_MNEMONIC_VCVTSS2SH,
    ZYDIS_MNEMONIC_VCVTSS2SI,
    ZYDIS_MNEMONIC_VCVTSS2USI,
    ZYDIS_MNEMONIC_VCVTTPD2DQ,
    ZYDIS_MNEMONIC_VCVTTPD2QQ,
    ZYDIS_MNEMONIC_VCVTTPD2UDQ,
    ZYDIS_MNEMONIC_VCVTTPD2UQQ,
    ZYDIS_MNEMONIC_VCVTTPH2DQ,
    ZYDIS_MNEMONIC_VCVTTPH2QQ,
    ZYDIS_MNEMONIC_VCVTTPH2UDQ,
    ZYDIS_MNEMONIC_VCVTTPH2UQQ,
    ZYDIS_MNEMONIC_VCVTTPH2UW,
    ZYDIS_MNEMONIC_VCVTTPH2W,
    ZYDIS_MNEMONIC_VCVTTPS2DQ,
    ZYDIS_MNEMONIC_VCVTTPS2QQ,
    ZYDIS_MNEMONIC_VCVTTPS2UDQ,
    ZYDIS_MNEMONIC_VCVTTPS2UQQ,
    ZYDIS_MNEMONIC_VCVTTSD2SI,
    ZYDIS_MNEMONIC_VCVTTSD2USI,
    ZYDIS_MNEMONIC_VCVTTSH2SI,
    ZYDIS_MNEMONIC_VCVTTSH2USI,
    ZYDIS_MNEMONIC_VCVTTSS2SI,
    ZYDIS_MNEMONIC_VCVTTSS2USI,
    ZYDIS_MNEMONIC_VCVTUDQ2PD,
    ZYDIS_MNEMONIC_VCVTUDQ2PH,
    ZYDIS_MNEMONIC_VCVTUDQ2PS,
    ZYDIS_MNEMONIC_VCVTUQQ2PD,
    ZYDIS_MNEMONIC_VCVTUQQ2PH,
    ZYDIS_MNEMONIC_VCVTUQQ2PS,
    ZYDIS_MNEMONIC_VCVTUSI2SD,
    ZYDIS_MNEMONIC_VCVTUSI2SH,
    ZYDIS_MNEMONIC_VCVTUSI2SS,
    ZYDIS_MNEMONIC_VCVTUW2PH,
    ZYDIS_MNEMONIC_VCVTW2PH,
    ZYDIS_MNEMONIC_VDBPSADBW,
    ZYDIS_MNEMONIC_VDIVPD,
    ZYDIS_MNEMONIC_VDIVPH,
    ZYDIS_MNEMONIC_VDIVPS,
    ZYDIS_MNEMONIC_VDIVSD,
    ZYDIS_MNEMONIC_VDIVSH,
    ZYDIS_MNEMONIC_VDIVSS,
    ZYDIS_MNEMONIC_VDPBF16PS,
    ZYDIS_MNEMONIC_VDPPD,
    ZYDIS_MNEMONIC_VDPPS,
    ZYDIS_MNEMONIC_VERR,
    ZYDIS_MNEMONIC_VERW,
    ZYDIS_MNEMONIC_VEXP223PS,
    ZYDIS_MNEMONIC_VEXP2PD,
    ZYDIS_MNEMONIC_VEXP2PS,
    ZYDIS_MNEMONIC_VEXPANDPD,
    ZYDIS_MNEMONIC_VEXPANDPS,
    ZYDIS_MNEMONIC_VEXTRACTF128,
    ZYDIS_MNEMONIC_VEXTRACTF32X4,
    ZYDIS_MNEMONIC_VEXTRACTF32X8,
    ZYDIS_MNEMONIC_VEXTRACTF64X2,
    ZYDIS_MNEMONIC_VEXTRACTF64X4,
    ZYDIS_MNEMONIC_VEXTRACTI128,
    ZYDIS_MNEMONIC_VEXTRACTI32X4,
    ZYDIS_MNEMONIC_VEXTRACTI32X8,
    ZYDIS_MNEMONIC_VEXTRACTI64X2,
    ZYDIS_MNEMONIC_VEXTRACTI64X4,
    ZYDIS_MNEMONIC_VEXTRACTPS,
    ZYDIS_MNEMONIC_VFCMADDCPH,
    ZYDIS_MNEMONIC_VFCMADDCSH,
    ZYDIS_MNEMONIC_VFCMULCPH,
    ZYDIS_MNEMONIC_VFCMULCSH,
    ZYDIS_MNEMONIC_VFIXUPIMMPD,
    ZYDIS_MNEMONIC_VFIXUPIMMPS,
    ZYDIS_MNEMONIC_VFIXUPIMMSD,
    ZYDIS_MNEMONIC_VFIXUPIMMSS,
    ZYDIS_MNEMONIC_VFIXUPNANPD,
    ZYDIS_MNEMONIC_VFIXUPNANPS,
    ZYDIS_MNEMONIC_VFMADD132PD,
    ZYDIS_MNEMONIC_VFMADD132PH,
    ZYDIS_MNEMONIC_VFMADD132PS,
    ZYDIS_MNEMONIC_VFMADD132SD,
    ZYDIS_MNEMONIC_VFMADD132SH,
    ZYDIS_MNEMONIC_VFMADD132SS,
    ZYDIS_MNEMONIC_VFMADD213PD,
    ZYDIS_MNEMONIC_VFMADD213PH,
    ZYDIS_MNEMONIC_VFMADD213PS,
    ZYDIS_MNEMONIC_VFMADD213SD,
    ZYDIS_MNEMONIC_VFMADD213SH,
    ZYDIS_MNEMONIC_VFMADD213SS,
    ZYDIS_MNEMONIC_VFMADD231PD,
    ZYDIS_MNEMONIC_VFMADD231PH,
    ZYDIS_MNEMONIC_VFMADD231PS,
    ZYDIS_MNEMONIC_VFMADD231SD,
    ZYDIS_MNEMONIC_VFMADD231SH,
    ZYDIS_MNEMONIC_VFMADD231SS,
    ZYDIS_MNEMONIC_VFMADD233PS,
    ZYDIS_MNEMONIC_VFMADDCPH,
    ZYDIS_MNEMONIC_VFMADDCSH,
    ZYDIS_MNEMONIC_VFMADDPD,
    ZYDIS_MNEMONIC_VFMADDPS,
    ZYDIS_MNEMONIC_VFMADDSD,
    ZYDIS_MNEMONIC_VFMADDSS,
    ZYDIS_MNEMONIC_VFMADDSUB132PD,
    ZYDIS_MNEMONIC_VFMADDSUB132PH,
    ZYDIS_MNEMONIC_VFMADDSUB132PS,
    ZYDIS_MNEMONIC_VFMADDSUB213PD,
    ZYDIS_MNEMONIC_VFMADDSUB213PH,
    ZYDIS_MNEMONIC_VFMADDSUB213PS,
    ZYDIS_MNEMONIC_VFMADDSUB231PD,
    ZYDIS_MNEMONIC_VFMADDSUB231PH,
    ZYDIS_MNEMONIC_VFMADDSUB231PS,
    ZYDIS_MNEMONIC_VFMADDSUBPD,
    ZYDIS_MNEMONIC_VFMADDSUBPS,
    ZYDIS_MNEMONIC_VFMSUB132PD,
    ZYDIS_MNEMONIC_VFMSUB132PH,
    ZYDIS_MNEMONIC_VFMSUB132PS,
    ZYDIS_MNEMONIC_VFMSUB132SD,
    ZYDIS_MNEMONIC_VFMSUB132SH,
    ZYDIS_MNEMONIC_VFMSUB132SS,
    ZYDIS_MNEMONIC_VFMSUB213PD,
    ZYDIS_MNEMONIC_VFMSUB213PH,
    ZYDIS_MNEMONIC_VFMSUB213PS,
    ZYDIS_MNEMONIC_VFMSUB213SD,
    ZYDIS_MNEMONIC_VFMSUB213SH,
    ZYDIS_MNEMONIC_VFMSUB213SS,
    ZYDIS_MNEMONIC_VFMSUB231PD,
    ZYDIS_MNEMONIC_VFMSUB231PH,
    ZYDIS_MNEMONIC_VFMSUB231PS,
    ZYDIS_MNEMONIC_VFMSUB231SD,
    ZYDIS_MNEMONIC_VFMSUB231SH,
    ZYDIS_MNEMONIC_VFMSUB231SS,
    ZYDIS_MNEMONIC_VFMSUBADD132PD,
    ZYDIS_MNEMONIC_VFMSUBADD132PH,
    ZYDIS_MNEMONIC_VFMSUBADD132PS,
    ZYDIS_MNEMONIC_VFMSUBADD213PD,
    ZYDIS_MNEMONIC_VFMSUBADD213PH,
    ZYDIS_MNEMONIC_VFMSUBADD213PS,
    ZYDIS_MNEMONIC_VFMSUBADD231PD,
    ZYDIS_MNEMONIC_VFMSUBADD231PH,
    ZYDIS_MNEMONIC_VFMSUBADD231PS,
    ZYDIS_MNEMONIC_VFMSUBADDPD,
    ZYDIS_MNEMONIC_VFMSUBADDPS,
    ZYDIS_MNEMONIC_VFMSUBPD,
    ZYDIS_MNEMONIC_VFMSUBPS,
    ZYDIS_MNEMONIC_VFMSUBSD,
    ZYDIS_MNEMONIC_VFMSUBSS,
    ZYDIS_MNEMONIC_VFMULCPH,
    ZYDIS_MNEMONIC_VFMULCSH,
    ZYDIS_MNEMONIC_VFNMADD132PD,
    ZYDIS_MNEMONIC_VFNMADD132PH,
    ZYDIS_MNEMONIC_VFNMADD132PS,
    ZYDIS_MNEMONIC_VFNMADD132SD,
    ZYDIS_MNEMONIC_VFNMADD132SH,
    ZYDIS_MNEMONIC_VFNMADD132SS,
    ZYDIS_MNEMONIC_VFNMADD213PD,
    ZYDIS_MNEMONIC_VFNMADD213PH,
    ZYDIS_MNEMONIC_VFNMADD213PS,
    ZYDIS_MNEMONIC_VFNMADD213SD,
    ZYDIS_MNEMONIC_VFNMADD213SH,
    ZYDIS_MNEMONIC_VFNMADD213SS,
    ZYDIS_MNEMONIC_VFNMADD231PD,
    ZYDIS_MNEMONIC_VFNMADD231PH,
    ZYDIS_MNEMONIC_VFNMADD231PS,
    ZYDIS_MNEMONIC_VFNMADD231SD,
    ZYDIS_MNEMONIC_VFNMADD231SH,
    ZYDIS_MNEMONIC_VFNMADD231SS,
    ZYDIS_MNEMONIC_VFNMADDPD,
    ZYDIS_MNEMONIC_VFNMADDPS,
    ZYDIS_MNEMONIC_VFNMADDSD,
    ZYDIS_MNEMONIC_VFNMADDSS,
    ZYDIS_MNEMONIC_VFNMSUB132PD,
    ZYDIS_MNEMONIC_VFNMSUB132PH,
    ZYDIS_MNEMONIC_VFNMSUB132PS,
    ZYDIS_MNEMONIC_VFNMSUB132SD,
    ZYDIS_MNEMONIC_VFNMSUB132SH,
    ZYDIS_MNEMONIC_VFNMSUB132SS,
    ZYDIS_MNEMONIC_VFNMSUB213PD,
    ZYDIS_MNEMONIC_VFNMSUB213PH,
    ZYDIS_MNEMONIC_VFNMSUB213PS,
    ZYDIS_MNEMONIC_VFNMSUB213SD,
    ZYDIS_MNEMONIC_VFNMSUB213SH,
    ZYDIS_MNEMONIC_VFNMSUB213SS,
    ZYDIS_MNEMONIC_VFNMSUB231PD,
    ZYDIS_MNEMONIC_VFNMSUB231PH,
    ZYDIS_MNEMONIC_VFNMSUB231PS,
    ZYDIS_MNEMONIC_VFNMSUB231SD,
    ZYDIS_MNEMONIC_VFNMSUB231SH,
    ZYDIS_MNEMONIC_VFNMSUB231SS,
    ZYDIS_MNEMONIC_VFNMSUBPD,
    ZYDIS_MNEMONIC_VFNMSUBPS,
    ZYDIS_MNEMONIC_VFNMSUBSD,
    ZYDIS_MNEMONIC_VFNMSUBSS,
    ZYDIS_MNEMONIC_VFPCLASSPD,
    ZYDIS_MNEMONIC_VFPCLASSPH,
    ZYDIS_MNEMONIC_VFPCLASSPS,
    ZYDIS_MNEMONIC_VFPCLASSSD,
    ZYDIS_MNEMONIC_VFPCLASSSH,
    ZYDIS_MNEMONIC_VFPCLASSSS,
    ZYDIS_MNEMONIC_VFRCZPD,
    ZYDIS_MNEMONIC_VFRCZPS,
    ZYDIS_MNEMONIC_VFRCZSD,
    ZYDIS_MNEMONIC_VFRCZSS,
    ZYDIS_MNEMONIC_VGATHERDPD,
    ZYDIS_MNEMONIC_VGATHERDPS,
    ZYDIS_MNEMONIC_VGATHERPF0DPD,
    ZYDIS_MNEMONIC_VGATHERPF0DPS,
    ZYDIS_MNEMONIC_VGATHERPF0HINTDPD,
    ZYDIS_MNEMONIC_VGATHERPF0HINTDPS,
    ZYDIS_MNEMONIC_VGATHERPF0QPD,
    ZYDIS_MNEMONIC_VGATHERPF0QPS,
    ZYDIS_MNEMONIC_VGATHERPF1DPD,
    ZYDIS_MNEMONIC_VGATHERPF1DPS,
    ZYDIS_MNEMONIC_VGATHERPF1QPD,
    ZYDIS_MNEMONIC_VGATHERPF1QPS,
    ZYDIS_MNEMONIC_VGATHERQPD,
    ZYDIS_MNEMONIC_VGATHERQPS,
    ZYDIS_MNEMONIC_VGETEXPPD,
    ZYDIS_MNEMONIC_VGETEXPPH,
    ZYDIS_MNEMONIC_VGETEXPPS,
    ZYDIS_MNEMONIC_VGETEXPSD,
    ZYDIS_MNEMONIC_VGETEXPSH,
    ZYDIS_MNEMONIC_VGETEXPSS,
    ZYDIS_MNEMONIC_VGETMANTPD,
    ZYDIS_MNEMONIC_VGETMANTPH,
    ZYDIS_MNEMONIC_VGETMANTPS,
    ZYDIS_MNEMONIC_VGETMANTSD,
    ZYDIS_MNEMONIC_VGETMANTSH,
    ZYDIS_MNEMONIC_VGETMANTSS,
    ZYDIS_MNEMONIC_VGF2P8AFFINEINVQB,
    ZYDIS_MNEMONIC_VGF2P8AFFINEQB,
    ZYDIS_MNEMONIC_VGF2P8MULB,
    ZYDIS_MNEMONIC_VGMAXABSPS,
    ZYDIS_MNEMONIC_VGMAXPD,
    ZYDIS_MNEMONIC_VGMAXPS,
    ZYDIS_MNEMONIC_VGMINPD,
    ZYDIS_MNEMONIC_VGMINPS,
    ZYDIS_MNEMONIC_VHADDPD,
    ZYDIS_MNEMONIC_VHADDPS,
    ZYDIS_MNEMONIC_VHSUBPD,
    ZYDIS_MNEMONIC_VHSUBPS,
    ZYDIS_MNEMONIC_VINSERTF128,
    ZYDIS_MNEMONIC_VINSERTF32X4,
    ZYDIS_MNEMONIC_VINSERTF32X8,
    ZYDIS_MNEMONIC_VINSERTF64X2,
    ZYDIS_MNEMONIC_VINSERTF64X4,
    ZYDIS_MNEMONIC_VINSERTI128,
    ZYDIS_MNEMONIC_VINSERTI32X4,
    ZYDIS_MNEMONIC_VINSERTI32X8,
    ZYDIS_MNEMONIC_VINSERTI64X2,
    ZYDIS_MNEMONIC_VINSERTI64X4,
    ZYDIS_MNEMONIC_VINSERTPS,
    ZYDIS_MNEMONIC_VLDDQU,
    ZYDIS_MNEMONIC_VLDMXCSR,
    ZYDIS_MNEMONIC_VLOADUNPACKHD,
    ZYDIS_MNEMONIC_VLOADUNPACKHPD,
    ZYDIS_MNEMONIC_VLOADUNPACKHPS,
    ZYDIS_MNEMONIC_VLOADUNPACKHQ,
    ZYDIS_MNEMONIC_VLOADUNPACKLD,
    ZYDIS_MNEMONIC_VLOADUNPACKLPD,
    ZYDIS_MNEMONIC_VLOADUNPACKLPS,
    ZYDIS_MNEMONIC_VLOADUNPACKLQ,
    ZYDIS_MNEMONIC_VLOG2PS,
    ZYDIS_MNEMONIC_VMASKMOVDQU,
    ZYDIS_MNEMONIC_VMASKMOVPD,
    ZYDIS_MNEMONIC_VMASKMOVPS,
    ZYDIS_MNEMONIC_VMAXPD,
    ZYDIS_MNEMONIC_VMAXPH,
    ZYDIS_MNEMONIC_VMAXPS,
    ZYDIS_MNEMONIC_VMAXSD,
    ZYDIS_MNEMONIC_VMAXSH,
    ZYDIS_MNEMONIC_VMAXSS,
    ZYDIS_MNEMONIC_VMCALL,
    ZYDIS_MNEMONIC_VMCLEAR,
    ZYDIS_MNEMONIC_VMFUNC,
    ZYDIS_MNEMONIC_VMINPD,
    ZYDIS_MNEMONIC_VMINPH,
    ZYDIS_MNEMONIC_VMINPS,
    ZYDIS_MNEMONIC_VMINSD,
    ZYDIS_MNEMONIC_VMINSH,
    ZYDIS_MNEMONIC_VMINSS,
    ZYDIS_MNEMONIC_VMLAUNCH,
    ZYDIS_MNEMONIC_VMLOAD,
    ZYDIS_MNEMONIC_VMMCALL,
    ZYDIS_MNEMONIC_VMOVAPD,
    ZYDIS_MNEMONIC_VMOVAPS,
    ZYDIS_MNEMONIC_VMOVD,
    ZYDIS_MNEMONIC_VMOVDDUP,
    ZYDIS_MNEMONIC_VMOVDQA,
    ZYDIS_MNEMONIC_VMOVDQA32,
    ZYDIS_MNEMONIC_VMOVDQA64,
    ZYDIS_MNEMONIC_VMOVDQU,
    ZYDIS_MNEMONIC_VMOVDQU16,
    ZYDIS_MNEMONIC_VMOVDQU32,
    ZYDIS_MNEMONIC_VMOVDQU64,
    ZYDIS_MNEMONIC_VMOVDQU8,
    ZYDIS_MNEMONIC_VMOVHLPS,
    ZYDIS_MNEMONIC_VMOVHPD,
    ZYDIS_MNEMONIC_VMOVHPS,
    ZYDIS_MNEMONIC_VMOVLHPS,
    ZYDIS_MNEMONIC_VMOVLPD,
    ZYDIS_MNEMONIC_VMOVLPS,
    ZYDIS_MNEMONIC_VMOVMSKPD,
    ZYDIS_MNEMONIC_VMOVMSKPS,
    ZYDIS_MNEMONIC_VMOVNRAPD,
    ZYDIS_MNEMONIC_VMOVNRAPS,
    ZYDIS_MNEMONIC_VMOVNRNGOAPD,
    ZYDIS_MNEMONIC_VMOVNRNGOAPS,
    ZYDIS_MNEMONIC_VMOVNTDQ,
    ZYDIS_MNEMONIC_VMOVNTDQA,
    ZYDIS_MNEMONIC_VMOVNTPD,
    ZYDIS_MNEMONIC_VMOVNTPS,
    ZYDIS_MNEMONIC_VMOVQ,
    ZYDIS_MNEMONIC_VMOVSD,
    ZYDIS_MNEMONIC_VMOVSH,
    ZYDIS_MNEMONIC_VMOVSHDUP,
    ZYDIS_MNEMONIC_VMOVSLDUP,
    ZYDIS_MNEMONIC_VMOVSS,
    ZYDIS_MNEMONIC_VMOVUPD,
    ZYDIS_MNEMONIC_VMOVUPS,
    ZYDIS_MNEMONIC_VMOVW,
    ZYDIS_MNEMONIC_VMPSADBW,
    ZYDIS_MNEMONIC_VMPTRLD,
    ZYDIS_MNEMONIC_VMPTRST,
    ZYDIS_MNEMONIC_VMREAD,
    ZYDIS_MNEMONIC_VMRESUME,
    ZYDIS_MNEMONIC_VMRUN,
    ZYDIS_MNEMONIC_VMSAVE,
    ZYDIS_MNEMONIC_VMULPD,
    ZYDIS_MNEMONIC_VMULPH,
    ZYDIS_MNEMONIC_VMULPS,
    ZYDIS_MNEMONIC_VMULSD,
    ZYDIS_MNEMONIC_VMULSH,
    ZYDIS_MNEMONIC_VMULSS,
    ZYDIS_MNEMONIC_VMWRITE,
    ZYDIS_MNEMONIC_VMXOFF,
    ZYDIS_MNEMONIC_VMXON,
    ZYDIS_MNEMONIC_VORPD,
    ZYDIS_MNEMONIC_VORPS,
    ZYDIS_MNEMONIC_VP2INTERSECTD,
    ZYDIS_MNEMONIC_VP2INTERSECTQ,
    ZYDIS_MNEMONIC_VP4DPWSSD,
    ZYDIS_MNEMONIC_VP4DPWSSDS,
    ZYDIS_MNEMONIC_VPABSB,
    ZYDIS_MNEMONIC_VPABSD,
    ZYDIS_MNEMONIC_VPABSQ,
    ZYDIS_MNEMONIC_VPABSW,
    ZYDIS_MNEMONIC_VPACKSSDW,
    ZYDIS_MNEMONIC_VPACKSSWB,
    ZYDIS_MNEMONIC_VPACKSTOREHD,
    ZYDIS_MNEMONIC_VPACKSTOREHPD,
    ZYDIS_MNEMONIC_VPACKSTOREHPS,
    ZYDIS_MNEMONIC_VPACKSTOREHQ,
    ZYDIS_MNEMONIC_VPACKSTORELD,
    ZYDIS_MNEMONIC_VPACKSTORELPD,
    ZYDIS_MNEMONIC_VPACKSTORELPS,
    ZYDIS_MNEMONIC_VPACKSTORELQ,
    ZYDIS_MNEMONIC_VPACKUSDW,
    ZYDIS_MNEMONIC_VPACKUSWB,
    ZYDIS_MNEMONIC_VPADCD,
    ZYDIS_MNEMONIC_VPADDB,
    ZYDIS_MNEMONIC_VPADDD,
    ZYDIS_MNEMONIC_VPADDQ,
    ZYDIS_MNEMONIC_VPADDSB,
    ZYDIS_MNEMONIC_VPADDSETCD,
    ZYDIS_MNEMONIC_VPADDSETSD,
    ZYDIS_MNEMONIC_VPADDSW,
    ZYDIS_MNEMONIC_VPADDUSB,
    ZYDIS_MNEMONIC_VPADDUSW,
    ZYDIS_MNEMONIC_VPADDW,
    ZYDIS_MNEMONIC_VPALIGNR,
    ZYDIS_MNEMONIC_VPAND,
    ZYDIS_MNEMONIC_VPANDD,
    ZYDIS_MNEMONIC_VPANDN,
    ZYDIS_MNEMONIC_VPANDND,
    ZYDIS_MNEMONIC_VPANDNQ,
    ZYDIS_MNEMONIC_VPANDQ,
    ZYDIS_MNEMONIC_VPAVGB,
    ZYDIS_MNEMONIC_VPAVGW,
    ZYDIS_MNEMONIC_VPBLENDD,
    ZYDIS_MNEMONIC_VPBLENDMB,
    ZYDIS_MNEMONIC_VPBLENDMD,
    ZYDIS_MNEMONIC_VPBLENDMQ,
    ZYDIS_MNEMONIC_VPBLENDMW,
    ZYDIS_MNEMONIC_VPBLENDVB,
    ZYDIS_MNEMONIC_VPBLENDW,
    ZYDIS_MNEMONIC_VPBROADCASTB,
    ZYDIS_MNEMONIC_VPBROADCASTD,
    ZYDIS_MNEMONIC_VPBROADCASTMB2Q,
    ZYDIS_MNEMONIC_VPBROADCASTMW2D,
    ZYDIS_MNEMONIC_VPBROADCASTQ,
    ZYDIS_MNEMONIC_VPBROADCASTW,
    ZYDIS_MNEMONIC_VPCLMULQDQ,
    ZYDIS_MNEMONIC_VPCMOV,
    ZYDIS_MNEMONIC_VPCMPB,
    ZYDIS_MNEMONIC_VPCMPD,
    ZYDIS_MNEMONIC_VPCMPEQB,
    ZYDIS_MNEMONIC_VPCMPEQD,
    ZYDIS_MNEMONIC_VPCMPEQQ,
    ZYDIS_MNEMONIC_VPCMPEQW,
    ZYDIS_MNEMONIC_VPCMPESTRI,
    ZYDIS_MNEMONIC_VPCMPESTRM,
    ZYDIS_MNEMONIC_VPCMPGTB,
    ZYDIS_MNEMONIC_VPCMPGTD,
    ZYDIS_MNEMONIC_VPCMPGTQ,
    ZYDIS_MNEMONIC_VPCMPGTW,
    ZYDIS_MNEMONIC_VPCMPISTRI,
    ZYDIS_MNEMONIC_VPCMPISTRM,
    ZYDIS_MNEMONIC_VPCMPLTD,
    ZYDIS_MNEMONIC_VPCMPQ,
    ZYDIS_MNEMONIC_VPCMPUB,
    ZYDIS_MNEMONIC_VPCMPUD,
    ZYDIS_MNEMONIC_VPCMPUQ,
    ZYDIS_MNEMONIC_VPCMPUW,
    ZYDIS_MNEMONIC_VPCMPW,
    ZYDIS_MNEMONIC_VPCOMB,
    ZYDIS_MNEMONIC_VPCOMD,
    ZYDIS_MNEMONIC_VPCOMPRESSB,
    ZYDIS_MNEMONIC_VPCOMPRESSD,
    ZYDIS_MNEMONIC_VPCOMPRESSQ,
    ZYDIS_MNEMONIC_VPCOMPRESSW,
    ZYDIS_MNEMONIC_VPCOMQ,
    ZYDIS_MNEMONIC_VPCOMUB,
    ZYDIS_MNEMONIC_VPCOMUD,
    ZYDIS_MNEMONIC_VPCOMUQ,
    ZYDIS_MNEMONIC_VPCOMUW,
    ZYDIS_MNEMONIC_VPCOMW,
    ZYDIS_MNEMONIC_VPCONFLICTD,
    ZYDIS_MNEMONIC_VPCONFLICTQ,
    ZYDIS_MNEMONIC_VPDPBSSD,
    ZYDIS_MNEMONIC_VPDPBSSDS,
    ZYDIS_MNEMONIC_VPDPBSUD,
    ZYDIS_MNEMONIC_VPDPBSUDS,
    ZYDIS_MNEMONIC_VPDPBUSD,
    ZYDIS_MNEMONIC_VPDPBUSDS,
    ZYDIS_MNEMONIC_VPDPBUUD,
    ZYDIS_MNEMONIC_VPDPBUUDS,
    ZYDIS_MNEMONIC_VPDPWSSD,
    ZYDIS_MNEMONIC_VPDPWSSDS,
    ZYDIS_MNEMONIC_VPDPWSUD,
    ZYDIS_MNEMONIC_VPDPWSUDS,
    ZYDIS_MNEMONIC_VPDPWUSD,
    ZYDIS_MNEMONIC_VPDPWUSDS,
    ZYDIS_MNEMONIC_VPDPWUUD,
    ZYDIS_MNEMONIC_VPDPWUUDS,
    ZYDIS_MNEMONIC_VPERM2F128,
    ZYDIS_MNEMONIC_VPERM2I128,
    ZYDIS_MNEMONIC_VPERMB,
    ZYDIS_MNEMONIC_VPERMD,
    ZYDIS_MNEMONIC_VPERMF32X4,
    ZYDIS_MNEMONIC_VPERMI2B,
    ZYDIS_MNEMONIC_VPERMI2D,
    ZYDIS_MNEMONIC_VPERMI2PD,
    ZYDIS_MNEMONIC_VPERMI2PS,
    ZYDIS_MNEMONIC_VPERMI2Q,
    ZYDIS_MNEMONIC_VPERMI2W,
    ZYDIS_MNEMONIC_VPERMIL2PD,
    ZYDIS_MNEMONIC_VPERMIL2PS,
    ZYDIS_MNEMONIC_VPERMILPD,
    ZYDIS_MNEMONIC_VPERMILPS,
    ZYDIS_MNEMONIC_VPERMPD,
    ZYDIS_MNEMONIC_VPERMPS,
    ZYDIS_MNEMONIC_VPERMQ,
    ZYDIS_MNEMONIC_VPERMT2B,
    ZYDIS_MNEMONIC_VPERMT2D,
    ZYDIS_MNEMONIC_VPERMT2PD,
    ZYDIS_MNEMONIC_VPERMT2PS,
    ZYDIS_MNEMONIC_VPERMT2Q,
    ZYDIS_MNEMONIC_VPERMT2W,
    ZYDIS_MNEMONIC_VPERMW,
    ZYDIS_MNEMONIC_VPEXPANDB,
    ZYDIS_MNEMONIC_VPEXPANDD,
    ZYDIS_MNEMONIC_VPEXPANDQ,
    ZYDIS_MNEMONIC_VPEXPANDW,
    ZYDIS_MNEMONIC_VPEXTRB,
    ZYDIS_MNEMONIC_VPEXTRD,
    ZYDIS_MNEMONIC_VPEXTRQ,
    ZYDIS_MNEMONIC_VPEXTRW,
    ZYDIS_MNEMONIC_VPGATHERDD,
    ZYDIS_MNEMONIC_VPGATHERDQ,
    ZYDIS_MNEMONIC_VPGATHERQD,
    ZYDIS_MNEMONIC_VPGATHERQQ,
    ZYDIS_MNEMONIC_VPHADDBD,
    ZYDIS_MNEMONIC_VPHADDBQ,
    ZYDIS_MNEMONIC_VPHADDBW,
    ZYDIS_MNEMONIC_VPHADDD,
    ZYDIS_MNEMONIC_VPHADDDQ,
    ZYDIS_MNEMONIC_VPHADDSW,
    ZYDIS_MNEMONIC_VPHADDUBD,
    ZYDIS_MNEMONIC_VPHADDUBQ,
    ZYDIS_MNEMONIC_VPHADDUBW,
    ZYDIS_MNEMONIC_VPHADDUDQ,
    ZYDIS_MNEMONIC_VPHADDUWD,
    ZYDIS_MNEMONIC_VPHADDUWQ,
    ZYDIS_MNEMONIC_VPHADDW,
    ZYDIS_MNEMONIC_VPHADDWD,
    ZYDIS_MNEMONIC_VPHADDWQ,
    ZYDIS_MNEMONIC_VPHMINPOSUW,
    ZYDIS_MNEMONIC_VPHSUBBW,
    ZYDIS_MNEMONIC_VPHSUBD,
    ZYDIS_MNEMONIC_VPHSUBDQ,
    ZYDIS_MNEMONIC_VPHSUBSW,
    ZYDIS_MNEMONIC_VPHSUBW,
    ZYDIS_MNEMONIC_VPHSUBWD,
    ZYDIS_MNEMONIC_VPINSRB,
    ZYDIS_MNEMONIC_VPINSRD,
    ZYDIS_MNEMONIC_VPINSRQ,
    ZYDIS_MNEMONIC_VPINSRW,
    ZYDIS_MNEMONIC_VPLZCNTD,
    ZYDIS_MNEMONIC_VPLZCNTQ,
    ZYDIS_MNEMONIC_VPMACSDD,
    ZYDIS_MNEMONIC_VPMACSDQH,
    ZYDIS_MNEMONIC_VPMACSDQL,
    ZYDIS_MNEMONIC_VPMACSSDD,
    ZYDIS_MNEMONIC_VPMACSSDQH,
    ZYDIS_MNEMONIC_VPMACSSDQL,
    ZYDIS_MNEMONIC_VPMACSSWD,
    ZYDIS_MNEMONIC_VPMACSSWW,
    ZYDIS_MNEMONIC_VPMACSWD,
    ZYDIS_MNEMONIC_VPMACSWW,
    ZYDIS_MNEMONIC_VPMADCSSWD,
    ZYDIS_MNEMONIC_VPMADCSWD,
    ZYDIS_MNEMONIC_VPMADD231D,
    ZYDIS_MNEMONIC_VPMADD233D,
    ZYDIS_MNEMONIC_VPMADD52HUQ,
    ZYDIS_MNEMONIC_VPMADD52LUQ,
    ZYDIS_MNEMONIC_VPMADDUBSW,
    ZYDIS_MNEMONIC_VPMADDWD,
    ZYDIS_MNEMONIC_VPMASKMOVD,
    ZYDIS_MNEMONIC_VPMASKMOVQ,
    ZYDIS_MNEMONIC_VPMAXSB,
    ZYDIS_MNEMONIC_VPMAXSD,
    ZYDIS_MNEMONIC_VPMAXSQ,
    ZYDIS_MNEMONIC_VPMAXSW,
    ZYDIS_MNEMONIC_VPMAXUB,
    ZYDIS_MNEMONIC_VPMAXUD,
    ZYDIS_MNEMONIC_VPMAXUQ,
    ZYDIS_MNEMONIC_VPMAXUW,
    ZYDIS_MNEMONIC_VPMINSB,
    ZYDIS_MNEMONIC_VPMINSD,
    ZYDIS_MNEMONIC_VPMINSQ,
    ZYDIS_MNEMONIC_VPMINSW,
    ZYDIS_MNEMONIC_VPMINUB,
    ZYDIS_MNEMONIC_VPMINUD,
    ZYDIS_MNEMONIC_VPMINUQ,
    ZYDIS_MNEMONIC_VPMINUW,
    ZYDIS_MNEMONIC_VPMOVB2M,
    ZYDIS_MNEMONIC_VPMOVD2M,
    ZYDIS_MNEMONIC_VPMOVDB,
    ZYDIS_MNEMONIC_VPMOVDW,
    ZYDIS_MNEMONIC_VPMOVM2B,
    ZYDIS_MNEMONIC_VPMOVM2D,
    ZYDIS_MNEMONIC_VPMOVM2Q,
    ZYDIS_MNEMONIC_VPMOVM2W,
    ZYDIS_MNEMONIC_VPMOVMSKB,
    ZYDIS_MNEMONIC_VPMOVQ2M,
    ZYDIS_MNEMONIC_VPMOVQB,
    ZYDIS_MNEMONIC_VPMOVQD,
    ZYDIS_MNEMONIC_VPMOVQW,
    ZYDIS_MNEMONIC_VPMOVSDB,
    ZYDIS_MNEMONIC_VPMOVSDW,
    ZYDIS_MNEMONIC_VPMOVSQB,
    ZYDIS_MNEMONIC_VPMOVSQD,
    ZYDIS_MNEMONIC_VPMOVSQW,
    ZYDIS_MNEMONIC_VPMOVSWB,
    ZYDIS_MNEMONIC_VPMOVSXBD,
    ZYDIS_MNEMONIC_VPMOVSXBQ,
    ZYDIS_MNEMONIC_VPMOVSXBW,
    ZYDIS_MNEMONIC_VPMOVSXDQ,
    ZYDIS_MNEMONIC_VPMOVSXWD,
    ZYDIS_MNEMONIC_VPMOVSXWQ,
    ZYDIS_MNEMONIC_VPMOVUSDB,
    ZYDIS_MNEMONIC_VPMOVUSDW,
    ZYDIS_MNEMONIC_VPMOVUSQB,
    ZYDIS_MNEMONIC_VPMOVUSQD,
    ZYDIS_MNEMONIC_VPMOVUSQW,
    ZYDIS_MNEMONIC_VPMOVUSWB,
    ZYDIS_MNEMONIC_VPMOVW2M,
    ZYDIS_MNEMONIC_VPMOVWB,
    ZYDIS_MNEMONIC_VPMOVZXBD,
    ZYDIS_MNEMONIC_VPMOVZXBQ,
    ZYDIS_MNEMONIC_VPMOVZXBW,
    ZYDIS_MNEMONIC_VPMOVZXDQ,
    ZYDIS_MNEMONIC_VPMOVZXWD,
    ZYDIS_MNEMONIC_VPMOVZXWQ,
    ZYDIS_MNEMONIC_VPMULDQ,
    ZYDIS_MNEMONIC_VPMULHD,
    ZYDIS_MNEMONIC_VPMULHRSW,
    ZYDIS_MNEMONIC_VPMULHUD,
    ZYDIS_MNEMONIC_VPMULHUW,
    ZYDIS_MNEMONIC_VPMULHW,
    ZYDIS_MNEMONIC_VPMULLD,
    ZYDIS_MNEMONIC_VPMULLQ,
    ZYDIS_MNEMONIC_VPMULLW,
    ZYDIS_MNEMONIC_VPMULTISHIFTQB,
    ZYDIS_MNEMONIC_VPMULUDQ,
    ZYDIS_MNEMONIC_VPOPCNTB,
    ZYDIS_MNEMONIC_VPOPCNTD,
    ZYDIS_MNEMONIC_VPOPCNTQ,
    ZYDIS_MNEMONIC_VPOPCNTW,
    ZYDIS_MNEMONIC_VPOR,
    ZYDIS_MNEMONIC_VPORD,
    ZYDIS_MNEMONIC_VPORQ,
    ZYDIS_MNEMONIC_VPPERM,
    ZYDIS_MNEMONIC_VPREFETCH0,
    ZYDIS_MNEMONIC_VPREFETCH1,
    ZYDIS_MNEMONIC_VPREFETCH2,
    ZYDIS_MNEMONIC_VPREFETCHE0,
    ZYDIS_MNEMONIC_VPREFETCHE1,
    ZYDIS_MNEMONIC_VPREFETCHE2,
    ZYDIS_MNEMONIC_VPREFETCHENTA,
    ZYDIS_MNEMONIC_VPREFETCHNTA,
    ZYDIS_MNEMONIC_VPROLD,
    ZYDIS_MNEMONIC_VPROLQ,
    ZYDIS_MNEMONIC_VPROLVD,
    ZYDIS_MNEMONIC_VPROLVQ,
    ZYDIS_MNEMONIC_VPRORD,
    ZYDIS_MNEMONIC_VPRORQ,
    ZYDIS_MNEMONIC_VPRORVD,
    ZYDIS_MNEMONIC_VPRORVQ,
    ZYDIS_MNEMONIC_VPROTB,
    ZYDIS_MNEMONIC_VPROTD,
    ZYDIS_MNEMONIC_VPROTQ,
    ZYDIS_MNEMONIC_VPROTW,
    ZYDIS_MNEMONIC_VPSADBW,
    ZYDIS_MNEMONIC_VPSBBD,
    ZYDIS_MNEMONIC_VPSBBRD,
    ZYDIS_MNEMONIC_VPSCATTERDD,
    ZYDIS_MNEMONIC_VPSCATTERDQ,
    ZYDIS_MNEMONIC_VPSCATTERQD,
    ZYDIS_MNEMONIC_VPSCATTERQQ,
    ZYDIS_MNEMONIC_VPSHAB,
    ZYDIS_MNEMONIC_VPSHAD,
    ZYDIS_MNEMONIC_VPSHAQ,
    ZYDIS_MNEMONIC_VPSHAW,
    ZYDIS_MNEMONIC_VPSHLB,
    ZYDIS_MNEMONIC_VPSHLD,
    ZYDIS_MNEMONIC_VPSHLDD,
    ZYDIS_MNEMONIC_VPSHLDQ,
    ZYDIS_MNEMONIC_VPSHLDVD,
    ZYDIS_MNEMONIC_VPSHLDVQ,
    ZYDIS_MNEMONIC_VPSHLDVW,
    ZYDIS_MNEMONIC_VPSHLDW,
    ZYDIS_MNEMONIC_VPSHLQ,
    ZYDIS_MNEMONIC_VPSHLW,
    ZYDIS_MNEMONIC_VPSHRDD,
    ZYDIS_MNEMONIC_VPSHRDQ,
    ZYDIS_MNEMONIC_VPSHRDVD,
    ZYDIS_MNEMONIC_VPSHRDVQ,
    ZYDIS_MNEMONIC_VPSHRDVW,
    ZYDIS_MNEMONIC_VPSHRDW,
    ZYDIS_MNEMONIC_VPSHUFB,
    ZYDIS_MNEMONIC_VPSHUFBITQMB,
    ZYDIS_MNEMONIC_VPSHUFD,
    ZYDIS_MNEMONIC_VPSHUFHW,
    ZYDIS_MNEMONIC_VPSHUFLW,
    ZYDIS_MNEMONIC_VPSIGNB,
    ZYDIS_MNEMONIC_VPSIGND,
    ZYDIS_MNEMONIC_VPSIGNW,
    ZYDIS_MNEMONIC_VPSLLD,
    ZYDIS_MNEMONIC_VPSLLDQ,
    ZYDIS_MNEMONIC_VPSLLQ,
    ZYDIS_MNEMONIC_VPSLLVD,
    ZYDIS_MNEMONIC_VPSLLVQ,
    ZYDIS_MNEMONIC_VPSLLVW,
    ZYDIS_MNEMONIC_VPSLLW,
    ZYDIS_MNEMONIC_VPSRAD,
    ZYDIS_MNEMONIC_VPSRAQ,
    ZYDIS_MNEMONIC_VPSRAVD,
    ZYDIS_MNEMONIC_VPSRAVQ,
    ZYDIS_MNEMONIC_VPSRAVW,
    ZYDIS_MNEMONIC_VPSRAW,
    ZYDIS_MNEMONIC_VPSRLD,
    ZYDIS_MNEMONIC_VPSRLDQ,
    ZYDIS_MNEMONIC_VPSRLQ,
    ZYDIS_MNEMONIC_VPSRLVD,
    ZYDIS_MNEMONIC_VPSRLVQ,
    ZYDIS_MNEMONIC_VPSRLVW,
    ZYDIS_MNEMONIC_VPSRLW,
    ZYDIS_MNEMONIC_VPSUBB,
    ZYDIS_MNEMONIC_VPSUBD,
    ZYDIS_MNEMONIC_VPSUBQ,
    ZYDIS_MNEMONIC_VPSUBRD,
    ZYDIS_MNEMONIC_VPSUBRSETBD,
    ZYDIS_MNEMONIC_VPSUBSB,
    ZYDIS_MNEMONIC_VPSUBSETBD,
    ZYDIS_MNEMONIC_VPSUBSW,
    ZYDIS_MNEMONIC_VPSUBUSB,
    ZYDIS_MNEMONIC_VPSUBUSW,
    ZYDIS_MNEMONIC_VPSUBW,
    ZYDIS_MNEMONIC_VPTERNLOGD,
    ZYDIS_MNEMONIC_VPTERNLOGQ,
    ZYDIS_MNEMONIC_VPTEST,
    ZYDIS_MNEMONIC_VPTESTMB,
    ZYDIS_MNEMONIC_VPTESTMD,
    ZYDIS_MNEMONIC_VPTESTMQ,
    ZYDIS_MNEMONIC_VPTESTMW,
    ZYDIS_MNEMONIC_VPTESTNMB,
    ZYDIS_MNEMONIC_VPTESTNMD,
    ZYDIS_MNEMONIC_VPTESTNMQ,
    ZYDIS_MNEMONIC_VPTESTNMW,
    ZYDIS_MNEMONIC_VPUNPCKHBW,
    ZYDIS_MNEMONIC_VPUNPCKHDQ,
    ZYDIS_MNEMONIC_VPUNPCKHQDQ,
    ZYDIS_MNEMONIC_VPUNPCKHWD,
    ZYDIS_MNEMONIC_VPUNPCKLBW,
    ZYDIS_MNEMONIC_VPUNPCKLDQ,
    ZYDIS_MNEMONIC_VPUNPCKLQDQ,
    ZYDIS_MNEMONIC_VPUNPCKLWD,
    ZYDIS_MNEMONIC_VPXOR,
    ZYDIS_MNEMONIC_VPXORD,
    ZYDIS_MNEMONIC_VPXORQ,
    ZYDIS_MNEMONIC_VRANGEPD,
    ZYDIS_MNEMONIC_VRANGEPS,
    ZYDIS_MNEMONIC_VRANGESD,
    ZYDIS_MNEMONIC_VRANGESS,
    ZYDIS_MNEMONIC_VRCP14PD,
    ZYDIS_MNEMONIC_VRCP14PS,
    ZYDIS_MNEMONIC_VRCP14SD,
    ZYDIS_MNEMONIC_VRCP14SS,
    ZYDIS_MNEMONIC_VRCP23PS,
    ZYDIS_MNEMONIC_VRCP28PD,
    ZYDIS_MNEMONIC_VRCP28PS,
    ZYDIS_MNEMONIC_VRCP28SD,
    ZYDIS_MNEMONIC_VRCP28SS,
    ZYDIS_MNEMONIC_VRCPPH,
    ZYDIS_MNEMONIC_VRCPPS,
    ZYDIS_MNEMONIC_VRCPSH,
    ZYDIS_MNEMONIC_VRCPSS,
    ZYDIS_MNEMONIC_VREDUCEPD,
    ZYDIS_MNEMONIC_VREDUCEPH,
    ZYDIS_MNEMONIC_VREDUCEPS,
    ZYDIS_MNEMONIC_VREDUCESD,
    ZYDIS_MNEMONIC_VREDUCESH,
    ZYDIS_MNEMONIC_VREDUCESS,
    ZYDIS_MNEMONIC_VRNDFXPNTPD,
    ZYDIS_MNEMONIC_VRNDFXPNTPS,
    ZYDIS_MNEMONIC_VRNDSCALEPD,
    ZYDIS_MNEMONIC_VRNDSCALEPH,
    ZYDIS_MNEMONIC_VRNDSCALEPS,
    ZYDIS_MNEMONIC_VRNDSCALESD,
    ZYDIS_MNEMONIC_VRNDSCALESH,
    ZYDIS_MNEMONIC_VRNDSCALESS,
    ZYDIS_MNEMONIC_VROUNDPD,
    ZYDIS_MNEMONIC_VROUNDPS,
    ZYDIS_MNEMONIC_VROUNDSD,
    ZYDIS_MNEMONIC_VROUNDSS,
    ZYDIS_MNEMONIC_VRSQRT14PD,
    ZYDIS_MNEMONIC_VRSQRT14PS,
    ZYDIS_MNEMONIC_VRSQRT14SD,
    ZYDIS_MNEMONIC_VRSQRT14SS,
    ZYDIS_MNEMONIC_VRSQRT23PS,
    ZYDIS_MNEMONIC_VRSQRT28PD,
    ZYDIS_MNEMONIC_VRSQRT28PS,
    ZYDIS_MNEMONIC_VRSQRT28SD,
    ZYDIS_MNEMONIC_VRSQRT28SS,
    ZYDIS_MNEMONIC_VRSQRTPH,
    ZYDIS_MNEMONIC_VRSQRTPS,
    ZYDIS_MNEMONIC_VRSQRTSH,
    ZYDIS_MNEMONIC_VRSQRTSS,
    ZYDIS_MNEMONIC_VSCALEFPD,
    ZYDIS_MNEMONIC_VSCALEFPH,
    ZYDIS_MNEMONIC_VSCALEFPS,
    ZYDIS_MNEMONIC_VSCALEFSD,
    ZYDIS_MNEMONIC_VSCALEFSH,
    ZYDIS_MNEMONIC_VSCALEFSS,
    ZYDIS_MNEMONIC_VSCALEPS,
    ZYDIS_MNEMONIC_VSCATTERDPD,
    ZYDIS_MNEMONIC_VSCATTERDPS,
    ZYDIS_MNEMONIC_VSCATTERPF0DPD,
    ZYDIS_MNEMONIC_VSCATTERPF0DPS,
    ZYDIS_MNEMONIC_VSCATTERPF0HINTDPD,
    ZYDIS_MNEMONIC_VSCATTERPF0HINTDPS,
    ZYDIS_MNEMONIC_VSCATTERPF0QPD,
    ZYDIS_MNEMONIC_VSCATTERPF0QPS,
    ZYDIS_MNEMONIC_VSCATTERPF1DPD,
    ZYDIS_MNEMONIC_VSCATTERPF1DPS,
    ZYDIS_MNEMONIC_VSCATTERPF1QPD,
    ZYDIS_MNEMONIC_VSCATTERPF1QPS,
    ZYDIS_MNEMONIC_VSCATTERQPD,
    ZYDIS_MNEMONIC_VSCATTERQPS,
    ZYDIS_MNEMONIC_VSHA512MSG1,
    ZYDIS_MNEMONIC_VSHA512MSG2,
    ZYDIS_MNEMONIC_VSHA512RNDS2,
    ZYDIS_MNEMONIC_VSHUFF32X4,
    ZYDIS_MNEMONIC_VSHUFF64X2,
    ZYDIS_MNEMONIC_VSHUFI32X4,
    ZYDIS_MNEMONIC_VSHUFI64X2,
    ZYDIS_MNEMONIC_VSHUFPD,
    ZYDIS_MNEMONIC_VSHUFPS,
    ZYDIS_MNEMONIC_VSM3MSG1,
    ZYDIS_MNEMONIC_VSM3MSG2,
    ZYDIS_MNEMONIC_VSM3RNDS2,
    ZYDIS_MNEMONIC_VSM4KEY4,
    ZYDIS_MNEMONIC_VSM4RNDS4,
    ZYDIS_MNEMONIC_VSQRTPD,
    ZYDIS_MNEMONIC_VSQRTPH,
    ZYDIS_MNEMONIC_VSQRTPS,
    ZYDIS_MNEMONIC_VSQRTSD,
    ZYDIS_MNEMONIC_VSQRTSH,
    ZYDIS_MNEMONIC_VSQRTSS,
    ZYDIS_MNEMONIC_VSTMXCSR,
    ZYDIS_MNEMONIC_VSUBPD,
    ZYDIS_MNEMONIC_VSUBPH,
    ZYDIS_MNEMONIC_VSUBPS,
    ZYDIS_MNEMONIC_VSUBRPD,
    ZYDIS_MNEMONIC_VSUBRPS,
    ZYDIS_MNEMONIC_VSUBSD,
    ZYDIS_MNEMONIC_VSUBSH,
    ZYDIS_MNEMONIC_VSUBSS,
    ZYDIS_MNEMONIC_VTESTPD,
    ZYDIS_MNEMONIC_VTESTPS,
    ZYDIS_MNEMONIC_VUCOMISD,
    ZYDIS_MNEMONIC_VUCOMISH,
    ZYDIS_MNEMONIC_VUCOMISS,
    ZYDIS_MNEMONIC_VUNPCKHPD,
    ZYDIS_MNEMONIC_VUNPCKHPS,
    ZYDIS_MNEMONIC_VUNPCKLPD,
    ZYDIS_MNEMONIC_VUNPCKLPS,
    ZYDIS_MNEMONIC_VXORPD,
    ZYDIS_MNEMONIC_VXORPS,
    ZYDIS_MNEMONIC_VZEROALL,
    ZYDIS_MNEMONIC_VZEROUPPER,
    ZYDIS_MNEMONIC_WBINVD,
    ZYDIS_MNEMONIC_WRFSBASE,
    ZYDIS_MNEMONIC_WRGSBASE,
    ZYDIS_MNEMONIC_WRMSR,
    ZYDIS_MNEMONIC_WRMSRLIST,
    ZYDIS_MNEMONIC_WRMSRNS,
    ZYDIS_MNEMONIC_WRPKRU,
    ZYDIS_MNEMONIC_WRSSD,
    ZYDIS_MNEMONIC_WRSSQ,
    ZYDIS_MNEMONIC_WRUSSD,
    ZYDIS_MNEMONIC_WRUSSQ,
    ZYDIS_MNEMONIC_XABORT,
    ZYDIS_MNEMONIC_XADD,
    ZYDIS_MNEMONIC_XBEGIN,
    ZYDIS_MNEMONIC_XCHG,
    ZYDIS_MNEMONIC_XCRYPT_CBC,
    ZYDIS_MNEMONIC_XCRYPT_CFB,
    ZYDIS_MNEMONIC_XCRYPT_CTR,
    ZYDIS_MNEMONIC_XCRYPT_ECB,
    ZYDIS_MNEMONIC_XCRYPT_OFB,
    ZYDIS_MNEMONIC_XEND,
    ZYDIS_MNEMONIC_XGETBV,
    ZYDIS_MNEMONIC_XLAT,
    ZYDIS_MNEMONIC_XOR,
    ZYDIS_MNEMONIC_XORPD,
    ZYDIS_MNEMONIC_XORPS,
    ZYDIS_MNEMONIC_XRESLDTRK,
    ZYDIS_MNEMONIC_XRSTOR,
    ZYDIS_MNEMONIC_XRSTOR64,
    ZYDIS_MNEMONIC_XRSTORS,
    ZYDIS_MNEMONIC_XRSTORS64,
    ZYDIS_MNEMONIC_XSAVE,
    ZYDIS_MNEMONIC_XSAVE64,
    ZYDIS_MNEMONIC_XSAVEC,
    ZYDIS_MNEMONIC_XSAVEC64,
    ZYDIS_MNEMONIC_XSAVEOPT,
    ZYDIS_MNEMONIC_XSAVEOPT64,
    ZYDIS_MNEMONIC_XSAVES,
    ZYDIS_MNEMONIC_XSAVES64,
    ZYDIS_MNEMONIC_XSETBV,
    ZYDIS_MNEMONIC_XSHA1,
    ZYDIS_MNEMONIC_XSHA256,
    ZYDIS_MNEMONIC_XSTORE,
    ZYDIS_MNEMONIC_XSUSLDTRK,
    ZYDIS_MNEMONIC_XTEST,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_MNEMONIC_MAX_VALUE = ZYDIS_MNEMONIC_XTEST,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_MNEMONIC_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_MNEMONIC_MAX_VALUE)
} ZydisMnemonic;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * @addtogroup mnemonic Mnemonic
 * Functions for retrieving mnemonic names.
 * @{
 */

/**
 * Returns the specified instruction mnemonic string.
 *
 * @param   mnemonic    The mnemonic.
 *
 * @return  The instruction mnemonic string or `ZYAN_NULL`, if an invalid mnemonic was passed.
 */
ZYDIS_EXPORT const char* ZydisMnemonicGetString(ZydisMnemonic mnemonic);

/**
 * Returns the specified instruction mnemonic as `ZydisShortString`.
 *
 * @param   mnemonic    The mnemonic.
 *
 * @return  The instruction mnemonic string or `ZYAN_NULL`, if an invalid mnemonic was passed.
 *
 * The `buffer` of the returned struct is guaranteed to be zero-terminated in this special case.
 */
ZYDIS_EXPORT const ZydisShortString* ZydisMnemonicGetStringWrapped(ZydisMnemonic mnemonic);

/**
 * @}
 */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_MNEMONIC_H */

//
// Header: Zydis/Register.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Utility functions and constants for registers.
 */

#ifndef ZYDIS_REGISTER_H
#define ZYDIS_REGISTER_H


//
// Header: Zydis/SharedTypes.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/Register.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Defines decoder/encoder-shared macros and types.
 */

#ifndef ZYDIS_SHAREDTYPES_H
#define ZYDIS_SHAREDTYPES_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Constants                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

#define ZYDIS_MAX_INSTRUCTION_LENGTH    15
#define ZYDIS_MAX_OPERAND_COUNT         10 // TODO: Auto generate
#define ZYDIS_MAX_OPERAND_COUNT_VISIBLE  5 // TODO: Auto generate

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Machine mode                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisMachineMode` enum.
 */
typedef enum ZydisMachineMode_
{
    /**
     * 64 bit mode.
     */
    ZYDIS_MACHINE_MODE_LONG_64,
    /**
     * 32 bit protected mode.
     */
    ZYDIS_MACHINE_MODE_LONG_COMPAT_32,
    /**
     * 16 bit protected mode.
     */
    ZYDIS_MACHINE_MODE_LONG_COMPAT_16,
    /**
     * 32 bit protected mode.
     */
    ZYDIS_MACHINE_MODE_LEGACY_32,
    /**
     * 16 bit protected mode.
     */
    ZYDIS_MACHINE_MODE_LEGACY_16,
    /**
     * 16 bit real mode.
     */
    ZYDIS_MACHINE_MODE_REAL_16,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_MACHINE_MODE_MAX_VALUE = ZYDIS_MACHINE_MODE_REAL_16,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_MACHINE_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_MACHINE_MODE_MAX_VALUE)
} ZydisMachineMode;

/* ---------------------------------------------------------------------------------------------- */
/* Stack width                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisStackWidth` enum.
 */
typedef enum ZydisStackWidth_
{
    ZYDIS_STACK_WIDTH_16,
    ZYDIS_STACK_WIDTH_32,
    ZYDIS_STACK_WIDTH_64,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_STACK_WIDTH_MAX_VALUE = ZYDIS_STACK_WIDTH_64,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_STACK_WIDTH_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_STACK_WIDTH_MAX_VALUE)
} ZydisStackWidth;

/* ---------------------------------------------------------------------------------------------- */
/* Element type                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisElementType` enum.
 */
typedef enum ZydisElementType_
{
    ZYDIS_ELEMENT_TYPE_INVALID,
    /**
     * A struct type.
     */
    ZYDIS_ELEMENT_TYPE_STRUCT,
    /**
     * Unsigned integer value.
     */
    ZYDIS_ELEMENT_TYPE_UINT,
    /**
     * Signed integer value.
     */
    ZYDIS_ELEMENT_TYPE_INT,
    /**
     * 16-bit floating point value (`half`).
     */
    ZYDIS_ELEMENT_TYPE_FLOAT16,
    /**
     * 32-bit floating point value (`single`).
     */
    ZYDIS_ELEMENT_TYPE_FLOAT32,
    /**
     * 64-bit floating point value (`double`).
     */
    ZYDIS_ELEMENT_TYPE_FLOAT64,
    /**
     * 80-bit floating point value (`extended`).
     */
    ZYDIS_ELEMENT_TYPE_FLOAT80,
    /**
     * 16-bit brain floating point value.
     */
    ZYDIS_ELEMENT_TYPE_BFLOAT16,
    /**
     * Binary coded decimal value.
     */
    ZYDIS_ELEMENT_TYPE_LONGBCD,
    /**
     * A condition code (e.g. used by `CMPPD`, `VCMPPD`, ...).
     */
    ZYDIS_ELEMENT_TYPE_CC,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_ELEMENT_TYPE_MAX_VALUE = ZYDIS_ELEMENT_TYPE_CC,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_ELEMENT_TYPE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_ELEMENT_TYPE_MAX_VALUE)
} ZydisElementType;

/* ---------------------------------------------------------------------------------------------- */
/* Element size                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisElementSize` datatype.
 */
typedef ZyanU16 ZydisElementSize;

/* ---------------------------------------------------------------------------------------------- */
/* Operand type                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisOperandType` enum.
 */
typedef enum ZydisOperandType_
{
    /**
     * The operand is not used.
     */
    ZYDIS_OPERAND_TYPE_UNUSED,
    /**
     * The operand is a register operand.
     */
    ZYDIS_OPERAND_TYPE_REGISTER,
    /**
     * The operand is a memory operand.
     */
    ZYDIS_OPERAND_TYPE_MEMORY,
    /**
     * The operand is a pointer operand with a segment:offset lvalue.
     */
    ZYDIS_OPERAND_TYPE_POINTER,
    /**
     * The operand is an immediate operand.
     */
    ZYDIS_OPERAND_TYPE_IMMEDIATE,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_OPERAND_TYPE_MAX_VALUE = ZYDIS_OPERAND_TYPE_IMMEDIATE,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_OPERAND_TYPE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_OPERAND_TYPE_MAX_VALUE)
} ZydisOperandType;

// If asserts are failing here remember to update encoder table generator before fixing asserts
ZYAN_STATIC_ASSERT(ZYAN_BITS_TO_REPRESENT(
    ZYDIS_OPERAND_TYPE_MAX_VALUE - ZYDIS_OPERAND_TYPE_REGISTER) == 2);

/* ---------------------------------------------------------------------------------------------- */
/* Operand encoding                                                                               */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisOperandEncoding` enum.
 */
typedef enum ZydisOperandEncoding_
{
    ZYDIS_OPERAND_ENCODING_NONE,
    ZYDIS_OPERAND_ENCODING_MODRM_REG,
    ZYDIS_OPERAND_ENCODING_MODRM_RM,
    ZYDIS_OPERAND_ENCODING_OPCODE,
    ZYDIS_OPERAND_ENCODING_NDSNDD,
    ZYDIS_OPERAND_ENCODING_IS4,
    ZYDIS_OPERAND_ENCODING_MASK,
    ZYDIS_OPERAND_ENCODING_DISP8,
    ZYDIS_OPERAND_ENCODING_DISP16,
    ZYDIS_OPERAND_ENCODING_DISP32,
    ZYDIS_OPERAND_ENCODING_DISP64,
    ZYDIS_OPERAND_ENCODING_DISP16_32_64,
    ZYDIS_OPERAND_ENCODING_DISP32_32_64,
    ZYDIS_OPERAND_ENCODING_DISP16_32_32,
    ZYDIS_OPERAND_ENCODING_UIMM8,
    ZYDIS_OPERAND_ENCODING_UIMM16,
    ZYDIS_OPERAND_ENCODING_UIMM32,
    ZYDIS_OPERAND_ENCODING_UIMM64,
    ZYDIS_OPERAND_ENCODING_UIMM16_32_64,
    ZYDIS_OPERAND_ENCODING_UIMM32_32_64,
    ZYDIS_OPERAND_ENCODING_UIMM16_32_32,
    ZYDIS_OPERAND_ENCODING_SIMM8,
    ZYDIS_OPERAND_ENCODING_SIMM16,
    ZYDIS_OPERAND_ENCODING_SIMM32,
    ZYDIS_OPERAND_ENCODING_SIMM64,
    ZYDIS_OPERAND_ENCODING_SIMM16_32_64,
    ZYDIS_OPERAND_ENCODING_SIMM32_32_64,
    ZYDIS_OPERAND_ENCODING_SIMM16_32_32,
    ZYDIS_OPERAND_ENCODING_JIMM8,
    ZYDIS_OPERAND_ENCODING_JIMM16,
    ZYDIS_OPERAND_ENCODING_JIMM32,
    ZYDIS_OPERAND_ENCODING_JIMM64,
    ZYDIS_OPERAND_ENCODING_JIMM16_32_64,
    ZYDIS_OPERAND_ENCODING_JIMM32_32_64,
    ZYDIS_OPERAND_ENCODING_JIMM16_32_32,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_OPERAND_ENCODING_MAX_VALUE = ZYDIS_OPERAND_ENCODING_JIMM16_32_32,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_OPERAND_ENCODING_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_OPERAND_ENCODING_MAX_VALUE)
} ZydisOperandEncoding;

/* ---------------------------------------------------------------------------------------------- */
/* Operand visibility                                                                             */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisOperandVisibility` enum.
 */
typedef enum ZydisOperandVisibility_
{
    ZYDIS_OPERAND_VISIBILITY_INVALID,
    /**
     * The operand is explicitly encoded in the instruction.
     */
    ZYDIS_OPERAND_VISIBILITY_EXPLICIT,
    /**
     * The operand is part of the opcode, but listed as an operand.
     */
    ZYDIS_OPERAND_VISIBILITY_IMPLICIT,
    /**
     * The operand is part of the opcode, and not typically listed as an operand.
     */
    ZYDIS_OPERAND_VISIBILITY_HIDDEN,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_OPERAND_VISIBILITY_MAX_VALUE = ZYDIS_OPERAND_VISIBILITY_HIDDEN,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_OPERAND_VISIBILITY_REQUIRED_BITS =
        ZYAN_BITS_TO_REPRESENT(ZYDIS_OPERAND_VISIBILITY_MAX_VALUE)
} ZydisOperandVisibility;

/* ---------------------------------------------------------------------------------------------- */
/* Operand action                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisOperandAction` enum.
 */
typedef enum ZydisOperandAction_
{
    /* ------------------------------------------------------------------------------------------ */
    /* Elemental actions                                                                          */
    /* ------------------------------------------------------------------------------------------ */

    /**
     * The operand is read by the instruction.
     */
    ZYDIS_OPERAND_ACTION_READ       = 0x01,
    /**
     * The operand is written by the instruction (must write).
     */
    ZYDIS_OPERAND_ACTION_WRITE      = 0x02,
    /**
     * The operand is conditionally read by the instruction.
     */
    ZYDIS_OPERAND_ACTION_CONDREAD   = 0x04,
    /**
     * The operand is conditionally written by the instruction (may write).
     */
    ZYDIS_OPERAND_ACTION_CONDWRITE  = 0x08,

    /* ------------------------------------------------------------------------------------------ */
    /* Combined actions                                                                           */
    /* ------------------------------------------------------------------------------------------ */

    /**
     * The operand is read (must read) and written by the instruction (must write).
     */
    ZYDIS_OPERAND_ACTION_READWRITE = ZYDIS_OPERAND_ACTION_READ | ZYDIS_OPERAND_ACTION_WRITE,
    /**
     * The operand is conditionally read (may read) and conditionally written by
     * the instruction (may write).
     */
    ZYDIS_OPERAND_ACTION_CONDREAD_CONDWRITE =
        ZYDIS_OPERAND_ACTION_CONDREAD | ZYDIS_OPERAND_ACTION_CONDWRITE,
    /**
     * The operand is read (must read) and conditionally written by the
     * instruction (may write).
     */
    ZYDIS_OPERAND_ACTION_READ_CONDWRITE =
        ZYDIS_OPERAND_ACTION_READ | ZYDIS_OPERAND_ACTION_CONDWRITE,
    /**
     * The operand is written (must write) and conditionally read by the
     * instruction (may read).
     */
    ZYDIS_OPERAND_ACTION_CONDREAD_WRITE =
        ZYDIS_OPERAND_ACTION_CONDREAD | ZYDIS_OPERAND_ACTION_WRITE,

    /**
     * Mask combining all reading access flags.
     */
    ZYDIS_OPERAND_ACTION_MASK_READ  = ZYDIS_OPERAND_ACTION_READ | ZYDIS_OPERAND_ACTION_CONDREAD,
    /**
     * Mask combining all writing access flags.
     */
    ZYDIS_OPERAND_ACTION_MASK_WRITE = ZYDIS_OPERAND_ACTION_WRITE | ZYDIS_OPERAND_ACTION_CONDWRITE,

    /* ------------------------------------------------------------------------------------------ */

    /**
     * The minimum number of bits required to represent all values of this bitset.
     */
    ZYDIS_OPERAND_ACTION_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_OPERAND_ACTION_CONDWRITE)
} ZydisOperandAction;

/**
 * Defines the `ZydisOperandActions` data-type.
 */
typedef ZyanU8 ZydisOperandActions;

/* ---------------------------------------------------------------------------------------------- */
/* Instruction encoding                                                                           */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisInstructionEncoding` enum.
 */
typedef enum ZydisInstructionEncoding_
{
    /**
     * The instruction uses the legacy encoding.
     */
    ZYDIS_INSTRUCTION_ENCODING_LEGACY,
    /**
     * The instruction uses the AMD 3DNow-encoding.
     */
    ZYDIS_INSTRUCTION_ENCODING_3DNOW,
    /**
     * The instruction uses the AMD XOP-encoding.
     */
    ZYDIS_INSTRUCTION_ENCODING_XOP,
    /**
     * The instruction uses the VEX-encoding.
     */
    ZYDIS_INSTRUCTION_ENCODING_VEX,
    /**
     * The instruction uses the EVEX-encoding.
     */
    ZYDIS_INSTRUCTION_ENCODING_EVEX,
    /**
     * The instruction uses the MVEX-encoding.
     */
    ZYDIS_INSTRUCTION_ENCODING_MVEX,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_INSTRUCTION_ENCODING_MAX_VALUE = ZYDIS_INSTRUCTION_ENCODING_MVEX,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_INSTRUCTION_ENCODING_REQUIRED_BITS =
        ZYAN_BITS_TO_REPRESENT(ZYDIS_INSTRUCTION_ENCODING_MAX_VALUE)
} ZydisInstructionEncoding;

/* ---------------------------------------------------------------------------------------------- */
/* Opcode map                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisOpcodeMap` enum.
 */
typedef enum ZydisOpcodeMap_
{
    ZYDIS_OPCODE_MAP_DEFAULT,
    ZYDIS_OPCODE_MAP_0F,
    ZYDIS_OPCODE_MAP_0F38,
    ZYDIS_OPCODE_MAP_0F3A,
    ZYDIS_OPCODE_MAP_MAP4, // not used
    ZYDIS_OPCODE_MAP_MAP5,
    ZYDIS_OPCODE_MAP_MAP6,
    ZYDIS_OPCODE_MAP_MAP7, // not used
    ZYDIS_OPCODE_MAP_0F0F,
    ZYDIS_OPCODE_MAP_XOP8,
    ZYDIS_OPCODE_MAP_XOP9,
    ZYDIS_OPCODE_MAP_XOPA,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_OPCODE_MAP_MAX_VALUE = ZYDIS_OPCODE_MAP_XOPA,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_OPCODE_MAP_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_OPCODE_MAP_MAX_VALUE)
} ZydisOpcodeMap;

/* ---------------------------------------------------------------------------------------------- */
/* Instruction attributes                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * @defgroup instruction_attributes Instruction attributes
 *
 * Constants describing various properties of an instruction. Used in the
 * @ref ZydisDecodedInstruction.attributes and @ref ZydisEncoderRequest.prefixes fields.
 *
 * @{
 */

/**
 * Defines the `ZydisInstructionAttributes` data-type.
 */
typedef ZyanU64 ZydisInstructionAttributes;

/**
 * The instruction has the `ModRM` byte.
 */
#define ZYDIS_ATTRIB_HAS_MODRM                  (1ULL <<  0)
/**
 * The instruction has the `SIB` byte.
 */
#define ZYDIS_ATTRIB_HAS_SIB                    (1ULL <<  1)
/**
 * The instruction has the `REX` prefix.
 */
#define ZYDIS_ATTRIB_HAS_REX                    (1ULL <<  2)
/**
 * The instruction has the `XOP` prefix.
 */
#define ZYDIS_ATTRIB_HAS_XOP                    (1ULL <<  3)
/**
 * The instruction has the `VEX` prefix.
 */
#define ZYDIS_ATTRIB_HAS_VEX                    (1ULL <<  4)
/**
 * The instruction has the `EVEX` prefix.
 */
#define ZYDIS_ATTRIB_HAS_EVEX                   (1ULL <<  5)
/**
 * The instruction has the `MVEX` prefix.
 */
#define ZYDIS_ATTRIB_HAS_MVEX                   (1ULL <<  6)
/**
 * The instruction has one or more operands with position-relative offsets.
 */
#define ZYDIS_ATTRIB_IS_RELATIVE                (1ULL <<  7)
/**
 * The instruction is privileged.
 *
 * Privileged instructions are any instructions that require a current ring level below 3.
 */
#define ZYDIS_ATTRIB_IS_PRIVILEGED              (1ULL <<  8)
/**
 * The instruction accesses one or more CPU-flags.
 */
#define ZYDIS_ATTRIB_CPUFLAG_ACCESS             (1ULL <<  9)
/**
 * The instruction may conditionally read the general CPU state.
 */
#define ZYDIS_ATTRIB_CPU_STATE_CR               (1ULL << 10)
/**
 * The instruction may conditionally write the general CPU state.
 */
#define ZYDIS_ATTRIB_CPU_STATE_CW               (1ULL << 11)
/**
 * The instruction may conditionally read the FPU state (X87, MMX).
 */
#define ZYDIS_ATTRIB_FPU_STATE_CR               (1ULL << 12)
/**
 * The instruction may conditionally write the FPU state (X87, MMX).
 */
#define ZYDIS_ATTRIB_FPU_STATE_CW               (1ULL << 13)
/**
 * The instruction may conditionally read the XMM state (AVX, AVX2, AVX-512).
 */
#define ZYDIS_ATTRIB_XMM_STATE_CR               (1ULL << 14)
/**
 * The instruction may conditionally write the XMM state (AVX, AVX2, AVX-512).
 */
#define ZYDIS_ATTRIB_XMM_STATE_CW               (1ULL << 15)
/**
 * The instruction accepts the `LOCK` prefix (`0xF0`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_LOCK               (1ULL << 16)
/**
 * The instruction accepts the `REP` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_REP                (1ULL << 17)
/**
 * The instruction accepts the `REPE`/`REPZ` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_REPE               (1ULL << 18)
/**
 * The instruction accepts the `REPE`/`REPZ` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_REPZ               ZYDIS_ATTRIB_ACCEPTS_REPE
/**
 * The instruction accepts the `REPNE`/`REPNZ` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_REPNE              (1ULL << 19)
/**
 * The instruction accepts the `REPNE`/`REPNZ` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_REPNZ              ZYDIS_ATTRIB_ACCEPTS_REPNE
/**
 * The instruction accepts the `BND` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_BND                (1ULL << 20)
/**
 * The instruction accepts the `XACQUIRE` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_XACQUIRE           (1ULL << 21)
/**
 * The instruction accepts the `XRELEASE` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_XRELEASE           (1ULL << 22)
/**
 * The instruction accepts the `XACQUIRE`/`XRELEASE` prefixes (`0xF2`, `0xF3`)
 * without the `LOCK` prefix (`0x0F`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_HLE_WITHOUT_LOCK   (1ULL << 23)
/**
 * The instruction accepts branch hints (0x2E, 0x3E).
 */
#define ZYDIS_ATTRIB_ACCEPTS_BRANCH_HINTS       (1ULL << 24)
/**
 * The instruction accepts the `CET` `no-track` prefix (`0x3E`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_NOTRACK            (1ULL << 25)
/**
 * The instruction accepts segment prefixes (`0x2E`, `0x36`, `0x3E`, `0x26`,
 * `0x64`, `0x65`).
 */
#define ZYDIS_ATTRIB_ACCEPTS_SEGMENT            (1ULL << 26)
/**
 * The instruction has the `LOCK` prefix (`0xF0`).
 */
#define ZYDIS_ATTRIB_HAS_LOCK                   (1ULL << 27)
/**
 * The instruction has the `REP` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_HAS_REP                    (1ULL << 28)
/**
 * The instruction has the `REPE`/`REPZ` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_HAS_REPE                   (1ULL << 29)
/**
 * The instruction has the `REPE`/`REPZ` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_HAS_REPZ                   ZYDIS_ATTRIB_HAS_REPE
/**
 * The instruction has the `REPNE`/`REPNZ` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_HAS_REPNE                  (1ULL << 30)
/**
 * The instruction has the `REPNE`/`REPNZ` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_HAS_REPNZ                  ZYDIS_ATTRIB_HAS_REPNE
/**
 * The instruction has the `BND` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_HAS_BND                    (1ULL << 31)
/**
 * The instruction has the `XACQUIRE` prefix (`0xF2`).
 */
#define ZYDIS_ATTRIB_HAS_XACQUIRE               (1ULL << 32)
/**
 * The instruction has the `XRELEASE` prefix (`0xF3`).
 */
#define ZYDIS_ATTRIB_HAS_XRELEASE               (1ULL << 33)
/**
 * The instruction has the branch-not-taken hint (`0x2E`).
 */
#define ZYDIS_ATTRIB_HAS_BRANCH_NOT_TAKEN       (1ULL << 34)
/**
 * The instruction has the branch-taken hint (`0x3E`).
 */
#define ZYDIS_ATTRIB_HAS_BRANCH_TAKEN           (1ULL << 35)
/**
 * The instruction has the `CET` `no-track` prefix (`0x3E`).
 */
#define ZYDIS_ATTRIB_HAS_NOTRACK                (1ULL << 36)
/**
 * The instruction has the `CS` segment modifier (`0x2E`).
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT_CS             (1ULL << 37)
/**
 * The instruction has the `SS` segment modifier (`0x36`).
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT_SS             (1ULL << 38)
/**
 * The instruction has the `DS` segment modifier (`0x3E`).
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT_DS             (1ULL << 39)
/**
 * The instruction has the `ES` segment modifier (`0x26`).
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT_ES             (1ULL << 40)
/**
 * The instruction has the `FS` segment modifier (`0x64`).
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT_FS             (1ULL << 41)
/**
 * The instruction has the `GS` segment modifier (`0x65`).
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT_GS             (1ULL << 42)
/**
 * The instruction has a segment modifier.
 */
#define ZYDIS_ATTRIB_HAS_SEGMENT                (ZYDIS_ATTRIB_HAS_SEGMENT_CS | \
                                                 ZYDIS_ATTRIB_HAS_SEGMENT_SS | \
                                                 ZYDIS_ATTRIB_HAS_SEGMENT_DS | \
                                                 ZYDIS_ATTRIB_HAS_SEGMENT_ES | \
                                                 ZYDIS_ATTRIB_HAS_SEGMENT_FS | \
                                                 ZYDIS_ATTRIB_HAS_SEGMENT_GS)
/**
 * The instruction has the operand-size override prefix (`0x66`).
 */
#define ZYDIS_ATTRIB_HAS_OPERANDSIZE            (1ULL << 43) // TODO: rename
/**
 * The instruction has the address-size override prefix (`0x67`).
 */
#define ZYDIS_ATTRIB_HAS_ADDRESSSIZE            (1ULL << 44) // TODO: rename
/**
 * The instruction has the `EVEX.b` bit set.
 *
 * This attribute is mainly used by the encoder.
 */
#define ZYDIS_ATTRIB_HAS_EVEX_B                 (1ULL << 45) // TODO: rename

/**
 * @}
 */

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_SHAREDTYPES_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Registers                                                                                      */
/* ---------------------------------------------------------------------------------------------- */


//
// Header: Zydis/Generated/EnumRegister.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/DecoderTypes.h
//   - Zydis/Register.h
//

/**
 * Defines the `ZydisRegister` enum.
 */
typedef enum ZydisRegister_
{
    ZYDIS_REGISTER_NONE,

    // General purpose registers  8-bit
    ZYDIS_REGISTER_AL,
    ZYDIS_REGISTER_CL,
    ZYDIS_REGISTER_DL,
    ZYDIS_REGISTER_BL,
    ZYDIS_REGISTER_AH,
    ZYDIS_REGISTER_CH,
    ZYDIS_REGISTER_DH,
    ZYDIS_REGISTER_BH,
    ZYDIS_REGISTER_SPL,
    ZYDIS_REGISTER_BPL,
    ZYDIS_REGISTER_SIL,
    ZYDIS_REGISTER_DIL,
    ZYDIS_REGISTER_R8B,
    ZYDIS_REGISTER_R9B,
    ZYDIS_REGISTER_R10B,
    ZYDIS_REGISTER_R11B,
    ZYDIS_REGISTER_R12B,
    ZYDIS_REGISTER_R13B,
    ZYDIS_REGISTER_R14B,
    ZYDIS_REGISTER_R15B,

    // General purpose registers 16-bit
    ZYDIS_REGISTER_AX,
    ZYDIS_REGISTER_CX,
    ZYDIS_REGISTER_DX,
    ZYDIS_REGISTER_BX,
    ZYDIS_REGISTER_SP,
    ZYDIS_REGISTER_BP,
    ZYDIS_REGISTER_SI,
    ZYDIS_REGISTER_DI,
    ZYDIS_REGISTER_R8W,
    ZYDIS_REGISTER_R9W,
    ZYDIS_REGISTER_R10W,
    ZYDIS_REGISTER_R11W,
    ZYDIS_REGISTER_R12W,
    ZYDIS_REGISTER_R13W,
    ZYDIS_REGISTER_R14W,
    ZYDIS_REGISTER_R15W,

    // General purpose registers 32-bit
    ZYDIS_REGISTER_EAX,
    ZYDIS_REGISTER_ECX,
    ZYDIS_REGISTER_EDX,
    ZYDIS_REGISTER_EBX,
    ZYDIS_REGISTER_ESP,
    ZYDIS_REGISTER_EBP,
    ZYDIS_REGISTER_ESI,
    ZYDIS_REGISTER_EDI,
    ZYDIS_REGISTER_R8D,
    ZYDIS_REGISTER_R9D,
    ZYDIS_REGISTER_R10D,
    ZYDIS_REGISTER_R11D,
    ZYDIS_REGISTER_R12D,
    ZYDIS_REGISTER_R13D,
    ZYDIS_REGISTER_R14D,
    ZYDIS_REGISTER_R15D,

    // General purpose registers 64-bit
    ZYDIS_REGISTER_RAX,
    ZYDIS_REGISTER_RCX,
    ZYDIS_REGISTER_RDX,
    ZYDIS_REGISTER_RBX,
    ZYDIS_REGISTER_RSP,
    ZYDIS_REGISTER_RBP,
    ZYDIS_REGISTER_RSI,
    ZYDIS_REGISTER_RDI,
    ZYDIS_REGISTER_R8,
    ZYDIS_REGISTER_R9,
    ZYDIS_REGISTER_R10,
    ZYDIS_REGISTER_R11,
    ZYDIS_REGISTER_R12,
    ZYDIS_REGISTER_R13,
    ZYDIS_REGISTER_R14,
    ZYDIS_REGISTER_R15,

    // Floating point legacy registers
    ZYDIS_REGISTER_ST0,
    ZYDIS_REGISTER_ST1,
    ZYDIS_REGISTER_ST2,
    ZYDIS_REGISTER_ST3,
    ZYDIS_REGISTER_ST4,
    ZYDIS_REGISTER_ST5,
    ZYDIS_REGISTER_ST6,
    ZYDIS_REGISTER_ST7,
    ZYDIS_REGISTER_X87CONTROL,
    ZYDIS_REGISTER_X87STATUS,
    ZYDIS_REGISTER_X87TAG,

    // Floating point multimedia registers
    ZYDIS_REGISTER_MM0,
    ZYDIS_REGISTER_MM1,
    ZYDIS_REGISTER_MM2,
    ZYDIS_REGISTER_MM3,
    ZYDIS_REGISTER_MM4,
    ZYDIS_REGISTER_MM5,
    ZYDIS_REGISTER_MM6,
    ZYDIS_REGISTER_MM7,

    // Floating point vector registers 128-bit
    ZYDIS_REGISTER_XMM0,
    ZYDIS_REGISTER_XMM1,
    ZYDIS_REGISTER_XMM2,
    ZYDIS_REGISTER_XMM3,
    ZYDIS_REGISTER_XMM4,
    ZYDIS_REGISTER_XMM5,
    ZYDIS_REGISTER_XMM6,
    ZYDIS_REGISTER_XMM7,
    ZYDIS_REGISTER_XMM8,
    ZYDIS_REGISTER_XMM9,
    ZYDIS_REGISTER_XMM10,
    ZYDIS_REGISTER_XMM11,
    ZYDIS_REGISTER_XMM12,
    ZYDIS_REGISTER_XMM13,
    ZYDIS_REGISTER_XMM14,
    ZYDIS_REGISTER_XMM15,
    ZYDIS_REGISTER_XMM16,
    ZYDIS_REGISTER_XMM17,
    ZYDIS_REGISTER_XMM18,
    ZYDIS_REGISTER_XMM19,
    ZYDIS_REGISTER_XMM20,
    ZYDIS_REGISTER_XMM21,
    ZYDIS_REGISTER_XMM22,
    ZYDIS_REGISTER_XMM23,
    ZYDIS_REGISTER_XMM24,
    ZYDIS_REGISTER_XMM25,
    ZYDIS_REGISTER_XMM26,
    ZYDIS_REGISTER_XMM27,
    ZYDIS_REGISTER_XMM28,
    ZYDIS_REGISTER_XMM29,
    ZYDIS_REGISTER_XMM30,
    ZYDIS_REGISTER_XMM31,

    // Floating point vector registers 256-bit
    ZYDIS_REGISTER_YMM0,
    ZYDIS_REGISTER_YMM1,
    ZYDIS_REGISTER_YMM2,
    ZYDIS_REGISTER_YMM3,
    ZYDIS_REGISTER_YMM4,
    ZYDIS_REGISTER_YMM5,
    ZYDIS_REGISTER_YMM6,
    ZYDIS_REGISTER_YMM7,
    ZYDIS_REGISTER_YMM8,
    ZYDIS_REGISTER_YMM9,
    ZYDIS_REGISTER_YMM10,
    ZYDIS_REGISTER_YMM11,
    ZYDIS_REGISTER_YMM12,
    ZYDIS_REGISTER_YMM13,
    ZYDIS_REGISTER_YMM14,
    ZYDIS_REGISTER_YMM15,
    ZYDIS_REGISTER_YMM16,
    ZYDIS_REGISTER_YMM17,
    ZYDIS_REGISTER_YMM18,
    ZYDIS_REGISTER_YMM19,
    ZYDIS_REGISTER_YMM20,
    ZYDIS_REGISTER_YMM21,
    ZYDIS_REGISTER_YMM22,
    ZYDIS_REGISTER_YMM23,
    ZYDIS_REGISTER_YMM24,
    ZYDIS_REGISTER_YMM25,
    ZYDIS_REGISTER_YMM26,
    ZYDIS_REGISTER_YMM27,
    ZYDIS_REGISTER_YMM28,
    ZYDIS_REGISTER_YMM29,
    ZYDIS_REGISTER_YMM30,
    ZYDIS_REGISTER_YMM31,

    // Floating point vector registers 512-bit
    ZYDIS_REGISTER_ZMM0,
    ZYDIS_REGISTER_ZMM1,
    ZYDIS_REGISTER_ZMM2,
    ZYDIS_REGISTER_ZMM3,
    ZYDIS_REGISTER_ZMM4,
    ZYDIS_REGISTER_ZMM5,
    ZYDIS_REGISTER_ZMM6,
    ZYDIS_REGISTER_ZMM7,
    ZYDIS_REGISTER_ZMM8,
    ZYDIS_REGISTER_ZMM9,
    ZYDIS_REGISTER_ZMM10,
    ZYDIS_REGISTER_ZMM11,
    ZYDIS_REGISTER_ZMM12,
    ZYDIS_REGISTER_ZMM13,
    ZYDIS_REGISTER_ZMM14,
    ZYDIS_REGISTER_ZMM15,
    ZYDIS_REGISTER_ZMM16,
    ZYDIS_REGISTER_ZMM17,
    ZYDIS_REGISTER_ZMM18,
    ZYDIS_REGISTER_ZMM19,
    ZYDIS_REGISTER_ZMM20,
    ZYDIS_REGISTER_ZMM21,
    ZYDIS_REGISTER_ZMM22,
    ZYDIS_REGISTER_ZMM23,
    ZYDIS_REGISTER_ZMM24,
    ZYDIS_REGISTER_ZMM25,
    ZYDIS_REGISTER_ZMM26,
    ZYDIS_REGISTER_ZMM27,
    ZYDIS_REGISTER_ZMM28,
    ZYDIS_REGISTER_ZMM29,
    ZYDIS_REGISTER_ZMM30,
    ZYDIS_REGISTER_ZMM31,

    // Matrix registers
    ZYDIS_REGISTER_TMM0,
    ZYDIS_REGISTER_TMM1,
    ZYDIS_REGISTER_TMM2,
    ZYDIS_REGISTER_TMM3,
    ZYDIS_REGISTER_TMM4,
    ZYDIS_REGISTER_TMM5,
    ZYDIS_REGISTER_TMM6,
    ZYDIS_REGISTER_TMM7,

    // Flags registers
    ZYDIS_REGISTER_FLAGS,
    ZYDIS_REGISTER_EFLAGS,
    ZYDIS_REGISTER_RFLAGS,

    // Instruction-pointer registers
    ZYDIS_REGISTER_IP,
    ZYDIS_REGISTER_EIP,
    ZYDIS_REGISTER_RIP,

    // Segment registers
    ZYDIS_REGISTER_ES,
    ZYDIS_REGISTER_CS,
    ZYDIS_REGISTER_SS,
    ZYDIS_REGISTER_DS,
    ZYDIS_REGISTER_FS,
    ZYDIS_REGISTER_GS,

    // Table registers
    ZYDIS_REGISTER_GDTR,
    ZYDIS_REGISTER_LDTR,
    ZYDIS_REGISTER_IDTR,
    ZYDIS_REGISTER_TR,

    // Test registers
    ZYDIS_REGISTER_TR0,
    ZYDIS_REGISTER_TR1,
    ZYDIS_REGISTER_TR2,
    ZYDIS_REGISTER_TR3,
    ZYDIS_REGISTER_TR4,
    ZYDIS_REGISTER_TR5,
    ZYDIS_REGISTER_TR6,
    ZYDIS_REGISTER_TR7,

    // Control registers
    ZYDIS_REGISTER_CR0,
    ZYDIS_REGISTER_CR1,
    ZYDIS_REGISTER_CR2,
    ZYDIS_REGISTER_CR3,
    ZYDIS_REGISTER_CR4,
    ZYDIS_REGISTER_CR5,
    ZYDIS_REGISTER_CR6,
    ZYDIS_REGISTER_CR7,
    ZYDIS_REGISTER_CR8,
    ZYDIS_REGISTER_CR9,
    ZYDIS_REGISTER_CR10,
    ZYDIS_REGISTER_CR11,
    ZYDIS_REGISTER_CR12,
    ZYDIS_REGISTER_CR13,
    ZYDIS_REGISTER_CR14,
    ZYDIS_REGISTER_CR15,

    // Debug registers
    ZYDIS_REGISTER_DR0,
    ZYDIS_REGISTER_DR1,
    ZYDIS_REGISTER_DR2,
    ZYDIS_REGISTER_DR3,
    ZYDIS_REGISTER_DR4,
    ZYDIS_REGISTER_DR5,
    ZYDIS_REGISTER_DR6,
    ZYDIS_REGISTER_DR7,
    ZYDIS_REGISTER_DR8,
    ZYDIS_REGISTER_DR9,
    ZYDIS_REGISTER_DR10,
    ZYDIS_REGISTER_DR11,
    ZYDIS_REGISTER_DR12,
    ZYDIS_REGISTER_DR13,
    ZYDIS_REGISTER_DR14,
    ZYDIS_REGISTER_DR15,

    // Mask registers
    ZYDIS_REGISTER_K0,
    ZYDIS_REGISTER_K1,
    ZYDIS_REGISTER_K2,
    ZYDIS_REGISTER_K3,
    ZYDIS_REGISTER_K4,
    ZYDIS_REGISTER_K5,
    ZYDIS_REGISTER_K6,
    ZYDIS_REGISTER_K7,

    // Bound registers
    ZYDIS_REGISTER_BND0,
    ZYDIS_REGISTER_BND1,
    ZYDIS_REGISTER_BND2,
    ZYDIS_REGISTER_BND3,
    ZYDIS_REGISTER_BNDCFG,
    ZYDIS_REGISTER_BNDSTATUS,

    // Uncategorized
    ZYDIS_REGISTER_MXCSR,
    ZYDIS_REGISTER_PKRU,
    ZYDIS_REGISTER_XCR0,
    ZYDIS_REGISTER_UIF,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_REGISTER_MAX_VALUE = ZYDIS_REGISTER_UIF,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_REGISTER_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_REGISTER_MAX_VALUE)
} ZydisRegister;

/* ---------------------------------------------------------------------------------------------- */
/* Register kinds                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisRegisterKind` enum.
 *
 * Please note that this enum does not contain a matching entry for all values of the
 * `ZydisRegister` enum, but only for those registers where it makes sense to logically group them
 * for decoding/encoding purposes.
 *
 * These are mainly the registers that can be identified by an id within their corresponding
 * register-class.
 */
typedef enum ZydisRegisterKind_
{
    ZYDIS_REGKIND_INVALID,
    ZYDIS_REGKIND_GPR,
    ZYDIS_REGKIND_X87,
    ZYDIS_REGKIND_MMX,
    ZYDIS_REGKIND_VR,
    ZYDIS_REGKIND_TMM,
    ZYDIS_REGKIND_SEGMENT,
    ZYDIS_REGKIND_TEST,
    ZYDIS_REGKIND_CONTROL,
    ZYDIS_REGKIND_DEBUG,
    ZYDIS_REGKIND_MASK,
    ZYDIS_REGKIND_BOUND,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_REGKIND_MAX_VALUE = ZYDIS_REGKIND_BOUND,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_REGKIND_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_REGKIND_MAX_VALUE)
} ZydisRegisterKind;

/* ---------------------------------------------------------------------------------------------- */
/* Register classes                                                                               */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisRegisterClass` enum.
 *
 * Please note that this enum does not contain a matching entry for all values of the
 * `ZydisRegister` enum, but only for those registers where it makes sense to logically group them
 * for decoding/encoding purposes.
 *
 * These are mainly the registers that can be identified by an id within their corresponding
 * register-class. The `IP` and `FLAGS` values are exceptions to this rule.
 */
typedef enum ZydisRegisterClass_
{
    ZYDIS_REGCLASS_INVALID,
    /**
     * 8-bit general-purpose registers.
     */
    ZYDIS_REGCLASS_GPR8,
    /**
     * 16-bit general-purpose registers.
     */
    ZYDIS_REGCLASS_GPR16,
    /**
     * 32-bit general-purpose registers.
     */
    ZYDIS_REGCLASS_GPR32,
    /**
     * 64-bit general-purpose registers.
     */
    ZYDIS_REGCLASS_GPR64,
    /**
     * Floating point legacy registers.
     */
    ZYDIS_REGCLASS_X87,
    /**
     * Floating point multimedia registers.
     */
    ZYDIS_REGCLASS_MMX,
    /**
     * 128-bit vector registers.
     */
    ZYDIS_REGCLASS_XMM,
    /**
     * 256-bit vector registers.
     */
    ZYDIS_REGCLASS_YMM,
    /**
     * 512-bit vector registers.
     */
    ZYDIS_REGCLASS_ZMM,
    /**
     * Matrix registers.
     */
    ZYDIS_REGCLASS_TMM,
    /*
     * Flags registers.
     */
    ZYDIS_REGCLASS_FLAGS,
    /**
     * Instruction-pointer registers.
     */
    ZYDIS_REGCLASS_IP,
    /**
     * Segment registers.
     */
    ZYDIS_REGCLASS_SEGMENT,
    /**
     * Table registers.
    */
    ZYDIS_REGCLASS_TABLE,
    /**
     * Test registers.
     */
    ZYDIS_REGCLASS_TEST,
    /**
     * Control registers.
     */
    ZYDIS_REGCLASS_CONTROL,
    /**
     * Debug registers.
     */
    ZYDIS_REGCLASS_DEBUG,
    /**
     * Mask registers.
     */
    ZYDIS_REGCLASS_MASK,
    /**
     * Bound registers.
     */
    ZYDIS_REGCLASS_BOUND,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_REGCLASS_MAX_VALUE = ZYDIS_REGCLASS_BOUND,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_REGCLASS_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_REGCLASS_MAX_VALUE)
} ZydisRegisterClass;

/* ---------------------------------------------------------------------------------------------- */
/* Register width                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisRegisterWidth` data-type.
 */
typedef ZyanU16 ZydisRegisterWidth;

/* ---------------------------------------------------------------------------------------------- */
/* Register context                                                                               */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisRegisterContext` struct.
 */
typedef struct ZydisRegisterContext_
{
    /**
     * The values stored in the register context.
     */
    ZyanU64 values[ZYDIS_REGISTER_MAX_VALUE + 1];
} ZydisRegisterContext;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * @addtogroup register Register
 * Functions allowing retrieval of information about registers.
 * @{
 */

/* ---------------------------------------------------------------------------------------------- */
/* Register                                                                                       */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the register specified by the `register_class` and `id` tuple.
 *
 * @param   register_class  The register class.
 * @param   id              The register id.
 *
 * @return  The register specified by the `register_class` and `id` tuple or `ZYDIS_REGISTER_NONE`,
 *          if an invalid parameter was passed.
 */
ZYDIS_EXPORT ZydisRegister ZydisRegisterEncode(ZydisRegisterClass register_class, ZyanU8 id);

/**
 * Returns the id of the specified register.
 *
 * @param   reg The register.
 *
 * @return  The id of the specified register, or -1 if an invalid parameter was passed.
 */
ZYDIS_EXPORT ZyanI8 ZydisRegisterGetId(ZydisRegister reg);

/**
 * Returns the register-class of the specified register.
 *
 * @param   reg The register.
 *
 * @return  The register-class of the specified register.
 */
ZYDIS_EXPORT ZydisRegisterClass ZydisRegisterGetClass(ZydisRegister reg);

/**
 * Returns the width of the specified register.
 *
 * @param   mode    The active machine mode.
 * @param   reg     The register.
 *
 * @return  The width of the specified register, or `ZYDIS_REGISTER_NONE` if the register is
 *          invalid for the active machine-mode.
 */
ZYDIS_EXPORT ZydisRegisterWidth ZydisRegisterGetWidth(ZydisMachineMode mode, ZydisRegister reg);

/**
 * Returns the largest enclosing register of the given register.
 *
 * @param   mode    The active machine mode.
 * @param   reg     The register.
 *
 * @return  The largest enclosing register of the given register, or `ZYDIS_REGISTER_NONE` if the
 *          register is invalid for the active machine-mode.
 */
ZYDIS_EXPORT ZydisRegister ZydisRegisterGetLargestEnclosing(ZydisMachineMode mode,
    ZydisRegister reg);

/**
 * Returns the specified register string.
 *
 * @param   reg The register.
 *
 * @return  The register string or `ZYAN_NULL`, if an invalid register was passed.
 */
ZYDIS_EXPORT const char* ZydisRegisterGetString(ZydisRegister reg);

/**
 * Returns the specified register string as `ZydisShortString`.
 *
 * @param   reg The register.
 *
 * @return  The register string or `ZYAN_NULL`, if an invalid register was passed.
 *
 * The `buffer` of the returned struct is guaranteed to be zero-terminated in this special case.
 */
ZYDIS_EXPORT const ZydisShortString* ZydisRegisterGetStringWrapped(ZydisRegister reg);

/* ---------------------------------------------------------------------------------------------- */
/* Register class                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the width of the specified register-class.
 *
 * @param   mode            The active machine mode.
 * @param   register_class  The register class.
 *
 * @return  The width of the specified register.
 */
ZYDIS_EXPORT ZydisRegisterWidth ZydisRegisterClassGetWidth(ZydisMachineMode mode,
    ZydisRegisterClass register_class);

/* ---------------------------------------------------------------------------------------------- */

/**
 * @}
 */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_REGISTER_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Decoded operand                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Operand attributes                                                                             */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisOperandAttributes` data-type.
 */
typedef ZyanU8 ZydisOperandAttributes;

/**
 * The operand is a `MULTISOURCE4` register operand.
 *
 * This is a special register operand-type used by `4FMAPS` instructions where the given register
 * points to the first register of a register range (4 registers in total).
 *
 * Example: ZMM3 -> [ZMM3..ZMM6]
 */
#define ZYDIS_OATTRIB_IS_MULTISOURCE4   0x01 // (1 <<  0)

/* ---------------------------------------------------------------------------------------------- */
/* Memory type                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisMemoryOperandType` enum.
 */
typedef enum ZydisMemoryOperandType_
{
    ZYDIS_MEMOP_TYPE_INVALID,
    /**
     * Normal memory operand.
     */
    ZYDIS_MEMOP_TYPE_MEM,
    /**
     * The memory operand is only used for address-generation. No real memory-access is
     * caused.
     */
    ZYDIS_MEMOP_TYPE_AGEN,
    /**
     * A memory operand using `SIB` addressing form, where the index register is not used
     * in address calculation and scale is ignored. No real memory-access is caused.
     */
    ZYDIS_MEMOP_TYPE_MIB,
    /**
     * A vector `SIB` memory addressing operand (`VSIB`).
     */
    ZYDIS_MEMOP_TYPE_VSIB,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_MEMOP_TYPE_MAX_VALUE = ZYDIS_MEMOP_TYPE_VSIB,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_MEMOP_TYPE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_MEMOP_TYPE_MAX_VALUE)
} ZydisMemoryOperandType;

/* ---------------------------------------------------------------------------------------------- */
/* Decoded operand                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Extended info for register-operands.
 */
typedef struct ZydisDecodedOperandReg_
{
    /**
     * The register value.
     */
    ZydisRegister value;
} ZydisDecodedOperandReg;

/**
 * Extended info for memory-operands.
 */
typedef struct ZydisDecodedOperandMem_
{
    /**
     * The type of the memory operand.
     */
    ZydisMemoryOperandType type;
    /**
     * The segment register.
     */
    ZydisRegister segment;
    /**
     * The base register.
     */
    ZydisRegister base;
    /**
     * The index register.
     */
    ZydisRegister index;
    /**
     * The scale factor.
     */
    ZyanU8 scale;
    /**
     * Extended info for memory-operands with displacement.
     */
    struct ZydisDecodedOperandMemDisp_
    {
        /**
         * Signals, if the displacement value is used.
         */
        ZyanBool has_displacement;
        /**
         * The displacement value
         */
        ZyanI64 value;
    } disp;
} ZydisDecodedOperandMem;

/**
 * Extended info for pointer-operands.
 */
typedef struct ZydisDecodedOperandPtr_
{
    ZyanU16 segment;
    ZyanU32 offset;
} ZydisDecodedOperandPtr;

/**
 * Extended info for immediate-operands.
 */
typedef struct ZydisDecodedOperandImm_
{
    /**
     * Signals, if the immediate value is signed.
     */
    ZyanBool is_signed;
    /**
     * Signals, if the immediate value contains a relative offset. You can use
     * `ZydisCalcAbsoluteAddress` to determine the absolute address value.
     */
    ZyanBool is_relative;
    /**
     * The immediate value.
     */
    union ZydisDecodedOperandImmValue_
    {
        ZyanU64 u;
        ZyanI64 s;
    } value;
} ZydisDecodedOperandImm;

/**
 * Defines the `ZydisDecodedOperand` struct.
 */
typedef struct ZydisDecodedOperand_
{
    /**
     * The operand-id.
     */
    ZyanU8 id;
    /**
     * The visibility of the operand.
     */
    ZydisOperandVisibility visibility;
    /**
     * The operand-actions.
     */
    ZydisOperandActions actions;
    /**
     * The operand-encoding.
     */
    ZydisOperandEncoding encoding;
    /**
     * The logical size of the operand (in bits).
     */
    ZyanU16 size;
    /**
     * The element-type.
     */
    ZydisElementType element_type;
    /**
     * The size of a single element.
     */
    ZydisElementSize element_size;
    /**
     * The number of elements.
     */
    ZyanU16 element_count;
    /*
     * Additional operand attributes.
     */
    ZydisOperandAttributes attributes;
    /**
     * The type of the operand.
     */
    ZydisOperandType type;
    /*
     * Operand type specific information.
     *
     * The enabled union variant is determined by the `type` field.
     */
    union
    {
        ZydisDecodedOperandReg reg;
        ZydisDecodedOperandMem mem;
        ZydisDecodedOperandPtr ptr;
        ZydisDecodedOperandImm imm;
    };
} ZydisDecodedOperand;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Decoded instruction                                                                            */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* CPU/FPU flags                                                                                  */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisAccessedFlagsMask` data-type.
 */
typedef ZyanU32 ZydisAccessedFlagsMask;

/**
 * @defgroup decoder_cpu_flags CPU flags
 * @ingroup decoder
 *
 * Constants used for testing CPU flags accessed by an instruction.
 *
 * @{
 */

/**
 * Carry flag.
 */
#define ZYDIS_CPUFLAG_CF    (1ul <<  0)
/**
 * Parity flag.
 */
#define ZYDIS_CPUFLAG_PF    (1ul <<  2)
/**
 * Adjust flag.
 */
#define ZYDIS_CPUFLAG_AF    (1ul <<  4)
/**
 * Zero flag.
 */
#define ZYDIS_CPUFLAG_ZF    (1ul <<  6)
/**
 * Sign flag.
 */
#define ZYDIS_CPUFLAG_SF    (1ul <<  7)
/**
 * Trap flag.
 */
#define ZYDIS_CPUFLAG_TF    (1ul <<  8)
/**
 * Interrupt enable flag.
 */
#define ZYDIS_CPUFLAG_IF    (1ul <<  9)
/**
 * Direction flag.
 */
#define ZYDIS_CPUFLAG_DF    (1ul << 10)
/**
 * Overflow flag.
 */
#define ZYDIS_CPUFLAG_OF    (1ul << 11)
/**
 * I/O privilege level flag.
 */
#define ZYDIS_CPUFLAG_IOPL  (1ul << 12)
/**
 * Nested task flag.
 */
#define ZYDIS_CPUFLAG_NT    (1ul << 14)
/**
 * Resume flag.
 */
#define ZYDIS_CPUFLAG_RF    (1ul << 16)
/**
 * Virtual 8086 mode flag.
 */
#define ZYDIS_CPUFLAG_VM    (1ul << 17)
/**
 * Alignment check.
 */
#define ZYDIS_CPUFLAG_AC    (1ul << 18)
/**
 * Virtual interrupt flag.
 */
#define ZYDIS_CPUFLAG_VIF   (1ul << 19)
/**
 * Virtual interrupt pending.
 */
#define ZYDIS_CPUFLAG_VIP   (1ul << 20)
/**
 * Able to use CPUID instruction.
 */
#define ZYDIS_CPUFLAG_ID    (1ul << 21)

/**
 * @}
 */

/**
 * @defgroup decoder_fpu_flags FPU flags
 * @ingroup decoder
 *
 * Constants used for testing FPU flags accessed by an instruction.
 *
 * @{
 */

/**
 * FPU condition-code flag 0.
 */
#define ZYDIS_FPUFLAG_C0    (1ul <<  0)
/**
 * FPU condition-code flag 1.
 */
#define ZYDIS_FPUFLAG_C1    (1ul <<  1)
 /**
  * FPU condition-code flag 2.
  */
#define ZYDIS_FPUFLAG_C2    (1ul <<  2)
/**
 * FPU condition-code flag 3.
 */
#define ZYDIS_FPUFLAG_C3    (1ul <<  3)

/**
 * @}
 */

/*
 * Information about CPU/FPU flags accessed by the instruction.
 */
typedef struct ZydisAccessedFlags_
{
    /*
     * As mask containing the flags `TESTED` by the instruction.
     */
    ZydisAccessedFlagsMask tested;
    /*
     * As mask containing the flags `MODIFIED` by the instruction.
     */
    ZydisAccessedFlagsMask modified;
    /*
     * As mask containing the flags `SET_0` by the instruction.
     */
    ZydisAccessedFlagsMask set_0;
    /*
     * As mask containing the flags `SET_1` by the instruction.
     */
    ZydisAccessedFlagsMask set_1;
    /*
     * As mask containing the flags `UNDEFINED` by the instruction.
     */
    ZydisAccessedFlagsMask undefined;
} ZydisAccessedFlags;

/* ---------------------------------------------------------------------------------------------- */
/* Branch types                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisBranchType` enum.
 */
typedef enum ZydisBranchType_
{
    /**
     * The instruction is not a branch instruction.
     */
    ZYDIS_BRANCH_TYPE_NONE,
    /**
     * The instruction is a short (8-bit) branch instruction.
     */
    ZYDIS_BRANCH_TYPE_SHORT,
    /**
     * The instruction is a near (16-bit or 32-bit) branch instruction.
     */
    ZYDIS_BRANCH_TYPE_NEAR,
    /**
     * The instruction is a far (inter-segment) branch instruction.
     */
    ZYDIS_BRANCH_TYPE_FAR,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_BRANCH_TYPE_MAX_VALUE = ZYDIS_BRANCH_TYPE_FAR,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_BRANCH_TYPE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_BRANCH_TYPE_MAX_VALUE)
} ZydisBranchType;

/* ---------------------------------------------------------------------------------------------- */
/* SSE/AVX exception-class                                                                        */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisExceptionClass` enum.
 */
typedef enum ZydisExceptionClass_
{
    ZYDIS_EXCEPTION_CLASS_NONE,
    // TODO: FP Exceptions
    ZYDIS_EXCEPTION_CLASS_SSE1,
    ZYDIS_EXCEPTION_CLASS_SSE2,
    ZYDIS_EXCEPTION_CLASS_SSE3,
    ZYDIS_EXCEPTION_CLASS_SSE4,
    ZYDIS_EXCEPTION_CLASS_SSE5,
    ZYDIS_EXCEPTION_CLASS_SSE7,
    ZYDIS_EXCEPTION_CLASS_AVX1,
    ZYDIS_EXCEPTION_CLASS_AVX2,
    ZYDIS_EXCEPTION_CLASS_AVX3,
    ZYDIS_EXCEPTION_CLASS_AVX4,
    ZYDIS_EXCEPTION_CLASS_AVX5,
    ZYDIS_EXCEPTION_CLASS_AVX6,
    ZYDIS_EXCEPTION_CLASS_AVX7,
    ZYDIS_EXCEPTION_CLASS_AVX8,
    ZYDIS_EXCEPTION_CLASS_AVX11,
    ZYDIS_EXCEPTION_CLASS_AVX12,
    ZYDIS_EXCEPTION_CLASS_E1,
    ZYDIS_EXCEPTION_CLASS_E1NF,
    ZYDIS_EXCEPTION_CLASS_E2,
    ZYDIS_EXCEPTION_CLASS_E2NF,
    ZYDIS_EXCEPTION_CLASS_E3,
    ZYDIS_EXCEPTION_CLASS_E3NF,
    ZYDIS_EXCEPTION_CLASS_E4,
    ZYDIS_EXCEPTION_CLASS_E4NF,
    ZYDIS_EXCEPTION_CLASS_E5,
    ZYDIS_EXCEPTION_CLASS_E5NF,
    ZYDIS_EXCEPTION_CLASS_E6,
    ZYDIS_EXCEPTION_CLASS_E6NF,
    ZYDIS_EXCEPTION_CLASS_E7NM,
    ZYDIS_EXCEPTION_CLASS_E7NM128,
    ZYDIS_EXCEPTION_CLASS_E9NF,
    ZYDIS_EXCEPTION_CLASS_E10,
    ZYDIS_EXCEPTION_CLASS_E10NF,
    ZYDIS_EXCEPTION_CLASS_E11,
    ZYDIS_EXCEPTION_CLASS_E11NF,
    ZYDIS_EXCEPTION_CLASS_E12,
    ZYDIS_EXCEPTION_CLASS_E12NP,
    ZYDIS_EXCEPTION_CLASS_K20,
    ZYDIS_EXCEPTION_CLASS_K21,
    ZYDIS_EXCEPTION_CLASS_AMXE1,
    ZYDIS_EXCEPTION_CLASS_AMXE2,
    ZYDIS_EXCEPTION_CLASS_AMXE3,
    ZYDIS_EXCEPTION_CLASS_AMXE4,
    ZYDIS_EXCEPTION_CLASS_AMXE5,
    ZYDIS_EXCEPTION_CLASS_AMXE6,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_EXCEPTION_CLASS_MAX_VALUE = ZYDIS_EXCEPTION_CLASS_AMXE6,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_EXCEPTION_CLASS_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_EXCEPTION_CLASS_MAX_VALUE)
} ZydisExceptionClass;

/* ---------------------------------------------------------------------------------------------- */
/* AVX mask mode                                                                                  */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisMaskMode` enum.
 */
typedef enum ZydisMaskMode_
{
    ZYDIS_MASK_MODE_INVALID,
    /**
     * Masking is disabled for the current instruction (`K0` register is used).
     */
    ZYDIS_MASK_MODE_DISABLED,
    /**
     * The embedded mask register is used as a merge-mask.
     */
    ZYDIS_MASK_MODE_MERGING,
    /**
     * The embedded mask register is used as a zero-mask.
     */
    ZYDIS_MASK_MODE_ZEROING,
    /**
     * The embedded mask register is used as a control-mask (element selector).
     */
    ZYDIS_MASK_MODE_CONTROL,
    /**
     * The embedded mask register is used as a zeroing control-mask (element selector).
     */
    ZYDIS_MASK_MODE_CONTROL_ZEROING,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_MASK_MODE_MAX_VALUE = ZYDIS_MASK_MODE_CONTROL_ZEROING,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_MASK_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_MASK_MODE_MAX_VALUE)
} ZydisMaskMode;

/* ---------------------------------------------------------------------------------------------- */
/* AVX broadcast-mode                                                                             */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisBroadcastMode` enum.
 */
typedef enum ZydisBroadcastMode_
{
    ZYDIS_BROADCAST_MODE_INVALID,
    ZYDIS_BROADCAST_MODE_1_TO_2,
    ZYDIS_BROADCAST_MODE_1_TO_4,
    ZYDIS_BROADCAST_MODE_1_TO_8,
    ZYDIS_BROADCAST_MODE_1_TO_16,
    ZYDIS_BROADCAST_MODE_1_TO_32,
    ZYDIS_BROADCAST_MODE_1_TO_64,
    ZYDIS_BROADCAST_MODE_2_TO_4,
    ZYDIS_BROADCAST_MODE_2_TO_8,
    ZYDIS_BROADCAST_MODE_2_TO_16,
    ZYDIS_BROADCAST_MODE_4_TO_8,
    ZYDIS_BROADCAST_MODE_4_TO_16,
    ZYDIS_BROADCAST_MODE_8_TO_16,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_BROADCAST_MODE_MAX_VALUE = ZYDIS_BROADCAST_MODE_8_TO_16,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_BROADCAST_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_BROADCAST_MODE_MAX_VALUE)
} ZydisBroadcastMode;

/* ---------------------------------------------------------------------------------------------- */
/* AVX rounding-mode                                                                              */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisRoundingMode` enum.
 */
typedef enum ZydisRoundingMode_
{
    ZYDIS_ROUNDING_MODE_INVALID,
    /**
     * Round to nearest.
     */
    ZYDIS_ROUNDING_MODE_RN,
    /**
     * Round down.
     */
    ZYDIS_ROUNDING_MODE_RD,
    /**
     * Round up.
     */
    ZYDIS_ROUNDING_MODE_RU,
    /**
     * Round towards zero.
     */
    ZYDIS_ROUNDING_MODE_RZ,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_ROUNDING_MODE_MAX_VALUE = ZYDIS_ROUNDING_MODE_RZ,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_ROUNDING_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_ROUNDING_MODE_MAX_VALUE)
} ZydisRoundingMode;

/* ---------------------------------------------------------------------------------------------- */
/* KNC swizzle-mode                                                                               */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisSwizzleMode` enum.
 */
typedef enum ZydisSwizzleMode_
{
    ZYDIS_SWIZZLE_MODE_INVALID,
    ZYDIS_SWIZZLE_MODE_DCBA,
    ZYDIS_SWIZZLE_MODE_CDAB,
    ZYDIS_SWIZZLE_MODE_BADC,
    ZYDIS_SWIZZLE_MODE_DACB,
    ZYDIS_SWIZZLE_MODE_AAAA,
    ZYDIS_SWIZZLE_MODE_BBBB,
    ZYDIS_SWIZZLE_MODE_CCCC,
    ZYDIS_SWIZZLE_MODE_DDDD,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_SWIZZLE_MODE_MAX_VALUE = ZYDIS_SWIZZLE_MODE_DDDD,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_SWIZZLE_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_SWIZZLE_MODE_MAX_VALUE)
} ZydisSwizzleMode;

/* ---------------------------------------------------------------------------------------------- */
/* KNC conversion-mode                                                                            */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisConversionMode` enum.
 */
typedef enum ZydisConversionMode_
{
    ZYDIS_CONVERSION_MODE_INVALID,
    ZYDIS_CONVERSION_MODE_FLOAT16,
    ZYDIS_CONVERSION_MODE_SINT8,
    ZYDIS_CONVERSION_MODE_UINT8,
    ZYDIS_CONVERSION_MODE_SINT16,
    ZYDIS_CONVERSION_MODE_UINT16,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_CONVERSION_MODE_MAX_VALUE = ZYDIS_CONVERSION_MODE_UINT16,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_CONVERSION_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_CONVERSION_MODE_MAX_VALUE)
} ZydisConversionMode;

/* ---------------------------------------------------------------------------------------------- */
/* Legacy prefix type                                                                             */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisPrefixType` enum.
 */
typedef enum ZydisPrefixType_
{
    /**
     * The prefix is ignored by the instruction.
     *
     * This applies to all prefixes that are not accepted by the instruction in general or the
     * ones that are overwritten by a prefix of the same group closer to the instruction opcode.
     */
    ZYDIS_PREFIX_TYPE_IGNORED,
    /**
     * The prefix is effectively used by the instruction.
     */
    ZYDIS_PREFIX_TYPE_EFFECTIVE,
    /**
     * The prefix is used as a mandatory prefix.
     *
     * A mandatory prefix is interpreted as an opcode extension and has no further effect on the
     * instruction.
     */
    ZYDIS_PREFIX_TYPE_MANDATORY,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_PREFIX_TYPE_MAX_VALUE = ZYDIS_PREFIX_TYPE_MANDATORY,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_PREFIX_TYPE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_PREFIX_TYPE_MAX_VALUE)
} ZydisPrefixType;

// TODO: Check effective for 66/67 prefixes (currently defaults to EFFECTIVE)

/* ---------------------------------------------------------------------------------------------- */
/* Decoded instruction                                                                            */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Detailed info about the `REX` prefix.
 */
typedef struct ZydisDecodedInstructionRawRex_
{
    /**
     * 64-bit operand-size promotion.
     */
    ZyanU8 W;
    /**
     * Extension of the `ModRM.reg` field.
     */
    ZyanU8 R;
    /**
     * Extension of the `SIB.index` field.
     */
    ZyanU8 X;
    /**
     * Extension of the `ModRM.rm`, `SIB.base`, or `opcode.reg` field.
     */
    ZyanU8 B;
    /**
     * The offset of the effective `REX` byte, relative to the beginning of the
     * instruction, in bytes.
     *
     * This offset always points to the "effective" `REX` prefix (the one closest to the
     * instruction opcode), if multiple `REX` prefixes are present.
     *
     * Note that the `REX` byte can be the first byte of the instruction, which would lead
     * to an offset of `0`. Please refer to the instruction attributes to check for the
     * presence of the `REX` prefix.
     */
    ZyanU8 offset;
} ZydisDecodedInstructionRawRex;

/**
 * Detailed info about the `XOP` prefix.
 */
typedef struct ZydisDecodedInstructionRawXop_
{
    /**
     * Extension of the `ModRM.reg` field (inverted).
     */
    ZyanU8 R;
    /**
     * Extension of the `SIB.index` field (inverted).
     */
    ZyanU8 X;
    /**
     * Extension of the `ModRM.rm`, `SIB.base`, or `opcode.reg` field (inverted).
     */
    ZyanU8 B;
    /**
     * Opcode-map specifier.
     */
    ZyanU8 m_mmmm;
    /**
     * 64-bit operand-size promotion or opcode-extension.
     */
    ZyanU8 W;
    /**
     * `NDS`/`NDD` (non-destructive-source/destination) register
     * specifier (inverted).
     */
    ZyanU8 vvvv;
    /**
     * Vector-length specifier.
     */
    ZyanU8 L;
    /**
     * Compressed legacy prefix.
     */
    ZyanU8 pp;
    /**
     * The offset of the first xop byte, relative to the beginning of
     * the instruction, in bytes.
     */
    ZyanU8 offset;
} ZydisDecodedInstructionRawXop;

/**
 * Detailed info about the `VEX` prefix.
 */
typedef struct ZydisDecodedInstructionRawVex_
{
    /**
     * Extension of the `ModRM.reg` field (inverted).
     */
    ZyanU8 R;
    /**
     * Extension of the `SIB.index` field (inverted).
     */
    ZyanU8 X;
    /**
     * Extension of the `ModRM.rm`, `SIB.base`, or `opcode.reg` field (inverted).
     */
    ZyanU8 B;
    /**
     * Opcode-map specifier.
     */
    ZyanU8 m_mmmm;
    /**
     * 64-bit operand-size promotion or opcode-extension.
     */
    ZyanU8 W;
    /**
     * `NDS`/`NDD` (non-destructive-source/destination) register specifier
     *  (inverted).
     */
    ZyanU8 vvvv;
    /**
     * Vector-length specifier.
     */
    ZyanU8 L;
    /**
     * Compressed legacy prefix.
     */
    ZyanU8 pp;
    /**
     * The offset of the first `VEX` byte, relative to the beginning of the instruction, in
     * bytes.
     */
    ZyanU8 offset;
    /**
     * The size of the `VEX` prefix, in bytes.
     */
    ZyanU8 size;
} ZydisDecodedInstructionRawVex;

/**
 * Detailed info about the `EVEX` prefix.
 */
typedef struct ZydisDecodedInstructionRawEvex
{
    /**
     * Extension of the `ModRM.reg` field (inverted).
     */
    ZyanU8 R;
    /**
     * Extension of the `SIB.index/vidx` field (inverted).
     */
    ZyanU8 X;
    /**
     * Extension of the `ModRM.rm` or `SIB.base` field (inverted).
     */
    ZyanU8 B;
    /**
     * High-16 register specifier modifier (inverted).
     */
    ZyanU8 R2;
    /**
     * Opcode-map specifier.
     */
    ZyanU8 mmm;
    /**
     * 64-bit operand-size promotion or opcode-extension.
     */
    ZyanU8 W;
    /**
     * `NDS`/`NDD` (non-destructive-source/destination) register specifier
     * (inverted).
     */
    ZyanU8 vvvv;
    /**
     * Compressed legacy prefix.
     */
    ZyanU8 pp;
    /**
     * Zeroing/Merging.
     */
    ZyanU8 z;
    /**
     * Vector-length specifier or rounding-control (most significant bit).
     */
    ZyanU8 L2;
    /**
     * Vector-length specifier or rounding-control (least significant bit).
     */
    ZyanU8 L;
    /**
     * Broadcast/RC/SAE context.
     */
    ZyanU8 b;
    /**
     * High-16 `NDS`/`VIDX` register specifier.
     */
    ZyanU8 V2;
    /**
     * Embedded opmask register specifier.
     */
    ZyanU8 aaa;
    /**
     * The offset of the first evex byte, relative to the beginning of the
     * instruction, in bytes.
     */
    ZyanU8 offset;
} ZydisDecodedInstructionRawEvex;

/**
 * Detailed info about the `MVEX` prefix.
 */
typedef struct ZydisDecodedInstructionRawMvex_
{
    /**
     * Extension of the `ModRM.reg` field (inverted).
     */
    ZyanU8 R;
    /**
     * Extension of the `SIB.index/vidx` field (inverted).
     */
    ZyanU8 X;
    /**
     * Extension of the `ModRM.rm` or `SIB.base` field (inverted).
     */
    ZyanU8 B;
    /**
     * High-16 register specifier modifier (inverted).
     */
    ZyanU8 R2;
    /**
     * Opcode-map specifier.
     */
    ZyanU8 mmmm;
    /**
     * 64-bit operand-size promotion or opcode-extension.
     */
    ZyanU8 W;
    /**
     * `NDS`/`NDD` (non-destructive-source/destination) register specifier
     *  (inverted).
     */
    ZyanU8 vvvv;
    /**
     * Compressed legacy prefix.
     */
    ZyanU8 pp;
    /**
     * Non-temporal/eviction hint.
     */
    ZyanU8 E;
    /**
     * Swizzle/broadcast/up-convert/down-convert/static-rounding controls.
     */
    ZyanU8 SSS;
    /**
     * High-16 `NDS`/`VIDX` register specifier.
     */
    ZyanU8 V2;
    /**
     * Embedded opmask register specifier.
     */
    ZyanU8 kkk;
    /**
     * The offset of the first mvex byte, relative to the beginning of the
     * instruction, in bytes.
     */
    ZyanU8 offset;
} ZydisDecodedInstructionRawMvex;

/**
 * Extended info for `AVX` instructions.
 */
typedef struct ZydisDecodedInstructionAvx_
{
    /**
     * The `AVX` vector-length.
     */
    ZyanU16 vector_length;
    /**
     * Info about the embedded writemask-register (`AVX-512` and `KNC` only).
     */
    struct ZydisDecodedInstructionAvxMask_
    {
        /**
         * The masking mode.
         */
        ZydisMaskMode mode;
        /**
         * The mask register.
         */
        ZydisRegister reg;
    } mask;
    /**
     * Contains info about the `AVX` broadcast.
     */
    struct ZydisDecodedInstructionAvxBroadcast_
    {
        /**
         * Signals, if the broadcast is a static broadcast.
         *
         * This is the case for instructions with inbuilt broadcast functionality, which is
         * always active and not controlled by the `EVEX/MVEX.RC` bits.
         */
        ZyanBool is_static;
        /**
         * The `AVX` broadcast-mode.
         */
        ZydisBroadcastMode mode;
    } broadcast;
    /**
     * Contains info about the `AVX` rounding.
     */
    struct ZydisDecodedInstructionAvxRounding_
    {
        /**
         * The `AVX` rounding-mode.
         */
        ZydisRoundingMode mode;
    } rounding;
    /**
     * Contains info about the `AVX` register-swizzle (`KNC` only).
     */
    struct ZydisDecodedInstructionAvxSwizzle_
    {
        /**
         * The `AVX` register-swizzle mode.
         */
        ZydisSwizzleMode mode;
    } swizzle;
    /**
     * Contains info about the `AVX` data-conversion (`KNC` only).
     */
    struct ZydisDecodedInstructionAvxConversion_
    {
        /**
         * The `AVX` data-conversion mode.
         */
        ZydisConversionMode mode;
    } conversion;
    /**
     * Signals, if the `SAE` (suppress-all-exceptions) functionality is
     * enabled for the instruction.
     */
    ZyanBool has_sae;
    /**
     * Signals, if the instruction has a memory-eviction-hint (`KNC` only).
     */
    ZyanBool has_eviction_hint;
    // TODO: publish EVEX tuple-type and MVEX functionality
} ZydisDecodedInstructionAvx;

/**
 * Instruction meta info.
 */
typedef struct ZydisDecodedInstructionMeta_
{
    /**
     * The instruction category.
     */
    ZydisInstructionCategory category;
    /**
     * The ISA-set.
     */
    ZydisISASet isa_set;
    /**
     * The ISA-set extension.
     */
    ZydisISAExt isa_ext;
    /**
     * The branch type.
     */
    ZydisBranchType branch_type;
    /**
     * The exception class.
     */
    ZydisExceptionClass exception_class;
} ZydisDecodedInstructionMeta;

/**
 * Detailed info about different instruction-parts like `ModRM`, `SIB` or
 * encoding-prefixes.
 */
typedef struct ZydisDecodedInstructionRaw_
{
    /**
     * The number of legacy prefixes.
     */
    ZyanU8 prefix_count;
    /**
     * Detailed info about the legacy prefixes (including `REX`).
     */
    struct ZydisDecodedInstructionRawPrefixes_
    {
        /**
         * The prefix type.
         */
        ZydisPrefixType type;
        /**
         * The prefix byte.
         */
        ZyanU8 value;
    } prefixes[ZYDIS_MAX_INSTRUCTION_LENGTH];

    /*
     * Copy of the `encoding` field.
     *
     * This is here to allow the Rust bindings to treat the following union as an `enum`,
     * sparing us a lot of unsafe code. Prefer using the regular `encoding` field in C/C++ code.
     */
    ZydisInstructionEncoding encoding2;
    /*
     * Union for things from various mutually exclusive encodings.
     */
    union
    {
        ZydisDecodedInstructionRawRex rex;
        ZydisDecodedInstructionRawXop xop;
        ZydisDecodedInstructionRawVex vex;
        ZydisDecodedInstructionRawEvex evex;
        ZydisDecodedInstructionRawMvex mvex;
    };

    /**
     * Detailed info about the `ModRM` byte.
     */
    struct ZydisDecodedInstructionModRm_
    {
        /**
         * The addressing mode.
         */
        ZyanU8 mod;
        /**
         * Register specifier or opcode-extension.
         */
        ZyanU8 reg;
        /**
         * Register specifier or opcode-extension.
         */
        ZyanU8 rm;
        /**
         * The offset of the `ModRM` byte, relative to the beginning of the
         * instruction, in bytes.
         */
        ZyanU8 offset;
    } modrm;
    /**
     * Detailed info about the `SIB` byte.
     */
    struct ZydisDecodedInstructionRawSib_
    {
        /**
         * The scale factor.
         */
        ZyanU8 scale;
        /**
         * The index-register specifier.
         */
        ZyanU8 index;
        /**
         * The base-register specifier.
         */
        ZyanU8 base;
        /**
         * The offset of the `SIB` byte, relative to the beginning of the
         * instruction, in bytes.
         */
        ZyanU8 offset;
    } sib;
    /**
     * Detailed info about displacement-bytes.
     */
    struct ZydisDecodedInstructionRawDisp_
    {
        /**
         * The displacement value
         */
        ZyanI64 value;
        /**
         * The physical displacement size, in bits.
         */
        ZyanU8 size;
        // TODO: publish cd8 scale
        /**
         * The offset of the displacement data, relative to the beginning of the
         * instruction, in bytes.
         */
        ZyanU8 offset;
    } disp;
    /**
     * Detailed info about immediate-bytes.
     */
    struct ZydisDecodedInstructionRawImm_
    {
        /**
         * Signals, if the immediate value is signed.
         */
        ZyanBool is_signed;
        /**
         * Signals, if the immediate value contains a relative offset. You can use
         * `ZydisCalcAbsoluteAddress` to determine the absolute address value.
         */
        ZyanBool is_relative;
        /**
         * The immediate value.
         */
        union ZydisDecodedInstructionRawImmValue_
        {
            ZyanU64 u;
            ZyanI64 s;
        } value;
        /**
         * The physical immediate size, in bits.
         */
        ZyanU8 size;
        /**
         * The offset of the immediate data, relative to the beginning of the
         * instruction, in bytes.
         */
        ZyanU8 offset;
    } imm[2];
} ZydisDecodedInstructionRaw;

/**
 * Information about a decoded instruction.
 */
typedef struct ZydisDecodedInstruction_
{
    /**
     * The machine mode used to decode this instruction.
     */
    ZydisMachineMode machine_mode;
    /**
     * The instruction-mnemonic.
     */
    ZydisMnemonic mnemonic;
    /**
     * The length of the decoded instruction.
     */
    ZyanU8 length;
    /**
     * The instruction-encoding (`LEGACY`, `3DNOW`, `VEX`, `EVEX`, `XOP`).
     */
    ZydisInstructionEncoding encoding;
    /**
     * The opcode-map.
     */
    ZydisOpcodeMap opcode_map;
    /**
     * The instruction-opcode.
     */
    ZyanU8 opcode;
    /**
     * The stack width.
     */
    ZyanU8 stack_width;
    /**
     * The effective operand width.
     */
    ZyanU8 operand_width;
    /**
     * The effective address width.
     */
    ZyanU8 address_width;
    /**
     * The number of instruction-operands.
     *
     * Explicit and implicit operands are guaranteed to be in the front and ordered as they are
     * printed by the formatter in `Intel` mode. No assumptions can be made about the order of
     * hidden operands, except that they always located behind the explicit and implicit operands.
     */
    ZyanU8 operand_count;
    /**
     * The number of explicit (visible) instruction-operands.
     *
     * Explicit and implicit operands are guaranteed to be in the front and ordered as they are
     * printed by the formatter in `Intel` mode.
     */
    ZyanU8 operand_count_visible;
    /**
     * See @ref instruction_attributes.
     */
    ZydisInstructionAttributes attributes;
    /**
     * Information about CPU flags accessed by the instruction.
     *
     * The bits in the masks correspond to the actual bits in the `FLAGS/EFLAGS/RFLAGS`
     * register. See @ref decoder_cpu_flags.
     */
    const ZydisAccessedFlags* cpu_flags;
    /**
     * Information about FPU flags accessed by the instruction.
     *
     * See @ref decoder_fpu_flags.
     */
    const ZydisAccessedFlags* fpu_flags;
    /**
     * Extended info for `AVX` instructions.
     */
    ZydisDecodedInstructionAvx avx;
    /**
     * Meta info.
     */
    ZydisDecodedInstructionMeta meta;
    /**
     * Detailed info about different instruction-parts like `ModRM`, `SIB` or
     * encoding-prefixes.
     */
    ZydisDecodedInstructionRaw raw;
} ZydisDecodedInstruction;

/* ---------------------------------------------------------------------------------------------- */
/* Decoder context                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * The decoder context is used to preserve some internal state between subsequent decode
 * operations for THE SAME instruction.
 *
 * The context is initialized by @c ZydisDecoderDecodeInstruction and required by e.g.
 * @c ZydisDecoderDecodeOperands.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 *
 * This struct is neither ABI nor API stable!
 */
typedef struct ZydisDecoderContext_
{
    /**
     * A pointer to the internal instruction definition.
     */
    const void* definition;
    /**
     * Contains the effective operand-size index.
     *
     * 0 = 16 bit, 1 = 32 bit, 2 = 64 bit
     */
    ZyanU8 eosz_index;
    /**
     * Contains the effective address-size index.
     *
     * 0 = 16 bit, 1 = 32 bit, 2 = 64 bit
     */
    ZyanU8 easz_index;
    /**
     * Contains some cached REX/XOP/VEX/EVEX/MVEX values to provide uniform access.
     */
    struct
    {
        ZyanU8 W;
        ZyanU8 R;
        ZyanU8 X;
        ZyanU8 B;
        ZyanU8 L;
        ZyanU8 LL;
        ZyanU8 R2;
        ZyanU8 V2;
        ZyanU8 vvvv;
        ZyanU8 mask;
    } vector_unified;
    /**
     * Information about encoded operand registers.
     */
    struct
    {
        /**
         * Signals if the `modrm.mod == 3` or `reg` form is forced for the instruction.
         */
        ZyanBool is_mod_reg;
        /**
         * The final register id for the `reg` encoded register.
         */
        ZyanU8 id_reg;
        /**
         * The final register id for the `rm` encoded register.
         *
         * This value is only set, if a register is encoded in `modrm.rm`.
         */
        ZyanU8 id_rm;
        /**
         * The final register id for the `ndsndd` (`.vvvv`) encoded register.
         */
        ZyanU8 id_ndsndd;
        /**
         * The final register id for the base register.
         *
         * This value is only set, if a memory operand is encoded in `modrm.rm`.
         */
        ZyanU8 id_base;
        /**
         * The final register id for the index register.
         *
         * This value is only set, if a memory operand is encoded in `modrm.rm` and the `SIB` byte
         * is present.
         */
        ZyanU8 id_index;
    } reg_info;
    /**
     * Internal EVEX-specific information.
     */
    struct
    {
        /**
         * The EVEX tuple-type.
         */
        ZyanU8 tuple_type;
        /**
         * The EVEX element-size.
         */
        ZyanU8 element_size;
    } evex;
    /**
     * Internal MVEX-specific information.
     */
    struct
    {
        /**
         * The MVEX functionality.
         */
        ZyanU8 functionality;
    } mvex;
    /**
     * The scale factor for EVEX/MVEX compressed 8-bit displacement values.
     */
    ZyanU8 cd8_scale; // TODO: Could make sense to expose this in the ZydisDecodedInstruction
} ZydisDecoderContext;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_INSTRUCTIONINFO_H */

//
// Header: Zydis/Status.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Status code definitions and check macros.
 */

#ifndef ZYDIS_STATUS_H
#define ZYDIS_STATUS_H


//
// Header: Zycore/Status.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Decoder.h
//   - Zydis/Status.h
//

/***************************************************************************************************

  Zyan Core Library (Zyan-C)

  Original Author : Florian Bernd, Joel Hoener

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Status code definitions and check macros.
 */

#ifndef ZYCORE_STATUS_H
#define ZYCORE_STATUS_H

#ifdef __cplusplus
extern "C" {
#endif


/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines the `ZyanStatus` data type.
 */
typedef ZyanU32 ZyanStatus;

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Definition                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines a zyan status code.
 *
 * @param   error   `1`, if the status code signals an error or `0`, if not.
 * @param   module  The module id.
 * @param   code    The actual code.
 *
 * @return  The zyan status code.
 */
#define ZYAN_MAKE_STATUS(error, module, code) \
    (ZyanStatus)((((error) & 0x01u) << 31u) | (((module) & 0x7FFu) << 20u) | ((code) & 0xFFFFFu))

/* ---------------------------------------------------------------------------------------------- */
/* Checks                                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Checks if a zyan operation was successful.
 *
 * @param   status  The zyan status-code to check.
 *
 * @return  `ZYAN_TRUE`, if the operation succeeded or `ZYAN_FALSE`, if not.
 */
#define ZYAN_SUCCESS(status) \
    (!((status) & 0x80000000u))

/**
 * Checks if a zyan operation failed.
 *
 * @param   status  The zyan status-code to check.
 *
 * @return  `ZYAN_TRUE`, if the operation failed or `ZYAN_FALSE`, if not.
 */
#define ZYAN_FAILED(status) \
    ((status) & 0x80000000u)

/**
 * Checks if a zyan operation was successful and returns with the status-code, if not.
 *
 * @param   status  The zyan status-code to check.
 */
#define ZYAN_CHECK(status) \
    do \
    { \
        const ZyanStatus status_047620348 = (status); \
        if (!ZYAN_SUCCESS(status_047620348)) \
        { \
            return status_047620348; \
        } \
    } while (0)

/* ---------------------------------------------------------------------------------------------- */
/* Information                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

 /**
 * Returns the module id of a zyan status-code.
 *
 * @param   status  The zyan status-code.
 *
 * @return  The module id of the zyan status-code.
 */
#define ZYAN_STATUS_MODULE(status) \
    (((status) >> 20) & 0x7FFu)

 /**
 * Returns the code of a zyan status-code.
 *
 * @param   status  The zyan status-code.
 *
 * @return  The code of the zyan status-code.
 */
#define ZYAN_STATUS_CODE(status) \
    ((status) & 0xFFFFFu)

/* ============================================================================================== */
/* Status codes                                                                                   */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Module IDs                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * The zycore generic module id.
 */
#define ZYAN_MODULE_ZYCORE      0x001u

/**
 * The zycore arg-parse submodule id.
 */
#define ZYAN_MODULE_ARGPARSE    0x003u

/**
 * The base module id for user-defined status codes.
 */
#define ZYAN_MODULE_USER        0x3FFu

/* ---------------------------------------------------------------------------------------------- */
/* Status codes (general purpose)                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * The operation completed successfully.
 */
#define ZYAN_STATUS_SUCCESS \
    ZYAN_MAKE_STATUS(0u, ZYAN_MODULE_ZYCORE, 0x00u)

/**
 * The operation failed with an generic error.
 */
#define ZYAN_STATUS_FAILED \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x01u)

/**
 * The operation completed successfully and returned `ZYAN_TRUE`.
 */
#define ZYAN_STATUS_TRUE \
    ZYAN_MAKE_STATUS(0u, ZYAN_MODULE_ZYCORE, 0x02u)

/**
 * The operation completed successfully and returned `ZYAN_FALSE`.
 */
#define ZYAN_STATUS_FALSE \
    ZYAN_MAKE_STATUS(0u, ZYAN_MODULE_ZYCORE, 0x03u)

/**
 * An invalid argument was passed to a function.
 */
#define ZYAN_STATUS_INVALID_ARGUMENT \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x04u)

/**
 * An attempt was made to perform an invalid operation.
 */
#define ZYAN_STATUS_INVALID_OPERATION \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x05u)

/**
 * Insufficient privileges to perform the requested operation.
 */
#define ZYAN_STATUS_ACCESS_DENIED \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x06u)

/**
 * The requested entity was not found.
 */
#define ZYAN_STATUS_NOT_FOUND \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x07u)

/**
 * An index passed to a function was out of bounds.
 */
#define ZYAN_STATUS_OUT_OF_RANGE \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x08u)

/**
 * A buffer passed to a function was too small to complete the requested operation.
 */
#define ZYAN_STATUS_INSUFFICIENT_BUFFER_SIZE \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x09u)

/**
 * Insufficient memory to perform the operation.
 */
#define ZYAN_STATUS_NOT_ENOUGH_MEMORY \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x0Au)

/**
 * An unknown error occurred during a system function call.
 */
#define ZYAN_STATUS_BAD_SYSTEMCALL \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x0Bu)

/**
 * The process ran out of resources while performing an operation.
 */
#define ZYAN_STATUS_OUT_OF_RESOURCES \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x0Cu)

/**
 * A dependency library was not found or does have an unexpected version number or
 * feature-set.
 */
#define ZYAN_STATUS_MISSING_DEPENDENCY \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYCORE, 0x0Du)

/* ---------------------------------------------------------------------------------------------- */
/* Status codes (arg parse)                                                                       */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Argument was not expected.
 */
#define ZYAN_STATUS_ARG_NOT_UNDERSTOOD \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ARGPARSE, 0x00u)

/**
 * Too few arguments were provided.
 */
#define ZYAN_STATUS_TOO_FEW_ARGS \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ARGPARSE, 0x01u)

/**
 * Too many arguments were provided.
 */
#define ZYAN_STATUS_TOO_MANY_ARGS \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ARGPARSE, 0x02u)

/**
 * An argument that expected a value misses its value.
 */
#define ZYAN_STATUS_ARG_MISSES_VALUE \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ARGPARSE, 0x03u)

/**
* A required argument is missing.
*/
#define ZYAN_STATUS_REQUIRED_ARG_MISSING \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ARGPARSE, 0x04u)

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYCORE_STATUS_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Status codes                                                                                   */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Module IDs                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * The zydis module id.
 */
#define ZYAN_MODULE_ZYDIS   0x002u

/* ---------------------------------------------------------------------------------------------- */
/* Status codes                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/* ---------------------------------------------------------------------------------------------- */
/* Decoder                                                                                        */
/* ---------------------------------------------------------------------------------------------- */

/**
 * An attempt was made to read data from an input data-source that has no more
 * data available.
 */
#define ZYDIS_STATUS_NO_MORE_DATA \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x00u)

/**
 * An general error occured while decoding the current instruction. The
 * instruction might be undefined.
 */
#define ZYDIS_STATUS_DECODING_ERROR \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x01u)

/**
 * The instruction exceeded the maximum length of 15 bytes.
 */
#define ZYDIS_STATUS_INSTRUCTION_TOO_LONG \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x02u)

/**
 * The instruction encoded an invalid register.
 */
#define ZYDIS_STATUS_BAD_REGISTER \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x03u)

/**
 * A lock-prefix (F0) was found while decoding an instruction that does not
 * support locking.
 */
#define ZYDIS_STATUS_ILLEGAL_LOCK \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x04u)

/**
 * A legacy-prefix (F2, F3, 66) was found while decoding a XOP/VEX/EVEX/MVEX
 * instruction.
 */
#define ZYDIS_STATUS_ILLEGAL_LEGACY_PFX \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x05u)

/**
 * A rex-prefix was found while decoding a XOP/VEX/EVEX/MVEX instruction.
 */
#define ZYDIS_STATUS_ILLEGAL_REX \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x06u)

/**
 * An invalid opcode-map value was found while decoding a XOP/VEX/EVEX/MVEX-prefix.
 */
#define ZYDIS_STATUS_INVALID_MAP \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x07u)

/**
 * An error occured while decoding the EVEX-prefix.
 */
#define ZYDIS_STATUS_MALFORMED_EVEX \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x08u)

/**
 * An error occured while decoding the MVEX-prefix.
 */
#define ZYDIS_STATUS_MALFORMED_MVEX \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x09u)

/**
 * An invalid write-mask was specified for an EVEX/MVEX instruction.
 */
#define ZYDIS_STATUS_INVALID_MASK \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x0Au)

/* ---------------------------------------------------------------------------------------------- */
/* Formatter                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returning this status code in some specified formatter callbacks will cause
 * the formatter to omit the corresponding token.
 *
 * Valid callbacks:
 * - `ZYDIS_FORMATTER_FUNC_PRE_OPERAND`
 * - `ZYDIS_FORMATTER_FUNC_POST_OPERAND`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_REG`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_MEM`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_PTR`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_IMM`
 */
#define ZYDIS_STATUS_SKIP_TOKEN \
    ZYAN_MAKE_STATUS(0u, ZYAN_MODULE_ZYDIS, 0x0Bu)

/* ---------------------------------------------------------------------------------------------- */
/* Encoder                                                                                        */
/* ---------------------------------------------------------------------------------------------- */

#define ZYDIS_STATUS_IMPOSSIBLE_INSTRUCTION \
    ZYAN_MAKE_STATUS(1u, ZYAN_MODULE_ZYDIS, 0x0Cu)

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */


#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_STATUS_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Decoder mode                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisDecoderMode` enum.
 */
typedef enum ZydisDecoderMode_
{
    /**
     * Enables minimal instruction decoding without semantic analysis.
     *
     * This mode provides access to the mnemonic, the instruction-length, the effective
     * operand-size, the effective address-width, some attributes (e.g. `ZYDIS_ATTRIB_IS_RELATIVE`)
     * and all of the information in the `raw` field of the `ZydisDecodedInstruction` struct.
     *
     * Operands, most attributes and other specific information (like `AVX` info) are not
     * accessible in this mode.
     *
     * This mode is NOT enabled by default.
     */
    ZYDIS_DECODER_MODE_MINIMAL,
    /**
     * Enables the `AMD`-branch mode.
     *
     * Intel ignores the operand-size override-prefix (`0x66`) for all branches with 32-bit
     * immediates and forces the operand-size of the instruction to 64-bit in 64-bit mode.
     * In `AMD`-branch mode `0x66` is not ignored and changes the operand-size and the size of the
     * immediate to 16-bit.
     *
     * This mode is NOT enabled by default.
     */
    ZYDIS_DECODER_MODE_AMD_BRANCHES,
    /**
     * Enables `KNC` compatibility-mode.
     *
     * `KNC` and `KNL+` chips are sharing opcodes and encodings for some mask-related instructions.
     * Enable this mode to use the old `KNC` specifications (different mnemonics, operands, ..).
     *
     * This mode is NOT enabled by default.
     */
    ZYDIS_DECODER_MODE_KNC,
    /**
     * Enables the `MPX` mode.
     *
     * The `MPX` isa-extension reuses (overrides) some of the widenop instruction opcodes.
     *
     * This mode is enabled by default.
     */
    ZYDIS_DECODER_MODE_MPX,
    /**
     * Enables the `CET` mode.
     *
     * The `CET` isa-extension reuses (overrides) some of the widenop instruction opcodes.
     *
     * This mode is enabled by default.
     */
    ZYDIS_DECODER_MODE_CET,
    /**
     * Enables the `LZCNT` mode.
     *
     * The `LZCNT` isa-extension reuses (overrides) some of the widenop instruction opcodes.
     *
     * This mode is enabled by default.
     */
    ZYDIS_DECODER_MODE_LZCNT,
    /**
     * Enables the `TZCNT` mode.
     *
     * The `TZCNT` isa-extension reuses (overrides) some of the widenop instruction opcodes.
     *
     * This mode is enabled by default.
     */
    ZYDIS_DECODER_MODE_TZCNT,
    /**
     * Enables the `WBNOINVD` mode.
     *
     * The `WBINVD` instruction is interpreted as `WBNOINVD` on ICL chips, if a `F3` prefix is
     * used.
     *
     * This mode is disabled by default.
     */
    ZYDIS_DECODER_MODE_WBNOINVD,
     /**
     * Enables the `CLDEMOTE` mode.
     *
     * The `CLDEMOTE` isa-extension reuses (overrides) some of the widenop instruction opcodes.
     *
     * This mode is enabled by default.
     */
    ZYDIS_DECODER_MODE_CLDEMOTE,
    /**
     * Enables the `IPREFETCH` mode.
     *
     * The `IPREFETCH` isa-extension reuses (overrides) some of the widenop instruction opcodes.
     *
     * This mode is enabled by default.
     */
    ZYDIS_DECODER_MODE_IPREFETCH,
    /**
     * Enables the `UD0` compatibility mode.
     *
     * Some processors decode the `UD0` instruction without a ModR/M byte. Enable this decoder mode
     * to mimic this behavior.
     *
     * This mode is disabled by default.
     */
    ZYDIS_DECODER_MODE_UD0_COMPAT,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_DECODER_MODE_MAX_VALUE = ZYDIS_DECODER_MODE_UD0_COMPAT,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_DECODER_MODE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_DECODER_MODE_MAX_VALUE)
} ZydisDecoderMode;

/* ---------------------------------------------------------------------------------------------- */
/* Decoder struct                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisDecoder` struct.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZydisDecoder_
{
    /**
     * The machine mode.
     */
    ZydisMachineMode machine_mode;
    /**
     * The stack width.
     */
    ZydisStackWidth stack_width;
    /**
     * The decoder mode bitmap.
     */
    ZyanU32 decoder_mode;
} ZydisDecoder;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * @addtogroup decoder Decoder
 * Functions allowing decoding of instruction bytes to a machine interpretable struct.
 * @{
 */

/**
 * Initializes the given `ZydisDecoder` instance.
 *
 * @param   decoder         A pointer to the `ZydisDecoder` instance.
 * @param   machine_mode    The machine mode.
 * @param   stack_width     The stack width.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisDecoderInit(ZydisDecoder* decoder, ZydisMachineMode machine_mode,
    ZydisStackWidth stack_width);

/**
 * Enables or disables the specified decoder-mode.
 *
 * @param   decoder A pointer to the `ZydisDecoder` instance.
 * @param   mode    The decoder mode.
 * @param   enabled `ZYAN_TRUE` to enable, or `ZYAN_FALSE` to disable the specified decoder-mode.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisDecoderEnableMode(ZydisDecoder* decoder, ZydisDecoderMode mode,
    ZyanBool enabled);

/**
 * Decodes the instruction in the given input `buffer` and returns all details (e.g. operands).
 *
 * @param   decoder         A pointer to the `ZydisDecoder` instance.
 * @param   buffer          A pointer to the input buffer.
 * @param   length          The length of the input buffer. Note that this can be bigger than the
 *                          actual size of the instruction -- you don't have to know the size up
 *                          front. This length is merely used to prevent Zydis from doing
 *                          out-of-bounds reads on your buffer.
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct receiving the details
 *                          about the decoded instruction.
 * @param   operands        A pointer to an array with `ZYDIS_MAX_OPERAND_COUNT` entries that
 *                          receives the decoded operands. The number of operands decoded is
 *                          determined by the `instruction.operand_count` field. Excess entries are
 *                          zeroed.
 *
 * This is a convenience function that combines the following functions into one call:
 *
 *   - `ZydisDecoderDecodeInstruction`
 *   - `ZydisDecoderDecodeOperands`
 *
 * Please refer to `ZydisDecoderDecodeInstruction` if operand decoding is not required or should
 * be done separately (`ZydisDecoderDecodeOperands`).
 *
 * This function is not available in MINIMAL_MODE.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisDecoderDecodeFull(const ZydisDecoder* decoder,
    const void* buffer, ZyanUSize length, ZydisDecodedInstruction* instruction,
    ZydisDecodedOperand operands[ZYDIS_MAX_OPERAND_COUNT]);

/**
 * Decodes the instruction in the given input `buffer`.
 *
 * @param   decoder     A pointer to the `ZydisDecoder` instance.
 * @param   context     A pointer to a decoder context struct which is required for further
 *                      decoding (e.g. operand decoding using `ZydisDecoderDecodeOperands`) or
 *                      `ZYAN_NULL` if not needed.
 * @param   buffer      A pointer to the input buffer.
 * @param   length      The length of the input buffer. Note that this can be bigger than the
 *                      actual size of the instruction -- you don't have to know the size up
 *                      front. This length is merely used to prevent Zydis from doing
 *                      out-of-bounds reads on your buffer.
 * @param   instruction A pointer to the `ZydisDecodedInstruction` struct, that receives the
 *                      details about the decoded instruction.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisDecoderDecodeInstruction(const ZydisDecoder* decoder,
    ZydisDecoderContext* context, const void* buffer, ZyanUSize length,
    ZydisDecodedInstruction* instruction);

/**
 * Decodes the instruction operands.
 *
 * @param   decoder         A pointer to the `ZydisDecoder` instance.
 * @param   context         A pointer to the `ZydisDecoderContext` struct.
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operands        The array that receives the decoded operands.
 *                          Refer to `ZYDIS_MAX_OPERAND_COUNT` or `ZYDIS_MAX_OPERAND_COUNT_VISIBLE`
 *                          when allocating space for the array to ensure that the buffer size is
 *                          sufficient to always fit all instruction operands.
 *                          Refer to `instruction.operand_count` or
 *                          `instruction.operand_count_visible' when allocating space for the array
 *                          to ensure that the buffer size is sufficient to fit all operands of
 *                          the given instruction.
 * @param   operand_count   The length of the `operands` array.
 *                          This argument as well limits the maximum amount of operands to decode.
 *                          If this value is `0`, no operands will be decoded and `ZYAN_NULL` will
 *                          be accepted for the `operands` argument.
 *
 * This function fails, if `operand_count` is larger than the total number of operands for the
 * given instruction (`instruction.operand_count`).
 *
 * This function is not available in MINIMAL_MODE.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisDecoderDecodeOperands(const ZydisDecoder* decoder,
    const ZydisDecoderContext* context, const ZydisDecodedInstruction* instruction,
    ZydisDecodedOperand* operands, ZyanU8 operand_count);

/** @} */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_DECODER_H */
#endif

#if !defined(ZYDIS_DISABLE_ENCODER)

//
// Header: Zydis/Encoder.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Mappa

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Functions for encoding instructions.
 */

#ifndef ZYDIS_ENCODER_H
#define ZYDIS_ENCODER_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Constants                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Maximum number of encodable (explicit and implicit) operands
 */
#define ZYDIS_ENCODER_MAX_OPERANDS 5

// If asserts are failing here remember to update encoder table generator before fixing asserts
ZYAN_STATIC_ASSERT(ZYAN_BITS_TO_REPRESENT(ZYDIS_ENCODER_MAX_OPERANDS) == 3);

/**
 * Combination of all user-encodable prefixes
 */
#define ZYDIS_ENCODABLE_PREFIXES   (ZYDIS_ATTRIB_HAS_LOCK | \
                                    ZYDIS_ATTRIB_HAS_REP | \
                                    ZYDIS_ATTRIB_HAS_REPE | \
                                    ZYDIS_ATTRIB_HAS_REPNE | \
                                    ZYDIS_ATTRIB_HAS_BND | \
                                    ZYDIS_ATTRIB_HAS_XACQUIRE | \
                                    ZYDIS_ATTRIB_HAS_XRELEASE | \
                                    ZYDIS_ATTRIB_HAS_BRANCH_NOT_TAKEN | \
                                    ZYDIS_ATTRIB_HAS_BRANCH_TAKEN | \
                                    ZYDIS_ATTRIB_HAS_NOTRACK | \
                                    ZYDIS_ATTRIB_HAS_SEGMENT_CS | \
                                    ZYDIS_ATTRIB_HAS_SEGMENT_SS | \
                                    ZYDIS_ATTRIB_HAS_SEGMENT_DS | \
                                    ZYDIS_ATTRIB_HAS_SEGMENT_ES | \
                                    ZYDIS_ATTRIB_HAS_SEGMENT_FS | \
                                    ZYDIS_ATTRIB_HAS_SEGMENT_GS)

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines possible physical instruction encodings as bit flags, so multiple acceptable encodings
 * can be specified simultaneously.
 */
typedef enum ZydisEncodableEncoding_
{
    ZYDIS_ENCODABLE_ENCODING_DEFAULT                = 0x00000000,
    ZYDIS_ENCODABLE_ENCODING_LEGACY                 = 0x00000001,
    ZYDIS_ENCODABLE_ENCODING_3DNOW                  = 0x00000002,
    ZYDIS_ENCODABLE_ENCODING_XOP                    = 0x00000004,
    ZYDIS_ENCODABLE_ENCODING_VEX                    = 0x00000008,
    ZYDIS_ENCODABLE_ENCODING_EVEX                   = 0x00000010,
    ZYDIS_ENCODABLE_ENCODING_MVEX                   = 0x00000020,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_ENCODABLE_ENCODING_MAX_VALUE              = (ZYDIS_ENCODABLE_ENCODING_MVEX |
                                                       (ZYDIS_ENCODABLE_ENCODING_MVEX - 1)),
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_ENCODABLE_ENCODING_REQUIRED_BITS          =
        ZYAN_BITS_TO_REPRESENT(ZYDIS_ENCODABLE_ENCODING_MAX_VALUE)
} ZydisEncodableEncoding;

/**
 * Defines encodable physical/effective sizes of relative immediate operands. See
 * `ZydisEncoderRequest.branch_width` for more details.
 */
typedef enum ZydisBranchWidth_
{
    ZYDIS_BRANCH_WIDTH_NONE,
    ZYDIS_BRANCH_WIDTH_8,
    ZYDIS_BRANCH_WIDTH_16,
    ZYDIS_BRANCH_WIDTH_32,
    ZYDIS_BRANCH_WIDTH_64,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_BRANCH_WIDTH_MAX_VALUE = ZYDIS_BRANCH_WIDTH_64,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_BRANCH_WIDTH_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_BRANCH_WIDTH_MAX_VALUE)
} ZydisBranchWidth;

/**
 * Defines possible values for address size hints. See `ZydisEncoderRequest` for more information
 * about address size hints.
 */
typedef enum ZydisAddressSizeHint_
{
    ZYDIS_ADDRESS_SIZE_HINT_NONE,
    ZYDIS_ADDRESS_SIZE_HINT_16,
    ZYDIS_ADDRESS_SIZE_HINT_32,
    ZYDIS_ADDRESS_SIZE_HINT_64,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_ADDRESS_SIZE_HINT_MAX_VALUE = ZYDIS_ADDRESS_SIZE_HINT_64,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_ADDRESS_SIZE_HINT_REQUIRED_BITS =
        ZYAN_BITS_TO_REPRESENT(ZYDIS_ADDRESS_SIZE_HINT_MAX_VALUE)
} ZydisAddressSizeHint;

/**
 * Defines possible values for operand size hints. See `ZydisEncoderRequest` for more information
 * about operand size hints.
 */
typedef enum ZydisOperandSizeHint_
{
    ZYDIS_OPERAND_SIZE_HINT_NONE,
    ZYDIS_OPERAND_SIZE_HINT_8,
    ZYDIS_OPERAND_SIZE_HINT_16,
    ZYDIS_OPERAND_SIZE_HINT_32,
    ZYDIS_OPERAND_SIZE_HINT_64,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_OPERAND_SIZE_HINT_MAX_VALUE = ZYDIS_OPERAND_SIZE_HINT_64,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_OPERAND_SIZE_HINT_REQUIRED_BITS =
        ZYAN_BITS_TO_REPRESENT(ZYDIS_OPERAND_SIZE_HINT_MAX_VALUE)
} ZydisOperandSizeHint;

/**
 * Describes explicit or implicit instruction operand.
 */
typedef struct ZydisEncoderOperand_
{
    /**
     * The type of the operand.
     */
    ZydisOperandType type;
    /**
     * Extended info for register-operands.
     */
    struct ZydisEncoderOperandReg_
    {
        /**
         * The register value.
         */
        ZydisRegister value;
        /**
         * Is this 4th operand (`VEX`/`XOP`). Despite its name, `is4` encoding can sometimes be
         * applied to 3rd operand instead of 4th. This field is used to resolve such ambiguities.
         * For all other operands it should be set to `ZYAN_FALSE`.
         */
        ZyanBool is4;
    } reg;
    /**
     * Extended info for memory-operands.
     */
    struct ZydisEncoderOperandMem_
    {
        /**
         * The base register.
         */
        ZydisRegister base;
        /**
         * The index register.
         */
        ZydisRegister index;
        /**
         * The scale factor.
         */
        ZyanU8 scale;
        /**
         * The displacement value. This value is always treated as 64-bit signed integer, so it's
         * important to take this into account when specifying absolute addresses. For example
         * to specify a 16-bit address 0x8000 in 16-bit mode it should be sign extended to
         * `0xFFFFFFFFFFFF8000`. See `address_size_hint` for more information about absolute
         * addresses.
         */
        ZyanI64 displacement;
        /**
         * Size of this operand in bytes.
         */
        ZyanU16 size;
    } mem;
    /**
     * Extended info for pointer-operands.
     */
    struct ZydisEncoderOperandPtr_
    {
        /**
         * The segment value.
         */
        ZyanU16 segment;
        /**
         * The offset value.
         */
        ZyanU32 offset;
    } ptr;
    /**
     * Extended info for immediate-operands.
     */
    union ZydisEncoderOperandImm_
    {
        /**
         * The unsigned immediate value.
         */
        ZyanU64 u;
        /**
         * The signed immediate value.
         */
        ZyanI64 s;
    } imm;
} ZydisEncoderOperand;

/**
 * Main structure consumed by the encoder. It represents full semantics of an instruction.
 */
typedef struct ZydisEncoderRequest_
{
    /**
     * The machine mode used to encode this instruction.
     */
    ZydisMachineMode machine_mode;
    /**
     * This optional field can be used to restrict allowed physical encodings for desired
     * instruction. Some mnemonics can be supported by more than one encoding, so this field can
     * resolve ambiguities e.g. you can disable `AVX-512` extensions by prohibiting usage of `EVEX`
     * prefix and allow only `VEX` variants.
     */
    ZydisEncodableEncoding allowed_encodings;
    /**
     * The instruction-mnemonic.
     */
    ZydisMnemonic mnemonic;
    /**
     * A combination of requested encodable prefixes (`ZYDIS_ATTRIB_HAS_*` flags) for desired
     * instruction. See `ZYDIS_ENCODABLE_PREFIXES` for list of available prefixes.
     */
    ZydisInstructionAttributes prefixes;
    /**
     * Branch type (required for branching instructions only). Use `ZYDIS_BRANCH_TYPE_NONE` to let
     * encoder pick size-optimal branch type automatically (`short` and `near` are prioritized over
     * `far`).
     */
    ZydisBranchType branch_type;
    /**
     * Specifies physical size for relative immediate operands. Use `ZYDIS_BRANCH_WIDTH_NONE` to
     * let encoder pick size-optimal branch width automatically. For segment:offset `far` branches
     * this field applies to physical size of the offset part. For branching instructions without
     * relative operands this field affects effective operand size attribute.
     */
    ZydisBranchWidth branch_width;
    /**
     * Optional address size hint used to resolve ambiguities for some instructions. Generally
     * encoder deduces address size from `ZydisEncoderOperand` structures that represent
     * explicit and implicit operands. This hint resolves conflicts when instruction's hidden
     * operands scale with address size attribute.
     *
     * This hint is also used for instructions with absolute memory addresses (memory operands with
     * displacement and no registers). Since displacement field is a 64-bit signed integer it's not
     * possible to determine actual size of the address value in all situations. This hint
     * specifies size of the address value provided inside encoder request rather than desired
     * address size attribute of encoded instruction. Use `ZYDIS_ADDRESS_SIZE_HINT_NONE` to assume
     * address size default for specified machine mode.
     */
    ZydisAddressSizeHint address_size_hint;
    /**
     * Optional operand size hint used to resolve ambiguities for some instructions. Generally
     * encoder deduces operand size from `ZydisEncoderOperand` structures that represent
     * explicit and implicit operands. This hint resolves conflicts when instruction's hidden
     * operands scale with operand size attribute.
     */
    ZydisOperandSizeHint operand_size_hint;
    /**
     * The number of instruction-operands.
     */
    ZyanU8 operand_count;
    /**
     * Detailed info for all explicit and implicit instruction operands.
     */
    ZydisEncoderOperand operands[ZYDIS_ENCODER_MAX_OPERANDS];
    /**
     * Extended info for `EVEX` instructions.
     */
    struct ZydisEncoderRequestEvexFeatures_
    {
        /**
         * The broadcast-mode. Specify `ZYDIS_BROADCAST_MODE_INVALID` for instructions with
         * static broadcast functionality.
         */
        ZydisBroadcastMode broadcast;
        /**
         * The rounding-mode.
         */
        ZydisRoundingMode rounding;
        /**
         * Signals, if the `SAE` (suppress-all-exceptions) functionality should be enabled for
         * the instruction.
         */
        ZyanBool sae;
        /**
         * Signals, if the zeroing-mask functionality should be enabled for the instruction.
         * Specify `ZYAN_TRUE` for instructions with forced zeroing mask.
         */
        ZyanBool zeroing_mask;
    } evex;
    /**
     * Extended info for `MVEX` instructions.
     */
    struct ZydisEncoderRequestMvexFeatures_
    {
        /**
         * The broadcast-mode.
         */
        ZydisBroadcastMode broadcast;
        /**
         * The data-conversion mode.
         */
        ZydisConversionMode conversion;
        /**
         * The rounding-mode.
         */
        ZydisRoundingMode rounding;
        /**
         * The `AVX` register-swizzle mode.
         */
        ZydisSwizzleMode swizzle;
        /**
         * Signals, if the `SAE` (suppress-all-exceptions) functionality is enabled for
         * the instruction.
         */
        ZyanBool sae;
        /**
         * Signals, if the instruction has a memory-eviction-hint (`KNC` only).
         */
        ZyanBool eviction_hint;
    } mvex;
} ZydisEncoderRequest;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * @addtogroup encoder Encoder
 * Functions allowing encoding of instruction bytes from a machine interpretable struct.
 * @{
 */

/**
 * Encodes instruction with semantics specified in encoder request structure.
 *
 * @param   request     A pointer to the `ZydisEncoderRequest` struct.
 * @param   buffer      A pointer to the output buffer receiving encoded instruction.
 * @param   length      A pointer to the variable containing length of the output buffer. Upon
 *                      successful return this variable receives length of the encoded instruction.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisEncoderEncodeInstruction(const ZydisEncoderRequest *request,
    void *buffer, ZyanUSize *length);

/**
 * Encodes instruction with semantics specified in encoder request structure. This function expects
 * absolute addresses inside encoder request instead of `EIP`/`RIP`-relative values. Function
 * predicts final instruction length prior to encoding and writes back calculated relative operands
 * to provided encoder request.
 *
 * @param   request         A pointer to the `ZydisEncoderRequest` struct.
 * @param   buffer          A pointer to the output buffer receiving encoded instruction.
 * @param   length          A pointer to the variable containing length of the output buffer. Upon
 *                          successful return this variable receives length of the encoded
 *                          instruction.
 * @param   runtime_address The runtime address of the instruction.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisEncoderEncodeInstructionAbsolute(ZydisEncoderRequest *request,
    void *buffer, ZyanUSize *length, ZyanU64 runtime_address);

/**
 * Converts decoded instruction to encoder request that can be passed to
 * `ZydisEncoderEncodeInstruction`.
 *
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operands        A pointer to the decoded operands.
 * @param   operand_count   The operand count.
 * @param   request         A pointer to the `ZydisEncoderRequest` struct, that receives
 *                          information necessary for encoder to re-encode the instruction.
 *
 * This function performs simple structure conversion and does minimal sanity checks on the
 * input. There's no guarantee that produced request will be accepted by
 * `ZydisEncoderEncodeInstruction` if malformed `ZydisDecodedInstruction` or malformed
 * `ZydisDecodedOperands` is passed to this function.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisEncoderDecodedInstructionToEncoderRequest(
    const ZydisDecodedInstruction* instruction, const ZydisDecodedOperand* operands,
    ZyanU8 operand_count, ZydisEncoderRequest* request);

/**
 * Fills provided buffer with `NOP` instructions using longest possible multi-byte instructions.
 *
 * @param   buffer  A pointer to the output buffer receiving encoded instructions.
 * @param   length  Size of the output buffer.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisEncoderNopFill(void *buffer, ZyanUSize length);

/** @} */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_ENCODER_H */
#endif

#if !defined(ZYDIS_DISABLE_FORMATTER)

//
// Header: Zydis/Formatter.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Functions for formatting instructions to human-readable text.
 */

#ifndef ZYDIS_FORMATTER_H
#define ZYDIS_FORMATTER_H


//
// Header: Zycore/String.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Formatter.h
//

/***************************************************************************************************

  Zyan Core Library (Zycore-C)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Implements a string type.
 */

#ifndef ZYCORE_STRING_H
#define ZYCORE_STRING_H


//
// Header: Zycore/Allocator.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Formatter.h
//   - Zycore/String.h
//

/***************************************************************************************************

  Zyan Core Library (Zycore-C)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * @brief
 */

#ifndef ZYCORE_ALLOCATOR_H
#define ZYCORE_ALLOCATOR_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

struct ZyanAllocator_;

/**
 * Defines the `ZyanAllocatorAllocate` function prototype.
 *
 * @param   allocator       A pointer to the `ZyanAllocator` instance.
 * @param   p               Receives a pointer to the first memory block sufficient to hold an
 *                          array of `n` elements with a size of `element_size`.
 * @param   element_size    The size of a single element.
 * @param   n               The number of elements to allocate storage for.
 *
 * @return  A zyan status code.
 *
 * This prototype is used for the `allocate()` and `reallocate()` functions.
 *
 * The result of the `reallocate()` function is undefined, if `p` does not point to a memory block
 * previously obtained by `(re-)allocate()`.
 */
typedef ZyanStatus (*ZyanAllocatorAllocate)(struct ZyanAllocator_* allocator, void** p,
    ZyanUSize element_size, ZyanUSize n);

/**
 * Defines the `ZyanAllocatorDeallocate` function prototype.
 *
 * @param   allocator       A pointer to the `ZyanAllocator` instance.
 * @param   p               The pointer obtained from `(re-)allocate()`.
 * @param   element_size    The size of a single element.
 * @param   n               The number of elements earlier passed to `(re-)allocate()`.
 *
  * @return  A zyan status code.
 */
typedef ZyanStatus (*ZyanAllocatorDeallocate)(struct ZyanAllocator_* allocator, void* p,
    ZyanUSize element_size, ZyanUSize n);

/**
 * Defines the `ZyanAllocator` struct.
 *
 * This is the base class for all custom allocator implementations.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZyanAllocator_
{
    /**
     * The allocate function.
     */
    ZyanAllocatorAllocate allocate;
    /**
     * The reallocate function.
     */
    ZyanAllocatorAllocate reallocate;
    /**
     * The deallocate function.
     */
    ZyanAllocatorDeallocate deallocate;
} ZyanAllocator;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * Initializes the given `ZyanAllocator` instance.
 *
 * @param   allocator   A pointer to the `ZyanAllocator` instance.
 * @param   allocate    The allocate function.
 * @param   reallocate  The reallocate function.
 * @param   deallocate  The deallocate function.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanAllocatorInit(ZyanAllocator* allocator, ZyanAllocatorAllocate allocate,
    ZyanAllocatorAllocate reallocate, ZyanAllocatorDeallocate deallocate);

#ifndef ZYAN_NO_LIBC

/**
 * Returns the default `ZyanAllocator` instance.
 *
 * @return  A pointer to the default `ZyanAllocator` instance.
 *
 * The default allocator uses the default memory manager to allocate memory on the heap.
 *
 * You should in no case modify the returned allocator instance to avoid unexpected behavior.
 */
ZYCORE_EXPORT ZYAN_REQUIRES_LIBC ZyanAllocator* ZyanAllocatorDefault(void);

#endif // ZYAN_NO_LIBC

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYCORE_ALLOCATOR_H */

//
// Header: Zycore/Vector.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Formatter.h
//   - Zycore/String.h
//

/***************************************************************************************************

  Zyan Core Library (Zycore-C)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Implements the vector container class.
 */

#ifndef ZYCORE_VECTOR_H
#define ZYCORE_VECTOR_H


//
// Header: Zycore/Comparison.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Formatter.h
//   - Zycore/String.h
//   - Zycore/Vector.h
//

/***************************************************************************************************

  Zyan Core Library (Zycore-C)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Defines prototypes of general-purpose comparison functions.
 */

#ifndef ZYCORE_COMPARISON_H
#define ZYCORE_COMPARISON_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines the `ZyanEqualityComparison` function prototype.
 *
 * @param   left    A pointer to the first element.
 * @param   right   A pointer to the second element.
 *
 * @return  This function should return `ZYAN_TRUE` if the `left` element equals the `right` one
 *          or `ZYAN_FALSE`, if not.
 */
typedef ZyanBool (*ZyanEqualityComparison)(const void* left, const void* right);

/**
 * Defines the `ZyanComparison` function prototype.
 *
 * @param   left    A pointer to the first element.
 * @param   right   A pointer to the second element.
 *
 * @return  This function should return values in the following range:
 *          `left == right -> result == 0`
 *          `left <  right -> result  < 0`
 *          `left >  right -> result  > 0`
 */
typedef ZyanI32 (*ZyanComparison)(const void* left, const void* right);

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Equality comparison functions                                                                  */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Declares a generic equality comparison function for an integral data-type.
 *
 * @param   name    The name of the function.
 * @param   type    The name of the integral data-type.
 */
#define ZYAN_DECLARE_EQUALITY_COMPARISON(name, type) \
    ZyanBool name(const type* left, const type* right) \
    { \
        ZYAN_ASSERT(left); \
        ZYAN_ASSERT(right); \
        \
        return (*left == *right) ? ZYAN_TRUE : ZYAN_FALSE; \
    }

/**
 * Declares a generic equality comparison function that compares a single integral
 *          data-type field of a struct.
 *
 * @param   name        The name of the function.
 * @param   type        The name of the integral data-type.
 * @param   field_name  The name of the struct field.
 */
#define ZYAN_DECLARE_EQUALITY_COMPARISON_FOR_FIELD(name, type, field_name) \
    ZyanBool name(const type* left, const type* right) \
    { \
        ZYAN_ASSERT(left); \
        ZYAN_ASSERT(right); \
        \
        return (left->field_name == right->field_name) ? ZYAN_TRUE : ZYAN_FALSE; \
    }

/* ---------------------------------------------------------------------------------------------- */
/* Comparison functions                                                                           */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Declares a generic comparison function for an integral data-type.
 *
 * @param   name    The name of the function.
 * @param   type    The name of the integral data-type.
 */
#define ZYAN_DECLARE_COMPARISON(name, type) \
    ZyanI32 name(const type* left, const type* right) \
    { \
        ZYAN_ASSERT(left); \
        ZYAN_ASSERT(right); \
        \
        if (*left < *right) \
        { \
            return -1; \
        } \
        if (*left > *right) \
        { \
            return  1; \
        } \
        return 0; \
    }

/**
 * Declares a generic comparison function that compares a single integral data-type field
 *          of a struct.
 *
 * @param   name        The name of the function.
 * @param   type        The name of the integral data-type.
 * @param   field_name  The name of the struct field.
 */
#define ZYAN_DECLARE_COMPARISON_FOR_FIELD(name, type, field_name) \
    ZyanI32 name(const type* left, const type* right) \
    { \
        ZYAN_ASSERT(left); \
        ZYAN_ASSERT(right); \
        \
        if (left->field_name < right->field_name) \
        { \
            return -1; \
        } \
        if (left->field_name > right->field_name) \
        { \
            return  1; \
        } \
        return 0; \
    }

 /* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Default equality comparison functions                                                          */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines a default equality comparison function for pointer values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `ZYAN_TRUE` if the `left` value equals the `right` one or `ZYAN_FALSE`, if
 *          not.
 */
ZYAN_INLINE ZYAN_DECLARE_EQUALITY_COMPARISON(ZyanEqualsPointer, void* const)

/**
 * Defines a default equality comparison function for `ZyanBool` values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `ZYAN_TRUE` if the `left` value equals the `right` one or `ZYAN_FALSE`, if
 *          not.
 */
ZYAN_INLINE ZYAN_DECLARE_EQUALITY_COMPARISON(ZyanEqualsBool, ZyanBool)

/**
 * Defines a default equality comparison function for 8-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `ZYAN_TRUE` if the `left` value equals the `right` one or `ZYAN_FALSE`, if
 *          not.
 */
ZYAN_INLINE ZYAN_DECLARE_EQUALITY_COMPARISON(ZyanEqualsNumeric8, ZyanU8)

/**
 * Defines a default equality comparison function for 16-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `ZYAN_TRUE` if the `left` value equals the `right` one or `ZYAN_FALSE`, if
 *          not.
 */
ZYAN_INLINE ZYAN_DECLARE_EQUALITY_COMPARISON(ZyanEqualsNumeric16, ZyanU16)

/**
 * Defines a default equality comparison function for 32-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `ZYAN_TRUE` if the `left` value equals the `right` one or `ZYAN_FALSE`, if
 *          not.
 */
ZYAN_INLINE ZYAN_DECLARE_EQUALITY_COMPARISON(ZyanEqualsNumeric32, ZyanU32)

/**
 * Defines a default equality comparison function for 64-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `ZYAN_TRUE` if the `left` value equals the `right` one or `ZYAN_FALSE`, if
 *          not.
 */
ZYAN_INLINE ZYAN_DECLARE_EQUALITY_COMPARISON(ZyanEqualsNumeric64, ZyanU64)

/* ---------------------------------------------------------------------------------------------- */
/* Default comparison functions                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines a default comparison function for pointer values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `0` if the `left` value equals the `right` one, `-1` if the `left` value is
 *          less than the `right` one, or `1` if the `left` value is greater than the `right` one.
 */
ZYAN_INLINE ZYAN_DECLARE_COMPARISON(ZyanComparePointer, void* const)

/**
 * Defines a default comparison function for `ZyanBool` values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `0` if the `left` value equals the `right` one, `-1` if the `left` value is
 *          less than the `right` one, or `1` if the `left` value is greater than the `right` one.
 */
ZYAN_INLINE ZYAN_DECLARE_COMPARISON(ZyanCompareBool, ZyanBool)

/**
 * Defines a default comparison function for 8-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `0` if the `left` value equals the `right` one, `-1` if the `left` value is
 *          less than the `right` one, or `1` if the `left` value is greater than the `right` one.
 */
ZYAN_INLINE ZYAN_DECLARE_COMPARISON(ZyanCompareNumeric8, ZyanU8)

/**
 * Defines a default comparison function for 16-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `0` if the `left` value equals the `right` one, `-1` if the `left` value is
 *          less than the `right` one, or `1` if the `left` value is greater than the `right` one.
 */
ZYAN_INLINE ZYAN_DECLARE_COMPARISON(ZyanCompareNumeric16, ZyanU16)

/**
 * Defines a default comparison function for 32-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `0` if the `left` value equals the `right` one, `-1` if the `left` value is
 *          less than the `right` one, or `1` if the `left` value is greater than the `right` one.
 */
ZYAN_INLINE ZYAN_DECLARE_COMPARISON(ZyanCompareNumeric32, ZyanU32)

/**
 * Defines a default comparison function for 64-bit numeric values.
 *
 * @param   left    A pointer to the first value.
 * @param   right   A pointer to the second value.
 *
 * @return  Returns `0` if the `left` value equals the `right` one, `-1` if the `left` value is
 *          less than the `right` one, or `1` if the `left` value is greater than the `right` one.
 */
ZYAN_INLINE ZYAN_DECLARE_COMPARISON(ZyanCompareNumeric64, ZyanU64)

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYCORE_COMPARISON_H */

//
// Header: Zycore/Object.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Formatter.h
//   - Zycore/String.h
//   - Zycore/Vector.h
//

/***************************************************************************************************

  Zyan Core Library (Zycore-C)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Defines some generic object-related datatypes.
 */

#ifndef ZYCORE_OBJECT_H
#define ZYCORE_OBJECT_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines the `ZyanMemberProcedure` function prototype.
 *
 * @param   object  A pointer to the object.
 */
typedef void (*ZyanMemberProcedure)(void* object);

/**
 * Defines the `ZyanConstMemberProcedure` function prototype.
 *
 * @param   object  A pointer to the object.
 */
typedef void (*ZyanConstMemberProcedure)(const void* object);

/**
 * Defines the `ZyanMemberFunction` function prototype.
 *
 * @param   object  A pointer to the object.
 *
 * @return  A zyan status code.
 */
typedef ZyanStatus (*ZyanMemberFunction)(void* object);

/**
 * Defines the `ZyanConstMemberFunction` function prototype.
 *
 * @param   object  A pointer to the object.
 *
 * @return  A zyan status code.
 */
typedef ZyanStatus (*ZyanConstMemberFunction)(const void* object);

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYCORE_OBJECT_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Constants                                                                                      */
/* ============================================================================================== */

/**
 * The initial minimum capacity (number of elements) for all dynamically allocated vector
 * instances.
 */
#define ZYAN_VECTOR_MIN_CAPACITY                1

/**
 * The default growth factor for all vector instances.
 */
#define ZYAN_VECTOR_DEFAULT_GROWTH_FACTOR       2

/**
 * The default shrink threshold for all vector instances.
 */
#define ZYAN_VECTOR_DEFAULT_SHRINK_THRESHOLD    4

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines the `ZyanVector` struct.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZyanVector_
{
    /**
     * The memory allocator.
     */
    ZyanAllocator* allocator;
    /**
     * The growth factor.
     */
    ZyanU8 growth_factor;
    /**
     * The shrink threshold.
     */
    ZyanU8 shrink_threshold;
    /**
     * The current number of elements in the vector.
     */
    ZyanUSize size;
    /**
     * The maximum capacity (number of elements).
     */
    ZyanUSize capacity;
    /**
     * The size of a single element in bytes.
     */
    ZyanUSize element_size;
    /**
     * The element destructor callback.
     */
    ZyanMemberProcedure destructor;
    /**
     * The data pointer.
     */
    void* data;
} ZyanVector;

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* General                                                                                        */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines an uninitialized `ZyanVector` instance.
 */
#define ZYAN_VECTOR_INITIALIZER \
    { \
        /* allocator        */ ZYAN_NULL, \
        /* growth_factor    */ 0, \
        /* shrink_threshold */ 0, \
        /* size             */ 0, \
        /* capacity         */ 0, \
        /* element_size     */ 0, \
        /* destructor       */ ZYAN_NULL, \
        /* data             */ ZYAN_NULL \
    }

/* ---------------------------------------------------------------------------------------------- */
/* Helper macros                                                                                  */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the value of the element at the given `index`.
 *
 * @param   type    The desired value type.
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The element index.
 *
 * @result  The value of the desired element in the vector.
 *
 * Note that this function is unsafe and might dereference a null-pointer.
 */
#ifdef __cplusplus
#define ZYAN_VECTOR_GET(type, vector, index) \
    (*reinterpret_cast<const type*>(ZyanVectorGet(vector, index)))
#else
#define ZYAN_VECTOR_GET(type, vector, index) \
    (*(const type*)ZyanVectorGet(vector, index))
#endif

/**
 * Loops through all elements of the vector.
 *
 * @param   type        The desired value type.
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   item_name   The name of the iterator item.
 * @param   body        The body to execute for each item in the vector.
 */
#define ZYAN_VECTOR_FOREACH(type, vector, item_name, body) \
    { \
        const ZyanUSize ZYAN_MACRO_CONCAT_EXPAND(size_d50d3303, item_name) = (vector)->size; \
        for (ZyanUSize ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name) = 0; \
            ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name) < \
            ZYAN_MACRO_CONCAT_EXPAND(size_d50d3303, item_name); \
            ++ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name)) \
        { \
            const type item_name = ZYAN_VECTOR_GET(type, vector, \
                ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name)); \
            body \
        } \
    }

/**
 * Loops through all elements of the vector.
 *
 * @param   type        The desired value type.
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   item_name   The name of the iterator item.
 * @param   body        The body to execute for each item in the vector.
 */
#define ZYAN_VECTOR_FOREACH_MUTABLE(type, vector, item_name, body) \
    { \
        const ZyanUSize ZYAN_MACRO_CONCAT_EXPAND(size_d50d3303, item_name) = (vector)->size; \
        for (ZyanUSize ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name) = 0; \
            ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name) < \
            ZYAN_MACRO_CONCAT_EXPAND(size_d50d3303, item_name); \
            ++ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name)) \
        { \
            type* const item_name = ZyanVectorGetMutable(vector, \
                ZYAN_MACRO_CONCAT_EXPAND(i_bfd62679, item_name)); \
            body \
        } \
    }

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Constructor and destructor                                                                     */
/* ---------------------------------------------------------------------------------------------- */

#ifndef ZYAN_NO_LIBC

/**
 * Initializes the given `ZyanVector` instance.
 *
 * @param   vector          A pointer to the `ZyanVector` instance.
 * @param   element_size    The size of a single element in bytes.
 * @param   capacity        The initial capacity (number of elements).
 * @param   destructor      A destructor callback that is invoked every time an item is deleted, or
 *                          `ZYAN_NULL` if not needed.
 *
 * @return  A zyan status code.
 *
 * The memory for the vector elements is dynamically allocated by the default allocator using the
 * default growth factor and the default shrink threshold.
 *
 * Finalization with `ZyanVectorDestroy` is required for all instances created by this function.
 */
ZYCORE_EXPORT ZYAN_REQUIRES_LIBC ZyanStatus ZyanVectorInit(ZyanVector* vector,
    ZyanUSize element_size, ZyanUSize capacity, ZyanMemberProcedure destructor);

#endif // ZYAN_NO_LIBC

/**
 * Initializes the given `ZyanVector` instance and sets a custom `allocator` and memory
 * allocation/deallocation parameters.
 *
 * @param   vector              A pointer to the `ZyanVector` instance.
 * @param   element_size        The size of a single element in bytes.
 * @param   capacity            The initial capacity (number of elements).
 * @param   destructor          A destructor callback that is invoked every time an item is deleted,
 *                              or `ZYAN_NULL` if not needed.
 * @param   allocator           A pointer to a `ZyanAllocator` instance.
 * @param   growth_factor       The growth factor.
 * @param   shrink_threshold    The shrink threshold.
 *
 * @return  A zyan status code.
 *
 * A growth factor of `1` disables overallocation and a shrink threshold of `0` disables
 * dynamic shrinking.
 *
 * Finalization with `ZyanVectorDestroy` is required for all instances created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorInitEx(ZyanVector* vector, ZyanUSize element_size,
    ZyanUSize capacity, ZyanMemberProcedure destructor, ZyanAllocator* allocator,
    ZyanU8 growth_factor, ZyanU8 shrink_threshold);

/**
 * Initializes the given `ZyanVector` instance and configures it to use a custom user
 * defined buffer with a fixed size.
 *
 * @param   vector          A pointer to the `ZyanVector` instance.
 * @param   element_size    The size of a single element in bytes.
 * @param   buffer          A pointer to the buffer that is used as storage for the elements.
 * @param   capacity        The maximum capacity (number of elements) of the buffer.
 * @param   destructor      A destructor callback that is invoked every time an item is deleted, or
 *                          `ZYAN_NULL` if not needed.
 *
 * @return  A zyan status code.
 *
 * Finalization is not required for instances created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorInitCustomBuffer(ZyanVector* vector, ZyanUSize element_size,
    void* buffer, ZyanUSize capacity, ZyanMemberProcedure destructor);

/**
 * Destroys the given `ZyanVector` instance.
 *
 * @param   vector  A pointer to the `ZyanVector` instance..
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorDestroy(ZyanVector* vector);

/* ---------------------------------------------------------------------------------------------- */
/* Duplication                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

#ifndef ZYAN_NO_LIBC

/**
 * Initializes a new `ZyanVector` instance by duplicating an existing vector.
 *
 * @param   destination A pointer to the (uninitialized) destination `ZyanVector` instance.
 * @param   source      A pointer to the source vector.
 * @param   capacity    The initial capacity (number of elements).
 *
 *                      This value is automatically adjusted to the size of the source vector, if
 *                      a smaller value was passed.
 *
 * @return  A zyan status code.
 *
 * The memory for the vector is dynamically allocated by the default allocator using the default
 * growth factor and the default shrink threshold.
 *
 * Finalization with `ZyanVectorDestroy` is required for all instances created by this function.
 */
ZYCORE_EXPORT ZYAN_REQUIRES_LIBC ZyanStatus ZyanVectorDuplicate(ZyanVector* destination,
    const ZyanVector* source, ZyanUSize capacity);

#endif // ZYAN_NO_LIBC

/**
 * Initializes a new `ZyanVector` instance by duplicating an existing vector and sets a
 * custom `allocator` and memory allocation/deallocation parameters.
 *
 * @param   destination         A pointer to the (uninitialized) destination `ZyanVector` instance.
 * @param   source              A pointer to the source vector.
 * @param   capacity            The initial capacity (number of elements).

 *                              This value is automatically adjusted to the size of the source
 *                              vector, if a smaller value was passed.
 * @param   allocator           A pointer to a `ZyanAllocator` instance.
 * @param   growth_factor       The growth factor.
 * @param   shrink_threshold    The shrink threshold.
 *
 * @return  A zyan status code.
 *
 * A growth factor of `1` disables overallocation and a shrink threshold of `0` disables
 * dynamic shrinking.
 *
 * Finalization with `ZyanVectorDestroy` is required for all instances created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorDuplicateEx(ZyanVector* destination, const ZyanVector* source,
    ZyanUSize capacity, ZyanAllocator* allocator, ZyanU8 growth_factor, ZyanU8 shrink_threshold);

/**
 * Initializes a new `ZyanVector` instance by duplicating an existing vector and
 * configures it to use a custom user defined buffer with a fixed size.
 *
 * @param   destination A pointer to the (uninitialized) destination `ZyanVector` instance.
 * @param   source      A pointer to the source vector.
 * @param   buffer      A pointer to the buffer that is used as storage for the elements.
 * @param   capacity    The maximum capacity (number of elements) of the buffer.

 *                      This function will fail, if the capacity of the buffer is less than the
 *                      size of the source vector.
 *
 * @return  A zyan status code.
 *
 * Finalization is not required for instances created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorDuplicateCustomBuffer(ZyanVector* destination,
    const ZyanVector* source, void* buffer, ZyanUSize capacity);

/* ---------------------------------------------------------------------------------------------- */
/* Element access                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns a constant pointer to the element at the given `index`.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   index       The element index.
 *
 * @return  A constant pointer to the desired element in the vector or `ZYAN_NULL`, if an error
 *          occurred.
 *
 * Note that the returned pointer might get invalid when the vector is resized by either a manual
 * call to the memory-management functions or implicitly by inserting or removing elements.
 *
 * Take a look at `ZyanVectorGetPointer` instead, if you need a function that returns a zyan status
 * code.
 */
ZYCORE_EXPORT const void* ZyanVectorGet(const ZyanVector* vector, ZyanUSize index);

/**
 * Returns a mutable pointer to the element at the given `index`.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   index       The element index.
 *
 * @return  A mutable pointer to the desired element in the vector or `ZYAN_NULL`, if an error
 *          occurred.
 *
 * Note that the returned pointer might get invalid when the vector is resized by either a manual
 * call to the memory-management functions or implicitly by inserting or removing elements.
 *
 * Take a look at `ZyanVectorGetPointerMutable` instead, if you need a function that returns a
 * zyan status code.
 */
ZYCORE_EXPORT void* ZyanVectorGetMutable(const ZyanVector* vector, ZyanUSize index);

/**
 * Returns a constant pointer to the element at the given `index`.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The element index.
 * @param   value   Receives a constant pointer to the desired element in the vector.
 *
 * Note that the returned pointer might get invalid when the vector is resized by either a manual
 * call to the memory-management functions or implicitly by inserting or removing elements.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorGetPointer(const ZyanVector* vector, ZyanUSize index,
    const void** value);

/**
 * Returns a mutable pointer to the element at the given `index`.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The element index.
 * @param   value Receives a mutable pointer to the desired element in the vector.
 *
 * Note that the returned pointer might get invalid when the vector is resized by either a manual
 * call to the memory-management functions or implicitly by inserting or removing elements.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorGetPointerMutable(const ZyanVector* vector, ZyanUSize index,
    void** value);

/**
 * Assigns a new value to the element at the given `index`.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The value index.
 * @param   value   The value to assign.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorSet(ZyanVector* vector, ZyanUSize index,
    const void* value);

/* ---------------------------------------------------------------------------------------------- */
/* Insertion                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Adds a new `element` to the end of the vector.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   element A pointer to the element to add.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorPushBack(ZyanVector* vector, const void* element);

/**
 * Inserts an `element` at the given `index` of the vector.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The insert index.
 * @param   element A pointer to the element to insert.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorInsert(ZyanVector* vector, ZyanUSize index,
    const void* element);

/**
 * Inserts multiple `elements` at the given `index` of the vector.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   index       The insert index.
 * @param   elements    A pointer to the first element.
 * @param   count       The number of elements to insert.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorInsertRange(ZyanVector* vector, ZyanUSize index,
    const void* elements, ZyanUSize count);

/**
 * Constructs an `element` in-place at the end of the vector.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   element     Receives a pointer to the new element.
 * @param   constructor The constructor callback or `ZYAN_NULL`. The new element will be in
 *                      undefined state, if no constructor was passed.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorEmplace(ZyanVector* vector, void** element,
    ZyanMemberFunction constructor);

/**
 * Constructs an `element` in-place and inserts it at the given `index` of the vector.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   index       The insert index.
 * @param   element     Receives a pointer to the new element.
 * @param   constructor The constructor callback or `ZYAN_NULL`. The new element will be in
 *                      undefined state, if no constructor was passed.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorEmplaceEx(ZyanVector* vector, ZyanUSize index,
    void** element, ZyanMemberFunction constructor);

/* ---------------------------------------------------------------------------------------------- */
/* Utils                                                                                          */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Swaps the element at `index_first` with the element at `index_second`.
 *
 * @param   vector          A pointer to the `ZyanVector` instance.
 * @param   index_first     The index of the first element.
 * @param   index_second    The index of the second element.
 *
 * @return  A zyan status code.
 *
 * This function requires the vector to have spare capacity for one temporary element. Call
 * `ZyanVectorReserve` before this function to increase capacity, if needed.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorSwapElements(ZyanVector* vector, ZyanUSize index_first,
    ZyanUSize index_second);

/* ---------------------------------------------------------------------------------------------- */
/* Deletion                                                                                       */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Deletes the element at the given `index` of the vector.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The element index.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorDelete(ZyanVector* vector, ZyanUSize index);

/**
 * Deletes multiple elements from the given vector, starting at `index`.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   index   The index of the first element to delete.
 * @param   count   The number of elements to delete.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorDeleteRange(ZyanVector* vector, ZyanUSize index,
    ZyanUSize count);

/**
 * Removes the last element of the vector.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorPopBack(ZyanVector* vector);

/**
 * Erases all elements of the given vector.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorClear(ZyanVector* vector);

/* ---------------------------------------------------------------------------------------------- */
/* Searching                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Sequentially searches for the first occurrence of `element` in the given vector.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   element     A pointer to the element to search for.
 * @param   found_index A pointer to a variable that receives the index of the found element.
 * @param   comparison  The comparison function to use.
 *
 * @return  `ZYAN_STATUS_TRUE` if the element was found, `ZYAN_STATUS_FALSE` if not or a generic
 *          zyan status code if an error occurred.
 *
 * The `found_index` is set to `-1`, if the element was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorFind(const ZyanVector* vector, const void* element,
    ZyanISize* found_index, ZyanEqualityComparison comparison);

/**
 * Sequentially searches for the first occurrence of `element` in the given vector.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   element     A pointer to the element to search for.
 * @param   found_index A pointer to a variable that receives the index of the found element.
 * @param   comparison  The comparison function to use.
 * @param   index       The start index.
 * @param   count       The maximum number of elements to iterate, beginning from the start `index`.
 *
 * @return  `ZYAN_STATUS_TRUE` if the element was found, `ZYAN_STATUS_FALSE` if not or a generic
 *          zyan status code if an error occurred.
 *
 * The `found_index` is set to `-1`, if the element was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorFindEx(const ZyanVector* vector, const void* element,
    ZyanISize* found_index, ZyanEqualityComparison comparison, ZyanUSize index, ZyanUSize count);

/**
 * Searches for the first occurrence of `element` in the given vector using a binary-
 * search algorithm.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   element     A pointer to the element to search for.
 * @param   found_index A pointer to a variable that receives the index of the found element.
 * @param   comparison  The comparison function to use.
 *
 * @return  `ZYAN_STATUS_TRUE` if the element was found, `ZYAN_STATUS_FALSE` if not or a generic
 *          zyan status code if an error occurred.
 *
 * If found, `found_index` contains the zero-based index of `element`. If not found, `found_index`
 * contains the index of the first entry larger than `element`.
 *
 * This function requires all elements in the vector to be strictly ordered (sorted).
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorBinarySearch(const ZyanVector* vector, const void* element,
    ZyanUSize* found_index, ZyanComparison comparison);

/**
 * Searches for the first occurrence of `element` in the given vector using a binary-
 * search algorithm.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   element     A pointer to the element to search for.
 * @param   found_index A pointer to a variable that receives the index of the found element.
 * @param   comparison  The comparison function to use.
 * @param   index       The start index.
 * @param   count       The maximum number of elements to iterate, beginning from the start `index`.
 *
 * @return  `ZYAN_STATUS_TRUE` if the element was found, `ZYAN_STATUS_FALSE` if not or a generic
 *          zyan status code if an error occurred.
 *
 * If found, `found_index` contains the zero-based index of `element`. If not found, `found_index`
 * contains the index of the first entry larger than `element`.
 *
 * This function requires all elements in the vector to be strictly ordered (sorted).
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorBinarySearchEx(const ZyanVector* vector, const void* element,
    ZyanUSize* found_index, ZyanComparison comparison, ZyanUSize index, ZyanUSize count);

/* ---------------------------------------------------------------------------------------------- */
/* Memory management                                                                              */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Resizes the given `ZyanVector` instance.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   size    The new size of the vector.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorResize(ZyanVector* vector, ZyanUSize size);

/**
 * Resizes the given `ZyanVector` instance.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   size        The new size of the vector.
 * @param   initializer A pointer to a value to be used as initializer for new items.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorResizeEx(ZyanVector* vector, ZyanUSize size,
    const void* initializer);

/**
 * Changes the capacity of the given `ZyanVector` instance.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   capacity    The new minimum capacity of the vector.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorReserve(ZyanVector* vector, ZyanUSize capacity);

/**
 * Shrinks the capacity of the given vector to match it's size.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorShrinkToFit(ZyanVector* vector);

/* ---------------------------------------------------------------------------------------------- */
/* Information                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the current capacity of the vector.
 *
 * @param   vector      A pointer to the `ZyanVector` instance.
 * @param   capacity    Receives the size of the vector.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorGetCapacity(const ZyanVector* vector, ZyanUSize* capacity);

/**
 * Returns the current size of the vector.
 *
 * @param   vector  A pointer to the `ZyanVector` instance.
 * @param   size    Receives the size of the vector.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanVectorGetSize(const ZyanVector* vector, ZyanUSize* size);

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYCORE_VECTOR_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Constants                                                                                      */
/* ============================================================================================== */

/**
 * The initial minimum capacity (number of characters) for all dynamically allocated
 * string instances - not including the terminating '\0'-character.
 */
#define ZYAN_STRING_MIN_CAPACITY                32

/**
 * The default growth factor for all string instances.
 */
#define ZYAN_STRING_DEFAULT_GROWTH_FACTOR       2

/**
 * The default shrink threshold for all string instances.
 */
#define ZYAN_STRING_DEFAULT_SHRINK_THRESHOLD    4

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* String flags                                                                                   */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZyanStringFlags` data-type.
 */
typedef ZyanU8 ZyanStringFlags;

/**
 * The string uses a custom user-defined buffer with a fixed capacity.
 */
#define ZYAN_STRING_HAS_FIXED_CAPACITY  0x01 // (1 << 0)

/* ---------------------------------------------------------------------------------------------- */
/* String                                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZyanString` struct.
 *
 * The `ZyanString` type is implemented as a size-prefixed string - which allows for a lot of
 * performance optimizations.
 * Nevertheless null-termination is guaranteed at all times to provide maximum compatibility with
 * default C-style strings (use `ZyanStringGetData` to access the C-style string).
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZyanString_
{
    /**
     * String flags.
     */
    ZyanStringFlags flags;
    /**
     * The vector that contains the actual string.
     */
    ZyanVector vector;
} ZyanString;

/* ---------------------------------------------------------------------------------------------- */
/* View                                                                                           */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZyanStringView` struct.
 *
 * The `ZyanStringView` type provides a view inside a string (`ZyanString` instances, null-
 * terminated C-style strings, or even not-null-terminated custom strings). A view is immutable
 * by design and can't be directly converted to a C-style string.
 *
 * Views might become invalid (e.g. pointing to invalid memory), if the underlying string gets
 * destroyed or resized.
 *
 * The `ZYAN_STRING_TO_VIEW` macro can be used to cast a `ZyanString` to a `ZyanStringView` pointer
 * without any runtime overhead.
 * Casting a view to a normal string is not supported and will lead to unexpected behavior (use
 * `ZyanStringDuplicate` to create a deep-copy instead).
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZyanStringView_
{
    /**
     * The string data.
     *
     * The view internally re-uses the normal string struct to allow casts without any runtime
     * overhead.
     */
    ZyanString string;
} ZyanStringView;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* General                                                                                        */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines an uninitialized `ZyanString` instance.
 */
#define ZYAN_STRING_INITIALIZER \
    { \
        /* flags  */ 0, \
        /* vector */ ZYAN_VECTOR_INITIALIZER \
    }

/* ---------------------------------------------------------------------------------------------- */
/* Helper macros                                                                                  */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Casts a `ZyanString` pointer to a constant `ZyanStringView` pointer.
 */
#define ZYAN_STRING_TO_VIEW(string) (const ZyanStringView*)(string)

/**
 * Defines a `ZyanStringView` struct that provides a view into a static C-style string.
 *
 * @param   string  The C-style string.
 */
#define ZYAN_DEFINE_STRING_VIEW(string) \
    { \
        /* string */ \
        { \
            /* flags  */ 0, \
            /* vector */ \
            { \
                /* allocator        */ ZYAN_NULL, \
                /* growth_factor    */ 1, \
                /* shrink_threshold */ 0, \
                /* size             */ sizeof(string), \
                /* capacity         */ sizeof(string), \
                /* element_size     */ sizeof(char), \
                /* destructor       */ ZYAN_NULL, \
                /* data             */ (char*)(string) \
            } \
        } \
    }

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Constructor and destructor                                                                     */
/* ---------------------------------------------------------------------------------------------- */

#ifndef ZYAN_NO_LIBC

/**
 * Initializes the given `ZyanString` instance.
 *
 * @param   string          A pointer to the `ZyanString` instance.
 * @param   capacity        The initial capacity (number of characters).
 *
 * @return  A zyan status code.
 *
 * The memory for the string is dynamically allocated by the default allocator using the default
 * growth factor and the default shrink threshold.
 *
 * The allocated buffer will be at least one character larger than the given `capacity`, to reserve
 * space for the terminating '\0'.
 *
 * Finalization with `ZyanStringDestroy` is required for all strings created by this function.
 */
ZYCORE_EXPORT ZYAN_REQUIRES_LIBC ZyanStatus ZyanStringInit(ZyanString* string, ZyanUSize capacity);

#endif // ZYAN_NO_LIBC

/**
 * Initializes the given `ZyanString` instance and sets a custom `allocator` and memory
 * allocation/deallocation parameters.
 *
 * @param   string              A pointer to the `ZyanString` instance.
 * @param   capacity            The initial capacity (number of characters).
 * @param   allocator           A pointer to a `ZyanAllocator` instance.
 * @param   growth_factor       The growth factor.
 * @param   shrink_threshold    The shrink threshold.
 *
 * @return  A zyan status code.
 *
 * A growth factor of `1` disables overallocation and a shrink threshold of `0` disables
 * dynamic shrinking.
 *
 * The allocated buffer will be at least one character larger than the given `capacity`, to reserve
 * space for the terminating '\0'.
 *
 * Finalization with `ZyanStringDestroy` is required for all strings created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringInitEx(ZyanString* string, ZyanUSize capacity,
    ZyanAllocator* allocator, ZyanU8 growth_factor, ZyanU8 shrink_threshold);

/**
 * Initializes the given `ZyanString` instance and configures it to use a custom user
 * defined buffer with a fixed size.
 *
 * @param   string          A pointer to the `ZyanString` instance.
 * @param   buffer          A pointer to the buffer that is used as storage for the string.
 * @param   capacity        The maximum capacity (number of characters) of the buffer, including
 *                          the terminating '\0'.
 *
 * @return  A zyan status code.
 *
 * Finalization is not required for strings created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringInitCustomBuffer(ZyanString* string, char* buffer,
    ZyanUSize capacity);

/**
 * Destroys the given `ZyanString` instance.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 *
 * @return  A zyan status code.
 *
 */
ZYCORE_EXPORT ZyanStatus ZyanStringDestroy(ZyanString* string);

/* ---------------------------------------------------------------------------------------------- */
/* Duplication                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

#ifndef ZYAN_NO_LIBC

/**
 * Initializes a new `ZyanString` instance by duplicating an existing string.
 *
 * @param   destination A pointer to the (uninitialized) destination `ZyanString` instance.
 * @param   source      A pointer to the source string.
 * @param   capacity    The initial capacity (number of characters).
 *
 *                      This value is automatically adjusted to the size of the source string, if
 *                      a smaller value was passed.
 *
 * @return  A zyan status code.
 *
 * The behavior of this function is undefined, if `source` is a view into the `destination`
 * string or `destination` points to an already initialized `ZyanString` instance.
 *
 * The memory for the string is dynamically allocated by the default allocator using the default
 * growth factor and the default shrink threshold.
 *
 * The allocated buffer will be at least one character larger than the given `capacity`, to reserve
 * space for the terminating '\0'.
 *
 * Finalization with `ZyanStringDestroy` is required for all strings created by this function.
 */
ZYCORE_EXPORT ZYAN_REQUIRES_LIBC ZyanStatus ZyanStringDuplicate(ZyanString* destination,
    const ZyanStringView* source, ZyanUSize capacity);

#endif // ZYAN_NO_LIBC

/**
 * Initializes a new `ZyanString` instance by duplicating an existing string and sets a
 * custom `allocator` and memory allocation/deallocation parameters.
 *
 * @param   destination         A pointer to the (uninitialized) destination `ZyanString` instance.
 * @param   source              A pointer to the source string.
 * @param   capacity            The initial capacity (number of characters).

 *                              This value is automatically adjusted to the size of the source
 *                              string, if a smaller value was passed.
 * @param   allocator           A pointer to a `ZyanAllocator` instance.
 * @param   growth_factor       The growth factor.
 * @param   shrink_threshold    The shrink threshold.
 *
 * @return  A zyan status code.
 *
 * The behavior of this function is undefined, if `source` is a view into the `destination`
 * string or `destination` points to an already initialized `ZyanString` instance.
 *
 * A growth factor of `1` disables overallocation and a shrink threshold of `0` disables
 * dynamic shrinking.
 *
 * The allocated buffer will be at least one character larger than the given `capacity`, to reserve
 * space for the terminating '\0'.
 *
 * Finalization with `ZyanStringDestroy` is required for all strings created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringDuplicateEx(ZyanString* destination,
    const ZyanStringView* source, ZyanUSize capacity, ZyanAllocator* allocator,
    ZyanU8 growth_factor, ZyanU8 shrink_threshold);

/**
 * Initializes a new `ZyanString` instance by duplicating an existing string and
 * configures it to use a custom user defined buffer with a fixed size.
 *
 * @param   destination A pointer to the (uninitialized) destination `ZyanString` instance.
 * @param   source      A pointer to the source string.
 * @param   buffer      A pointer to the buffer that is used as storage for the string.
 * @param   capacity    The maximum capacity (number of characters) of the buffer, including the
 *                      terminating '\0'.

 *                      This function will fail, if the capacity of the buffer is less or equal to
 *                      the size of the source string.
 *
 * @return  A zyan status code.
 *
 * The behavior of this function is undefined, if `source` is a view into the `destination`
 * string or `destination` points to an already initialized `ZyanString` instance.
 *
 * Finalization is not required for strings created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringDuplicateCustomBuffer(ZyanString* destination,
    const ZyanStringView* source, char* buffer, ZyanUSize capacity);

/* ---------------------------------------------------------------------------------------------- */
/* Concatenation                                                                                  */
/* ---------------------------------------------------------------------------------------------- */

#ifndef ZYAN_NO_LIBC

/**
 * Initializes a new `ZyanString` instance by concatenating two existing strings.
 *
 * @param   destination A pointer to the (uninitialized) destination `ZyanString` instance.
 *
 *                      This function will fail, if the destination `ZyanString` instance equals
 *                      one of the source strings.
 * @param   s1          A pointer to the first source string.
 * @param   s2          A pointer to the second source string.
 * @param   capacity    The initial capacity (number of characters).

 *                      This value is automatically adjusted to the combined size of the source
 *                      strings, if a smaller value was passed.
 *
 * @return  A zyan status code.
 *
 * The behavior of this function is undefined, if `s1` or `s2` are views into the `destination`
 * string or `destination` points to an already initialized `ZyanString` instance.
 *
 * The memory for the string is dynamically allocated by the default allocator using the default
 * growth factor and the default shrink threshold.
 *
 * The allocated buffer will be at least one character larger than the given `capacity`, to reserve
 * space for the terminating '\0'.
 *
 * Finalization with `ZyanStringDestroy` is required for all strings created by this function.
 */
ZYCORE_EXPORT ZYAN_REQUIRES_LIBC ZyanStatus ZyanStringConcat(ZyanString* destination,
    const ZyanStringView* s1, const ZyanStringView* s2, ZyanUSize capacity);

#endif // ZYAN_NO_LIBC

/**
 * Initializes a new `ZyanString` instance by concatenating two existing strings and sets
 * a custom `allocator` and memory allocation/deallocation parameters.
 *
 * @param   destination         A pointer to the (uninitialized) destination `ZyanString` instance.
 *
 *                              This function will fail, if the destination `ZyanString` instance
 *                              equals one of the source strings.
 * @param   s1                  A pointer to the first source string.
 * @param   s2                  A pointer to the second source string.
 * @param   capacity            The initial capacity (number of characters).
 *
 *                              This value is automatically adjusted to the combined size of the
 *                              source strings, if a smaller value was passed.
 * @param   allocator           A pointer to a `ZyanAllocator` instance.
 * @param   growth_factor       The growth factor.
 * @param   shrink_threshold    The shrink threshold.
 *
 * @return  A zyan status code.
 *
 * The behavior of this function is undefined, if `s1` or `s2` are views into the `destination`
 * string or `destination` points to an already initialized `ZyanString` instance.
 *
 * A growth factor of `1` disables overallocation and a shrink threshold of `0` disables
 * dynamic shrinking.
 *
 * The allocated buffer will be at least one character larger than the given `capacity`, to reserve
 * space for the terminating '\0'.
 *
 * Finalization with `ZyanStringDestroy` is required for all strings created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringConcatEx(ZyanString* destination, const ZyanStringView* s1,
    const ZyanStringView* s2, ZyanUSize capacity, ZyanAllocator* allocator, ZyanU8 growth_factor,
    ZyanU8 shrink_threshold);

/**
 * Initializes a new `ZyanString` instance by concatenating two existing strings and
 * configures it to use a custom user defined buffer with a fixed size.
 *
 * @param   destination A pointer to the (uninitialized) destination `ZyanString` instance.
 *
 *                      This function will fail, if the destination `ZyanString` instance equals
 *                      one of the source strings.
 * @param   s1          A pointer to the first source string.
 * @param   s2          A pointer to the second source string.
 * @param   buffer      A pointer to the buffer that is used as storage for the string.
 * @param   capacity    The maximum capacity (number of characters) of the buffer.
 *
 *                      This function will fail, if the capacity of the buffer is less or equal to
 *                      the combined size of the source strings.
 *
 * @return  A zyan status code.
 *
 * The behavior of this function is undefined, if `s1` or `s2` are views into the `destination`
 * string or `destination` points to an already initialized `ZyanString` instance.
 *
 * Finalization is not required for strings created by this function.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringConcatCustomBuffer(ZyanString* destination,
    const ZyanStringView* s1, const ZyanStringView* s2, char* buffer, ZyanUSize capacity);

/* ---------------------------------------------------------------------------------------------- */
/* Views                                                                                          */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns a view inside an existing view/string.
 *
 * @param   view    A pointer to the `ZyanStringView` instance.
 * @param   source  A pointer to the source string.
 *
 * @return  A zyan status code.
 *
 * The `ZYAN_STRING_TO_VEW` macro can be used to pass any `ZyanString` instance as value for the
 * `source` string.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringViewInsideView(ZyanStringView* view,
    const ZyanStringView* source);

/**
 * Returns a view inside an existing view/string starting from the given `index`.
 *
 * @param   view    A pointer to the `ZyanStringView` instance.
 * @param   source  A pointer to the source string.
 * @param   index   The start index.
 * @param   count   The number of characters.
 *
 * @return  A zyan status code.
 *
 * The `ZYAN_STRING_TO_VEW` macro can be used to pass any `ZyanString` instance as value for the
 * `source` string.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringViewInsideViewEx(ZyanStringView* view,
    const ZyanStringView* source, ZyanUSize index, ZyanUSize count);

/**
 * Returns a view inside a null-terminated C-style string.
 *
 * @param   view    A pointer to the `ZyanStringView` instance.
 * @param   string  The C-style string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringViewInsideBuffer(ZyanStringView* view, const char* string);

/**
 * Returns a view inside a character buffer with custom length.
 *
 * @param   view    A pointer to the `ZyanStringView` instance.
 * @param   buffer  A pointer to the buffer containing the string characters.
 * @param   length  The length of the string (number of characters).
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringViewInsideBufferEx(ZyanStringView* view, const char* buffer,
    ZyanUSize length);

/**
 * Returns the size (number of characters) of the view.
 *
 * @param   view    A pointer to the `ZyanStringView` instance.
 * @param   size    Receives the size (number of characters) of the view.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringViewGetSize(const ZyanStringView* view, ZyanUSize* size);

/**
 * Returns the C-style string of the given `ZyanString` instance.
 *
 * @warning The string is not guaranteed to be null terminated!
 *
 * @param   view    A pointer to the `ZyanStringView` instance.
 * @param   buffer  Receives a pointer to the C-style string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringViewGetData(const ZyanStringView* view, const char** buffer);

/* ---------------------------------------------------------------------------------------------- */
/* Character access                                                                               */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the character at the given `index`.
 *
 * @param   string  A pointer to the `ZyanStringView` instance.
 * @param   index   The character index.
 * @param   value   Receives the desired character of the string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringGetChar(const ZyanStringView* string, ZyanUSize index,
    char* value);

/**
 * Returns a pointer to the character at the given `index`.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   index   The character index.
 * @param   value   Receives a pointer to the desired character in the string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringGetCharMutable(ZyanString* string, ZyanUSize index,
    char** value);

/**
 * Assigns a new value to the character at the given `index`.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   index   The character index.
 * @param   value   The character to assign.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringSetChar(ZyanString* string, ZyanUSize index, char value);

/* ---------------------------------------------------------------------------------------------- */
/* Insertion                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Inserts the content of the source string in the destination string at the given `index`.
 *
 * @param   destination The destination string.
 * @param   index       The insert index.
 * @param   source      The source string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringInsert(ZyanString* destination, ZyanUSize index,
    const ZyanStringView* source);

/**
 * Inserts `count` characters of the source string in the destination string at the given
 * `index`.
 *
 * @param   destination         The destination string.
 * @param   destination_index   The insert index.
 * @param   source              The source string.
 * @param   source_index        The index of the first character to be inserted from the source
 *                              string.
 * @param   count               The number of chars to insert from the source string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringInsertEx(ZyanString* destination, ZyanUSize destination_index,
    const ZyanStringView* source, ZyanUSize source_index, ZyanUSize count);

/* ---------------------------------------------------------------------------------------------- */
/* Appending                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Appends the content of the source string to the end of the destination string.
 *
 * @param   destination The destination string.
 * @param   source      The source string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringAppend(ZyanString* destination, const ZyanStringView* source);

/**
 * Appends `count` characters of the source string to the end of the destination string.
 *
 * @param   destination     The destination string.
 * @param   source          The source string.
 * @param   source_index    The index of the first character to be appended from the source string.
 * @param   count           The number of chars to append from the source string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringAppendEx(ZyanString* destination, const ZyanStringView* source,
    ZyanUSize source_index, ZyanUSize count);

/* ---------------------------------------------------------------------------------------------- */
/* Deletion                                                                                       */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Deletes characters from the given string, starting at `index`.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   index   The index of the first character to delete.
 * @param   count   The number of characters to delete.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringDelete(ZyanString* string, ZyanUSize index, ZyanUSize count);

/**
 * Deletes all remaining characters from the given string, starting at `index`.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   index   The index of the first character to delete.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringTruncate(ZyanString* string, ZyanUSize index);

/**
 * Erases the given string.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringClear(ZyanString* string);

/* ---------------------------------------------------------------------------------------------- */
/* Searching                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Searches for the first occurrence of `needle` in the given `haystack` starting from the
 * left.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occured.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringLPos(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index);

/**
 * Searches for the first occurrence of `needle` in the given `haystack` starting from the
 * left.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 * @param   index       The start index.
 * @param   count       The maximum number of characters to iterate, beginning from the start
 *                      `index`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occured.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringLPosEx(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index, ZyanUSize index, ZyanUSize count);

/**
 * Performs a case-insensitive search for the first occurrence of `needle` in the given
 * `haystack` starting from the left.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occured.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringLPosI(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index);

/**
 * Performs a case-insensitive search for the first occurrence of `needle` in the given
 * `haystack` starting from the left.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 * @param   index       The start index.
 * @param   count       The maximum number of characters to iterate, beginning from the start
 *                      `index`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringLPosIEx(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index, ZyanUSize index, ZyanUSize count);

/**
 * Searches for the first occurrence of `needle` in the given `haystack` starting from the
 * right.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringRPos(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index);

/**
 * Searches for the first occurrence of `needle` in the given `haystack` starting from the
 *          right.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 * @param   index       The start index.
 * @param   count       The maximum number of characters to iterate, beginning from the start
 *                      `index`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringRPosEx(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index, ZyanUSize index, ZyanUSize count);

/**
 * Performs a case-insensitive search for the first occurrence of `needle` in the given
 * `haystack` starting from the right.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringRPosI(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index);

/**
 * Performs a case-insensitive search for the first occurrence of `needle` in the given
 * `haystack` starting from the right.
 *
 * @param   haystack    The string to search in.
 * @param   needle      The sub-string to search for.
 * @param   found_index A pointer to a variable that receives the index of the first occurrence of
 *                      `needle`.
 * @param   index       The start index.
 * @param   count       The maximum number of characters to iterate, beginning from the start
 *                      `index`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the needle was found, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 *
 * The `found_index` is set to `-1`, if the needle was not found.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringRPosIEx(const ZyanStringView* haystack,
    const ZyanStringView* needle, ZyanISize* found_index, ZyanUSize index, ZyanUSize count);

/* ---------------------------------------------------------------------------------------------- */
/* Comparing                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Compares two strings.
 *
 * @param   s1      The first string
 * @param   s2      The second string.
 * @param   result  Receives the comparison result.
 *
 *                  Values:
 *                  - `result  < 0` -> The first character that does not match has a lower value
 *                    in `s1` than in `s2`.
 *                  - `result == 0` -> The contents of both strings are equal.
 *                  - `result  > 0` -> The first character that does not match has a greater value
 *                    in `s1` than in `s2`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the strings are equal, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringCompare(const ZyanStringView* s1, const ZyanStringView* s2,
    ZyanI32* result);

/**
 * Performs a case-insensitive comparison of two strings.
 *
 * @param   s1      The first string
 * @param   s2      The second string.
 * @param   result  Receives the comparison result.
 *
 *                  Values:
 *                  - `result  < 0` -> The first character that does not match has a lower value
 *                    in `s1` than in `s2`.
 *                  - `result == 0` -> The contents of both strings are equal.
 *                  - `result  > 0` -> The first character that does not match has a greater value
 *                    in `s1` than in `s2`.
 *
 * @return  `ZYAN_STATUS_TRUE`, if the strings are equal, `ZYAN_STATUS_FALSE`, if not, or another
 *          zyan status code, if an error occurred.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringCompareI(const ZyanStringView* s1, const ZyanStringView* s2,
    ZyanI32* result);

/* ---------------------------------------------------------------------------------------------- */
/* Case conversion                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Converts the given string to lowercase letters.
 *
 * @param   string      A pointer to the `ZyanString` instance.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringToLowerCase(ZyanString* string);

/**
 * Converts `count` characters of the given string to lowercase letters.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   index   The start index.
 * @param   count   The number of characters to convert, beginning from the start `index`.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringToLowerCaseEx(ZyanString* string, ZyanUSize index,
    ZyanUSize count);

/**
 * Converts the given string to uppercase letters.
 *
 * @param   string      A pointer to the `ZyanString` instance.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringToUpperCase(ZyanString* string);

/**
 * Converts `count` characters of the given string to uppercase letters.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   index   The start index.
 * @param   count   The number of characters to convert, beginning from the start `index`.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringToUpperCaseEx(ZyanString* string, ZyanUSize index,
    ZyanUSize count);

/* ---------------------------------------------------------------------------------------------- */
/* Memory management                                                                              */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Resizes the given `ZyanString` instance.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   size    The new size of the string.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringResize(ZyanString* string, ZyanUSize size);

/**
 * Changes the capacity of the given `ZyanString` instance.
 *
 * @param   string      A pointer to the `ZyanString` instance.
 * @param   capacity    The new minimum capacity of the string.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringReserve(ZyanString* string, ZyanUSize capacity);

/**
 * Shrinks the capacity of the given string to match it's size.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 *
 * @return  A zyan status code.
 *
 * This function will fail, if the `ZYAN_STRING_IS_IMMUTABLE` flag is set for the specified
 * `ZyanString` instance.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringShrinkToFit(ZyanString* string);

/* ---------------------------------------------------------------------------------------------- */
/* Information                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the current capacity of the string.
 *
 * @param   string      A pointer to the `ZyanString` instance.
 * @param   capacity    Receives the size of the string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringGetCapacity(const ZyanString* string, ZyanUSize* capacity);

/**
 * Returns the current size (number of characters) of the string (excluding the
 * terminating zero character).
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   size    Receives the size (number of characters) of the string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringGetSize(const ZyanString* string, ZyanUSize* size);

/**
 * Returns the C-style string of the given `ZyanString` instance.
 *
 * @param   string  A pointer to the `ZyanString` instance.
 * @param   value   Receives a pointer to the C-style string.
 *
 * @return  A zyan status code.
 */
ZYCORE_EXPORT ZyanStatus ZyanStringGetData(const ZyanString* string, const char** value);

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif // ZYCORE_STRING_H

//
// Header: Zydis/FormatterBuffer.h
//
// Include stack:
//   - Zydis/Zydis.h
//   - Zydis/Formatter.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Implements the `ZydisFormatterToken` type and provides functions to use it.
 */

#ifndef ZYDIS_FORMATTER_TOKEN_H
#define ZYDIS_FORMATTER_TOKEN_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Constants                                                                                      */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Token types                                                                                    */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisTokenType` data-type.
 */
typedef ZyanU8 ZydisTokenType;

#define ZYDIS_TOKEN_INVALID             0x00
/**
 * A whitespace character.
 */
#define ZYDIS_TOKEN_WHITESPACE          0x01
/**
 * A delimiter character (like `','`, `':'`, `'+'`, `'-'`, `'*'`).
 */
#define ZYDIS_TOKEN_DELIMITER           0x02
/**
 * An opening parenthesis character (like `'('`, `'['`, `'{'`).
 */
#define ZYDIS_TOKEN_PARENTHESIS_OPEN    0x03
/**
 * A closing parenthesis character (like `')'`, `']'`, `'}'`).
 */
#define ZYDIS_TOKEN_PARENTHESIS_CLOSE   0x04
/**
 * A prefix literal (like `"LOCK"`, `"REP"`).
 */
#define ZYDIS_TOKEN_PREFIX              0x05
/**
 * A mnemonic literal (like `"MOV"`, `"VCMPPSD"`, `"LCALL"`).
 */
#define ZYDIS_TOKEN_MNEMONIC            0x06
/**
 * A register literal (like `"RAX"`, `"DS"`, `"%ECX"`).
 */
#define ZYDIS_TOKEN_REGISTER            0x07
/**
 * An absolute address literal (like `0x00400000`).
 */
#define ZYDIS_TOKEN_ADDRESS_ABS         0x08
/**
 * A relative address literal (like `-0x100`).
 */
#define ZYDIS_TOKEN_ADDRESS_REL         0x09
/**
 * A displacement literal (like `0xFFFFFFFF`, `-0x100`, `+0x1234`).
 */
#define ZYDIS_TOKEN_DISPLACEMENT        0x0A
/**
 * An immediate literal (like `0xC0`, `-0x1234`, `$0x0000`).
 */
#define ZYDIS_TOKEN_IMMEDIATE           0x0B
/**
 * A typecast literal (like `DWORD PTR`).
 */
#define ZYDIS_TOKEN_TYPECAST            0x0C
/**
 * A decorator literal (like `"Z"`, `"1TO4"`).
 */
#define ZYDIS_TOKEN_DECORATOR           0x0D
/**
 * A symbol literal.
 */
#define ZYDIS_TOKEN_SYMBOL              0x0E

/**
 * The base for user-defined token types.
 */
#define ZYDIS_TOKEN_USER                0x80

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Token                                                                                          */
/* ---------------------------------------------------------------------------------------------- */

#pragma pack(push, 1)

/**
 * Defines the `ZydisFormatterToken` struct.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 */
typedef struct ZydisFormatterToken_
{
    /**
     * The token type.
     */
    ZydisTokenType type;
    /**
     * An offset to the next token, or `0`.
     */
    ZyanU8 next;
} ZydisFormatterToken;

#pragma pack(pop)

/**
 * Defines the `ZydisFormatterTokenConst` data-type.
 */
typedef const ZydisFormatterToken ZydisFormatterTokenConst;

/* ---------------------------------------------------------------------------------------------- */
/* Buffer                                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisFormatterBuffer` struct.
 *
 * All fields in this struct should be considered as "private". Any changes may
 * lead to unexpected behavior.
 */
typedef struct ZydisFormatterBuffer_
{
    /**
     * `ZYAN_TRUE`, if the buffer contains a token stream or `ZYAN_FALSE, if it
     *  contains a simple string.
     */
    ZyanBool is_token_list;
    /**
     * The remaining capacity of the buffer.
     */
    ZyanUSize capacity;
    /**
     * The `ZyanString` instance that refers to the literal value of the most
     * recently added token.
     */
    ZyanString string;
} ZydisFormatterBuffer;

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Token                                                                                          */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the `type` and the string `value` of the given `token`.
 *
 * @param   token   A pointer to the `ZydisFormatterToken` struct.
 * @param   type    Receives the token type.
 * @param   value   Receives a pointer to the string value of the token.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterTokenGetValue(const ZydisFormatterToken* token,
    ZydisTokenType* type, ZyanConstCharPointer* value);

/**
 * Obtains the next `token` linked to the passed one.
 *
 * @param   token   Receives a pointer to the next `ZydisFormatterToken` struct
 *                  linked to the passed one.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterTokenNext(ZydisFormatterTokenConst** token);

/* ---------------------------------------------------------------------------------------------- */
/* Buffer                                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Returns the current (most recently added) token.
 *
 * @param   buffer  A pointer to the `ZydisFormatterBuffer` struct.
 * @param   token   Receives a pointer to the current token.
 *
 * @return  A zyan status code.
 *
 * This function returns `ZYAN_STATUS_INVALID_OPERATION`, if the buffer does not contain at least
 * one token.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterBufferGetToken(const ZydisFormatterBuffer* buffer,
    ZydisFormatterTokenConst** token);

/**
 * Returns the `ZyanString` instance associated with the given buffer.
 *
 * @param   buffer  A pointer to the `ZydisFormatterBuffer` struct.
 * @param   string  Receives a pointer to the `ZyanString` instance associated with the given
 *                  buffer.
 *
 * @return  A zyan status code.
 *
 * This function returns `ZYAN_STATUS_INVALID_OPERATION`, if the buffer does not contain at least
 * one token.
 *
 * The returned string always refers to the literal value of the current (most recently added)
 * token and will remain valid until the buffer is destroyed.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterBufferGetString(ZydisFormatterBuffer* buffer,
    ZyanString** string);

/**
 * Appends a new token to the `buffer`.
 *
 * @param   buffer  A pointer to the `ZydisFormatterBuffer` struct.
 * @param   type    The type of the new token.
 *
 * @return  A zyan status code.
 *
 * Note that the `ZyanString` instance returned by `ZydisFormatterBufferGetString` will
 * automatically be updated by calling this function.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterBufferAppend(ZydisFormatterBuffer* buffer,
    ZydisTokenType type);

/**
 * Returns a snapshot of the buffer-state.
 *
 * @param   buffer  A pointer to the `ZydisFormatterBuffer` struct.
 * @param   state   Receives a snapshot of the buffer-state.
 *
 * @return  A zyan status code.
 *
 * Note that the buffer-state is saved inside the buffer itself and thus becomes invalid as soon
 * as the buffer gets overwritten or destroyed.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterBufferRemember(const ZydisFormatterBuffer* buffer,
    ZyanUPointer* state);

/**
 * Restores a previously saved buffer-state.
 *
 * @param   buffer  A pointer to the `ZydisFormatterBuffer` struct.
 * @param   state   The buffer-state to restore.
 *
 * @return  A zyan status code.
 *
 * All tokens added after obtaining the given `state` snapshot will be removed. This function
 * does NOT restore any string content.
 *
 * Note that the `ZyanString` instance returned by `ZydisFormatterBufferGetString` will
 * automatically be updated by calling this function.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterBufferRestore(ZydisFormatterBuffer* buffer,
    ZyanUPointer state);

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_FORMATTER_TOKEN_H */

#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Constants                                                                                      */
/* ============================================================================================== */

/**
 * Use this constant as value for `runtime_address` in `ZydisFormatterFormatInstruction(Ex)`
 * or `ZydisFormatterFormatOperand(Ex)` to print relative values for all addresses.
 */
#define ZYDIS_RUNTIME_ADDRESS_NONE (ZyanU64)(-1)

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Formatter style                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum selecting the syntax to format the disassembly in.
 */
typedef enum ZydisFormatterStyle_
{
    /**
     * Generates `AT&T`-style disassembly.
     */
    ZYDIS_FORMATTER_STYLE_ATT,
    /**
     * Generates `Intel`-style disassembly.
     */
    ZYDIS_FORMATTER_STYLE_INTEL,
    /**
     * Generates `MASM`-style disassembly that is directly accepted as input for
     * the `MASM` assembler.
     *
     * The runtime-address is ignored in this mode.
     */
    ZYDIS_FORMATTER_STYLE_INTEL_MASM,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_FORMATTER_STYLE_MAX_VALUE = ZYDIS_FORMATTER_STYLE_INTEL_MASM,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_FORMATTER_STYLE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_FORMATTER_STYLE_MAX_VALUE)
} ZydisFormatterStyle;

/* ---------------------------------------------------------------------------------------------- */
/* Properties                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum selecting a property of the formatter.
 */
typedef enum ZydisFormatterProperty_
{
    /* ---------------------------------------------------------------------------------------- */
    /* General                                                                                  */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the printing of effective operand-size suffixes (`AT&T`) or operand-sizes
     * of memory operands (`INTEL`).
     *
     * Pass `ZYAN_TRUE` as value to force the formatter to always print the size, or `ZYAN_FALSE`
     * to only print it if needed.
     */
    ZYDIS_FORMATTER_PROP_FORCE_SIZE,
    /**
     * Controls the printing of segment prefixes.
     *
     * Pass `ZYAN_TRUE` as value to force the formatter to always print the segment register of
     * memory-operands or `ZYAN_FALSE` to omit implicit `DS`/`SS` segments.
     */
    ZYDIS_FORMATTER_PROP_FORCE_SEGMENT,
    /**
     * Controls the printing of the scale-factor component for memory operands.
     *
     * Pass `ZYAN_TRUE` as value to force the formatter to always print the scale-factor component
     * of memory operands or `ZYAN_FALSE` to omit the scale factor for values of `1`.
     */
     ZYDIS_FORMATTER_PROP_FORCE_SCALE_ONE,
    /**
     * Controls the printing of branch addresses.
     *
     * Pass `ZYAN_TRUE` as value to force the formatter to always print relative branch addresses
     * or `ZYAN_FALSE` to use absolute addresses, if a runtime-address different to
     * `ZYDIS_RUNTIME_ADDRESS_NONE` was passed.
     */
    ZYDIS_FORMATTER_PROP_FORCE_RELATIVE_BRANCHES,
    /**
     * Controls the printing of `EIP`/`RIP`-relative addresses.
     *
     * Pass `ZYAN_TRUE` as value to force the formatter to always print relative addresses for
     * `EIP`/`RIP`-relative operands or `ZYAN_FALSE` to use absolute addresses, if a runtime-
     * address different to `ZYDIS_RUNTIME_ADDRESS_NONE` was passed.
     */
    ZYDIS_FORMATTER_PROP_FORCE_RELATIVE_RIPREL,
    /**
     * Controls the printing of branch-instructions sizes.
     *
     * Pass `ZYAN_TRUE` as value to print the size (`short`, `near`) of branch
     * instructions or `ZYAN_FALSE` to hide it.
     *
     * Note that the `far`/`l` modifier is always printed.
     */
    ZYDIS_FORMATTER_PROP_PRINT_BRANCH_SIZE,

    /**
     * Controls the printing of instruction prefixes.
     *
     * Pass `ZYAN_TRUE` as value to print all instruction-prefixes (even ignored or duplicate
     * ones) or `ZYAN_FALSE` to only print prefixes that are effectively used by the instruction.
     */
    ZYDIS_FORMATTER_PROP_DETAILED_PREFIXES,

    /* ---------------------------------------------------------------------------------------- */
    /* Numeric values                                                                           */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the base of address values.
     */
    ZYDIS_FORMATTER_PROP_ADDR_BASE,
    /**
     * Controls the signedness of relative addresses. Absolute addresses are
     * always unsigned.
     */
    ZYDIS_FORMATTER_PROP_ADDR_SIGNEDNESS,
    /**
     * Controls the padding of absolute address values.
     *
     * Pass `ZYDIS_PADDING_DISABLED` to disable padding, `ZYDIS_PADDING_AUTO` to pad all
     * addresses to the current address width (hexadecimal only), or any other integer value for
     * custom padding.
     */
    ZYDIS_FORMATTER_PROP_ADDR_PADDING_ABSOLUTE,
    /**
     * Controls the padding of relative address values.
     *
     * Pass `ZYDIS_PADDING_DISABLED` to disable padding, `ZYDIS_PADDING_AUTO` to pad all
     * addresses to the current address width (hexadecimal only), or any other integer value for
     * custom padding.
     */
    ZYDIS_FORMATTER_PROP_ADDR_PADDING_RELATIVE,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the base of displacement values.
     */
    ZYDIS_FORMATTER_PROP_DISP_BASE,
    /**
     * Controls the signedness of displacement values.
     */
    ZYDIS_FORMATTER_PROP_DISP_SIGNEDNESS,
    /**
     * Controls the padding of displacement values.
     *
     * Pass `ZYDIS_PADDING_DISABLED` to disable padding, or any other integer value for custom
     * padding.
     */
    ZYDIS_FORMATTER_PROP_DISP_PADDING,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the base of immediate values.
     */
    ZYDIS_FORMATTER_PROP_IMM_BASE,
    /**
     * Controls the signedness of immediate values.
     *
     * Pass `ZYDIS_SIGNEDNESS_AUTO` to automatically choose the most suitable mode based on the
     * operands `ZydisDecodedOperand.imm.is_signed` attribute.
     */
    ZYDIS_FORMATTER_PROP_IMM_SIGNEDNESS,
    /**
     * Controls the padding of immediate values.
     *
     * Pass `ZYDIS_PADDING_DISABLED` to disable padding, `ZYDIS_PADDING_AUTO` to padd all
     * immediates to the operand-width (hexadecimal only), or any other integer value for custom
     * padding.
     */
    ZYDIS_FORMATTER_PROP_IMM_PADDING,

    /* ---------------------------------------------------------------------------------------- */
    /* Text formatting                                                                          */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the letter-case for prefixes.
     *
     * Pass `ZYAN_TRUE` as value to format in uppercase or `ZYAN_FALSE` to format in lowercase.
     */
    ZYDIS_FORMATTER_PROP_UPPERCASE_PREFIXES,
    /**
     * Controls the letter-case for the mnemonic.
     *
     * Pass `ZYAN_TRUE` as value to format in uppercase or `ZYAN_FALSE` to format in lowercase.
     */
    ZYDIS_FORMATTER_PROP_UPPERCASE_MNEMONIC,
    /**
     * Controls the letter-case for registers.
     *
     * Pass `ZYAN_TRUE` as value to format in uppercase or `ZYAN_FALSE` to format in lowercase.
     */
    ZYDIS_FORMATTER_PROP_UPPERCASE_REGISTERS,
    /**
     * Controls the letter-case for typecasts.
     *
     * Pass `ZYAN_TRUE` as value to format in uppercase or `ZYAN_FALSE` to format in lowercase.
     */
    ZYDIS_FORMATTER_PROP_UPPERCASE_TYPECASTS,
    /**
     * Controls the letter-case for decorators.
     *
     * Pass `ZYAN_TRUE` as value to format in uppercase or `ZYAN_FALSE` to format in lowercase.
     *
     * WARNING: this is currently not implemented (ignored).
     */
    ZYDIS_FORMATTER_PROP_UPPERCASE_DECORATORS,

    /* ---------------------------------------------------------------------------------------- */
    /* Number formatting                                                                        */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the prefix for decimal values.
     *
     * Pass a pointer to a null-terminated C-style string with a maximum length of 10 characters
     * to set a custom prefix, or `ZYAN_NULL` to disable it.
     *
     * The string is deep-copied into an internal buffer.
     */
    ZYDIS_FORMATTER_PROP_DEC_PREFIX,
    /**
     * Controls the suffix for decimal values.
     *
     * Pass a pointer to a null-terminated C-style string with a maximum length of 10 characters
     * to set a custom suffix, or `ZYAN_NULL` to disable it.
     *
     * The string is deep-copied into an internal buffer.
     */
    ZYDIS_FORMATTER_PROP_DEC_SUFFIX,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * Controls the letter-case of hexadecimal values.
     *
     * Pass `ZYAN_TRUE` as value to format in uppercase and `ZYAN_FALSE` to format in lowercase.
     *
     * The default value is `ZYAN_TRUE`.
     */
    ZYDIS_FORMATTER_PROP_HEX_UPPERCASE,
    /**
     * Controls whether to prepend hexadecimal values with a leading zero if the first character
     * is non-numeric.
     *
     * Pass `ZYAN_TRUE` to prepend a leading zero if the first character is non-numeric or
     * `ZYAN_FALSE` to disable this functionality.
     *
     * The default value is `ZYAN_FALSE`.
     */
    ZYDIS_FORMATTER_PROP_HEX_FORCE_LEADING_NUMBER,
    /**
     * Controls the prefix for hexadecimal values.
     *
     * Pass a pointer to a null-terminated C-style string with a maximum length of 10 characters
     * to set a custom prefix, or `ZYAN_NULL` to disable it.
     *
     * The string is deep-copied into an internal buffer.
     */
    ZYDIS_FORMATTER_PROP_HEX_PREFIX,
    /**
     * Controls the suffix for hexadecimal values.
     *
     * Pass a pointer to a null-terminated C-style string with a maximum length of 10 characters
     * to set a custom suffix, or `ZYAN_NULL` to disable it.
     *
     * The string is deep-copied into an internal buffer.
     */
    ZYDIS_FORMATTER_PROP_HEX_SUFFIX,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * Maximum value of this enum.
     */
    ZYDIS_FORMATTER_PROP_MAX_VALUE = ZYDIS_FORMATTER_PROP_HEX_SUFFIX,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_FORMATTER_PROP_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_FORMATTER_PROP_MAX_VALUE)
} ZydisFormatterProperty;

/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum defining different mantissae to be used during formatting.
 */
typedef enum ZydisNumericBase_
{
    /**
     * Decimal system.
     */
    ZYDIS_NUMERIC_BASE_DEC,
    /**
     * Hexadecimal system.
     */
    ZYDIS_NUMERIC_BASE_HEX,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_NUMERIC_BASE_MAX_VALUE = ZYDIS_NUMERIC_BASE_HEX,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_NUMERIC_BASE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_NUMERIC_BASE_MAX_VALUE)
} ZydisNumericBase;

/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum defining the signeness of integers to be used during formatting.
 */
typedef enum ZydisSignedness_
{
    /**
     * Automatically choose the most suitable mode based on the operands
     * ZydisDecodedOperand.imm.is_signed` attribute.
     */
    ZYDIS_SIGNEDNESS_AUTO,
    /**
     * Force signed values.
     */
    ZYDIS_SIGNEDNESS_SIGNED,
    /**
     * Force unsigned values.
     */
    ZYDIS_SIGNEDNESS_UNSIGNED,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_SIGNEDNESS_MAX_VALUE = ZYDIS_SIGNEDNESS_UNSIGNED,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_SIGNEDNESS_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_SIGNEDNESS_MAX_VALUE)
} ZydisSignedness;

/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum definining magic values that receive special treatment when used as padding properties
 * of the formatter.
 */
typedef enum ZydisPadding_
{
    /**
     * Disables padding.
     */
    ZYDIS_PADDING_DISABLED = 0,
    /**
     * Padds the value to the current stack-width for addresses, or to the
     * operand-width for immediate values (hexadecimal only).
     */
    ZYDIS_PADDING_AUTO     = (-1),

    /**
     * Maximum value of this enum.
     */
    ZYDIS_PADDING_MAX_VALUE = ZYDIS_PADDING_AUTO,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_PADDING_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_PADDING_MAX_VALUE)
} ZydisPadding;

/* ---------------------------------------------------------------------------------------------- */
/* Function types                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum selecting a formatter function to be replaced with hooks.
 *
 * Do NOT change the order of the values this enum or the function fields inside the
 * `ZydisFormatter` struct.
 */
typedef enum ZydisFormatterFunction_
{
    /* ---------------------------------------------------------------------------------------- */
    /* Instruction                                                                              */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function is invoked before the formatter formats an instruction.
     */
    ZYDIS_FORMATTER_FUNC_PRE_INSTRUCTION,
    /**
     * This function is invoked after the formatter formatted an instruction.
     */
    ZYDIS_FORMATTER_FUNC_POST_INSTRUCTION,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function refers to the main formatting function.
     *
     * Replacing this function allows for complete custom formatting, but indirectly disables all
     * other hooks except for `ZYDIS_FORMATTER_FUNC_PRE_INSTRUCTION` and
     * `ZYDIS_FORMATTER_FUNC_POST_INSTRUCTION`.
     */
    ZYDIS_FORMATTER_FUNC_FORMAT_INSTRUCTION,

    /* ---------------------------------------------------------------------------------------- */
    /* Operands                                                                                 */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function is invoked before the formatter formats an operand.
     */
    ZYDIS_FORMATTER_FUNC_PRE_OPERAND,
    /**
     * This function is invoked after the formatter formatted an operand.
     */
    ZYDIS_FORMATTER_FUNC_POST_OPERAND,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function is invoked to format a register operand.
     */
    ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_REG,
    /**
     * This function is invoked to format a memory operand.
     *
     * Replacing this function might indirectly disable some specific calls to the
     * `ZYDIS_FORMATTER_FUNC_PRINT_TYPECAST`, `ZYDIS_FORMATTER_FUNC_PRINT_SEGMENT`,
     * `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS` and `ZYDIS_FORMATTER_FUNC_PRINT_DISP` functions.
     */
    ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_MEM,
    /**
     * This function is invoked to format a pointer operand.
     */
    ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_PTR,
    /**
     * This function is invoked to format an immediate operand.
     *
     * Replacing this function might indirectly disable some specific calls to the
     * `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS`, `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_REL` and
     * `ZYDIS_FORMATTER_FUNC_PRINT_IMM` functions.
     */
    ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_IMM,

    /* ---------------------------------------------------------------------------------------- */
    /* Elemental tokens                                                                         */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function is invoked to print the instruction mnemonic.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_MNEMONIC,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function is invoked to print a register.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_REGISTER,
    /**
     * This function is invoked to print absolute addresses.
     *
     * Conditionally invoked, if a runtime-address different to `ZYDIS_RUNTIME_ADDRESS_NONE` was
     * passed:
     * - `IMM` operands with relative address (e.g. `JMP`, `CALL`, ...)
     * - `MEM` operands with `EIP`/`RIP`-relative address (e.g. `MOV RAX, [RIP+0x12345678]`)
     *
     * Always invoked for:
     * - `MEM` operands with absolute address (e.g. `MOV RAX, [0x12345678]`)
     */
    ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS,
    /**
     * This function is invoked to print relative addresses.
     *
     * Conditionally invoked, if `ZYDIS_RUNTIME_ADDRESS_NONE` was passed as runtime-address:
     * - `IMM` operands with relative address (e.g. `JMP`, `CALL`, ...)
     */
    ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_REL,
    /**
     * This function is invoked to print a memory displacement value.
     *
     * If the memory displacement contains an address and a runtime-address different to
     * `ZYDIS_RUNTIME_ADDRESS_NONE` was passed, `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS` is called
     * instead.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_DISP,
    /**
     * This function is invoked to print an immediate value.
     *
     * If the immediate contains an address and a runtime-address different to
     * `ZYDIS_RUNTIME_ADDRESS_NONE` was passed, `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS` is called
     * instead.
     *
     * If the immediate contains an address and `ZYDIS_RUNTIME_ADDRESS_NONE` was passed as
     * runtime-address, `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_REL` is called instead.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_IMM,

    /* ---------------------------------------------------------------------------------------- */
    /* Optional tokens                                                                          */
    /* ---------------------------------------------------------------------------------------- */

    /**
     * This function is invoked to print the size of a memory operand (`INTEL` only).
     */
    ZYDIS_FORMATTER_FUNC_PRINT_TYPECAST,
    /**
     * This function is invoked to print the segment-register of a memory operand.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_SEGMENT,
    /**
     * This function is invoked to print the instruction prefixes.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_PREFIXES,
    /**
     * This function is invoked after formatting an operand to print a `EVEX`/`MVEX`
     * decorator.
     */
    ZYDIS_FORMATTER_FUNC_PRINT_DECORATOR,

    /* ---------------------------------------------------------------------------------------- */

    /**
     * Maximum value of this enum.
     */
    ZYDIS_FORMATTER_FUNC_MAX_VALUE = ZYDIS_FORMATTER_FUNC_PRINT_DECORATOR,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_FORMATTER_FUNC_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_FORMATTER_FUNC_MAX_VALUE)
} ZydisFormatterFunction;

/* ---------------------------------------------------------------------------------------------- */
/* Decorator types                                                                                */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Enum of all decorator types.
 */
typedef enum ZydisDecorator_
{
    ZYDIS_DECORATOR_INVALID,
    /**
     * The embedded-mask decorator.
     */
    ZYDIS_DECORATOR_MASK,
    /**
     * The broadcast decorator.
     */
    ZYDIS_DECORATOR_BC,
    /**
     * The rounding-control decorator.
     */
    ZYDIS_DECORATOR_RC,
    /**
     * The suppress-all-exceptions decorator.
     */
    ZYDIS_DECORATOR_SAE,
    /**
     * The register-swizzle decorator.
     */
    ZYDIS_DECORATOR_SWIZZLE,
    /**
     * The conversion decorator.
     */
    ZYDIS_DECORATOR_CONVERSION,
    /**
     * The eviction-hint decorator.
     */
    ZYDIS_DECORATOR_EH,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_DECORATOR_MAX_VALUE = ZYDIS_DECORATOR_EH,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_DECORATOR_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_DECORATOR_MAX_VALUE)
} ZydisDecorator;

/* ---------------------------------------------------------------------------------------------- */
/* Formatter context                                                                              */
/* ---------------------------------------------------------------------------------------------- */

typedef struct ZydisFormatter_ ZydisFormatter;

/**
 * Context structure that that is passed to all formatter.
 */
typedef struct ZydisFormatterContext_
{
    /**
     * A pointer to the `ZydisDecodedInstruction` struct.
     */
    const ZydisDecodedInstruction* instruction;
    /**
     * A pointer to the first `ZydisDecodedOperand` struct of the instruction.
     */
    const ZydisDecodedOperand* operands;
    /**
     * A pointer to the `ZydisDecodedOperand` struct.
     */
    const ZydisDecodedOperand* operand;
    /**
     * The runtime address of the instruction.
     */
    ZyanU64 runtime_address;
    /**
     * A pointer to user-defined data.
     *
     * This is the value that was previously passed as the `user_data` argument to
     * @ref ZydisFormatterFormatInstruction or @ref ZydisFormatterTokenizeOperand.
     */
    void* user_data;
} ZydisFormatterContext;

/* ---------------------------------------------------------------------------------------------- */
/* Function prototypes                                                                            */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Defines the `ZydisFormatterFunc` function prototype.
 *
 * @param   formatter   A pointer to the `ZydisFormatter` instance.
 * @param   buffer      A pointer to the `ZydisFormatterBuffer` struct.
 * @param   context     A pointer to the `ZydisFormatterContext` struct.
 *
 * @return  A zyan status code.
 *
 * Returning a status code other than `ZYAN_STATUS_SUCCESS` will immediately cause the formatting
 * process to fail (see exceptions below).
 *
 * Returning `ZYDIS_STATUS_SKIP_TOKEN` is valid for functions of the following types and will
 * instruct the formatter to omit the whole operand:
 * - `ZYDIS_FORMATTER_FUNC_PRE_OPERAND`
 * - `ZYDIS_FORMATTER_FUNC_POST_OPERAND`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_REG`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_MEM`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_PTR`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_IMM`
 *
 * This function prototype is used by functions of the following types:
 * - `ZYDIS_FORMATTER_FUNC_PRE_INSTRUCTION`
 * - `ZYDIS_FORMATTER_FUNC_POST_INSTRUCTION`
 * - `ZYDIS_FORMATTER_FUNC_PRE_OPERAND`
 * - `ZYDIS_FORMATTER_FUNC_POST_OPERAND`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_INSTRUCTION`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_MNEMONIC`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_PREFIXES`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_REG`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_MEM`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_PTR`
 * - `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_IMM`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_REL`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_DISP`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_IMM`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_TYPECAST`
 * - `ZYDIS_FORMATTER_FUNC_PRINT_SEGMENT`
 */
typedef ZyanStatus (*ZydisFormatterFunc)(const ZydisFormatter* formatter,
    ZydisFormatterBuffer* buffer, ZydisFormatterContext* context);

 /**
 * Defines the `ZydisFormatterRegisterFunc` function prototype.
 *
 * @param   formatter   A pointer to the `ZydisFormatter` instance.
 * @param   buffer      A pointer to the `ZydisFormatterBuffer` struct.
 * @param   context     A pointer to the `ZydisFormatterContext` struct.
 * @param   reg         The register.
 *
 * @return  Returning a status code other than `ZYAN_STATUS_SUCCESS` will immediately cause the
 *          formatting process to fail.
 *
 * This function prototype is used by functions of the following types:
 * - `ZYDIS_FORMATTER_FUNC_PRINT_REGISTER`.
 */
typedef ZyanStatus (*ZydisFormatterRegisterFunc)(const ZydisFormatter* formatter,
    ZydisFormatterBuffer* buffer, ZydisFormatterContext* context, ZydisRegister reg);

/**
 * Defines the `ZydisFormatterDecoratorFunc` function prototype.
 *
 * @param   formatter   A pointer to the `ZydisFormatter` instance.
 * @param   buffer      A pointer to the `ZydisFormatterBuffer` struct.
 * @param   context     A pointer to the `ZydisFormatterContext` struct.
 * @param   decorator   The decorator type.
 *
 * @return  Returning a status code other than `ZYAN_STATUS_SUCCESS` will immediately cause the
 *          formatting process to fail.
 *
 * This function type is used for:
 * - `ZYDIS_FORMATTER_FUNC_PRINT_DECORATOR`
 */
typedef ZyanStatus (*ZydisFormatterDecoratorFunc)(const ZydisFormatter* formatter,
    ZydisFormatterBuffer* buffer, ZydisFormatterContext* context, ZydisDecorator decorator);

/* ---------------------------------------------------------------------------------------------- */
/* Formatter struct                                                                               */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Context structure keeping track of internal state of the formatter.
 *
 * All fields in this struct should be considered as "private". Any changes may lead to unexpected
 * behavior.
 *
 * Do NOT change the order of the function fields or the values of the `ZydisFormatterFunction`
 * enum.
 */
struct ZydisFormatter_
{
    /**
     * The formatter style.
     */
    ZydisFormatterStyle style;
    /**
     * The `ZYDIS_FORMATTER_PROP_FORCE_SIZE` property.
     */
    ZyanBool force_memory_size;
    /**
     * The `ZYDIS_FORMATTER_PROP_FORCE_SEGMENT` property.
     */
    ZyanBool force_memory_segment;
    /**
     * The `ZYDIS_FORMATTER_PROP_FORCE_SCALE_ONE` property.
     */
    ZyanBool force_memory_scale;
    /**
     * The `ZYDIS_FORMATTER_PROP_FORCE_RELATIVE_BRANCHES` property.
     */
    ZyanBool force_relative_branches;
    /**
     * The `ZYDIS_FORMATTER_PROP_FORCE_RELATIVE_RIPREL` property.
     */
    ZyanBool force_relative_riprel;
    /**
     * The `ZYDIS_FORMATTER_PROP_PRINT_BRANCH_SIZE` property.
     */
    ZyanBool print_branch_size;
    /**
     * The `ZYDIS_FORMATTER_PROP_DETAILED_PREFIXES` property.
     */
    ZyanBool detailed_prefixes;
    /**
     * The `ZYDIS_FORMATTER_PROP_ADDR_BASE` property.
     */
    ZydisNumericBase addr_base;
    /**
     * The `ZYDIS_FORMATTER_PROP_ADDR_SIGNEDNESS` property.
     */
    ZydisSignedness addr_signedness;
    /**
     * The `ZYDIS_FORMATTER_PROP_ADDR_PADDING_ABSOLUTE` property.
     */
    ZydisPadding addr_padding_absolute;
    /**
     * The `ZYDIS_FORMATTER_PROP_ADDR_PADDING_RELATIVE` property.
     */
    ZydisPadding addr_padding_relative;
    /**
     * The `ZYDIS_FORMATTER_PROP_DISP_BASE` property.
     */
    ZydisNumericBase disp_base;
    /**
     * The `ZYDIS_FORMATTER_PROP_DISP_SIGNEDNESS` property.
     */
    ZydisSignedness disp_signedness;
    /**
     * The `ZYDIS_FORMATTER_PROP_DISP_PADDING` property.
     */
    ZydisPadding disp_padding;
    /**
     * The `ZYDIS_FORMATTER_PROP_IMM_BASE` property.
     */
    ZydisNumericBase imm_base;
    /**
     * The `ZYDIS_FORMATTER_PROP_IMM_SIGNEDNESS` property.
     */
    ZydisSignedness imm_signedness;
    /**
     * The `ZYDIS_FORMATTER_PROP_IMM_PADDING` property.
     */
    ZydisPadding imm_padding;
    /**
     * The `ZYDIS_FORMATTER_PROP_UPPERCASE_PREFIXES` property.
     */
    ZyanI32 case_prefixes;
    /**
     * The `ZYDIS_FORMATTER_PROP_UPPERCASE_MNEMONIC` property.
     */
    ZyanI32 case_mnemonic;
    /**
     * The `ZYDIS_FORMATTER_PROP_UPPERCASE_REGISTERS` property.
     */
    ZyanI32 case_registers;
    /**
     * The `ZYDIS_FORMATTER_PROP_UPPERCASE_TYPECASTS` property.
     */
    ZyanI32 case_typecasts;
    /**
     * The `ZYDIS_FORMATTER_PROP_UPPERCASE_DECORATORS` property.
     */
    ZyanI32 case_decorators;
    /**
     * The `ZYDIS_FORMATTER_PROP_HEX_UPPERCASE` property.
     */
    ZyanBool hex_uppercase;
    /**
     * The `ZYDIS_FORMATTER_PROP_HEX_FORCE_LEADING_NUMBER` property.
     */
    ZyanBool hex_force_leading_number;
    /**
     * The number formats for all numeric bases.
     *
     * Index 0 = prefix
     * Index 1 = suffix
     */
    struct
    {
        /**
         * A pointer to the `ZyanStringView` to use as prefix/suffix.
         */
        const ZyanStringView* string;
        /**
         * The `ZyanStringView` to use as prefix/suffix
         */
        ZyanStringView string_data;
        /**
         * The actual string data.
         */
        char buffer[11];
    } number_format[ZYDIS_NUMERIC_BASE_MAX_VALUE + 1][2];
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRE_INSTRUCTION` function.
     */
    ZydisFormatterFunc func_pre_instruction;
    /**
     * The `ZYDIS_FORMATTER_FUNC_POST_INSTRUCTION` function.
     */
    ZydisFormatterFunc func_post_instruction;
    /**
     * The `ZYDIS_FORMATTER_FUNC_FORMAT_INSTRUCTION` function.
     */
    ZydisFormatterFunc func_format_instruction;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRE_OPERAND` function.
     */
    ZydisFormatterFunc func_pre_operand;
    /**
     * The `ZYDIS_FORMATTER_FUNC_POST_OPERAND` function.
     */
    ZydisFormatterFunc func_post_operand;
    /**
     * The `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_REG` function.
     */
    ZydisFormatterFunc func_format_operand_reg;
    /**
     * The `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_MEM` function.
     */
    ZydisFormatterFunc func_format_operand_mem;
    /**
     * The `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_PTR` function.
     */
    ZydisFormatterFunc func_format_operand_ptr;
    /**
     * The `ZYDIS_FORMATTER_FUNC_FORMAT_OPERAND_IMM` function.
     */
    ZydisFormatterFunc func_format_operand_imm;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_MNEMONIC function.
     */
    ZydisFormatterFunc func_print_mnemonic;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_REGISTER` function.
     */
    ZydisFormatterRegisterFunc func_print_register;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_ABS` function.
     */
    ZydisFormatterFunc func_print_address_abs;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_ADDRESS_REL` function.
     */
    ZydisFormatterFunc func_print_address_rel;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_DISP` function.
     */
    ZydisFormatterFunc func_print_disp;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_IMM` function.
     */
    ZydisFormatterFunc func_print_imm;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_TYPECAST` function.
     */
    ZydisFormatterFunc func_print_typecast;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_SEGMENT` function.
     */
    ZydisFormatterFunc func_print_segment;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_PREFIXES` function.
     */
    ZydisFormatterFunc func_print_prefixes;
    /**
     * The `ZYDIS_FORMATTER_FUNC_PRINT_DECORATOR` function.
     */
    ZydisFormatterDecoratorFunc func_print_decorator;
};

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * @addtogroup formatter Formatter
 * Functions allowing formatting of previously decoded instructions to human readable text.
 * @{
 */

/* ---------------------------------------------------------------------------------------------- */
/* Initialization                                                                                 */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Initializes the given `ZydisFormatter` instance.
 *
 * @param   formatter   A pointer to the `ZydisFormatter` instance.
 * @param   style       The base formatter style (either `AT&T` or `Intel` style).
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterInit(ZydisFormatter* formatter, ZydisFormatterStyle style);

/* ---------------------------------------------------------------------------------------------- */
/* Setter                                                                                         */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Changes the value of the specified formatter `property`.
 *
 * @param   formatter   A pointer to the `ZydisFormatter` instance.
 * @param   property    The id of the formatter-property.
 * @param   value       The new value.
 *
 * @return  A zyan status code.
 *
 * This function returns `ZYAN_STATUS_INVALID_OPERATION` if a property can't be changed for the
 * current formatter-style.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterSetProperty(ZydisFormatter* formatter,
    ZydisFormatterProperty property, ZyanUPointer value);

/**
 * Replaces a formatter function with a custom callback and/or retrieves the currently
 * used function.
 *
 * @param   formatter   A pointer to the `ZydisFormatter` instance.
 * @param   type        The formatter function-type.
 * @param   callback    A pointer to a variable that contains the pointer of the callback function
 *                      and receives the pointer of the currently used function.
 *
 * @return  A zyan status code.
 *
 * Call this function with `callback` pointing to a `ZYAN_NULL` value to retrieve the currently
 * used function without replacing it.
 *
 * This function returns `ZYAN_STATUS_INVALID_OPERATION` if a function can't be replaced for the
 * current formatter-style.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterSetHook(ZydisFormatter* formatter,
    ZydisFormatterFunction type, const void** callback);

/* ---------------------------------------------------------------------------------------------- */
/* Formatting                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Formats the given instruction and writes it into the output buffer.
 *
 * @param   formatter       A pointer to the `ZydisFormatter` instance.
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operands        A pointer to the decoded operands array.
 * @param   operand_count   The length of the `operands` array. Must be equal to or greater than
 *                          the value of `instruction->operand_count_visible`.
 * @param   buffer          A pointer to the output buffer.
 * @param   length          The length of the output buffer (in characters).
 * @param   runtime_address The runtime address of the instruction or `ZYDIS_RUNTIME_ADDRESS_NONE`
 *                          to print relative addresses.
 * @param   user_data       A pointer to user-defined data which can be used in custom formatter
 *                          callbacks. Can be `ZYAN_NULL`.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterFormatInstruction(const ZydisFormatter* formatter,
    const ZydisDecodedInstruction* instruction, const ZydisDecodedOperand* operands,
    ZyanU8 operand_count, char* buffer, ZyanUSize length, ZyanU64 runtime_address,
    void* user_data);

/**
 * Formats the given operand and writes it into the output buffer.
 *
 * @param   formatter       A pointer to the `ZydisFormatter` instance.
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operand         A pointer to the `ZydisDecodedOperand` struct of the operand to format.
 * @param   buffer          A pointer to the output buffer.
 * @param   length          The length of the output buffer (in characters).
 * @param   runtime_address The runtime address of the instruction or `ZYDIS_RUNTIME_ADDRESS_NONE`
 *                          to print relative addresses.
 * @param   user_data       A pointer to user-defined data which can be used in custom formatter
 *                          callbacks. Can be `ZYAN_NULL`.
 *
 * @return  A zyan status code.
 *
 * Use `ZydisFormatterFormatInstruction` or `ZydisFormatterFormatInstructionEx` to format a
 * complete instruction.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterFormatOperand(const ZydisFormatter* formatter,
    const ZydisDecodedInstruction* instruction, const ZydisDecodedOperand* operand,
    char* buffer, ZyanUSize length, ZyanU64 runtime_address, void* user_data);

/* ---------------------------------------------------------------------------------------------- */
/* Tokenizing                                                                                     */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Tokenizes the given instruction and writes it into the output buffer.
 *
 * @param   formatter       A pointer to the `ZydisFormatter` instance.
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operands        A pointer to the decoded operands array.
 * @param   operand_count   The length of the `operands` array. Must be equal to or greater than
 *                          the value of `instruction->operand_count_visible`.
 * @param   buffer          A pointer to the output buffer.
 * @param   length          The length of the output buffer (in bytes).
 * @param   runtime_address The runtime address of the instruction or `ZYDIS_RUNTIME_ADDRESS_NONE`
 *                          to print relative addresses.
 * @param   token           Receives a pointer to the first token in the output buffer.
 * @param   user_data       A pointer to user-defined data which can be used in custom formatter
 *                          callbacks. Can be `ZYAN_NULL`.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterTokenizeInstruction(const ZydisFormatter* formatter,
    const ZydisDecodedInstruction* instruction, const ZydisDecodedOperand* operands,
    ZyanU8 operand_count, void* buffer, ZyanUSize length, ZyanU64 runtime_address,
    ZydisFormatterTokenConst** token, void* user_data);

/**
 * Tokenizes the given operand and writes it into the output buffer.
 *
 * @param   formatter       A pointer to the `ZydisFormatter` instance.
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operand         A pointer to the `ZydisDecodedOperand` struct of the operand to format.
 * @param   buffer          A pointer to the output buffer.
 * @param   length          The length of the output buffer (in bytes).
 * @param   runtime_address The runtime address of the instruction or `ZYDIS_RUNTIME_ADDRESS_NONE`
 *                          to print relative addresses.
 * @param   token           Receives a pointer to the first token in the output buffer.
 * @param   user_data       A pointer to user-defined data which can be used in custom formatter
 *                          callbacks. Can be `ZYAN_NULL`.
 *
 * @return  A zyan status code.
 *
 * Use `ZydisFormatterTokenizeInstruction` to tokenize a complete instruction.
 */
ZYDIS_EXPORT ZyanStatus ZydisFormatterTokenizeOperand(const ZydisFormatter* formatter,
    const ZydisDecodedInstruction* instruction, const ZydisDecodedOperand* operand,
    void* buffer, ZyanUSize length, ZyanU64 runtime_address, ZydisFormatterTokenConst** token,
    void* user_data);

/* ---------------------------------------------------------------------------------------------- */

/**
 * @}
 */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_FORMATTER_H */
#endif

#if !defined(ZYDIS_DISABLE_SEGMENT)

//
// Header: Zydis/Segment.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Functions and types providing encoding information about individual instruction bytes.
 */

#ifndef ZYDIS_SEGMENT_H
#define ZYDIS_SEGMENT_H


#ifdef __cplusplus
extern "C" {
#endif

/**
* @addtogroup segment Segment
* Functions and types providing encoding information about individual instruction bytes.
* @{
*/

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Constants                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

#define ZYDIS_MAX_INSTRUCTION_SEGMENT_COUNT 9

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines the `ZydisInstructionSegment` struct.
 */
typedef enum ZydisInstructionSegment_
{
    ZYDIS_INSTR_SEGMENT_NONE,
    /**
     * The legacy prefixes (including ignored `REX` prefixes).
     */
    ZYDIS_INSTR_SEGMENT_PREFIXES,
    /**
     * The effective `REX` prefix byte.
     */
    ZYDIS_INSTR_SEGMENT_REX,
    /**
     * The `XOP` prefix bytes.
     */
    ZYDIS_INSTR_SEGMENT_XOP,
    /**
     * The `VEX` prefix bytes.
     */
    ZYDIS_INSTR_SEGMENT_VEX,
    /**
     * The `EVEX` prefix bytes.
     */
    ZYDIS_INSTR_SEGMENT_EVEX,
    /**
     * The `MVEX` prefix bytes.
     */
    ZYDIS_INSTR_SEGMENT_MVEX,
    /**
     * The opcode bytes.
     */
    ZYDIS_INSTR_SEGMENT_OPCODE,
    /**
     * The `ModRM` byte.
     */
    ZYDIS_INSTR_SEGMENT_MODRM,
    /**
     * The `SIB` byte.
     */
    ZYDIS_INSTR_SEGMENT_SIB,
    /**
     * The displacement bytes.
     */
    ZYDIS_INSTR_SEGMENT_DISPLACEMENT,
    /**
     * The immediate bytes.
     */
    ZYDIS_INSTR_SEGMENT_IMMEDIATE,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_INSTR_SEGMENT_MAX_VALUE = ZYDIS_INSTR_SEGMENT_IMMEDIATE,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_INSTR_SEGMENT_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_INSTR_SEGMENT_MAX_VALUE)
} ZydisInstructionSegment;

/**
 * Defines the `ZydisInstructionSegments` struct.
 */
typedef struct ZydisInstructionSegments_
{
    /**
     * The number of logical instruction segments.
     */
    ZyanU8 count;
    struct
    {
        /**
         * The type of the segment.
         */
        ZydisInstructionSegment type;
        /**
         * The offset of the segment relative to the start of the instruction (in bytes).
         */
        ZyanU8 offset;
        /**
         * The size of the segment, in bytes.
         */
        ZyanU8 size;
    } segments[ZYDIS_MAX_INSTRUCTION_SEGMENT_COUNT];
} ZydisInstructionSegments;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * Returns offsets and sizes of all logical instruction segments (e.g. `OPCODE`,
 * `MODRM`, ...).
 *
 * @param   instruction A pointer to the `ZydisDecodedInstruction` struct.
 * @param   segments    Receives the instruction segments information.
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisGetInstructionSegments(const ZydisDecodedInstruction* instruction,
        ZydisInstructionSegments* segments);

/* ============================================================================================== */

/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_SEGMENT_H */
#endif

#if !defined(ZYDIS_DISABLE_DECODER) && !defined(ZYDIS_DISABLE_FORMATTER)

//
// Header: Zydis/Disassembler.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Joel Hoener

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * All-in-one convenience function providing the simplest possible way to use Zydis.
 */

#ifndef ZYDIS_DISASSEMBLER_H
#define ZYDIS_DISASSEMBLER_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Types                                                                                          */
/* ============================================================================================== */

/**
 * All commonly used information about a decoded instruction that Zydis can provide.
 *
 * This structure is filled in by calling `ZydisDisassembleIntel` or `ZydisDisassembleATT`.
 */
typedef struct ZydisDisassembledInstruction_
{
    /**
     * The runtime address that was passed when disassembling the instruction.
     */
    ZyanU64 runtime_address;
    /**
     * General information about the decoded instruction in machine-readable format.
     */
    ZydisDecodedInstruction info;
    /**
     * The operands of the decoded instruction in a machine-readable format.
     *
     * The amount of actual operands can be determined by inspecting the corresponding fields
     * in the `info` member of this struct. Inspect `operand_count_visible` if you care about
     * visible operands (those that are printed by the formatter) or `operand_count` if you're
     * also interested in implicit operands (for example the registers implicitly accessed by
     * `pushad`). Unused entries are zeroed.
     */
    ZydisDecodedOperand operands[ZYDIS_MAX_OPERAND_COUNT];
    /**
     * The textual, human-readable representation of the instruction.
     *
     * Guaranteed to be zero-terminated.
     */
    char text[96];
} ZydisDisassembledInstruction;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * Disassemble an instruction and format it to human-readable text in a single step (Intel syntax).
 *
 * @param machine_mode      The machine mode to assume when disassembling. When in doubt, pass
 *                          `ZYDIS_MACHINE_MODE_LONG_64` for what is typically referred to as
 *                          "64-bit mode" or `ZYDIS_MACHINE_MODE_LEGACY_32` for "32-bit mode".
 * @param runtime_address   The program counter (`eip` / `rip`) to assume when formatting the
 *                          instruction. Many instructions behave differently depending on the
 *                          address they are located at.
 * @param buffer            A pointer to the raw instruction bytes that you wish to decode.
 * @param length            The length of the input buffer. Note that this can be bigger than the
 *                          actual size of the instruction -- you don't have to know the size up
 *                          front. This length is merely used to prevent Zydis from doing
 *                          out-of-bounds reads on your buffer.
 * @param instruction       A pointer to receive the decoded instruction information. Can be
 *                          uninitialized and reused on later calls.
 *
 * This is a convenience function intended as a quick path for getting started with using Zydis.
 * It internally calls a range of other more advanced functions to obtain all commonly needed
 * information about the instruction. It is likely that you won't need most of this information in
 * practice, so it is advisable to instead call these more advanced functions directly if you're
 * concerned about performance.
 *
 * This function essentially combines the following more advanced functions into a single call:
 *
 *   - `ZydisDecoderInit`
 *   - `ZydisDecoderDecodeInstruction`
 *   - `ZydisDecoderDecodeOperands`
 *   - `ZydisFormatterInit`
 *   - `ZydisFormatterFormatInstruction`
 *
 * @return  A zyan status code.
 */
ZYDIS_EXPORT ZyanStatus ZydisDisassembleIntel(ZydisMachineMode machine_mode,
    ZyanU64 runtime_address, const void* buffer, ZyanUSize length,
    ZydisDisassembledInstruction *instruction);

/**
 * Disassemble an instruction and format it to human-readable text in a single step (AT&T syntax).
 *
 * @copydetails ZydisDisassembleIntel
 */
ZYDIS_EXPORT ZyanStatus ZydisDisassembleATT(ZydisMachineMode machine_mode,
    ZyanU64 runtime_address, const void* buffer, ZyanUSize length,
    ZydisDisassembledInstruction *instruction);

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_DISASSEMBLER_H */
#endif


//
// Header: Zydis/Utils.h
//
// Include stack:
//   - Zydis/Zydis.h
//

/***************************************************************************************************

  Zyan Disassembler Library (Zydis)

  Original Author : Florian Bernd

 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.

***************************************************************************************************/

/**
 * @file
 * Other utility functions.
 */

#ifndef ZYDIS_UTILS_H
#define ZYDIS_UTILS_H


#ifdef __cplusplus
extern "C" {
#endif

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * @addtogroup utils Utils
 * Miscellaneous utility functions. Address translation and other helpers.
 * @{
 */

/* ---------------------------------------------------------------------------------------------- */
/* Address calculation                                                                            */
/* ---------------------------------------------------------------------------------------------- */

// TODO: Provide a function that works in minimal-mode and does not require a operand parameter

/**
 * Calculates the absolute address value for the given instruction operand.
 *
 * @param   instruction     A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operand         A pointer to the `ZydisDecodedOperand` struct.
 * @param   runtime_address The runtime address of the instruction.
 * @param   result_address  A pointer to the memory that receives the absolute address.
 *
 * @return  A zyan status code.
 *
 * You should use this function in the following cases:
 * - `IMM` operands with relative address (e.g. `JMP`, `CALL`, ...)
 * - `MEM` operands with `RIP`/`EIP`-relative address (e.g. `MOV RAX, [RIP+0x12345678]`)
 * - `MEM` operands with absolute address (e.g. `MOV RAX, [0x12345678]`)
 *   - The displacement needs to get truncated and zero extended
 */
ZYDIS_EXPORT ZyanStatus ZydisCalcAbsoluteAddress(const ZydisDecodedInstruction* instruction,
    const ZydisDecodedOperand* operand, ZyanU64 runtime_address, ZyanU64* result_address);

/**
 * Calculates the absolute address value for the given instruction operand.
 *
 * @param   instruction         A pointer to the `ZydisDecodedInstruction` struct.
 * @param   operand             A pointer to the `ZydisDecodedOperand` struct.
 * @param   runtime_address     The runtime address of the instruction.
 * @param   register_context    A pointer to the `ZydisRegisterContext` struct.
 * @param   result_address      A pointer to the memory that receives the absolute target-address.
 *
 * @return  A zyan status code.
 *
 * This function behaves like `ZydisCalcAbsoluteAddress` but takes an additional register-context
 * argument to allow calculation of addresses depending on runtime register values.
 *
 * Note that `IP/EIP/RIP` from the register-context will be ignored in favor of the passed
 * runtime-address.
 */
ZYDIS_EXPORT ZyanStatus ZydisCalcAbsoluteAddressEx(const ZydisDecodedInstruction* instruction,
    const ZydisDecodedOperand* operand, ZyanU64 runtime_address,
    const ZydisRegisterContext* register_context, ZyanU64* result_address);

/* ---------------------------------------------------------------------------------------------- */

/**
 * @}
 */

/* ============================================================================================== */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_UTILS_H */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @addtogroup version Version
 *
 * Functions for checking the library version and build options.
 *
 * @{
 */

/* ============================================================================================== */
/* Macros                                                                                         */
/* ============================================================================================== */

/* ---------------------------------------------------------------------------------------------- */
/* Constants                                                                                      */
/* ---------------------------------------------------------------------------------------------- */

/**
 * A macro that defines the zydis version.
 */
#define ZYDIS_VERSION (ZyanU64)0x0004000100000000

/* ---------------------------------------------------------------------------------------------- */
/* Helper macros                                                                                  */
/* ---------------------------------------------------------------------------------------------- */

/**
 * Extracts the major-part of the zydis version.
 *
 * @param   version The zydis version value
 */
#define ZYDIS_VERSION_MAJOR(version) (ZyanU16)(((version) & 0xFFFF000000000000) >> 48)

/**
 * Extracts the minor-part of the zydis version.
 *
 * @param   version The zydis version value
 */
#define ZYDIS_VERSION_MINOR(version) (ZyanU16)(((version) & 0x0000FFFF00000000) >> 32)

/**
 * Extracts the patch-part of the zydis version.
 *
 * @param   version The zydis version value
 */
#define ZYDIS_VERSION_PATCH(version) (ZyanU16)(((version) & 0x00000000FFFF0000) >> 16)

/**
 * Extracts the build-part of the zydis version.
 *
 * @param   version The zydis version value
 */
#define ZYDIS_VERSION_BUILD(version) (ZyanU16)((version) & 0x000000000000FFFF)

/* ---------------------------------------------------------------------------------------------- */

/* ============================================================================================== */
/* Enums and types                                                                                */
/* ============================================================================================== */

/**
 * Defines the `ZydisFeature` enum.
 */
typedef enum ZydisFeature_
{
    ZYDIS_FEATURE_DECODER,
    ZYDIS_FEATURE_ENCODER,
    ZYDIS_FEATURE_FORMATTER,
    ZYDIS_FEATURE_AVX512,
    ZYDIS_FEATURE_KNC,
    ZYDIS_FEATURE_SEGMENT,

    /**
     * Maximum value of this enum.
     */
    ZYDIS_FEATURE_MAX_VALUE = ZYDIS_FEATURE_SEGMENT,
    /**
     * The minimum number of bits required to represent all values of this enum.
     */
    ZYDIS_FEATURE_REQUIRED_BITS = ZYAN_BITS_TO_REPRESENT(ZYDIS_FEATURE_MAX_VALUE)
} ZydisFeature;

/* ============================================================================================== */
/* Exported functions                                                                             */
/* ============================================================================================== */

/**
 * Returns the zydis version.
 *
 * @return  The zydis version.
 *
 * Use the macros provided in this file to extract the major, minor, patch and build part from the
 * returned version value.
 */
ZYDIS_EXPORT ZyanU64 ZydisGetVersion(void);

/**
 * Checks, if the specified feature is enabled in the current zydis library instance.
 *
 * @param   feature The feature.
 *
 * @return  `ZYAN_STATUS_TRUE` if the feature is enabled, `ZYAN_STATUS_FALSE` if not. Another
 *          zyan status code, if an error occured.
 */
ZYDIS_EXPORT ZyanStatus ZydisIsFeatureEnabled(ZydisFeature feature);

/* ============================================================================================== */

/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* ZYDIS_H */