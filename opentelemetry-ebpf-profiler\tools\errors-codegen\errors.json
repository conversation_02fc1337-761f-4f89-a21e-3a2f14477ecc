[{"id": 0, "name": "ok", "description": "Sentinel value for success: not actually an error"}, {"id": 1, "name": "unreachable", "description": "Entered code that was believed to be unreachable"}, {"id": 2, "name": "stack_length_exceeded", "description": "The stack trace has reached its maximum length and could not be unwound further"}, {"id": 3, "name": "empty_stack", "description": "The trace stack was empty after unwinding completed"}, {"obsolete": true, "id": 4, "name": "lookup_per_cpu_frame_list", "description": "Failed to lookup entry in the per-CPU frame list"}, {"id": 5, "name": "max_tail_calls", "description": "Maximum number of tail calls was reached"}, {"id": 1000, "name": "hotspot_no_codeblob", "description": "Hotspot: Failure to get CodeBlob address (no heap or bad segmap)"}, {"id": 1001, "name": "hotspot_interpreter_fp", "description": "Hotspot: Failure to unwind interpreter due to invalid FP"}, {"id": 1002, "name": "hotspot_invalid_ra", "description": "Hotspot: Failure to unwind because return address was not found with heuristic"}, {"id": 1003, "name": "hotspot_invalid_codeblob", "description": "Hotspot: Failure to get codeblob data or matching it to current unwind state"}, {"id": 1004, "name": "hotspot_lr_unwinding_mid_trace", "description": "Hotspot: Unwind instructions requested LR unwinding mid-trace (nonsensical)"}, {"id": 2000, "name": "python_bad_code_object_addr", "description": "Python: Unable to read current PyCodeObject"}, {"id": 2001, "name": "python_no_proc_info", "description": "Python: No entry for this process exists in the Python process info array"}, {"id": 2002, "name": "python_bad_frame_object_addr", "description": "Python: Unable to read current PyFrameObject"}, {"id": 2003, "name": "python_bad_cframe_current_frame_addr", "description": "Python: Unable to read _PyCFrame.current_frame"}, {"id": 2004, "name": "python_read_thread_state_addr", "description": "Python: Unable to read the thread state pointer from TLD"}, {"id": 2005, "name": "python_zero_thread_state", "description": "Python: The thread state pointer read from TSD is zero"}, {"id": 2006, "name": "python_bad_thread_state_frame_addr", "description": "Python: Unable to read the frame pointer from the thread state object"}, {"id": 2007, "name": "python_bad_auto_tls_key_addr", "description": "Python: Unable to read autoTLSkey"}, {"id": 2008, "name": "python_read_tsd_base", "description": "Python: Unable to determine the base address for thread-specific data"}, {"id": 3000, "name": "ruby_no_proc_info", "description": "Ruby: No entry for this process exists in the Ruby process info array"}, {"id": 3001, "name": "ruby_read_stack_ptr", "description": "Ruby: Unable to read the stack pointer from the Ruby context"}, {"id": 3002, "name": "ruby_read_stack_size", "description": "Ruby: Unable to read the size of the VM stack from the Ruby context"}, {"id": 3003, "name": "ruby_read_cfp", "description": "Ruby: Unable to read the control frame pointer from the Ruby context"}, {"id": 3004, "name": "ruby_read_ep", "description": "Ruby: Unable to read the expression path from the Ruby frame"}, {"id": 3005, "name": "ruby_read_iseq_body", "description": "Ruby: Unable to read instruction sequence body"}, {"id": 3006, "name": "ruby_read_iseq_encoded", "description": "Ruby: Unable to read the instruction sequence encoded size"}, {"id": 3007, "name": "ruby_read_iseq_size", "description": "Ruby: Unable to read the instruction sequence size"}, {"id": 4000, "name": "native_lookup_text_section", "description": "Native: Unable to find the code section in the stack delta page info map"}, {"id": 4001, "name": "native_lookup_stack_delta_outer_map", "description": "Native: Unable to look up the outer stack delta map (invalid map ID)"}, {"id": 4002, "name": "native_lookup_stack_delta_inner_map", "description": "Native: Unable to look up the inner stack delta map (unknown text section ID)"}, {"id": 4003, "name": "native_exceeded_delta_lookup_iterations", "description": "Native: Exceeded the maximum number of binary search steps during stack delta lookup"}, {"id": 4004, "name": "native_lookup_range", "description": "Native: Unable to look up the stack delta from the inner map"}, {"id": 4005, "name": "native_stack_delta_invalid", "description": "Native: The stack delta read from the delta map is marked as invalid"}, {"id": 4006, "name": "native_stack_delta_stop", "description": "Native: The stack delta read from the delta map is a stop record"}, {"id": 4007, "name": "native_pc_read", "description": "Native: Unable to read the next instruction pointer from memory"}, {"id": 4008, "name": "native_lr_unwinding_mid_trace", "description": "Native: Unwind instructions requested LR unwinding mid-trace (nonsensical)"}, {"id": 4009, "name": "native_read_kernelmode_regs", "description": "Native: Unable to read the kernel-mode registers"}, {"id": 4010, "name": "native_chase_irq_stack_link", "description": "Native: Unable to read the IRQ stack link"}, {"id": 4011, "name": "native_unexpected_kernel_address", "description": "Native: Unexpectedly encountered a kernel mode pointer while attempting to unwind user-mode stack"}, {"id": 4012, "name": "native_no_pid_page_mapping", "description": "Native: Unable to locate the PID page mapping for the current instruction pointer"}, {"id": 4013, "name": "native_zero_pc", "description": "Native: Unexpectedly encountered a instruction pointer of zero"}, {"id": 4014, "name": "native_small_pc", "description": "Native: The instruction pointer is too small to be valid"}, {"id": 4015, "name": "native_bad_unwind_info_index", "description": "Native: Encountered an invalid unwind_info_array index"}, {"id": 4016, "name": "native_aarch64_32bit_compat_mode", "description": "Native: Code is running in ARM 32-bit compat mode."}, {"id": 4017, "name": "native_x64_32bit_compat_mode", "description": "Native: Code is running in x86_64 32-bit compat mode."}, {"id": 5000, "name": "v8_bad_fp", "description": "V8: Encountered a bad frame pointer during V8 unwinding"}, {"id": 5001, "name": "v8_bad_js_func", "description": "V8: The JavaScript function object read from memory is invalid"}, {"id": 5002, "name": "v8_no_proc_info", "description": "V8: No entry for this process exists in the V8 process info array"}, {"id": 6000, "name": "dotnet_no_proc_info", "description": "Dotnet: No entry for this process exists in the dotnet process info array"}, {"id": 6001, "name": "dotnet_bad_fp", "description": "Dotnet: Encountered a bad frame pointer during dotnet unwinding"}, {"id": 6002, "name": "dotnet_code_header", "description": "Dotnet: Failed to find or read CodeHeader"}, {"id": 6003, "name": "dotnet_code_too_large", "description": "Dotnet: Code object was too large to unwind in eBPF"}, {"id": 7000, "name": "luajit_no_proc_info", "description": "LuaJIT: No entry for this process exists in the LuaJIT process info array"}, {"id": 7001, "name": "luajit_read_lua_context", "description": "LuaJIT: Unable to read the Lua context"}, {"id": 7002, "name": "luajit_frame_read", "description": "LuaJIT: Unable to read the Lua frame"}, {"id": 7003, "name": "luajit_l_mismatch", "description": "LuaJIT: context pointer validity check failed"}, {"id": 7004, "name": "luajit_invalid_pc", "description": "LuaJIT: PC exceeds 24 bits"}]