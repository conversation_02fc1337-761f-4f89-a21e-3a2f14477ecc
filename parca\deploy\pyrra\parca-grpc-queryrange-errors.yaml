apiVersion: pyrra.dev/v1alpha1
kind: ServiceLevelObjective
metadata:
  name: parca-grpc-queryrange-errors
  namespace: parca
  labels:
    prometheus: k8s
    role: alert-rules
    pyrra.dev/team: parca
spec:
  target: '99'
  window: 2w
  indicator:
    ratio:
      errors:
        metric: grpc_server_handled_total{grpc_service="parca.query.v1alpha1.QueryService",grpc_method="QueryRange",grpc_code=~"Aborted|Unavailable|Internal|Unknown|Unimplemented|DataLoss"}
      total:
        metric: grpc_server_handled_total{grpc_service="parca.query.v1alpha1.QueryService",grpc_method="QueryRange"}
