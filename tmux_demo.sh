#!/bin/bash

# Deep-eBPF Tmux Demo Script
# This script creates a tmux session with synchronized windows for demo

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SESSION_NAME="deep-ebpf-demo"
DEEP_EBPF_PATH="/opt/deep-ebpf"

echo -e "${BLUE}🚀 Deep-eBPF Tmux Demo Setup${NC}"
echo "================================"

# Check if tmux is installed
if ! command -v tmux &> /dev/null; then
    echo -e "${RED}❌ tmux is not installed. Installing...${NC}"
    sudo apt update && sudo apt install -y tmux
fi

# Check if we're in WSL
if grep -q microsoft /proc/version; then
    echo -e "${YELLOW}⚠️  Running in WSL environment${NC}"
    DEEP_EBPF_PATH="/mnt/c/github-current/ebpf-tracing"
fi

# Verify deep-ebpf exists
if [ ! -d "$DEEP_EBPF_PATH" ]; then
    echo -e "${RED}❌ Deep-eBPF not found at $DEEP_EBPF_PATH${NC}"
    echo "Please update DEEP_EBPF_PATH in this script"
    exit 1
fi

echo -e "${GREEN}✅ Found Deep-eBPF at: $DEEP_EBPF_PATH${NC}"

# Kill existing session if it exists
tmux kill-session -t $SESSION_NAME 2>/dev/null || true

echo -e "${BLUE}📺 Creating tmux session: $SESSION_NAME${NC}"

# Create new tmux session with first window
tmux new-session -d -s $SESSION_NAME -n "server" -c "$DEEP_EBPF_PATH/deep-ebpf-server"

# Configure the server window
tmux send-keys -t $SESSION_NAME:server "clear" Enter
tmux send-keys -t $SESSION_NAME:server "echo '🖥️  Deep-eBPF Server Window'" Enter
tmux send-keys -t $SESSION_NAME:server "echo 'Commands available:'" Enter
tmux send-keys -t $SESSION_NAME:server "echo '  start-server  - Start the deep-ebpf-server'" Enter
tmux send-keys -t $SESSION_NAME:server "echo '  check-health  - Check server health'" Enter
tmux send-keys -t $SESSION_NAME:server "echo '  stop-server   - Stop the server'" Enter
tmux send-keys -t $SESSION_NAME:server "echo ''" Enter

# Add server aliases
tmux send-keys -t $SESSION_NAME:server "alias start-server='sudo ./deep-ebpf-server --dev --listen :8080'" Enter
tmux send-keys -t $SESSION_NAME:server "alias check-health='curl -s http://localhost:8080/health && echo'" Enter
tmux send-keys -t $SESSION_NAME:server "alias check-status='curl -s http://localhost:8080/api/status | head -20'" Enter
tmux send-keys -t $SESSION_NAME:server "alias stop-server='sudo pkill deep-ebpf-server'" Enter

# Create tracer window
tmux new-window -t $SESSION_NAME -n "tracer" -c "$DEEP_EBPF_PATH/deep-ebpf-node"

# Configure the tracer window
tmux send-keys -t $SESSION_NAME:tracer "clear" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '🔍 Deep-eBPF Tracer Window'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo 'Commands available:'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '  start-test     - Start test program and get PID'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '  trace-human    - Trace with human-readable output'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '  trace-json     - Trace with JSON output'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '  trace-server   - Trace with server integration'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '  trace-all      - Trace ALL processes (system-wide)'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo '  stop-test      - Stop test programs'" Enter
tmux send-keys -t $SESSION_NAME:tracer "echo ''" Enter

# Add tracer aliases
tmux send-keys -t $SESSION_NAME:tracer "alias start-test='./test/simple_test & export TEST_PID=\$!; echo \"Test PID: \$TEST_PID\"'" Enter
tmux send-keys -t $SESSION_NAME:tracer "alias trace-human='sudo ./deep-ebpf-node --target-pid \$TEST_PID --format human --enable-stack-traces --enable-arguments --debug'" Enter
tmux send-keys -t $SESSION_NAME:tracer "alias trace-json='sudo ./deep-ebpf-node --target-pid \$TEST_PID --format json --debug'" Enter
tmux send-keys -t $SESSION_NAME:tracer "alias trace-server='sudo ./deep-ebpf-node --target-pid \$TEST_PID --format server --agent-id demo-agent --debug'" Enter
tmux send-keys -t $SESSION_NAME:tracer "alias trace-all='sudo ./deep-ebpf-node --format human --enable-stack-traces --enable-arguments --debug'" Enter
tmux send-keys -t $SESSION_NAME:tracer "alias stop-test='pkill simple_test'" Enter

# Create test window
tmux new-window -t $SESSION_NAME -n "test" -c "$DEEP_EBPF_PATH/deep-ebpf-node/test"

# Configure the test window
tmux send-keys -t $SESSION_NAME:test "clear" Enter
tmux send-keys -t $SESSION_NAME:test "echo '🧪 Test Programs Window'" Enter
tmux send-keys -t $SESSION_NAME:test "echo 'Available test programs:'" Enter
tmux send-keys -t $SESSION_NAME:test "ls -la simple_test 2>/dev/null || echo 'Building test programs...'" Enter
tmux send-keys -t $SESSION_NAME:test "make all" Enter
tmux send-keys -t $SESSION_NAME:test "echo 'Commands available:'" Enter
tmux send-keys -t $SESSION_NAME:test "echo '  ./simple_test          - Run C test program'" Enter
tmux send-keys -t $SESSION_NAME:test "echo '  watch-processes        - Monitor running processes'" Enter
tmux send-keys -t $SESSION_NAME:test "echo ''" Enter

# Add test aliases
tmux send-keys -t $SESSION_NAME:test "alias watch-processes='watch \"ps aux | grep -E (simple_test|deep-ebpf) | grep -v grep\"'" Enter

# Create monitoring window
tmux new-window -t $SESSION_NAME -n "monitor" -c "$DEEP_EBPF_PATH"

# Configure monitoring window
tmux send-keys -t $SESSION_NAME:monitor "clear" Enter
tmux send-keys -t $SESSION_NAME:monitor "echo '📊 System Monitoring Window'" Enter
tmux send-keys -t $SESSION_NAME:monitor "echo 'Commands available:'" Enter
tmux send-keys -t $SESSION_NAME:monitor "echo '  watch-system   - Monitor system resources'" Enter
tmux send-keys -t $SESSION_NAME:monitor "echo '  watch-ebpf     - Monitor eBPF programs'" Enter
tmux send-keys -t $SESSION_NAME:monitor "echo '  watch-logs     - Monitor system logs'" Enter
tmux send-keys -t $SESSION_NAME:monitor "echo ''" Enter

# Add monitoring aliases
tmux send-keys -t $SESSION_NAME:monitor "alias watch-system='htop'" Enter
tmux send-keys -t $SESSION_NAME:monitor "alias watch-ebpf='watch \"sudo bpftool prog list 2>/dev/null || echo eBPF programs not available\"'" Enter
tmux send-keys -t $SESSION_NAME:monitor "alias watch-logs='sudo journalctl -f'" Enter

# Set up pane layout for demo window
tmux new-window -t $SESSION_NAME -n "demo" -c "$DEEP_EBPF_PATH/deep-ebpf-node"

# Split demo window into panes
tmux split-window -t $SESSION_NAME:demo -h -c "$DEEP_EBPF_PATH/deep-ebpf-node/test"
tmux split-window -t $SESSION_NAME:demo.0 -v -c "$DEEP_EBPF_PATH/deep-ebpf-node"

# Configure demo panes
tmux send-keys -t $SESSION_NAME:demo.0 "echo '🔍 TRACER PANE'" Enter
tmux send-keys -t $SESSION_NAME:demo.0 "echo 'Ready to trace...'" Enter

tmux send-keys -t $SESSION_NAME:demo.1 "echo '📊 MONITOR PANE'" Enter
tmux send-keys -t $SESSION_NAME:demo.1 "echo 'System monitoring...'" Enter

tmux send-keys -t $SESSION_NAME:demo.2 "echo '🧪 TEST PANE'" Enter
tmux send-keys -t $SESSION_NAME:demo.2 "echo 'Test programs ready...'" Enter

# Go back to server window
tmux select-window -t $SESSION_NAME:server

# Create demo script
cat > /tmp/demo_commands.txt << 'EOF'
# Deep-eBPF Demo Commands

## 1. Start Server (in server window)
start-server

## 2. Check Health (in server window)
check-health

## 3. Start Test Program (in tracer window)
start-test

## 4. Trace with Human Output (in tracer window)
trace-human

## 5. Trace All Processes (in tracer window)
trace-all

## 6. Stop Test (in tracer window)
stop-test

## 7. Check Server Status (in server window)
check-status
EOF

echo -e "${GREEN}✅ Tmux session created successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Demo Instructions:${NC}"
echo "1. Attach to session: ${YELLOW}tmux attach -t $SESSION_NAME${NC}"
echo "2. Navigate windows: ${YELLOW}Ctrl+b + [0-4]${NC} or ${YELLOW}Ctrl+b + n/p${NC}"
echo "3. Available windows:"
echo "   - ${YELLOW}server${NC}  : Deep-eBPF Server"
echo "   - ${YELLOW}tracer${NC}  : Deep-eBPF Node (Tracer)"
echo "   - ${YELLOW}test${NC}    : Test Programs"
echo "   - ${YELLOW}monitor${NC} : System Monitoring"
echo "   - ${YELLOW}demo${NC}    : Split-pane demo layout"
echo ""
echo -e "${BLUE}🎯 Quick Demo Sequence:${NC}"
echo "1. Go to server window: ${YELLOW}Ctrl+b + 0${NC}"
echo "2. Start server: ${YELLOW}start-server${NC}"
echo "3. Go to tracer window: ${YELLOW}Ctrl+b + 1${NC}"
echo "4. Start test: ${YELLOW}start-test${NC}"
echo "5. Trace functions: ${YELLOW}trace-human${NC}"
echo ""
echo -e "${GREEN}🚀 Ready for demo! Run: ${YELLOW}tmux attach -t $SESSION_NAME${NC}"

# Auto-attach if requested
if [[ "$1" == "--attach" ]]; then
    echo -e "${BLUE}🔗 Auto-attaching to session...${NC}"
    tmux attach -t $SESSION_NAME
fi
