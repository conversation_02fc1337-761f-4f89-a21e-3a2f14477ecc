{"name": "@parca/store", "version": "0.16.178", "description": "State management for Parca UI", "main": "dist/index.js", "scripts": {"test": "jest --coverage --config ../../../jest.config.js ./src/*", "prepublish": "pnpm run build", "build": "tsc", "build-swc": "swc ./src -d dist --copy-files", "watch": "tsc-watch"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@parca/client": "workspace:*", "@parca/utilities": "workspace:*", "@reduxjs/toolkit": "^1.7.2", "immer": "9.0.21", "react-redux": "^8.0.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tsc-watch": "6.3.1"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "f92c5502bce797d27d67f57a39f8af30d0d04e1e"}