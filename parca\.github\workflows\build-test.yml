name: Go

on:
  push:
    branches:
    - main
    - release-*
  pull_request:
    branches:
    - main
    - release-*
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=npm depName=pnpm versioning=npm
  PNPM_VERSION: '10.13.1'

jobs:
  build-and-test:
    name: Test on ${{ matrix.arch }}
    runs-on: ${{ matrix.runner }}
    timeout-minutes: 90
    strategy:
      matrix:
        include:
          - arch: amd64
            runner: ubuntu-latest
          - arch: arm64
            runner: ubuntu-24.04-arm
    steps:
    - id: skip-check
      uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
      continue-on-error: true
      with:
        do_not_skip: '["schedule", "workflow_dispatch"]'
        paths: |-
          [
            "**.go",
            ".github/workflows/build-test.yml",
            ".go-version",
            ".node-version",
            "go.mod",
            "go.sum",
            "ui/**"
          ]
        skip_after_successful_duplicate: false

    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      if: ${{ steps.skip-check.outputs.should_skip != 'true' }}

    - name: Set up Go
      if: ${{ steps.skip-check.outputs.should_skip != 'true' }}
      uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version-file: .go-version

    - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
      with:
        version: ${{ env.PNPM_VERSION }}

    - name: Set up Node.js
      if: ${{ steps.skip-check.outputs.should_skip != 'true' }}
      uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
      with:
        node-version-file: .node-version
        cache: 'pnpm'
        cache-dependency-path: ui/pnpm-lock.yaml

    - name: Build
      if: ${{ steps.skip-check.outputs.should_skip != 'true' }}
      run: |
        make build

    - name: Test
      if: ${{ steps.skip-check.outputs.should_skip != 'true' }}
      run: |
        make test ENABLE_RACE=yes

    - name: Benchmark
      if: ${{ steps.skip-check.outputs.should_skip != 'true' && matrix.arch == 'amd64' }}
      run: |
        make go/bench

    - name: Archive generated artifacts
      if: ${{ steps.skip-check.outputs.should_skip != 'true' }}
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      with:
        name: parca-bin-${{ matrix.arch }}
        if-no-files-found: error
        path: |
          bin
