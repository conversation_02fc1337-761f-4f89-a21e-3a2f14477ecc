package python

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/mexyusef/deep-ebpf-node/ebpftracer"
	"k8s.io/klog/v2"
)

// PythonTracer provides Python-specific tracing capabilities
type PythonTracer struct {
	baseTracer *ebpftracer.Tracer
	pythonLib  string
	version    string
}

// PythonFrameInfo represents Python frame information
type PythonFrameInfo struct {
	Filename   string
	Function   string
	LineNumber int
	CodeObject uint64
}

// NewPythonTracer creates a new Python tracer
func NewPythonTracer(baseTracer *ebpftracer.Tracer) *PythonTracer {
	return &PythonTracer{
		baseTracer: baseTracer,
	}
}

// AttachToPythonProcess attaches Python-specific tracing to a process
func (pt *PythonTracer) AttachToPythonProcess(pid uint32) error {
	klog.V(2).Infof("Attaching Python tracer to PID %d", pid)

	// Find Python library
	pythonLib, err := pt.findPythonLibrary(pid)
	if err != nil {
		return fmt.Errorf("failed to find Python library: %w", err)
	}
	pt.pythonLib = pythonLib

	// Detect Python version
	version, err := pt.detectPythonVersion(pid)
	if err != nil {
		klog.V(2).Infof("Failed to detect Python version for PID %d: %v", pid, err)
		version = "unknown"
	}
	pt.version = version

	klog.V(2).Infof("Python %s detected for PID %d, library: %s", version, pid, pythonLib)

	// Attach to Python-specific functions
	return pt.attachPythonUprobes(pid, pythonLib)
}

// findPythonLibrary finds the Python library in the process memory map
func (pt *PythonTracer) findPythonLibrary(pid uint32) (string, error) {
	mapsPath := fmt.Sprintf("/proc/%d/maps", pid)
	file, err := os.Open(mapsPath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		
		// Look for libpython
		if strings.Contains(line, "libpython") && strings.Contains(line, ".so") {
			parts := strings.Fields(line)
			if len(parts) >= 6 {
				libPath := parts[5]
				if strings.Contains(libPath, "libpython") {
					return libPath, nil
				}
			}
		}
		
		// Look for python executable
		if strings.Contains(line, "python") && strings.Contains(line, "/usr") {
			parts := strings.Fields(line)
			if len(parts) >= 6 {
				execPath := parts[5]
				if strings.Contains(execPath, "python") && !strings.Contains(execPath, ".so") {
					return execPath, nil
				}
			}
		}
	}

	return "", fmt.Errorf("Python library not found in process memory map")
}

// detectPythonVersion detects the Python version
func (pt *PythonTracer) detectPythonVersion(pid uint32) (string, error) {
	// Try to read from /proc/PID/environ
	environPath := fmt.Sprintf("/proc/%d/environ", pid)
	data, err := os.ReadFile(environPath)
	if err != nil {
		return "", err
	}

	environ := string(data)
	
	// Look for Python version in environment
	if strings.Contains(environ, "PYTHON_VERSION=") {
		parts := strings.Split(environ, "PYTHON_VERSION=")
		if len(parts) > 1 {
			version := strings.Split(parts[1], "\x00")[0]
			return version, nil
		}
	}

	// Try to extract from executable path
	exePath, err := os.Readlink(fmt.Sprintf("/proc/%d/exe", pid))
	if err == nil {
		baseName := filepath.Base(exePath)
		if strings.Contains(baseName, "python3") {
			return "3.x", nil
		} else if strings.Contains(baseName, "python2") {
			return "2.x", nil
		}
	}

	return "unknown", nil
}

// attachPythonUprobes attaches uprobes to Python-specific functions
func (pt *PythonTracer) attachPythonUprobes(pid uint32, pythonLib string) error {
	// Python-specific functions to trace
	pythonFunctions := []string{
		"PyEval_EvalFrameEx",     // Python 2.x frame evaluation
		"_PyEval_EvalFrameDefault", // Python 3.x frame evaluation
		"PyObject_Call",          // Function calls
		"PyFunction_Call",        // Function calls
		"PyMethod_Call",          // Method calls
		"PyErr_SetString",        // Exception handling
	}

	attachedCount := 0
	for _, funcName := range pythonFunctions {
		if err := pt.attachPythonFunction(pid, pythonLib, funcName); err != nil {
			klog.V(2).Infof("Failed to attach to %s: %v", funcName, err)
			continue
		}
		attachedCount++
		klog.V(2).Infof("Attached uprobe to Python function: %s", funcName)
	}

	if attachedCount == 0 {
		return fmt.Errorf("failed to attach to any Python functions")
	}

	klog.V(2).Infof("Successfully attached to %d Python functions", attachedCount)
	return nil
}

// attachPythonFunction attaches a uprobe to a specific Python function
func (pt *PythonTracer) attachPythonFunction(pid uint32, pythonLib, funcName string) error {
	// This would use the base tracer to attach uprobes
	// For now, we'll simulate the attachment
	klog.V(3).Infof("Attaching uprobe to %s in %s for PID %d", funcName, pythonLib, pid)
	
	// In a real implementation, this would:
	// 1. Resolve the function symbol in the Python library
	// 2. Attach a uprobe using the base tracer
	// 3. Configure Python-specific event handling
	
	return nil
}

// ExtractPythonFrameInfo extracts Python frame information from an event
func (pt *PythonTracer) ExtractPythonFrameInfo(event *ebpftracer.Event) (*PythonFrameInfo, error) {
	if event.FunctionData == nil {
		return nil, fmt.Errorf("no function data in event")
	}

	// This would extract Python-specific information from the event
	// For now, we'll return basic information
	frameInfo := &PythonFrameInfo{
		Filename:   "unknown.py",
		Function:   "python_function",
		LineNumber: 0,
		CodeObject: event.FunctionData.FunctionAddr,
	}

	return frameInfo, nil
}

// GetPythonCallStack gets the Python call stack for a process
func (pt *PythonTracer) GetPythonCallStack(pid uint32) ([]PythonFrameInfo, error) {
	// This would walk the Python call stack
	// For now, return empty stack
	return []PythonFrameInfo{}, nil
}

// FormatPythonEvent formats a Python event for output
func (pt *PythonTracer) FormatPythonEvent(event *ebpftracer.Event) (string, error) {
	frameInfo, err := pt.ExtractPythonFrameInfo(event)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("Python: %s() in %s:%d", 
		frameInfo.Function, 
		frameInfo.Filename, 
		frameInfo.LineNumber), nil
}

// GetPythonVersion returns the detected Python version
func (pt *PythonTracer) GetPythonVersion() string {
	return pt.version
}

// GetPythonLibrary returns the Python library path
func (pt *PythonTracer) GetPythonLibrary() string {
	return pt.pythonLib
}

// IsPythonProcess checks if a process is running Python
func IsPythonProcess(pid uint32) bool {
	// Check executable path
	exePath, err := os.Readlink(fmt.Sprintf("/proc/%d/exe", pid))
	if err != nil {
		return false
	}

	baseName := filepath.Base(exePath)
	if strings.Contains(baseName, "python") {
		return true
	}

	// Check command line
	cmdlinePath := fmt.Sprintf("/proc/%d/cmdline", pid)
	data, err := os.ReadFile(cmdlinePath)
	if err != nil {
		return false
	}

	cmdline := string(data)
	return strings.Contains(cmdline, "python")
}

// GetPythonProcessInfo gets information about a Python process
func GetPythonProcessInfo(pid uint32) (map[string]string, error) {
	if !IsPythonProcess(pid) {
		return nil, fmt.Errorf("process %d is not a Python process", pid)
	}

	info := make(map[string]string)
	
	// Get executable path
	if exePath, err := os.Readlink(fmt.Sprintf("/proc/%d/exe", pid)); err == nil {
		info["executable"] = exePath
		info["version"] = "unknown"
		
		baseName := filepath.Base(exePath)
		if strings.Contains(baseName, "python3") {
			info["version"] = "3.x"
		} else if strings.Contains(baseName, "python2") {
			info["version"] = "2.x"
		}
	}

	// Get command line
	if cmdlineData, err := os.ReadFile(fmt.Sprintf("/proc/%d/cmdline", pid)); err == nil {
		cmdline := strings.Split(string(cmdlineData), "\x00")
		if len(cmdline) > 1 {
			info["script"] = cmdline[1]
		}
	}

	// Get environment variables
	if environData, err := os.ReadFile(fmt.Sprintf("/proc/%d/environ", pid)); err == nil {
		environ := strings.Split(string(environData), "\x00")
		for _, env := range environ {
			if strings.HasPrefix(env, "VIRTUAL_ENV=") {
				info["virtual_env"] = strings.TrimPrefix(env, "VIRTUAL_ENV=")
			}
			if strings.HasPrefix(env, "PYTHON_VERSION=") {
				info["version"] = strings.TrimPrefix(env, "PYTHON_VERSION=")
			}
		}
	}

	return info, nil
}
