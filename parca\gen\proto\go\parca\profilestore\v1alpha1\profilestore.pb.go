// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/profilestore/v1alpha1/profilestore.proto

package profilestorev1alpha1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WriteRequest may contain an apache arrow record that only contains profiling
// samples with a reference to a stacktrace ID, or a full stacktrace. If it
// only contains samples, the server may request the full stacktrace from the
// client should it not already know them.
type WriteRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The bytes containing the arrow record.
	Record        []byte `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WriteRequest) Reset() {
	*x = WriteRequest{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteRequest) ProtoMessage() {}

func (x *WriteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteRequest.ProtoReflect.Descriptor instead.
func (*WriteRequest) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{0}
}

func (x *WriteRequest) GetRecord() []byte {
	if x != nil {
		return x.Record
	}
	return nil
}

// WriteResponse may be empty if the server doesn't need any further
// information, or contain an arrow record that contains the stacktrace IDs
// that are unknown and therefore requested by the client from the server.
type WriteResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// When record is non-empty it contains the bytes of an arrow record that
	// contains a column containing the stacktraces that are unknown.
	Record        []byte `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WriteResponse) Reset() {
	*x = WriteResponse{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteResponse) ProtoMessage() {}

func (x *WriteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteResponse.ProtoReflect.Descriptor instead.
func (*WriteResponse) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{1}
}

func (x *WriteResponse) GetRecord() []byte {
	if x != nil {
		return x.Record
	}
	return nil
}

// WriteRawRequest writes a pprof profile for a given tenant
type WriteRawRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// tenant is the given tenant to store the pprof profile under
	//
	// Deprecated: Marked as deprecated in parca/profilestore/v1alpha1/profilestore.proto.
	Tenant string `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// series is a set raw pprof profiles and accompanying labels
	Series []*RawProfileSeries `protobuf:"bytes,2,rep,name=series,proto3" json:"series,omitempty"`
	// normalized is a flag indicating if the addresses in the profile is normalized for position independent code
	Normalized    bool `protobuf:"varint,3,opt,name=normalized,proto3" json:"normalized,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WriteRawRequest) Reset() {
	*x = WriteRawRequest{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteRawRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteRawRequest) ProtoMessage() {}

func (x *WriteRawRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteRawRequest.ProtoReflect.Descriptor instead.
func (*WriteRawRequest) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{2}
}

// Deprecated: Marked as deprecated in parca/profilestore/v1alpha1/profilestore.proto.
func (x *WriteRawRequest) GetTenant() string {
	if x != nil {
		return x.Tenant
	}
	return ""
}

func (x *WriteRawRequest) GetSeries() []*RawProfileSeries {
	if x != nil {
		return x.Series
	}
	return nil
}

func (x *WriteRawRequest) GetNormalized() bool {
	if x != nil {
		return x.Normalized
	}
	return false
}

// WriteRawResponse is the empty response
type WriteRawResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WriteRawResponse) Reset() {
	*x = WriteRawResponse{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteRawResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteRawResponse) ProtoMessage() {}

func (x *WriteRawResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteRawResponse.ProtoReflect.Descriptor instead.
func (*WriteRawResponse) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{3}
}

// RawProfileSeries represents the pprof profile and its associated labels
type RawProfileSeries struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// LabelSet is the key value pairs to identify the corresponding profile
	Labels *LabelSet `protobuf:"bytes,1,opt,name=labels,proto3" json:"labels,omitempty"`
	// samples are the set of profile bytes
	Samples       []*RawSample `protobuf:"bytes,2,rep,name=samples,proto3" json:"samples,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RawProfileSeries) Reset() {
	*x = RawProfileSeries{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawProfileSeries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawProfileSeries) ProtoMessage() {}

func (x *RawProfileSeries) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawProfileSeries.ProtoReflect.Descriptor instead.
func (*RawProfileSeries) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{4}
}

func (x *RawProfileSeries) GetLabels() *LabelSet {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *RawProfileSeries) GetSamples() []*RawSample {
	if x != nil {
		return x.Samples
	}
	return nil
}

// Label is a key value pair of identifiers
type Label struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name is the label name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// value is the value for the label name
	Value         string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Label) Reset() {
	*x = Label{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{5}
}

func (x *Label) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Label) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// LabelSet is a group of labels
type LabelSet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// labels are the grouping of labels
	Labels        []*Label `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelSet) Reset() {
	*x = LabelSet{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelSet) ProtoMessage() {}

func (x *LabelSet) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelSet.ProtoReflect.Descriptor instead.
func (*LabelSet) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{6}
}

func (x *LabelSet) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

// RawSample is the set of bytes that correspond to a pprof profile
type RawSample struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// raw_profile is the set of bytes of the pprof profile
	RawProfile []byte `protobuf:"bytes,1,opt,name=raw_profile,json=rawProfile,proto3" json:"raw_profile,omitempty"`
	// information about the executable and executable section for normalizaton
	// purposes.
	ExecutableInfo []*ExecutableInfo `protobuf:"bytes,2,rep,name=executable_info,json=executableInfo,proto3" json:"executable_info,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RawSample) Reset() {
	*x = RawSample{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawSample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawSample) ProtoMessage() {}

func (x *RawSample) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawSample.ProtoReflect.Descriptor instead.
func (*RawSample) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{7}
}

func (x *RawSample) GetRawProfile() []byte {
	if x != nil {
		return x.RawProfile
	}
	return nil
}

func (x *RawSample) GetExecutableInfo() []*ExecutableInfo {
	if x != nil {
		return x.ExecutableInfo
	}
	return nil
}

// ExecutableInfo is the information about the executable and executable
// section for normalizaton purposes before symbolization.
type ExecutableInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// elf_type is the type of the elf executable. Technically the elf type is a
	// 16 bit integer, but protobuf's smallest unsigned integer is 32 bits.
	ElfType uint32 `protobuf:"varint,1,opt,name=elf_type,json=elfType,proto3" json:"elf_type,omitempty"`
	// load_segment is the load segment of the executable.
	LoadSegment   *LoadSegment `protobuf:"bytes,2,opt,name=load_segment,json=loadSegment,proto3" json:"load_segment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecutableInfo) Reset() {
	*x = ExecutableInfo{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecutableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutableInfo) ProtoMessage() {}

func (x *ExecutableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutableInfo.ProtoReflect.Descriptor instead.
func (*ExecutableInfo) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{8}
}

func (x *ExecutableInfo) GetElfType() uint32 {
	if x != nil {
		return x.ElfType
	}
	return 0
}

func (x *ExecutableInfo) GetLoadSegment() *LoadSegment {
	if x != nil {
		return x.LoadSegment
	}
	return nil
}

// LoadSegment is the load segment of the executable
type LoadSegment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The offset from the beginning of the file at which the first byte of the segment resides.
	Offset uint64 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// The virtual address at which the first byte of the segment resides in memory.
	Vaddr         uint64 `protobuf:"varint,2,opt,name=vaddr,proto3" json:"vaddr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadSegment) Reset() {
	*x = LoadSegment{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadSegment) ProtoMessage() {}

func (x *LoadSegment) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadSegment.ProtoReflect.Descriptor instead.
func (*LoadSegment) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{9}
}

func (x *LoadSegment) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *LoadSegment) GetVaddr() uint64 {
	if x != nil {
		return x.Vaddr
	}
	return 0
}

// AgentsRequest is the request to retrieve a list of agents
type AgentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentsRequest) Reset() {
	*x = AgentsRequest{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentsRequest) ProtoMessage() {}

func (x *AgentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentsRequest.ProtoReflect.Descriptor instead.
func (*AgentsRequest) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{10}
}

// AgentsResponse is the request to retrieve a list of agents
type AgentsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// agents is a list of agents
	Agents        []*Agent `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentsResponse) Reset() {
	*x = AgentsResponse{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentsResponse) ProtoMessage() {}

func (x *AgentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentsResponse.ProtoReflect.Descriptor instead.
func (*AgentsResponse) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{11}
}

func (x *AgentsResponse) GetAgents() []*Agent {
	if x != nil {
		return x.Agents
	}
	return nil
}

// Agent is the agent representation
type Agent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is the agent identity that either represent by the node name or the IP address.
	// When node name is not found, this will fallback to IP address.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// last_error is the error message most recently received from a push attempt
	LastError string `protobuf:"bytes,2,opt,name=last_error,json=lastError,proto3" json:"last_error,omitempty"`
	// last_push is the time stamp the last push request was performed
	LastPush *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_push,json=lastPush,proto3" json:"last_push,omitempty"`
	// last_push_duration is the duration of the last push request
	LastPushDuration *durationpb.Duration `protobuf:"bytes,4,opt,name=last_push_duration,json=lastPushDuration,proto3" json:"last_push_duration,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Agent) Reset() {
	*x = Agent{}
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Agent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Agent) ProtoMessage() {}

func (x *Agent) ProtoReflect() protoreflect.Message {
	mi := &file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Agent.ProtoReflect.Descriptor instead.
func (*Agent) Descriptor() ([]byte, []int) {
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP(), []int{12}
}

func (x *Agent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Agent) GetLastError() string {
	if x != nil {
		return x.LastError
	}
	return ""
}

func (x *Agent) GetLastPush() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPush
	}
	return nil
}

func (x *Agent) GetLastPushDuration() *durationpb.Duration {
	if x != nil {
		return x.LastPushDuration
	}
	return nil
}

var File_parca_profilestore_v1alpha1_profilestore_proto protoreflect.FileDescriptor

const file_parca_profilestore_v1alpha1_profilestore_proto_rawDesc = "" +
	"\n" +
	".parca/profilestore/v1alpha1/profilestore.proto\x12\x1bparca.profilestore.v1alpha1\x1a\x1cgoogle/api/annotations.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"&\n" +
	"\fWriteRequest\x12\x16\n" +
	"\x06record\x18\x01 \x01(\fR\x06record\"'\n" +
	"\rWriteResponse\x12\x16\n" +
	"\x06record\x18\x01 \x01(\fR\x06record\"\x94\x01\n" +
	"\x0fWriteRawRequest\x12\x1a\n" +
	"\x06tenant\x18\x01 \x01(\tB\x02\x18\x01R\x06tenant\x12E\n" +
	"\x06series\x18\x02 \x03(\v2-.parca.profilestore.v1alpha1.RawProfileSeriesR\x06series\x12\x1e\n" +
	"\n" +
	"normalized\x18\x03 \x01(\bR\n" +
	"normalized\"\x12\n" +
	"\x10WriteRawResponse\"\x93\x01\n" +
	"\x10RawProfileSeries\x12=\n" +
	"\x06labels\x18\x01 \x01(\v2%.parca.profilestore.v1alpha1.LabelSetR\x06labels\x12@\n" +
	"\asamples\x18\x02 \x03(\v2&.parca.profilestore.v1alpha1.RawSampleR\asamples\"1\n" +
	"\x05Label\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"F\n" +
	"\bLabelSet\x12:\n" +
	"\x06labels\x18\x01 \x03(\v2\".parca.profilestore.v1alpha1.LabelR\x06labels\"\x82\x01\n" +
	"\tRawSample\x12\x1f\n" +
	"\vraw_profile\x18\x01 \x01(\fR\n" +
	"rawProfile\x12T\n" +
	"\x0fexecutable_info\x18\x02 \x03(\v2+.parca.profilestore.v1alpha1.ExecutableInfoR\x0eexecutableInfo\"x\n" +
	"\x0eExecutableInfo\x12\x19\n" +
	"\belf_type\x18\x01 \x01(\rR\aelfType\x12K\n" +
	"\fload_segment\x18\x02 \x01(\v2(.parca.profilestore.v1alpha1.LoadSegmentR\vloadSegment\";\n" +
	"\vLoadSegment\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x04R\x06offset\x12\x14\n" +
	"\x05vaddr\x18\x02 \x01(\x04R\x05vaddr\"\x0f\n" +
	"\rAgentsRequest\"L\n" +
	"\x0eAgentsResponse\x12:\n" +
	"\x06agents\x18\x01 \x03(\v2\".parca.profilestore.v1alpha1.AgentR\x06agents\"\xb8\x01\n" +
	"\x05Agent\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"last_error\x18\x02 \x01(\tR\tlastError\x127\n" +
	"\tlast_push\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\blastPush\x12G\n" +
	"\x12last_push_duration\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\x10lastPushDuration2\x9e\x02\n" +
	"\x13ProfileStoreService\x12\x86\x01\n" +
	"\bWriteRaw\x12,.parca.profilestore.v1alpha1.WriteRawRequest\x1a-.parca.profilestore.v1alpha1.WriteRawResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/profiles/writeraw\x12~\n" +
	"\x05Write\x12).parca.profilestore.v1alpha1.WriteRequest\x1a*.parca.profilestore.v1alpha1.WriteResponse\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/profiles/write(\x010\x012\x83\x01\n" +
	"\rAgentsService\x12r\n" +
	"\x06Agents\x12*.parca.profilestore.v1alpha1.AgentsRequest\x1a+.parca.profilestore.v1alpha1.AgentsResponse\"\x0f\x82\xd3\xe4\x93\x02\t\x12\a/agentsB\x9c\x02\n" +
	"\x1fcom.parca.profilestore.v1alpha1B\x11ProfilestoreProtoP\x01ZXgithub.com/parca-dev/parca/gen/proto/go/parca/profilestore/v1alpha1;profilestorev1alpha1\xa2\x02\x03PPX\xaa\x02\x1bParca.Profilestore.V1alpha1\xca\x02\x1bParca\\Profilestore\\V1alpha1\xe2\x02'Parca\\Profilestore\\V1alpha1\\GPBMetadata\xea\x02\x1dParca::Profilestore::V1alpha1b\x06proto3"

var (
	file_parca_profilestore_v1alpha1_profilestore_proto_rawDescOnce sync.Once
	file_parca_profilestore_v1alpha1_profilestore_proto_rawDescData []byte
)

func file_parca_profilestore_v1alpha1_profilestore_proto_rawDescGZIP() []byte {
	file_parca_profilestore_v1alpha1_profilestore_proto_rawDescOnce.Do(func() {
		file_parca_profilestore_v1alpha1_profilestore_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_profilestore_v1alpha1_profilestore_proto_rawDesc), len(file_parca_profilestore_v1alpha1_profilestore_proto_rawDesc)))
	})
	return file_parca_profilestore_v1alpha1_profilestore_proto_rawDescData
}

var file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_parca_profilestore_v1alpha1_profilestore_proto_goTypes = []any{
	(*WriteRequest)(nil),          // 0: parca.profilestore.v1alpha1.WriteRequest
	(*WriteResponse)(nil),         // 1: parca.profilestore.v1alpha1.WriteResponse
	(*WriteRawRequest)(nil),       // 2: parca.profilestore.v1alpha1.WriteRawRequest
	(*WriteRawResponse)(nil),      // 3: parca.profilestore.v1alpha1.WriteRawResponse
	(*RawProfileSeries)(nil),      // 4: parca.profilestore.v1alpha1.RawProfileSeries
	(*Label)(nil),                 // 5: parca.profilestore.v1alpha1.Label
	(*LabelSet)(nil),              // 6: parca.profilestore.v1alpha1.LabelSet
	(*RawSample)(nil),             // 7: parca.profilestore.v1alpha1.RawSample
	(*ExecutableInfo)(nil),        // 8: parca.profilestore.v1alpha1.ExecutableInfo
	(*LoadSegment)(nil),           // 9: parca.profilestore.v1alpha1.LoadSegment
	(*AgentsRequest)(nil),         // 10: parca.profilestore.v1alpha1.AgentsRequest
	(*AgentsResponse)(nil),        // 11: parca.profilestore.v1alpha1.AgentsResponse
	(*Agent)(nil),                 // 12: parca.profilestore.v1alpha1.Agent
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 14: google.protobuf.Duration
}
var file_parca_profilestore_v1alpha1_profilestore_proto_depIdxs = []int32{
	4,  // 0: parca.profilestore.v1alpha1.WriteRawRequest.series:type_name -> parca.profilestore.v1alpha1.RawProfileSeries
	6,  // 1: parca.profilestore.v1alpha1.RawProfileSeries.labels:type_name -> parca.profilestore.v1alpha1.LabelSet
	7,  // 2: parca.profilestore.v1alpha1.RawProfileSeries.samples:type_name -> parca.profilestore.v1alpha1.RawSample
	5,  // 3: parca.profilestore.v1alpha1.LabelSet.labels:type_name -> parca.profilestore.v1alpha1.Label
	8,  // 4: parca.profilestore.v1alpha1.RawSample.executable_info:type_name -> parca.profilestore.v1alpha1.ExecutableInfo
	9,  // 5: parca.profilestore.v1alpha1.ExecutableInfo.load_segment:type_name -> parca.profilestore.v1alpha1.LoadSegment
	12, // 6: parca.profilestore.v1alpha1.AgentsResponse.agents:type_name -> parca.profilestore.v1alpha1.Agent
	13, // 7: parca.profilestore.v1alpha1.Agent.last_push:type_name -> google.protobuf.Timestamp
	14, // 8: parca.profilestore.v1alpha1.Agent.last_push_duration:type_name -> google.protobuf.Duration
	2,  // 9: parca.profilestore.v1alpha1.ProfileStoreService.WriteRaw:input_type -> parca.profilestore.v1alpha1.WriteRawRequest
	0,  // 10: parca.profilestore.v1alpha1.ProfileStoreService.Write:input_type -> parca.profilestore.v1alpha1.WriteRequest
	10, // 11: parca.profilestore.v1alpha1.AgentsService.Agents:input_type -> parca.profilestore.v1alpha1.AgentsRequest
	3,  // 12: parca.profilestore.v1alpha1.ProfileStoreService.WriteRaw:output_type -> parca.profilestore.v1alpha1.WriteRawResponse
	1,  // 13: parca.profilestore.v1alpha1.ProfileStoreService.Write:output_type -> parca.profilestore.v1alpha1.WriteResponse
	11, // 14: parca.profilestore.v1alpha1.AgentsService.Agents:output_type -> parca.profilestore.v1alpha1.AgentsResponse
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_parca_profilestore_v1alpha1_profilestore_proto_init() }
func file_parca_profilestore_v1alpha1_profilestore_proto_init() {
	if File_parca_profilestore_v1alpha1_profilestore_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_profilestore_v1alpha1_profilestore_proto_rawDesc), len(file_parca_profilestore_v1alpha1_profilestore_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_parca_profilestore_v1alpha1_profilestore_proto_goTypes,
		DependencyIndexes: file_parca_profilestore_v1alpha1_profilestore_proto_depIdxs,
		MessageInfos:      file_parca_profilestore_v1alpha1_profilestore_proto_msgTypes,
	}.Build()
	File_parca_profilestore_v1alpha1_profilestore_proto = out.File
	file_parca_profilestore_v1alpha1_profilestore_proto_goTypes = nil
	file_parca_profilestore_v1alpha1_profilestore_proto_depIdxs = nil
}
