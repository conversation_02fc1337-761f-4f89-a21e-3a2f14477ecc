repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: end-of-file-fixer
        exclude: ^ui/packages/shared/client/
      - id: trailing-whitespace
        exclude: ^ui/packages/shared/client/
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.5
    hooks:
      - id: insert-license
        name: Insert license in comments (Go)
        args:
          - --license-filepath=scripts/LICENSE.txt
          - --comment-style=//
          - --use-current-year
          - --fuzzy-match-generates-todo
        exclude: \.pb(?:\.gw)?\.go$
        types: [go]
      - id: insert-license
        name: Insert license in comments (Shell)
        args:
          - --license-filepath=scripts/LICENSE.txt
          - --comment-style=#
          - --use-current-year
          - --fuzzy-match-generates-todo
        types: [shell]
      - id: insert-license
        name: Insert license in comments (TypeScript)
        args:
          - --license-filepath=scripts/LICENSE.txt
          - --comment-style=//
          - --fuzzy-match-generates-todo
        files: \.tsx?$
        exclude: ^ui/packages/shared/client/
  -   repo: https://github.com/tekwizely/pre-commit-golang
      rev: v1.0.0-rc.1
      hooks:
        - id: go-mod-tidy
  - repo: https://github.com/bufbuild/buf
    rev: v1.55.1
    hooks:
      # Hijack an existing hook ID to run buf generate
      # It also mean running `pre-commit run buf-format` will run both format and generate
      - id: buf-format
        name: buf generate
        entry: make proto/generate
      - id: buf-lint
      - id: buf-format
      - id: buf-breaking
        args:
          - --against=https://github.com/parca-dev/parca.git#branch=main,ref=HEAD~1,subdir=proto
  - repo: https://github.com/golangci/golangci-lint
    rev: v1.64.8
    hooks:
      - id: golangci-lint
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.1-beta
    hooks:
      - id: hadolint-docker
  - repo: https://github.com/koalaman/shellcheck-precommit
    rev: v0.10.0
    hooks:
      - id: shellcheck
  - repo: https://github.com/scop/pre-commit-shfmt
    rev: v3.7.0-1
    hooks:
      - id: shfmt
        # Google-style w/ 4-space indent
        args:
          - --write
          - --indent=4
          - --case-indent
          - --binary-next-line
  - repo: https://github.com/google/go-jsonnet
    rev: v0.20.0
    hooks:
      - id: jsonnet-format
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        files: \.(?:[jt]sx?|md|html|s?css)$
        additional_dependencies:
          - "prettier@2.8.3"
          - "@ianvs/prettier-plugin-sort-imports@3.7.1"
