# vim: ft=dockerfile
FROM docker.io/node:16.20.2-alpine@sha256:a1f9d027912b58a7c75be7716c97cfbc6d3099f3a97ed84aa490be9dee20e787 AS ui-deps

WORKDIR /app

COPY ui/packages/shared ./packages/shared
COPY ui/packages/app/web/package.json ./packages/app/web/package.json
COPY ui/package.json ui/pnpm-lock.yaml ./
RUN pnpm --filter @parca/web install --frozen-lockfile --prefer-offline

FROM docker.io/node:16.20.2-alpine@sha256:a1f9d027912b58a7c75be7716c97cfbc6d3099f3a97ed84aa490be9dee20e787 AS ui-builder

WORKDIR /app

COPY ui .
COPY --from=ui-deps /app/node_modules ./node_modules
RUN pnpm install --frozen-lockfile --prefer-offline && pnpm run build

FROM docker.io/golang:1.24.5-alpine@sha256:ddf52008bce1be455fe2b22d780b6693259aaf97b16383b6372f4b22dd33ad66 AS builder

# renovate: datasource=go depName=github.com/go-delve/delve
ARG DLV_VERSION=v1.25.0

# renovate: datasource=go depName=github.com/grpc-ecosystem/grpc-health-probe
ARG GRPC_HEALTH_PROBE_VERSION=v0.4.39

WORKDIR /app

# hadolint ignore=DL3018
RUN apk update && apk add --no-cache build-base
RUN go install "github.com/go-delve/delve/cmd/dlv@${DLV_VERSION}"
# hadolint ignore=DL3059
RUN go install "github.com/grpc-ecosystem/grpc-health-probe@${GRPC_HEALTH_PROBE_VERSION}"

COPY go.mod go.sum /app/
RUN go mod download -modcacherw

COPY ./ui/ui.go ./ui/ui.go
COPY --from=ui-builder /app/packages/app/web/build ./ui/packages/app/web/build

COPY ./cmd /app/cmd
COPY ./pkg /app/pkg
COPY ./proto /app/proto
COPY ./gen /app/gen

RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -gcflags="all=-N -l" -o parca ./cmd/parca

FROM docker.io/golang:1.24.5-alpine@sha256:ddf52008bce1be455fe2b22d780b6693259aaf97b16383b6372f4b22dd33ad66

COPY --from=builder /go/bin/dlv /
COPY --from=builder /go/bin/grpc-health-probe /
COPY --from=builder /app/parca /parca
COPY parca.yaml /parca.yaml

EXPOSE 7070

ENTRYPOINT ["/dlv", "--listen=:40000", "--headless=true", "--api-version=2", "--accept-multiclient", "exec", "--continue", "--"]
