name: Continuous Integration

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  GO:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.8'
      - run: sudo apt install -y libsystemd-dev
      - name: gofmt -l .
        run: files=$(gofmt -l .); if [[ -n "$files" ]]; then echo "$files"; exit 1; fi
      - name: goimports -l .
        run: |
          go install golang.org/x/tools/cmd/goimports@latest
          files=$(goimports -l .); if [[ -n "$files" ]]; then echo "$files"; exit 1; fi
      - run: go vet ./...
      - run: go test ./...
      - run: go build -mod=readonly .
