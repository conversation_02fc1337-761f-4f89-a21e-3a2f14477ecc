{"coredump-ref": "09d9e469f8c86f8b23bf8642a6d1749874a6f9c9bf139850c3dff44d0ae7581a", "threads": [{"lwp": 43723, "frames": ["libc-2.31.so+0x4618b", "libc-2.31.so+0x25858", "libjvm.so+0x244002", "libjvm.so+0xf2fa83", "libjvm.so+0xf3036e", "libjvm.so+0xf303a1", "libjvm.so+0xdcd7fd", "libc-2.31.so+0x4620f", "libpthread-2.31.so+0xacd5", "libjli.so+0x904e", "libjli.so+0x5d80", "libjli.so+0x76d4", "java+0x12b2", "libc-2.31.so+0x270b2", "java+0x135d"]}, {"lwp": 43726, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe072c", "libjvm.so+0x712151", "libjvm.so+0x7132c2", "libjvm.so+0x601a4e", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43725, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xf5efdf", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43737, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe085a", "libjvm.so+0x5e5565", "libjvm.so+0x5e83b0", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43738, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xe45ffa", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43724, "frames": ["libc-2.31.so+0x11121f", "libjava.so+0x16bb7", "libjava.so+0x16610", "libjava.so+0xeb1a", "void java.io.FileOutputStream.writeBytes(byte[], int, int, boolean)+0 in FileOutputStream.java:0", "void java.io.FileOutputStream.write(byte[], int, int)+0 in FileOutputStream.java:347", "void java.io.BufferedOutputStream.flushBuffer()+1 in BufferedOutputStream.java:81", "void java.io.BufferedOutputStream.flush()+0 in BufferedOutputStream.java:142", "void java.io.PrintStream.write(byte[], int, int)+4 in PrintStream.java:570", "void sun.nio.cs.StreamEncoder.writeBytes()+11 in StreamEncoder.java:242", "void sun.nio.cs.StreamEncoder.implFlushBuffer()+1 in StreamEncoder.java:321", "void sun.nio.cs.StreamEncoder.flushBuffer()+2 in StreamEncoder.java:110", "void java.io.OutputStreamWriter.flushBuffer()+0 in OutputStreamWriter.java:178", "void java.io.PrintStream.write(java.lang.String)+4 in PrintStream.java:699", "void java.io.PrintStream.print(java.lang.String)+0 in PrintStream.java:863", "void Deopt.Handle(int)+9 in Deopt.java:20", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.Handle(int)+4 in Deopt.java:15", "void Deopt.main(java.lang.String[])+3 in Deopt.java:31", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x8d0734", "libjvm.so+0x8d2f62", "libjli.so+0x4a4d", "libjli.so+0x841c", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43727, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xf5efdf", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43743, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xf5efdf", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43735, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xbd446a", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43728, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0x7152ab", "libjvm.so+0x601a4e", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43729, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0x76b18a", "libjvm.so+0x76b397", "libjvm.so+0x601a4e", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43734, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe072c", "libjvm.so+0xcdd989", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43741, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc31d45", "libjvm.so+0xc03e24", "libjvm.so+0xe50735", "libjvm.so+0x8fa0b9", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "void jdk.internal.ref.CleanerImpl.run()+12 in CleanerImpl.java:140", "void java.lang.Thread.run()+1 in Thread.java:831", "void jdk.internal.misc.InnocuousThread.run()+2 in InnocuousThread.java:134", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x83dbb2", "libjvm.so+0x8f89a3", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43733, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xdcd310", "libjvm.so+0xc1ae14", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43740, "frames": ["libc-2.31.so+0xe03bf", "libc-2.31.so+0xe6046", "libjvm.so+0xc30b79", "libjvm.so+0xea7aca", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43730, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xf3623c", "libjvm.so+0xf36e47", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43739, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe072c", "libjvm.so+0xbf69e1", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43731, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe085a", "libjvm.so+0x9028cb", "void java.lang.ref.Reference.waitForReferencePendingList()+0 in Reference.java:0", "void java.lang.ref.Reference.processPendingReferences()+0 in Reference.java:243", "void java.lang.ref.Reference$ReferenceHandler.run()+0 in Reference.java:215", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x83dbb2", "libjvm.so+0x8f89a3", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43736, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe085a", "libjvm.so+0x5e5565", "libjvm.so+0x5e83b0", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43732, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc31b9a", "libjvm.so+0xc0416c", "libjvm.so+0xe50735", "libjvm.so+0x8fa0b9", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove()+0 in ReferenceQueue.java:176", "void java.lang.ref.Finalizer$FinalizerThread.run()+17 in Finalizer.java:171", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x83dbb2", "libjvm.so+0x8f89a3", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}], "modules": null}