// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: parca/query/v1alpha1/query.proto

package queryv1alpha1

import (
	v1alpha11 "github.com/parca-dev/parca/gen/proto/go/parca/metastore/v1alpha1"
	v1alpha1 "github.com/parca-dev/parca/gen/proto/go/parca/profilestore/v1alpha1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Mode specifies the type of diff
type ProfileDiffSelection_Mode int32

const (
	// MODE_SINGLE_UNSPECIFIED default unspecified
	ProfileDiffSelection_MODE_SINGLE_UNSPECIFIED ProfileDiffSelection_Mode = 0
	// MODE_MERGE merge profile
	ProfileDiffSelection_MODE_MERGE ProfileDiffSelection_Mode = 1
)

// Enum value maps for ProfileDiffSelection_Mode.
var (
	ProfileDiffSelection_Mode_name = map[int32]string{
		0: "MODE_SINGLE_UNSPECIFIED",
		1: "MODE_MERGE",
	}
	ProfileDiffSelection_Mode_value = map[string]int32{
		"MODE_SINGLE_UNSPECIFIED": 0,
		"MODE_MERGE":              1,
	}
)

func (x ProfileDiffSelection_Mode) Enum() *ProfileDiffSelection_Mode {
	p := new(ProfileDiffSelection_Mode)
	*p = x
	return p
}

func (x ProfileDiffSelection_Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProfileDiffSelection_Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_query_v1alpha1_query_proto_enumTypes[0].Descriptor()
}

func (ProfileDiffSelection_Mode) Type() protoreflect.EnumType {
	return &file_parca_query_v1alpha1_query_proto_enumTypes[0]
}

func (x ProfileDiffSelection_Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProfileDiffSelection_Mode.Descriptor instead.
func (ProfileDiffSelection_Mode) EnumDescriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{10, 0}
}

// Mode is the type of query request
type QueryRequest_Mode int32

const (
	// MODE_SINGLE_UNSPECIFIED query unspecified
	QueryRequest_MODE_SINGLE_UNSPECIFIED QueryRequest_Mode = 0
	// MODE_DIFF is a diff query
	QueryRequest_MODE_DIFF QueryRequest_Mode = 1
	// MODE_MERGE is a merge query
	QueryRequest_MODE_MERGE QueryRequest_Mode = 2
)

// Enum value maps for QueryRequest_Mode.
var (
	QueryRequest_Mode_name = map[int32]string{
		0: "MODE_SINGLE_UNSPECIFIED",
		1: "MODE_DIFF",
		2: "MODE_MERGE",
	}
	QueryRequest_Mode_value = map[string]int32{
		"MODE_SINGLE_UNSPECIFIED": 0,
		"MODE_DIFF":               1,
		"MODE_MERGE":              2,
	}
)

func (x QueryRequest_Mode) Enum() *QueryRequest_Mode {
	p := new(QueryRequest_Mode)
	*p = x
	return p
}

func (x QueryRequest_Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueryRequest_Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_query_v1alpha1_query_proto_enumTypes[1].Descriptor()
}

func (QueryRequest_Mode) Type() protoreflect.EnumType {
	return &file_parca_query_v1alpha1_query_proto_enumTypes[1]
}

func (x QueryRequest_Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueryRequest_Mode.Descriptor instead.
func (QueryRequest_Mode) EnumDescriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{11, 0}
}

// ReportType is the type of report to return
type QueryRequest_ReportType int32

const (
	// REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED unspecified
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	QueryRequest_REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED QueryRequest_ReportType = 0
	// REPORT_TYPE_PPROF unspecified
	QueryRequest_REPORT_TYPE_PPROF QueryRequest_ReportType = 1
	// REPORT_TYPE_TOP unspecified
	QueryRequest_REPORT_TYPE_TOP QueryRequest_ReportType = 2
	// REPORT_TYPE_CALLGRAPH unspecified
	QueryRequest_REPORT_TYPE_CALLGRAPH QueryRequest_ReportType = 3
	// REPORT_TYPE_FLAMEGRAPH_TABLE unspecified
	QueryRequest_REPORT_TYPE_FLAMEGRAPH_TABLE QueryRequest_ReportType = 4
	// REPORT_TYPE_FLAMEGRAPH_ARROW unspecified
	QueryRequest_REPORT_TYPE_FLAMEGRAPH_ARROW QueryRequest_ReportType = 5
	// REPORT_TYPE_SOURCE contains source code annotated with profiling information
	QueryRequest_REPORT_TYPE_SOURCE QueryRequest_ReportType = 6
	// REPORT_TYPE_TABLE_ARROW unspecified
	QueryRequest_REPORT_TYPE_TABLE_ARROW QueryRequest_ReportType = 7
	// REPORT_TYPE_PROFILE_METADATA contains metadata about the profile i.e. binaries, labels
	QueryRequest_REPORT_TYPE_PROFILE_METADATA QueryRequest_ReportType = 8
	// REPORT_TYPE_FLAMECHART contains flamechart representation of the report
	QueryRequest_REPORT_TYPE_FLAMECHART QueryRequest_ReportType = 9
)

// Enum value maps for QueryRequest_ReportType.
var (
	QueryRequest_ReportType_name = map[int32]string{
		0: "REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED",
		1: "REPORT_TYPE_PPROF",
		2: "REPORT_TYPE_TOP",
		3: "REPORT_TYPE_CALLGRAPH",
		4: "REPORT_TYPE_FLAMEGRAPH_TABLE",
		5: "REPORT_TYPE_FLAMEGRAPH_ARROW",
		6: "REPORT_TYPE_SOURCE",
		7: "REPORT_TYPE_TABLE_ARROW",
		8: "REPORT_TYPE_PROFILE_METADATA",
		9: "REPORT_TYPE_FLAMECHART",
	}
	QueryRequest_ReportType_value = map[string]int32{
		"REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED": 0,
		"REPORT_TYPE_PPROF":                  1,
		"REPORT_TYPE_TOP":                    2,
		"REPORT_TYPE_CALLGRAPH":              3,
		"REPORT_TYPE_FLAMEGRAPH_TABLE":       4,
		"REPORT_TYPE_FLAMEGRAPH_ARROW":       5,
		"REPORT_TYPE_SOURCE":                 6,
		"REPORT_TYPE_TABLE_ARROW":            7,
		"REPORT_TYPE_PROFILE_METADATA":       8,
		"REPORT_TYPE_FLAMECHART":             9,
	}
)

func (x QueryRequest_ReportType) Enum() *QueryRequest_ReportType {
	p := new(QueryRequest_ReportType)
	*p = x
	return p
}

func (x QueryRequest_ReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueryRequest_ReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_parca_query_v1alpha1_query_proto_enumTypes[2].Descriptor()
}

func (QueryRequest_ReportType) Type() protoreflect.EnumType {
	return &file_parca_query_v1alpha1_query_proto_enumTypes[2]
}

func (x QueryRequest_ReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueryRequest_ReportType.Descriptor instead.
func (QueryRequest_ReportType) EnumDescriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{11, 1}
}

// ProfileTypesRequest is the request to retrieve the list of available profile types.
type ProfileTypesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileTypesRequest) Reset() {
	*x = ProfileTypesRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileTypesRequest) ProtoMessage() {}

func (x *ProfileTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileTypesRequest.ProtoReflect.Descriptor instead.
func (*ProfileTypesRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{0}
}

// ProfileTypesResponse is the response to retrieve the list of available profile types.
type ProfileTypesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// types is the list of available profile types.
	Types         []*ProfileType `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileTypesResponse) Reset() {
	*x = ProfileTypesResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileTypesResponse) ProtoMessage() {}

func (x *ProfileTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileTypesResponse.ProtoReflect.Descriptor instead.
func (*ProfileTypesResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{1}
}

func (x *ProfileTypesResponse) GetTypes() []*ProfileType {
	if x != nil {
		return x.Types
	}
	return nil
}

// ProfileType is the type of a profile as well as the units the profile type is available in.
type ProfileType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name is the name of the profile type.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// sample_type is the type of the samples in the profile.
	SampleType string `protobuf:"bytes,2,opt,name=sample_type,json=sampleType,proto3" json:"sample_type,omitempty"`
	// sample_unit is the unit of the samples in the profile.
	SampleUnit string `protobuf:"bytes,3,opt,name=sample_unit,json=sampleUnit,proto3" json:"sample_unit,omitempty"`
	// period_type is the type of the periods in the profile.
	PeriodType string `protobuf:"bytes,4,opt,name=period_type,json=periodType,proto3" json:"period_type,omitempty"`
	// period_unit is the unit of the periods in the profile.
	PeriodUnit string `protobuf:"bytes,5,opt,name=period_unit,json=periodUnit,proto3" json:"period_unit,omitempty"`
	// delta describes whether the profile is a delta profile.
	Delta         bool `protobuf:"varint,6,opt,name=delta,proto3" json:"delta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileType) Reset() {
	*x = ProfileType{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileType) ProtoMessage() {}

func (x *ProfileType) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileType.ProtoReflect.Descriptor instead.
func (*ProfileType) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{2}
}

func (x *ProfileType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProfileType) GetSampleType() string {
	if x != nil {
		return x.SampleType
	}
	return ""
}

func (x *ProfileType) GetSampleUnit() string {
	if x != nil {
		return x.SampleUnit
	}
	return ""
}

func (x *ProfileType) GetPeriodType() string {
	if x != nil {
		return x.PeriodType
	}
	return ""
}

func (x *ProfileType) GetPeriodUnit() string {
	if x != nil {
		return x.PeriodUnit
	}
	return ""
}

func (x *ProfileType) GetDelta() bool {
	if x != nil {
		return x.Delta
	}
	return false
}

// QueryRangeRequest is the request for a set of profiles matching a query over a time window
type QueryRangeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// query is the query string to match profiles against
	Query string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	// start is the start of the query time window
	Start *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start,proto3" json:"start,omitempty"`
	// end is the end of the query time window
	End *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end,proto3" json:"end,omitempty"`
	// limit is the max number of profiles to include in the response
	Limit uint32 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	// step is the duration of each sample returned.
	Step *durationpb.Duration `protobuf:"bytes,5,opt,name=step,proto3" json:"step,omitempty"`
	// sum_by is the set of labels to sum by
	SumBy         []string `protobuf:"bytes,6,rep,name=sum_by,json=sumBy,proto3" json:"sum_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryRangeRequest) Reset() {
	*x = QueryRangeRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeRequest) ProtoMessage() {}

func (x *QueryRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeRequest.ProtoReflect.Descriptor instead.
func (*QueryRangeRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{3}
}

func (x *QueryRangeRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *QueryRangeRequest) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *QueryRangeRequest) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *QueryRangeRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *QueryRangeRequest) GetStep() *durationpb.Duration {
	if x != nil {
		return x.Step
	}
	return nil
}

func (x *QueryRangeRequest) GetSumBy() []string {
	if x != nil {
		return x.SumBy
	}
	return nil
}

// QueryRangeResponse is the set of matching profile values
type QueryRangeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// series is the set of metrics series that satisfy the query range request
	Series        []*MetricsSeries `protobuf:"bytes,1,rep,name=series,proto3" json:"series,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryRangeResponse) Reset() {
	*x = QueryRangeResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRangeResponse) ProtoMessage() {}

func (x *QueryRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRangeResponse.ProtoReflect.Descriptor instead.
func (*QueryRangeResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{4}
}

func (x *QueryRangeResponse) GetSeries() []*MetricsSeries {
	if x != nil {
		return x.Series
	}
	return nil
}

// MetricsSeries is a set of labels and corresponding sample values
type MetricsSeries struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// labelset is the set of key value pairs
	Labelset *v1alpha1.LabelSet `protobuf:"bytes,1,opt,name=labelset,proto3" json:"labelset,omitempty"`
	// samples is the set of top-level cumulative values of the corresponding profiles
	Samples []*MetricsSample `protobuf:"bytes,2,rep,name=samples,proto3" json:"samples,omitempty"`
	// period_type is the value type of profile period
	PeriodType *ValueType `protobuf:"bytes,3,opt,name=period_type,json=periodType,proto3" json:"period_type,omitempty"`
	// sample_type is the value type of profile sample
	SampleType    *ValueType `protobuf:"bytes,4,opt,name=sample_type,json=sampleType,proto3" json:"sample_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetricsSeries) Reset() {
	*x = MetricsSeries{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetricsSeries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricsSeries) ProtoMessage() {}

func (x *MetricsSeries) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricsSeries.ProtoReflect.Descriptor instead.
func (*MetricsSeries) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{5}
}

func (x *MetricsSeries) GetLabelset() *v1alpha1.LabelSet {
	if x != nil {
		return x.Labelset
	}
	return nil
}

func (x *MetricsSeries) GetSamples() []*MetricsSample {
	if x != nil {
		return x.Samples
	}
	return nil
}

func (x *MetricsSeries) GetPeriodType() *ValueType {
	if x != nil {
		return x.PeriodType
	}
	return nil
}

func (x *MetricsSeries) GetSampleType() *ValueType {
	if x != nil {
		return x.SampleType
	}
	return nil
}

// MetricsSample is a cumulative value and timestamp of a profile
type MetricsSample struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// timestamp is the time the profile was ingested
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// value is the cumulative value for the profile
	Value int64 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// value_per_second is the calculated per second average in the steps duration
	ValuePerSecond float64 `protobuf:"fixed64,3,opt,name=value_per_second,json=valuePerSecond,proto3" json:"value_per_second,omitempty"`
	// duration is the normalized aggregated duration the metric samples has been observed over.
	Duration      int64 `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetricsSample) Reset() {
	*x = MetricsSample{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetricsSample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricsSample) ProtoMessage() {}

func (x *MetricsSample) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricsSample.ProtoReflect.Descriptor instead.
func (*MetricsSample) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{6}
}

func (x *MetricsSample) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *MetricsSample) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *MetricsSample) GetValuePerSecond() float64 {
	if x != nil {
		return x.ValuePerSecond
	}
	return 0
}

func (x *MetricsSample) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

// MergeProfile contains parameters for a merge request
type MergeProfile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// query is the query string to match profiles for merge
	Query string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	// start is the beginning of the evaluation time window
	Start *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start,proto3" json:"start,omitempty"`
	// end is the end of the evaluation time window
	End           *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end,proto3" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MergeProfile) Reset() {
	*x = MergeProfile{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MergeProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeProfile) ProtoMessage() {}

func (x *MergeProfile) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeProfile.ProtoReflect.Descriptor instead.
func (*MergeProfile) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{7}
}

func (x *MergeProfile) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *MergeProfile) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *MergeProfile) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

// SingleProfile contains parameters for a single profile query request
type SingleProfile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// time is the point in time to perform the profile request
	Time *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	// query is the query string to retrieve the profile
	Query         string `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SingleProfile) Reset() {
	*x = SingleProfile{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SingleProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleProfile) ProtoMessage() {}

func (x *SingleProfile) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleProfile.ProtoReflect.Descriptor instead.
func (*SingleProfile) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{8}
}

func (x *SingleProfile) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *SingleProfile) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

// DiffProfile contains parameters for a profile diff request
type DiffProfile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// a is the first profile to diff
	A *ProfileDiffSelection `protobuf:"bytes,1,opt,name=a,proto3" json:"a,omitempty"`
	// b is the second profile to diff
	B *ProfileDiffSelection `protobuf:"bytes,2,opt,name=b,proto3" json:"b,omitempty"`
	// absolute diffing, by default comparisons are relative
	Absolute      *bool `protobuf:"varint,3,opt,name=absolute,proto3,oneof" json:"absolute,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DiffProfile) Reset() {
	*x = DiffProfile{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DiffProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiffProfile) ProtoMessage() {}

func (x *DiffProfile) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiffProfile.ProtoReflect.Descriptor instead.
func (*DiffProfile) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{9}
}

func (x *DiffProfile) GetA() *ProfileDiffSelection {
	if x != nil {
		return x.A
	}
	return nil
}

func (x *DiffProfile) GetB() *ProfileDiffSelection {
	if x != nil {
		return x.B
	}
	return nil
}

func (x *DiffProfile) GetAbsolute() bool {
	if x != nil && x.Absolute != nil {
		return *x.Absolute
	}
	return false
}

// ProfileDiffSelection contains the parameters of a diff selection
type ProfileDiffSelection struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// mode is the selection of the diff mode
	Mode ProfileDiffSelection_Mode `protobuf:"varint,1,opt,name=mode,proto3,enum=parca.query.v1alpha1.ProfileDiffSelection_Mode" json:"mode,omitempty"`
	// options are the available options for a diff selection
	//
	// Types that are valid to be assigned to Options:
	//
	//	*ProfileDiffSelection_Merge
	//	*ProfileDiffSelection_Single
	Options       isProfileDiffSelection_Options `protobuf_oneof:"options"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileDiffSelection) Reset() {
	*x = ProfileDiffSelection{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileDiffSelection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileDiffSelection) ProtoMessage() {}

func (x *ProfileDiffSelection) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileDiffSelection.ProtoReflect.Descriptor instead.
func (*ProfileDiffSelection) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{10}
}

func (x *ProfileDiffSelection) GetMode() ProfileDiffSelection_Mode {
	if x != nil {
		return x.Mode
	}
	return ProfileDiffSelection_MODE_SINGLE_UNSPECIFIED
}

func (x *ProfileDiffSelection) GetOptions() isProfileDiffSelection_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *ProfileDiffSelection) GetMerge() *MergeProfile {
	if x != nil {
		if x, ok := x.Options.(*ProfileDiffSelection_Merge); ok {
			return x.Merge
		}
	}
	return nil
}

func (x *ProfileDiffSelection) GetSingle() *SingleProfile {
	if x != nil {
		if x, ok := x.Options.(*ProfileDiffSelection_Single); ok {
			return x.Single
		}
	}
	return nil
}

type isProfileDiffSelection_Options interface {
	isProfileDiffSelection_Options()
}

type ProfileDiffSelection_Merge struct {
	// merge contains options for a merge request
	Merge *MergeProfile `protobuf:"bytes,2,opt,name=merge,proto3,oneof"`
}

type ProfileDiffSelection_Single struct {
	// single contains options for a single profile request
	Single *SingleProfile `protobuf:"bytes,3,opt,name=single,proto3,oneof"`
}

func (*ProfileDiffSelection_Merge) isProfileDiffSelection_Options() {}

func (*ProfileDiffSelection_Single) isProfileDiffSelection_Options() {}

// QueryRequest is a request for a profile query
type QueryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// mode indicates the type of query performed
	Mode QueryRequest_Mode `protobuf:"varint,1,opt,name=mode,proto3,enum=parca.query.v1alpha1.QueryRequest_Mode" json:"mode,omitempty"`
	// options are the options corresponding to the mode
	//
	// Types that are valid to be assigned to Options:
	//
	//	*QueryRequest_Diff
	//	*QueryRequest_Merge
	//	*QueryRequest_Single
	Options isQueryRequest_Options `protobuf_oneof:"options"`
	// report_type is the type of report to return
	ReportType QueryRequest_ReportType `protobuf:"varint,5,opt,name=report_type,json=reportType,proto3,enum=parca.query.v1alpha1.QueryRequest_ReportType" json:"report_type,omitempty"`
	// filter_query is the query string to filter the profile samples
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	FilterQuery *string `protobuf:"bytes,6,opt,name=filter_query,json=filterQuery,proto3,oneof" json:"filter_query,omitempty"`
	// node_trim_threshold is the threshold % where the nodes with Value less than this will be removed from the report
	NodeTrimThreshold *float32 `protobuf:"fixed32,7,opt,name=node_trim_threshold,json=nodeTrimThreshold,proto3,oneof" json:"node_trim_threshold,omitempty"`
	// group_by indicates the fields to group by
	GroupBy *GroupBy `protobuf:"bytes,8,opt,name=group_by,json=groupBy,proto3,oneof" json:"group_by,omitempty"`
	// source information about the source requested, required if source report is requested
	SourceReference *SourceReference `protobuf:"bytes,9,opt,name=source_reference,json=sourceReference,proto3,oneof" json:"source_reference,omitempty"`
	// which runtime frames to filter out, often interpreter frames like python or ruby are not super useful by default
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	RuntimeFilter *RuntimeFilter `protobuf:"bytes,10,opt,name=runtime_filter,json=runtimeFilter,proto3,oneof" json:"runtime_filter,omitempty"`
	// invert_call_stack inverts the call stacks in the flamegraph
	InvertCallStack *bool `protobuf:"varint,11,opt,name=invert_call_stack,json=invertCallStack,proto3,oneof" json:"invert_call_stack,omitempty"`
	// a set of filter to apply to the query request
	Filter []*Filter `protobuf:"bytes,12,rep,name=filter,proto3" json:"filter,omitempty"`
	// sandwich_by_function is a function name to use for sandwich view functionality
	SandwichByFunction *string `protobuf:"bytes,13,opt,name=sandwich_by_function,json=sandwichByFunction,proto3,oneof" json:"sandwich_by_function,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *QueryRequest) Reset() {
	*x = QueryRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRequest) ProtoMessage() {}

func (x *QueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRequest.ProtoReflect.Descriptor instead.
func (*QueryRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{11}
}

func (x *QueryRequest) GetMode() QueryRequest_Mode {
	if x != nil {
		return x.Mode
	}
	return QueryRequest_MODE_SINGLE_UNSPECIFIED
}

func (x *QueryRequest) GetOptions() isQueryRequest_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *QueryRequest) GetDiff() *DiffProfile {
	if x != nil {
		if x, ok := x.Options.(*QueryRequest_Diff); ok {
			return x.Diff
		}
	}
	return nil
}

func (x *QueryRequest) GetMerge() *MergeProfile {
	if x != nil {
		if x, ok := x.Options.(*QueryRequest_Merge); ok {
			return x.Merge
		}
	}
	return nil
}

func (x *QueryRequest) GetSingle() *SingleProfile {
	if x != nil {
		if x, ok := x.Options.(*QueryRequest_Single); ok {
			return x.Single
		}
	}
	return nil
}

func (x *QueryRequest) GetReportType() QueryRequest_ReportType {
	if x != nil {
		return x.ReportType
	}
	return QueryRequest_REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED
}

// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
func (x *QueryRequest) GetFilterQuery() string {
	if x != nil && x.FilterQuery != nil {
		return *x.FilterQuery
	}
	return ""
}

func (x *QueryRequest) GetNodeTrimThreshold() float32 {
	if x != nil && x.NodeTrimThreshold != nil {
		return *x.NodeTrimThreshold
	}
	return 0
}

func (x *QueryRequest) GetGroupBy() *GroupBy {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *QueryRequest) GetSourceReference() *SourceReference {
	if x != nil {
		return x.SourceReference
	}
	return nil
}

// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
func (x *QueryRequest) GetRuntimeFilter() *RuntimeFilter {
	if x != nil {
		return x.RuntimeFilter
	}
	return nil
}

func (x *QueryRequest) GetInvertCallStack() bool {
	if x != nil && x.InvertCallStack != nil {
		return *x.InvertCallStack
	}
	return false
}

func (x *QueryRequest) GetFilter() []*Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *QueryRequest) GetSandwichByFunction() string {
	if x != nil && x.SandwichByFunction != nil {
		return *x.SandwichByFunction
	}
	return ""
}

type isQueryRequest_Options interface {
	isQueryRequest_Options()
}

type QueryRequest_Diff struct {
	// diff contains the diff query options
	Diff *DiffProfile `protobuf:"bytes,2,opt,name=diff,proto3,oneof"`
}

type QueryRequest_Merge struct {
	// merge contains the merge query options
	Merge *MergeProfile `protobuf:"bytes,3,opt,name=merge,proto3,oneof"`
}

type QueryRequest_Single struct {
	// single contains the single query options
	Single *SingleProfile `protobuf:"bytes,4,opt,name=single,proto3,oneof"`
}

func (*QueryRequest_Diff) isQueryRequest_Options() {}

func (*QueryRequest_Merge) isQueryRequest_Options() {}

func (*QueryRequest_Single) isQueryRequest_Options() {}

// Filter to apply to the query request
type Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter is a oneof type of filter to apply to the query request
	//
	// Types that are valid to be assigned to Filter:
	//
	//	*Filter_StackFilter
	//	*Filter_FrameFilter
	Filter        isFilter_Filter `protobuf_oneof:"filter"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Filter) Reset() {
	*x = Filter{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{12}
}

func (x *Filter) GetFilter() isFilter_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *Filter) GetStackFilter() *StackFilter {
	if x != nil {
		if x, ok := x.Filter.(*Filter_StackFilter); ok {
			return x.StackFilter
		}
	}
	return nil
}

func (x *Filter) GetFrameFilter() *FrameFilter {
	if x != nil {
		if x, ok := x.Filter.(*Filter_FrameFilter); ok {
			return x.FrameFilter
		}
	}
	return nil
}

type isFilter_Filter interface {
	isFilter_Filter()
}

type Filter_StackFilter struct {
	// stack_filter is a filter for filtering by stacks
	StackFilter *StackFilter `protobuf:"bytes,1,opt,name=stack_filter,json=stackFilter,proto3,oneof"`
}

type Filter_FrameFilter struct {
	// frame_filter is a filter for filtering by frames
	FrameFilter *FrameFilter `protobuf:"bytes,2,opt,name=frame_filter,json=frameFilter,proto3,oneof"`
}

func (*Filter_StackFilter) isFilter_Filter() {}

func (*Filter_FrameFilter) isFilter_Filter() {}

// StackFilter is a filter for filtering by stacks
type StackFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter contains the different methods in which you can filter a stack
	//
	// Types that are valid to be assigned to Filter:
	//
	//	*StackFilter_FunctionNameStackFilter
	Filter        isStackFilter_Filter `protobuf_oneof:"filter"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StackFilter) Reset() {
	*x = StackFilter{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StackFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StackFilter) ProtoMessage() {}

func (x *StackFilter) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StackFilter.ProtoReflect.Descriptor instead.
func (*StackFilter) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{13}
}

func (x *StackFilter) GetFilter() isStackFilter_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *StackFilter) GetFunctionNameStackFilter() *FunctionNameStackFilter {
	if x != nil {
		if x, ok := x.Filter.(*StackFilter_FunctionNameStackFilter); ok {
			return x.FunctionNameStackFilter
		}
	}
	return nil
}

type isStackFilter_Filter interface {
	isStackFilter_Filter()
}

type StackFilter_FunctionNameStackFilter struct {
	// function_name_stack_filter is the function name to filter by
	FunctionNameStackFilter *FunctionNameStackFilter `protobuf:"bytes,1,opt,name=function_name_stack_filter,json=functionNameStackFilter,proto3,oneof"`
}

func (*StackFilter_FunctionNameStackFilter) isStackFilter_Filter() {}

// FunctionNameStackFilter is a filter for filtering by function name
type FunctionNameStackFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// function_to_filter is the function name to filter by
	FunctionToFilter string `protobuf:"bytes,1,opt,name=function_to_filter,json=functionToFilter,proto3" json:"function_to_filter,omitempty"`
	// exclude determines whether to exclude stacks matching the function
	Exclude       bool `protobuf:"varint,2,opt,name=exclude,proto3" json:"exclude,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FunctionNameStackFilter) Reset() {
	*x = FunctionNameStackFilter{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FunctionNameStackFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionNameStackFilter) ProtoMessage() {}

func (x *FunctionNameStackFilter) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionNameStackFilter.ProtoReflect.Descriptor instead.
func (*FunctionNameStackFilter) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{14}
}

func (x *FunctionNameStackFilter) GetFunctionToFilter() string {
	if x != nil {
		return x.FunctionToFilter
	}
	return ""
}

func (x *FunctionNameStackFilter) GetExclude() bool {
	if x != nil {
		return x.Exclude
	}
	return false
}

// FrameFilter is a filter for filtering by frames
type FrameFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter contains the different methods in which you can filter a frame
	//
	// Types that are valid to be assigned to Filter:
	//
	//	*FrameFilter_BinaryFrameFilter
	Filter        isFrameFilter_Filter `protobuf_oneof:"filter"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameFilter) Reset() {
	*x = FrameFilter{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameFilter) ProtoMessage() {}

func (x *FrameFilter) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameFilter.ProtoReflect.Descriptor instead.
func (*FrameFilter) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{15}
}

func (x *FrameFilter) GetFilter() isFrameFilter_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FrameFilter) GetBinaryFrameFilter() *BinaryFrameFilter {
	if x != nil {
		if x, ok := x.Filter.(*FrameFilter_BinaryFrameFilter); ok {
			return x.BinaryFrameFilter
		}
	}
	return nil
}

type isFrameFilter_Filter interface {
	isFrameFilter_Filter()
}

type FrameFilter_BinaryFrameFilter struct {
	// binary_frame_filter is the list of binary names to filter by
	BinaryFrameFilter *BinaryFrameFilter `protobuf:"bytes,1,opt,name=binary_frame_filter,json=binaryFrameFilter,proto3,oneof"`
}

func (*FrameFilter_BinaryFrameFilter) isFrameFilter_Filter() {}

// BinaryFrameFilter is a filter for filtering by binaries
type BinaryFrameFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// include_binaries is the list of binaries to filter by
	IncludeBinaries []string `protobuf:"bytes,1,rep,name=include_binaries,json=includeBinaries,proto3" json:"include_binaries,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BinaryFrameFilter) Reset() {
	*x = BinaryFrameFilter{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BinaryFrameFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BinaryFrameFilter) ProtoMessage() {}

func (x *BinaryFrameFilter) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BinaryFrameFilter.ProtoReflect.Descriptor instead.
func (*BinaryFrameFilter) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{16}
}

func (x *BinaryFrameFilter) GetIncludeBinaries() []string {
	if x != nil {
		return x.IncludeBinaries
	}
	return nil
}

// RuntimeFilter configures which runtimes to filter frames out for.
type RuntimeFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether to show frames of the python runtime.
	ShowPython bool `protobuf:"varint,1,opt,name=show_python,json=showPython,proto3" json:"show_python,omitempty"`
	// Whether to show frames of the ruby runtime.
	ShowRuby bool `protobuf:"varint,2,opt,name=show_ruby,json=showRuby,proto3" json:"show_ruby,omitempty"`
	// Whether to only show interpreted frames.
	ShowInterpretedOnly bool `protobuf:"varint,3,opt,name=show_interpreted_only,json=showInterpretedOnly,proto3" json:"show_interpreted_only,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RuntimeFilter) Reset() {
	*x = RuntimeFilter{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuntimeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuntimeFilter) ProtoMessage() {}

func (x *RuntimeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuntimeFilter.ProtoReflect.Descriptor instead.
func (*RuntimeFilter) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{17}
}

func (x *RuntimeFilter) GetShowPython() bool {
	if x != nil {
		return x.ShowPython
	}
	return false
}

func (x *RuntimeFilter) GetShowRuby() bool {
	if x != nil {
		return x.ShowRuby
	}
	return false
}

func (x *RuntimeFilter) GetShowInterpretedOnly() bool {
	if x != nil {
		return x.ShowInterpretedOnly
	}
	return false
}

// SourceReference contains a reference to source code.
type SourceReference struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The build ID to request the source of.
	BuildId string `protobuf:"bytes,1,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"`
	// The filename requested.
	Filename string `protobuf:"bytes,2,opt,name=filename,proto3" json:"filename,omitempty"`
	// Whether to perform a full query or just retrieve the source.
	SourceOnly    bool `protobuf:"varint,3,opt,name=source_only,json=sourceOnly,proto3" json:"source_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SourceReference) Reset() {
	*x = SourceReference{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SourceReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceReference) ProtoMessage() {}

func (x *SourceReference) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceReference.ProtoReflect.Descriptor instead.
func (*SourceReference) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{18}
}

func (x *SourceReference) GetBuildId() string {
	if x != nil {
		return x.BuildId
	}
	return ""
}

func (x *SourceReference) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *SourceReference) GetSourceOnly() bool {
	if x != nil {
		return x.SourceOnly
	}
	return false
}

// GroupBy encapsulates the repeated fields to group by
type GroupBy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the names of the fields to group by.
	// special fields are the ones prefixed with "labels." which are grouping by pprof labels.
	Fields        []string `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupBy) Reset() {
	*x = GroupBy{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupBy) ProtoMessage() {}

func (x *GroupBy) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupBy.ProtoReflect.Descriptor instead.
func (*GroupBy) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{19}
}

func (x *GroupBy) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

// Top is the top report type
type Top struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list are the list of ordered elements of the table
	List []*TopNode `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// reported is the number of lines reported
	Reported int32 `protobuf:"varint,2,opt,name=reported,proto3" json:"reported,omitempty"`
	// total is the number of lines that exist in the report
	// Use total from the top level query response instead.
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	// unit is the unit represented by top table
	Unit          string `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Top) Reset() {
	*x = Top{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Top) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Top) ProtoMessage() {}

func (x *Top) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Top.ProtoReflect.Descriptor instead.
func (*Top) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{20}
}

func (x *Top) GetList() []*TopNode {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *Top) GetReported() int32 {
	if x != nil {
		return x.Reported
	}
	return 0
}

// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
func (x *Top) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Top) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

// TopNode is a node entry in a top list
type TopNode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// meta is the metadata about the node
	Meta *TopNodeMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	// cumulative is the cumulative value of the node
	Cumulative int64 `protobuf:"varint,2,opt,name=cumulative,proto3" json:"cumulative,omitempty"`
	// flat is the flat value of the node
	Flat int64 `protobuf:"varint,3,opt,name=flat,proto3" json:"flat,omitempty"`
	// diff is the diff value between two profiles
	Diff          int64 `protobuf:"varint,4,opt,name=diff,proto3" json:"diff,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TopNode) Reset() {
	*x = TopNode{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TopNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopNode) ProtoMessage() {}

func (x *TopNode) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopNode.ProtoReflect.Descriptor instead.
func (*TopNode) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{21}
}

func (x *TopNode) GetMeta() *TopNodeMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *TopNode) GetCumulative() int64 {
	if x != nil {
		return x.Cumulative
	}
	return 0
}

func (x *TopNode) GetFlat() int64 {
	if x != nil {
		return x.Flat
	}
	return 0
}

func (x *TopNode) GetDiff() int64 {
	if x != nil {
		return x.Diff
	}
	return 0
}

// TopNodeMeta is the metadata for a given node
type TopNodeMeta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// location is the location for the code
	Location *v1alpha11.Location `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// mapping is the mapping into code
	Mapping *v1alpha11.Mapping `protobuf:"bytes,2,opt,name=mapping,proto3" json:"mapping,omitempty"`
	// function is the function information
	Function *v1alpha11.Function `protobuf:"bytes,3,opt,name=function,proto3" json:"function,omitempty"`
	// line is the line location
	Line          *v1alpha11.Line `protobuf:"bytes,4,opt,name=line,proto3" json:"line,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TopNodeMeta) Reset() {
	*x = TopNodeMeta{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TopNodeMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopNodeMeta) ProtoMessage() {}

func (x *TopNodeMeta) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopNodeMeta.ProtoReflect.Descriptor instead.
func (*TopNodeMeta) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{22}
}

func (x *TopNodeMeta) GetLocation() *v1alpha11.Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *TopNodeMeta) GetMapping() *v1alpha11.Mapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *TopNodeMeta) GetFunction() *v1alpha11.Function {
	if x != nil {
		return x.Function
	}
	return nil
}

func (x *TopNodeMeta) GetLine() *v1alpha11.Line {
	if x != nil {
		return x.Line
	}
	return nil
}

// Flamegraph is the flame graph report type
type Flamegraph struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// root is the root of the flame graph
	Root *FlamegraphRootNode `protobuf:"bytes,1,opt,name=root,proto3" json:"root,omitempty"`
	// total is the total weight of the flame graph
	// Use total from the top level query response instead.
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	Total int64 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	// unit is the unit represented by the flame graph
	Unit string `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	// height is the max height of the graph
	Height int32 `protobuf:"varint,4,opt,name=height,proto3" json:"height,omitempty"`
	// string_table holds all deduplicated strings used in the meta data.
	StringTable []string `protobuf:"bytes,5,rep,name=string_table,json=stringTable,proto3" json:"string_table,omitempty"`
	// locations deduplicated by their ID to be referenced by nodes.
	Locations []*v1alpha11.Location `protobuf:"bytes,6,rep,name=locations,proto3" json:"locations,omitempty"`
	// mapping deduplicated by their ID to be referenced by nodes.
	Mapping []*v1alpha11.Mapping `protobuf:"bytes,7,rep,name=mapping,proto3" json:"mapping,omitempty"`
	// function deduplicated by their ID to be referenced by nodes.
	Function []*v1alpha11.Function `protobuf:"bytes,8,rep,name=function,proto3" json:"function,omitempty"`
	// untrimmed_total is the total weight of the flame graph before trimming.
	// Use trimmed instead.
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	UntrimmedTotal int64 `protobuf:"varint,9,opt,name=untrimmed_total,json=untrimmedTotal,proto3" json:"untrimmed_total,omitempty"`
	// trimmed is the amount of cumulative value trimmed from the flame graph.
	Trimmed       int64 `protobuf:"varint,10,opt,name=trimmed,proto3" json:"trimmed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Flamegraph) Reset() {
	*x = Flamegraph{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Flamegraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Flamegraph) ProtoMessage() {}

func (x *Flamegraph) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Flamegraph.ProtoReflect.Descriptor instead.
func (*Flamegraph) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{23}
}

func (x *Flamegraph) GetRoot() *FlamegraphRootNode {
	if x != nil {
		return x.Root
	}
	return nil
}

// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
func (x *Flamegraph) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Flamegraph) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *Flamegraph) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Flamegraph) GetStringTable() []string {
	if x != nil {
		return x.StringTable
	}
	return nil
}

func (x *Flamegraph) GetLocations() []*v1alpha11.Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *Flamegraph) GetMapping() []*v1alpha11.Mapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *Flamegraph) GetFunction() []*v1alpha11.Function {
	if x != nil {
		return x.Function
	}
	return nil
}

// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
func (x *Flamegraph) GetUntrimmedTotal() int64 {
	if x != nil {
		return x.UntrimmedTotal
	}
	return 0
}

func (x *Flamegraph) GetTrimmed() int64 {
	if x != nil {
		return x.Trimmed
	}
	return 0
}

// Flamegraph is the flame graph report type
type FlamegraphArrow struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// record is the arrow record containing the actual flamegraph data
	Record []byte `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	// unit is the unit represented by the flame graph
	Unit string `protobuf:"bytes,2,opt,name=unit,proto3" json:"unit,omitempty"`
	// height is the max height of the graph
	Height int32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	// trimmed is the amount of cumulative value trimmed from the flame graph.
	Trimmed       int64 `protobuf:"varint,4,opt,name=trimmed,proto3" json:"trimmed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlamegraphArrow) Reset() {
	*x = FlamegraphArrow{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlamegraphArrow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlamegraphArrow) ProtoMessage() {}

func (x *FlamegraphArrow) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlamegraphArrow.ProtoReflect.Descriptor instead.
func (*FlamegraphArrow) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{24}
}

func (x *FlamegraphArrow) GetRecord() []byte {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *FlamegraphArrow) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *FlamegraphArrow) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *FlamegraphArrow) GetTrimmed() int64 {
	if x != nil {
		return x.Trimmed
	}
	return 0
}

// Source is the result of the source report type.
type Source struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// An arrow record that contains a row per source code line with value and diff columns for flat and cumulative.
	Record []byte `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	// The actual source file content.
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// The unit of the values in the record.
	Unit          string `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Source) Reset() {
	*x = Source{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{25}
}

func (x *Source) GetRecord() []byte {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *Source) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Source) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

// FlamegraphRootNode is a root node of a flame graph
type FlamegraphRootNode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cumulative is the cumulative value of the graph
	Cumulative int64 `protobuf:"varint,1,opt,name=cumulative,proto3" json:"cumulative,omitempty"`
	// diff is the diff
	Diff int64 `protobuf:"varint,2,opt,name=diff,proto3" json:"diff,omitempty"`
	// children are the list of the children of the root node
	Children      []*FlamegraphNode `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlamegraphRootNode) Reset() {
	*x = FlamegraphRootNode{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlamegraphRootNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlamegraphRootNode) ProtoMessage() {}

func (x *FlamegraphRootNode) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlamegraphRootNode.ProtoReflect.Descriptor instead.
func (*FlamegraphRootNode) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{26}
}

func (x *FlamegraphRootNode) GetCumulative() int64 {
	if x != nil {
		return x.Cumulative
	}
	return 0
}

func (x *FlamegraphRootNode) GetDiff() int64 {
	if x != nil {
		return x.Diff
	}
	return 0
}

func (x *FlamegraphRootNode) GetChildren() []*FlamegraphNode {
	if x != nil {
		return x.Children
	}
	return nil
}

// FlamegraphNode represents a node in the graph
type FlamegraphNode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// meta is the metadata about the node
	Meta *FlamegraphNodeMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	// cumulative is the cumulative value of the node
	Cumulative int64 `protobuf:"varint,2,opt,name=cumulative,proto3" json:"cumulative,omitempty"`
	// diff is the diff
	Diff int64 `protobuf:"varint,3,opt,name=diff,proto3" json:"diff,omitempty"`
	// children are the child nodes
	Children      []*FlamegraphNode `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlamegraphNode) Reset() {
	*x = FlamegraphNode{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlamegraphNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlamegraphNode) ProtoMessage() {}

func (x *FlamegraphNode) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlamegraphNode.ProtoReflect.Descriptor instead.
func (*FlamegraphNode) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{27}
}

func (x *FlamegraphNode) GetMeta() *FlamegraphNodeMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *FlamegraphNode) GetCumulative() int64 {
	if x != nil {
		return x.Cumulative
	}
	return 0
}

func (x *FlamegraphNode) GetDiff() int64 {
	if x != nil {
		return x.Diff
	}
	return 0
}

func (x *FlamegraphNode) GetChildren() []*FlamegraphNode {
	if x != nil {
		return x.Children
	}
	return nil
}

// FlamegraphNodeMeta is the metadata for a given node
type FlamegraphNodeMeta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// location is the location for the code
	Location *v1alpha11.Location `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// mapping is the mapping into code
	Mapping *v1alpha11.Mapping `protobuf:"bytes,2,opt,name=mapping,proto3" json:"mapping,omitempty"`
	// function is the function information
	Function *v1alpha11.Function `protobuf:"bytes,3,opt,name=function,proto3" json:"function,omitempty"`
	// line is the line location
	Line *v1alpha11.Line `protobuf:"bytes,4,opt,name=line,proto3" json:"line,omitempty"`
	// location_index has the index to the deduplicated location in the location table.
	LocationIndex uint32 `protobuf:"varint,5,opt,name=location_index,json=locationIndex,proto3" json:"location_index,omitempty"`
	// line_index is the line index within the referenced location.
	LineIndex     uint32 `protobuf:"varint,6,opt,name=line_index,json=lineIndex,proto3" json:"line_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlamegraphNodeMeta) Reset() {
	*x = FlamegraphNodeMeta{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlamegraphNodeMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlamegraphNodeMeta) ProtoMessage() {}

func (x *FlamegraphNodeMeta) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlamegraphNodeMeta.ProtoReflect.Descriptor instead.
func (*FlamegraphNodeMeta) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{28}
}

func (x *FlamegraphNodeMeta) GetLocation() *v1alpha11.Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *FlamegraphNodeMeta) GetMapping() *v1alpha11.Mapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *FlamegraphNodeMeta) GetFunction() *v1alpha11.Function {
	if x != nil {
		return x.Function
	}
	return nil
}

func (x *FlamegraphNodeMeta) GetLine() *v1alpha11.Line {
	if x != nil {
		return x.Line
	}
	return nil
}

func (x *FlamegraphNodeMeta) GetLocationIndex() uint32 {
	if x != nil {
		return x.LocationIndex
	}
	return 0
}

func (x *FlamegraphNodeMeta) GetLineIndex() uint32 {
	if x != nil {
		return x.LineIndex
	}
	return 0
}

// CallgraphNode represents a node in the graph
type CallgraphNode struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is the unique id of the node
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// meta is the metadata about the node
	Meta *CallgraphNodeMeta `protobuf:"bytes,2,opt,name=meta,proto3" json:"meta,omitempty"`
	// cumulative is the cumulative value of the node
	Cumulative int64 `protobuf:"varint,3,opt,name=cumulative,proto3" json:"cumulative,omitempty"`
	// flat is the flat value of the node
	Flat          int64 `protobuf:"varint,4,opt,name=flat,proto3" json:"flat,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallgraphNode) Reset() {
	*x = CallgraphNode{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallgraphNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallgraphNode) ProtoMessage() {}

func (x *CallgraphNode) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallgraphNode.ProtoReflect.Descriptor instead.
func (*CallgraphNode) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{29}
}

func (x *CallgraphNode) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CallgraphNode) GetMeta() *CallgraphNodeMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *CallgraphNode) GetCumulative() int64 {
	if x != nil {
		return x.Cumulative
	}
	return 0
}

func (x *CallgraphNode) GetFlat() int64 {
	if x != nil {
		return x.Flat
	}
	return 0
}

// TopNodeMeta is the metadata for a given node
type CallgraphNodeMeta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// location is the location for the code
	Location *v1alpha11.Location `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// mapping is the mapping into code
	Mapping *v1alpha11.Mapping `protobuf:"bytes,2,opt,name=mapping,proto3" json:"mapping,omitempty"`
	// function is the function information
	Function *v1alpha11.Function `protobuf:"bytes,3,opt,name=function,proto3" json:"function,omitempty"`
	// line is the line location
	Line          *v1alpha11.Line `protobuf:"bytes,4,opt,name=line,proto3" json:"line,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallgraphNodeMeta) Reset() {
	*x = CallgraphNodeMeta{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallgraphNodeMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallgraphNodeMeta) ProtoMessage() {}

func (x *CallgraphNodeMeta) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallgraphNodeMeta.ProtoReflect.Descriptor instead.
func (*CallgraphNodeMeta) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{30}
}

func (x *CallgraphNodeMeta) GetLocation() *v1alpha11.Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *CallgraphNodeMeta) GetMapping() *v1alpha11.Mapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *CallgraphNodeMeta) GetFunction() *v1alpha11.Function {
	if x != nil {
		return x.Function
	}
	return nil
}

func (x *CallgraphNodeMeta) GetLine() *v1alpha11.Line {
	if x != nil {
		return x.Line
	}
	return nil
}

// CallgraphEdge represents an edge in the graph
type CallgraphEdge struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is the unique id of the edge
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// source represents the id of the source node
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// target represents the id of the target node
	Target string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	// cumulative is the cumulative value of the edge
	Cumulative int64 `protobuf:"varint,4,opt,name=cumulative,proto3" json:"cumulative,omitempty"`
	// is_collapsed indicates if the edge is collapsed
	IsCollapsed   bool `protobuf:"varint,5,opt,name=is_collapsed,json=isCollapsed,proto3" json:"is_collapsed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallgraphEdge) Reset() {
	*x = CallgraphEdge{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallgraphEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallgraphEdge) ProtoMessage() {}

func (x *CallgraphEdge) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallgraphEdge.ProtoReflect.Descriptor instead.
func (*CallgraphEdge) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{31}
}

func (x *CallgraphEdge) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CallgraphEdge) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CallgraphEdge) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *CallgraphEdge) GetCumulative() int64 {
	if x != nil {
		return x.Cumulative
	}
	return 0
}

func (x *CallgraphEdge) GetIsCollapsed() bool {
	if x != nil {
		return x.IsCollapsed
	}
	return false
}

// Callgraph is the callgraph report type
type Callgraph struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// nodes are the nodes in the callgraph
	Nodes []*CallgraphNode `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	// edges are the edges connecting nodes in the callgraph
	Edges []*CallgraphEdge `protobuf:"bytes,2,rep,name=edges,proto3" json:"edges,omitempty"`
	// cumulative is the total cumulative value of the callgraph
	// Use total from the top level query response instead.
	//
	// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
	Cumulative    int64 `protobuf:"varint,3,opt,name=cumulative,proto3" json:"cumulative,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Callgraph) Reset() {
	*x = Callgraph{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Callgraph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Callgraph) ProtoMessage() {}

func (x *Callgraph) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Callgraph.ProtoReflect.Descriptor instead.
func (*Callgraph) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{32}
}

func (x *Callgraph) GetNodes() []*CallgraphNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Callgraph) GetEdges() []*CallgraphEdge {
	if x != nil {
		return x.Edges
	}
	return nil
}

// Deprecated: Marked as deprecated in parca/query/v1alpha1/query.proto.
func (x *Callgraph) GetCumulative() int64 {
	if x != nil {
		return x.Cumulative
	}
	return 0
}

// QueryResponse is the returned report for the given query
type QueryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// report is the generated report
	//
	// Types that are valid to be assigned to Report:
	//
	//	*QueryResponse_Flamegraph
	//	*QueryResponse_Pprof
	//	*QueryResponse_Top
	//	*QueryResponse_Callgraph
	//	*QueryResponse_FlamegraphArrow
	//	*QueryResponse_Source
	//	*QueryResponse_TableArrow
	//	*QueryResponse_ProfileMetadata
	Report isQueryResponse_Report `protobuf_oneof:"report"`
	// total is the total number of samples shown in the report.
	Total int64 `protobuf:"varint,9,opt,name=total,proto3" json:"total,omitempty"`
	// filtered is the number of samples filtered out of the report.
	Filtered      int64 `protobuf:"varint,10,opt,name=filtered,proto3" json:"filtered,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryResponse) Reset() {
	*x = QueryResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryResponse) ProtoMessage() {}

func (x *QueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryResponse.ProtoReflect.Descriptor instead.
func (*QueryResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{33}
}

func (x *QueryResponse) GetReport() isQueryResponse_Report {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *QueryResponse) GetFlamegraph() *Flamegraph {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Flamegraph); ok {
			return x.Flamegraph
		}
	}
	return nil
}

func (x *QueryResponse) GetPprof() []byte {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Pprof); ok {
			return x.Pprof
		}
	}
	return nil
}

func (x *QueryResponse) GetTop() *Top {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Top); ok {
			return x.Top
		}
	}
	return nil
}

func (x *QueryResponse) GetCallgraph() *Callgraph {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Callgraph); ok {
			return x.Callgraph
		}
	}
	return nil
}

func (x *QueryResponse) GetFlamegraphArrow() *FlamegraphArrow {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_FlamegraphArrow); ok {
			return x.FlamegraphArrow
		}
	}
	return nil
}

func (x *QueryResponse) GetSource() *Source {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_Source); ok {
			return x.Source
		}
	}
	return nil
}

func (x *QueryResponse) GetTableArrow() *TableArrow {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_TableArrow); ok {
			return x.TableArrow
		}
	}
	return nil
}

func (x *QueryResponse) GetProfileMetadata() *ProfileMetadata {
	if x != nil {
		if x, ok := x.Report.(*QueryResponse_ProfileMetadata); ok {
			return x.ProfileMetadata
		}
	}
	return nil
}

func (x *QueryResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *QueryResponse) GetFiltered() int64 {
	if x != nil {
		return x.Filtered
	}
	return 0
}

type isQueryResponse_Report interface {
	isQueryResponse_Report()
}

type QueryResponse_Flamegraph struct {
	// flamegraph is a flamegraph representation of the report
	Flamegraph *Flamegraph `protobuf:"bytes,5,opt,name=flamegraph,proto3,oneof"`
}

type QueryResponse_Pprof struct {
	// pprof is a pprof profile as compressed bytes
	Pprof []byte `protobuf:"bytes,6,opt,name=pprof,proto3,oneof"`
}

type QueryResponse_Top struct {
	// top is a top list representation of the report
	Top *Top `protobuf:"bytes,7,opt,name=top,proto3,oneof"`
}

type QueryResponse_Callgraph struct {
	// callgraph is a callgraph nodes and edges representation of the report
	Callgraph *Callgraph `protobuf:"bytes,8,opt,name=callgraph,proto3,oneof"`
}

type QueryResponse_FlamegraphArrow struct {
	// flamegraph_arrow is a flamegraph encoded as a arrow record
	FlamegraphArrow *FlamegraphArrow `protobuf:"bytes,11,opt,name=flamegraph_arrow,json=flamegraphArrow,proto3,oneof"`
}

type QueryResponse_Source struct {
	// source is the source report type result
	Source *Source `protobuf:"bytes,12,opt,name=source,proto3,oneof"`
}

type QueryResponse_TableArrow struct {
	// table_arrow is a table encoded as a arrow record
	TableArrow *TableArrow `protobuf:"bytes,13,opt,name=table_arrow,json=tableArrow,proto3,oneof"`
}

type QueryResponse_ProfileMetadata struct {
	// profile_metadata contains metadata about the profile i.e. binaries, labels
	ProfileMetadata *ProfileMetadata `protobuf:"bytes,14,opt,name=profile_metadata,json=profileMetadata,proto3,oneof"`
}

func (*QueryResponse_Flamegraph) isQueryResponse_Report() {}

func (*QueryResponse_Pprof) isQueryResponse_Report() {}

func (*QueryResponse_Top) isQueryResponse_Report() {}

func (*QueryResponse_Callgraph) isQueryResponse_Report() {}

func (*QueryResponse_FlamegraphArrow) isQueryResponse_Report() {}

func (*QueryResponse_Source) isQueryResponse_Report() {}

func (*QueryResponse_TableArrow) isQueryResponse_Report() {}

func (*QueryResponse_ProfileMetadata) isQueryResponse_Report() {}

// SeriesRequest is unimplemented
type SeriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// match ...
	Match []string `protobuf:"bytes,1,rep,name=match,proto3" json:"match,omitempty"`
	// start ...
	Start *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start,proto3" json:"start,omitempty"`
	// end ...
	End           *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end,proto3" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SeriesRequest) Reset() {
	*x = SeriesRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesRequest) ProtoMessage() {}

func (x *SeriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesRequest.ProtoReflect.Descriptor instead.
func (*SeriesRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{34}
}

func (x *SeriesRequest) GetMatch() []string {
	if x != nil {
		return x.Match
	}
	return nil
}

func (x *SeriesRequest) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *SeriesRequest) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

// SeriesResponse is unimplemented
type SeriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SeriesResponse) Reset() {
	*x = SeriesResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesResponse) ProtoMessage() {}

func (x *SeriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesResponse.ProtoReflect.Descriptor instead.
func (*SeriesResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{35}
}

// LabelsRequest are the request values for labels
type LabelsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// match are the set of matching strings
	Match []string `protobuf:"bytes,1,rep,name=match,proto3" json:"match,omitempty"`
	// start is the start of the time window to perform the query
	Start *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start,proto3" json:"start,omitempty"`
	// end is the end of the time window to perform the query
	End *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end,proto3" json:"end,omitempty"`
	// profile_type is the type of profile to filter by
	ProfileType   *string `protobuf:"bytes,4,opt,name=profile_type,json=profileType,proto3,oneof" json:"profile_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelsRequest) Reset() {
	*x = LabelsRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelsRequest) ProtoMessage() {}

func (x *LabelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelsRequest.ProtoReflect.Descriptor instead.
func (*LabelsRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{36}
}

func (x *LabelsRequest) GetMatch() []string {
	if x != nil {
		return x.Match
	}
	return nil
}

func (x *LabelsRequest) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *LabelsRequest) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *LabelsRequest) GetProfileType() string {
	if x != nil && x.ProfileType != nil {
		return *x.ProfileType
	}
	return ""
}

// LabelsResponse is the set of matching label names
type LabelsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// / label_names are the set of matching label names
	LabelNames []string `protobuf:"bytes,1,rep,name=label_names,json=labelNames,proto3" json:"label_names,omitempty"`
	// warnings is unimplemented
	Warnings      []string `protobuf:"bytes,2,rep,name=warnings,proto3" json:"warnings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelsResponse) Reset() {
	*x = LabelsResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelsResponse) ProtoMessage() {}

func (x *LabelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelsResponse.ProtoReflect.Descriptor instead.
func (*LabelsResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{37}
}

func (x *LabelsResponse) GetLabelNames() []string {
	if x != nil {
		return x.LabelNames
	}
	return nil
}

func (x *LabelsResponse) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

// ValuesRequest are the request values for a values request
type ValuesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// label_name is the label name to match values against
	LabelName string `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	// match are the set of matching strings to match values against
	Match []string `protobuf:"bytes,2,rep,name=match,proto3" json:"match,omitempty"`
	// start is the start of the time window to perform the query
	Start *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"`
	// end is the end of the time window to perform the query
	End *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`
	// profile_type is the type of profile to filter by
	ProfileType   *string `protobuf:"bytes,5,opt,name=profile_type,json=profileType,proto3,oneof" json:"profile_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValuesRequest) Reset() {
	*x = ValuesRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValuesRequest) ProtoMessage() {}

func (x *ValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValuesRequest.ProtoReflect.Descriptor instead.
func (*ValuesRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{38}
}

func (x *ValuesRequest) GetLabelName() string {
	if x != nil {
		return x.LabelName
	}
	return ""
}

func (x *ValuesRequest) GetMatch() []string {
	if x != nil {
		return x.Match
	}
	return nil
}

func (x *ValuesRequest) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *ValuesRequest) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *ValuesRequest) GetProfileType() string {
	if x != nil && x.ProfileType != nil {
		return *x.ProfileType
	}
	return ""
}

// ValuesResponse are the set of matching values
type ValuesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// label_values are the set of matching label values
	LabelValues []string `protobuf:"bytes,1,rep,name=label_values,json=labelValues,proto3" json:"label_values,omitempty"`
	// warnings is unimplemented
	Warnings      []string `protobuf:"bytes,2,rep,name=warnings,proto3" json:"warnings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValuesResponse) Reset() {
	*x = ValuesResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValuesResponse) ProtoMessage() {}

func (x *ValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValuesResponse.ProtoReflect.Descriptor instead.
func (*ValuesResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{39}
}

func (x *ValuesResponse) GetLabelValues() []string {
	if x != nil {
		return x.LabelValues
	}
	return nil
}

func (x *ValuesResponse) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

// ValueType represents a value, including its type and unit
type ValueType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// type is the type of the value
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// unit is the unit of the value
	Unit          string `protobuf:"bytes,2,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValueType) Reset() {
	*x = ValueType{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValueType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueType) ProtoMessage() {}

func (x *ValueType) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueType.ProtoReflect.Descriptor instead.
func (*ValueType) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{40}
}

func (x *ValueType) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ValueType) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

// ShareProfileRequest represents the query denoting the profile and a description about the profile
type ShareProfileRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// QueryRequest that refers to the profile to be shared
	QueryRequest *QueryRequest `protobuf:"bytes,1,opt,name=query_request,json=queryRequest,proto3" json:"query_request,omitempty"`
	// description about the profile
	Description   *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShareProfileRequest) Reset() {
	*x = ShareProfileRequest{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShareProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareProfileRequest) ProtoMessage() {}

func (x *ShareProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareProfileRequest.ProtoReflect.Descriptor instead.
func (*ShareProfileRequest) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{41}
}

func (x *ShareProfileRequest) GetQueryRequest() *QueryRequest {
	if x != nil {
		return x.QueryRequest
	}
	return nil
}

func (x *ShareProfileRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

// ShareProfileResponse represents the shared link of a profile
type ShareProfileResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// link to access the profile
	Link          string `protobuf:"bytes,1,opt,name=link,proto3" json:"link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShareProfileResponse) Reset() {
	*x = ShareProfileResponse{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShareProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareProfileResponse) ProtoMessage() {}

func (x *ShareProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareProfileResponse.ProtoReflect.Descriptor instead.
func (*ShareProfileResponse) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{42}
}

func (x *ShareProfileResponse) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

// TableArrow has the table encoded as a arrow record
type TableArrow struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// record is the arrow record containing the actual table data
	Record []byte `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	// unit is the unit represented by the flame graph
	Unit          string `protobuf:"bytes,2,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableArrow) Reset() {
	*x = TableArrow{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableArrow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableArrow) ProtoMessage() {}

func (x *TableArrow) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableArrow.ProtoReflect.Descriptor instead.
func (*TableArrow) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{43}
}

func (x *TableArrow) GetRecord() []byte {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *TableArrow) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

// ProfileMetadata contains metadata about the profile i.e. binaries, labels
type ProfileMetadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// mapping_files is the list of binaries in the profile
	MappingFiles []string `protobuf:"bytes,1,rep,name=mapping_files,json=mappingFiles,proto3" json:"mapping_files,omitempty"`
	// labels is the list of labels in the profile
	Labels        []string `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProfileMetadata) Reset() {
	*x = ProfileMetadata{}
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProfileMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileMetadata) ProtoMessage() {}

func (x *ProfileMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_parca_query_v1alpha1_query_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileMetadata.ProtoReflect.Descriptor instead.
func (*ProfileMetadata) Descriptor() ([]byte, []int) {
	return file_parca_query_v1alpha1_query_proto_rawDescGZIP(), []int{44}
}

func (x *ProfileMetadata) GetMappingFiles() []string {
	if x != nil {
		return x.MappingFiles
	}
	return nil
}

func (x *ProfileMetadata) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

var File_parca_query_v1alpha1_query_proto protoreflect.FileDescriptor

const file_parca_query_v1alpha1_query_proto_rawDesc = "" +
	"\n" +
	" parca/query/v1alpha1/query.proto\x12\x14parca.query.v1alpha1\x1a\x1cgoogle/api/annotations.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a(parca/metastore/v1alpha1/metastore.proto\x1a.parca/profilestore/v1alpha1/profilestore.proto\"\x15\n" +
	"\x13ProfileTypesRequest\"O\n" +
	"\x14ProfileTypesResponse\x127\n" +
	"\x05types\x18\x01 \x03(\v2!.parca.query.v1alpha1.ProfileTypeR\x05types\"\xbb\x01\n" +
	"\vProfileType\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1f\n" +
	"\vsample_type\x18\x02 \x01(\tR\n" +
	"sampleType\x12\x1f\n" +
	"\vsample_unit\x18\x03 \x01(\tR\n" +
	"sampleUnit\x12\x1f\n" +
	"\vperiod_type\x18\x04 \x01(\tR\n" +
	"periodType\x12\x1f\n" +
	"\vperiod_unit\x18\x05 \x01(\tR\n" +
	"periodUnit\x12\x14\n" +
	"\x05delta\x18\x06 \x01(\bR\x05delta\"\xe5\x01\n" +
	"\x11QueryRangeRequest\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query\x120\n" +
	"\x05start\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x05start\x12,\n" +
	"\x03end\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x03end\x12\x14\n" +
	"\x05limit\x18\x04 \x01(\rR\x05limit\x12-\n" +
	"\x04step\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\x04step\x12\x15\n" +
	"\x06sum_by\x18\x06 \x03(\tR\x05sumBy\"Q\n" +
	"\x12QueryRangeResponse\x12;\n" +
	"\x06series\x18\x01 \x03(\v2#.parca.query.v1alpha1.MetricsSeriesR\x06series\"\x95\x02\n" +
	"\rMetricsSeries\x12A\n" +
	"\blabelset\x18\x01 \x01(\v2%.parca.profilestore.v1alpha1.LabelSetR\blabelset\x12=\n" +
	"\asamples\x18\x02 \x03(\v2#.parca.query.v1alpha1.MetricsSampleR\asamples\x12@\n" +
	"\vperiod_type\x18\x03 \x01(\v2\x1f.parca.query.v1alpha1.ValueTypeR\n" +
	"periodType\x12@\n" +
	"\vsample_type\x18\x04 \x01(\v2\x1f.parca.query.v1alpha1.ValueTypeR\n" +
	"sampleType\"\xa5\x01\n" +
	"\rMetricsSample\x128\n" +
	"\ttimestamp\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x03R\x05value\x12(\n" +
	"\x10value_per_second\x18\x03 \x01(\x01R\x0evaluePerSecond\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x03R\bduration\"\x84\x01\n" +
	"\fMergeProfile\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query\x120\n" +
	"\x05start\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x05start\x12,\n" +
	"\x03end\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x03end\"U\n" +
	"\rSingleProfile\x12.\n" +
	"\x04time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\x04time\x12\x14\n" +
	"\x05query\x18\x02 \x01(\tR\x05query\"\xaf\x01\n" +
	"\vDiffProfile\x128\n" +
	"\x01a\x18\x01 \x01(\v2*.parca.query.v1alpha1.ProfileDiffSelectionR\x01a\x128\n" +
	"\x01b\x18\x02 \x01(\v2*.parca.query.v1alpha1.ProfileDiffSelectionR\x01b\x12\x1f\n" +
	"\babsolute\x18\x03 \x01(\bH\x00R\babsolute\x88\x01\x01B\v\n" +
	"\t_absolute\"\x96\x02\n" +
	"\x14ProfileDiffSelection\x12C\n" +
	"\x04mode\x18\x01 \x01(\x0e2/.parca.query.v1alpha1.ProfileDiffSelection.ModeR\x04mode\x12:\n" +
	"\x05merge\x18\x02 \x01(\v2\".parca.query.v1alpha1.MergeProfileH\x00R\x05merge\x12=\n" +
	"\x06single\x18\x03 \x01(\v2#.parca.query.v1alpha1.SingleProfileH\x00R\x06single\"3\n" +
	"\x04Mode\x12\x1b\n" +
	"\x17MODE_SINGLE_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"MODE_MERGE\x10\x01B\t\n" +
	"\aoptions\"\xce\n" +
	"\n" +
	"\fQueryRequest\x12;\n" +
	"\x04mode\x18\x01 \x01(\x0e2'.parca.query.v1alpha1.QueryRequest.ModeR\x04mode\x127\n" +
	"\x04diff\x18\x02 \x01(\v2!.parca.query.v1alpha1.DiffProfileH\x00R\x04diff\x12:\n" +
	"\x05merge\x18\x03 \x01(\v2\".parca.query.v1alpha1.MergeProfileH\x00R\x05merge\x12=\n" +
	"\x06single\x18\x04 \x01(\v2#.parca.query.v1alpha1.SingleProfileH\x00R\x06single\x12N\n" +
	"\vreport_type\x18\x05 \x01(\x0e2-.parca.query.v1alpha1.QueryRequest.ReportTypeR\n" +
	"reportType\x12*\n" +
	"\ffilter_query\x18\x06 \x01(\tB\x02\x18\x01H\x01R\vfilterQuery\x88\x01\x01\x123\n" +
	"\x13node_trim_threshold\x18\a \x01(\x02H\x02R\x11nodeTrimThreshold\x88\x01\x01\x12=\n" +
	"\bgroup_by\x18\b \x01(\v2\x1d.parca.query.v1alpha1.GroupByH\x03R\agroupBy\x88\x01\x01\x12U\n" +
	"\x10source_reference\x18\t \x01(\v2%.parca.query.v1alpha1.SourceReferenceH\x04R\x0fsourceReference\x88\x01\x01\x12S\n" +
	"\x0eruntime_filter\x18\n" +
	" \x01(\v2#.parca.query.v1alpha1.RuntimeFilterB\x02\x18\x01H\x05R\rruntimeFilter\x88\x01\x01\x12/\n" +
	"\x11invert_call_stack\x18\v \x01(\bH\x06R\x0finvertCallStack\x88\x01\x01\x124\n" +
	"\x06filter\x18\f \x03(\v2\x1c.parca.query.v1alpha1.FilterR\x06filter\x125\n" +
	"\x14sandwich_by_function\x18\r \x01(\tH\aR\x12sandwichByFunction\x88\x01\x01\"B\n" +
	"\x04Mode\x12\x1b\n" +
	"\x17MODE_SINGLE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tMODE_DIFF\x10\x01\x12\x0e\n" +
	"\n" +
	"MODE_MERGE\x10\x02\"\xb6\x02\n" +
	"\n" +
	"ReportType\x12*\n" +
	"\"REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED\x10\x00\x1a\x02\b\x01\x12\x15\n" +
	"\x11REPORT_TYPE_PPROF\x10\x01\x12\x13\n" +
	"\x0fREPORT_TYPE_TOP\x10\x02\x12\x19\n" +
	"\x15REPORT_TYPE_CALLGRAPH\x10\x03\x12 \n" +
	"\x1cREPORT_TYPE_FLAMEGRAPH_TABLE\x10\x04\x12 \n" +
	"\x1cREPORT_TYPE_FLAMEGRAPH_ARROW\x10\x05\x12\x16\n" +
	"\x12REPORT_TYPE_SOURCE\x10\x06\x12\x1b\n" +
	"\x17REPORT_TYPE_TABLE_ARROW\x10\a\x12 \n" +
	"\x1cREPORT_TYPE_PROFILE_METADATA\x10\b\x12\x1a\n" +
	"\x16REPORT_TYPE_FLAMECHART\x10\tB\t\n" +
	"\aoptionsB\x0f\n" +
	"\r_filter_queryB\x16\n" +
	"\x14_node_trim_thresholdB\v\n" +
	"\t_group_byB\x13\n" +
	"\x11_source_referenceB\x11\n" +
	"\x0f_runtime_filterB\x14\n" +
	"\x12_invert_call_stackB\x17\n" +
	"\x15_sandwich_by_function\"\xa2\x01\n" +
	"\x06Filter\x12F\n" +
	"\fstack_filter\x18\x01 \x01(\v2!.parca.query.v1alpha1.StackFilterH\x00R\vstackFilter\x12F\n" +
	"\fframe_filter\x18\x02 \x01(\v2!.parca.query.v1alpha1.FrameFilterH\x00R\vframeFilterB\b\n" +
	"\x06filter\"\x85\x01\n" +
	"\vStackFilter\x12l\n" +
	"\x1afunction_name_stack_filter\x18\x01 \x01(\v2-.parca.query.v1alpha1.FunctionNameStackFilterH\x00R\x17functionNameStackFilterB\b\n" +
	"\x06filter\"a\n" +
	"\x17FunctionNameStackFilter\x12,\n" +
	"\x12function_to_filter\x18\x01 \x01(\tR\x10functionToFilter\x12\x18\n" +
	"\aexclude\x18\x02 \x01(\bR\aexclude\"r\n" +
	"\vFrameFilter\x12Y\n" +
	"\x13binary_frame_filter\x18\x01 \x01(\v2'.parca.query.v1alpha1.BinaryFrameFilterH\x00R\x11binaryFrameFilterB\b\n" +
	"\x06filter\">\n" +
	"\x11BinaryFrameFilter\x12)\n" +
	"\x10include_binaries\x18\x01 \x03(\tR\x0fincludeBinaries\"\x81\x01\n" +
	"\rRuntimeFilter\x12\x1f\n" +
	"\vshow_python\x18\x01 \x01(\bR\n" +
	"showPython\x12\x1b\n" +
	"\tshow_ruby\x18\x02 \x01(\bR\bshowRuby\x122\n" +
	"\x15show_interpreted_only\x18\x03 \x01(\bR\x13showInterpretedOnly\"i\n" +
	"\x0fSourceReference\x12\x19\n" +
	"\bbuild_id\x18\x01 \x01(\tR\abuildId\x12\x1a\n" +
	"\bfilename\x18\x02 \x01(\tR\bfilename\x12\x1f\n" +
	"\vsource_only\x18\x03 \x01(\bR\n" +
	"sourceOnly\"!\n" +
	"\aGroupBy\x12\x16\n" +
	"\x06fields\x18\x01 \x03(\tR\x06fields\"\x82\x01\n" +
	"\x03Top\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.parca.query.v1alpha1.TopNodeR\x04list\x12\x1a\n" +
	"\breported\x18\x02 \x01(\x05R\breported\x12\x18\n" +
	"\x05total\x18\x03 \x01(\x05B\x02\x18\x01R\x05total\x12\x12\n" +
	"\x04unit\x18\x04 \x01(\tR\x04unit\"\x88\x01\n" +
	"\aTopNode\x125\n" +
	"\x04meta\x18\x01 \x01(\v2!.parca.query.v1alpha1.TopNodeMetaR\x04meta\x12\x1e\n" +
	"\n" +
	"cumulative\x18\x02 \x01(\x03R\n" +
	"cumulative\x12\x12\n" +
	"\x04flat\x18\x03 \x01(\x03R\x04flat\x12\x12\n" +
	"\x04diff\x18\x04 \x01(\x03R\x04diff\"\xfe\x01\n" +
	"\vTopNodeMeta\x12>\n" +
	"\blocation\x18\x01 \x01(\v2\".parca.metastore.v1alpha1.LocationR\blocation\x12;\n" +
	"\amapping\x18\x02 \x01(\v2!.parca.metastore.v1alpha1.MappingR\amapping\x12>\n" +
	"\bfunction\x18\x03 \x01(\v2\".parca.metastore.v1alpha1.FunctionR\bfunction\x122\n" +
	"\x04line\x18\x04 \x01(\v2\x1e.parca.metastore.v1alpha1.LineR\x04line\"\xb9\x03\n" +
	"\n" +
	"Flamegraph\x12<\n" +
	"\x04root\x18\x01 \x01(\v2(.parca.query.v1alpha1.FlamegraphRootNodeR\x04root\x12\x18\n" +
	"\x05total\x18\x02 \x01(\x03B\x02\x18\x01R\x05total\x12\x12\n" +
	"\x04unit\x18\x03 \x01(\tR\x04unit\x12\x16\n" +
	"\x06height\x18\x04 \x01(\x05R\x06height\x12!\n" +
	"\fstring_table\x18\x05 \x03(\tR\vstringTable\x12@\n" +
	"\tlocations\x18\x06 \x03(\v2\".parca.metastore.v1alpha1.LocationR\tlocations\x12;\n" +
	"\amapping\x18\a \x03(\v2!.parca.metastore.v1alpha1.MappingR\amapping\x12>\n" +
	"\bfunction\x18\b \x03(\v2\".parca.metastore.v1alpha1.FunctionR\bfunction\x12+\n" +
	"\x0funtrimmed_total\x18\t \x01(\x03B\x02\x18\x01R\x0euntrimmedTotal\x12\x18\n" +
	"\atrimmed\x18\n" +
	" \x01(\x03R\atrimmed\"o\n" +
	"\x0fFlamegraphArrow\x12\x16\n" +
	"\x06record\x18\x01 \x01(\fR\x06record\x12\x12\n" +
	"\x04unit\x18\x02 \x01(\tR\x04unit\x12\x16\n" +
	"\x06height\x18\x03 \x01(\x05R\x06height\x12\x18\n" +
	"\atrimmed\x18\x04 \x01(\x03R\atrimmed\"L\n" +
	"\x06Source\x12\x16\n" +
	"\x06record\x18\x01 \x01(\fR\x06record\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x12\n" +
	"\x04unit\x18\x03 \x01(\tR\x04unit\"\x8a\x01\n" +
	"\x12FlamegraphRootNode\x12\x1e\n" +
	"\n" +
	"cumulative\x18\x01 \x01(\x03R\n" +
	"cumulative\x12\x12\n" +
	"\x04diff\x18\x02 \x01(\x03R\x04diff\x12@\n" +
	"\bchildren\x18\x03 \x03(\v2$.parca.query.v1alpha1.FlamegraphNodeR\bchildren\"\xc4\x01\n" +
	"\x0eFlamegraphNode\x12<\n" +
	"\x04meta\x18\x01 \x01(\v2(.parca.query.v1alpha1.FlamegraphNodeMetaR\x04meta\x12\x1e\n" +
	"\n" +
	"cumulative\x18\x02 \x01(\x03R\n" +
	"cumulative\x12\x12\n" +
	"\x04diff\x18\x03 \x01(\x03R\x04diff\x12@\n" +
	"\bchildren\x18\x04 \x03(\v2$.parca.query.v1alpha1.FlamegraphNodeR\bchildren\"\xcb\x02\n" +
	"\x12FlamegraphNodeMeta\x12>\n" +
	"\blocation\x18\x01 \x01(\v2\".parca.metastore.v1alpha1.LocationR\blocation\x12;\n" +
	"\amapping\x18\x02 \x01(\v2!.parca.metastore.v1alpha1.MappingR\amapping\x12>\n" +
	"\bfunction\x18\x03 \x01(\v2\".parca.metastore.v1alpha1.FunctionR\bfunction\x122\n" +
	"\x04line\x18\x04 \x01(\v2\x1e.parca.metastore.v1alpha1.LineR\x04line\x12%\n" +
	"\x0elocation_index\x18\x05 \x01(\rR\rlocationIndex\x12\x1d\n" +
	"\n" +
	"line_index\x18\x06 \x01(\rR\tlineIndex\"\x90\x01\n" +
	"\rCallgraphNode\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12;\n" +
	"\x04meta\x18\x02 \x01(\v2'.parca.query.v1alpha1.CallgraphNodeMetaR\x04meta\x12\x1e\n" +
	"\n" +
	"cumulative\x18\x03 \x01(\x03R\n" +
	"cumulative\x12\x12\n" +
	"\x04flat\x18\x04 \x01(\x03R\x04flat\"\x84\x02\n" +
	"\x11CallgraphNodeMeta\x12>\n" +
	"\blocation\x18\x01 \x01(\v2\".parca.metastore.v1alpha1.LocationR\blocation\x12;\n" +
	"\amapping\x18\x02 \x01(\v2!.parca.metastore.v1alpha1.MappingR\amapping\x12>\n" +
	"\bfunction\x18\x03 \x01(\v2\".parca.metastore.v1alpha1.FunctionR\bfunction\x122\n" +
	"\x04line\x18\x04 \x01(\v2\x1e.parca.metastore.v1alpha1.LineR\x04line\"\x92\x01\n" +
	"\rCallgraphEdge\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x16\n" +
	"\x06target\x18\x03 \x01(\tR\x06target\x12\x1e\n" +
	"\n" +
	"cumulative\x18\x04 \x01(\x03R\n" +
	"cumulative\x12!\n" +
	"\fis_collapsed\x18\x05 \x01(\bR\visCollapsed\"\xa5\x01\n" +
	"\tCallgraph\x129\n" +
	"\x05nodes\x18\x01 \x03(\v2#.parca.query.v1alpha1.CallgraphNodeR\x05nodes\x129\n" +
	"\x05edges\x18\x02 \x03(\v2#.parca.query.v1alpha1.CallgraphEdgeR\x05edges\x12\"\n" +
	"\n" +
	"cumulative\x18\x03 \x01(\x03B\x02\x18\x01R\n" +
	"cumulative\"\xbc\x04\n" +
	"\rQueryResponse\x12B\n" +
	"\n" +
	"flamegraph\x18\x05 \x01(\v2 .parca.query.v1alpha1.FlamegraphH\x00R\n" +
	"flamegraph\x12\x16\n" +
	"\x05pprof\x18\x06 \x01(\fH\x00R\x05pprof\x12-\n" +
	"\x03top\x18\a \x01(\v2\x19.parca.query.v1alpha1.TopH\x00R\x03top\x12?\n" +
	"\tcallgraph\x18\b \x01(\v2\x1f.parca.query.v1alpha1.CallgraphH\x00R\tcallgraph\x12R\n" +
	"\x10flamegraph_arrow\x18\v \x01(\v2%.parca.query.v1alpha1.FlamegraphArrowH\x00R\x0fflamegraphArrow\x126\n" +
	"\x06source\x18\f \x01(\v2\x1c.parca.query.v1alpha1.SourceH\x00R\x06source\x12C\n" +
	"\vtable_arrow\x18\r \x01(\v2 .parca.query.v1alpha1.TableArrowH\x00R\n" +
	"tableArrow\x12R\n" +
	"\x10profile_metadata\x18\x0e \x01(\v2%.parca.query.v1alpha1.ProfileMetadataH\x00R\x0fprofileMetadata\x12\x14\n" +
	"\x05total\x18\t \x01(\x03R\x05total\x12\x1a\n" +
	"\bfiltered\x18\n" +
	" \x01(\x03R\bfilteredB\b\n" +
	"\x06report\"\x85\x01\n" +
	"\rSeriesRequest\x12\x14\n" +
	"\x05match\x18\x01 \x03(\tR\x05match\x120\n" +
	"\x05start\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x05start\x12,\n" +
	"\x03end\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x03end\"\x10\n" +
	"\x0eSeriesResponse\"\xbe\x01\n" +
	"\rLabelsRequest\x12\x14\n" +
	"\x05match\x18\x01 \x03(\tR\x05match\x120\n" +
	"\x05start\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x05start\x12,\n" +
	"\x03end\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x03end\x12&\n" +
	"\fprofile_type\x18\x04 \x01(\tH\x00R\vprofileType\x88\x01\x01B\x0f\n" +
	"\r_profile_type\"M\n" +
	"\x0eLabelsResponse\x12\x1f\n" +
	"\vlabel_names\x18\x01 \x03(\tR\n" +
	"labelNames\x12\x1a\n" +
	"\bwarnings\x18\x02 \x03(\tR\bwarnings\"\xdd\x01\n" +
	"\rValuesRequest\x12\x1d\n" +
	"\n" +
	"label_name\x18\x01 \x01(\tR\tlabelName\x12\x14\n" +
	"\x05match\x18\x02 \x03(\tR\x05match\x120\n" +
	"\x05start\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x05start\x12,\n" +
	"\x03end\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x03end\x12&\n" +
	"\fprofile_type\x18\x05 \x01(\tH\x00R\vprofileType\x88\x01\x01B\x0f\n" +
	"\r_profile_type\"O\n" +
	"\x0eValuesResponse\x12!\n" +
	"\flabel_values\x18\x01 \x03(\tR\vlabelValues\x12\x1a\n" +
	"\bwarnings\x18\x02 \x03(\tR\bwarnings\"3\n" +
	"\tValueType\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x12\n" +
	"\x04unit\x18\x02 \x01(\tR\x04unit\"\x95\x01\n" +
	"\x13ShareProfileRequest\x12G\n" +
	"\rquery_request\x18\x01 \x01(\v2\".parca.query.v1alpha1.QueryRequestR\fqueryRequest\x12%\n" +
	"\vdescription\x18\x02 \x01(\tH\x00R\vdescription\x88\x01\x01B\x0e\n" +
	"\f_description\"*\n" +
	"\x14ShareProfileResponse\x12\x12\n" +
	"\x04link\x18\x01 \x01(\tR\x04link\"8\n" +
	"\n" +
	"TableArrow\x12\x16\n" +
	"\x06record\x18\x01 \x01(\fR\x06record\x12\x12\n" +
	"\x04unit\x18\x02 \x01(\tR\x04unit\"N\n" +
	"\x0fProfileMetadata\x12#\n" +
	"\rmapping_files\x18\x01 \x03(\tR\fmappingFiles\x12\x16\n" +
	"\x06labels\x18\x02 \x03(\tR\x06labels2\xdf\x06\n" +
	"\fQueryService\x12~\n" +
	"\n" +
	"QueryRange\x12'.parca.query.v1alpha1.QueryRangeRequest\x1a(.parca.query.v1alpha1.QueryRangeResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/profiles/query_range\x12i\n" +
	"\x05Query\x12\".parca.query.v1alpha1.QueryRequest\x1a#.parca.query.v1alpha1.QueryResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/profiles/query\x12m\n" +
	"\x06Series\x12#.parca.query.v1alpha1.SeriesRequest\x1a$.parca.query.v1alpha1.SeriesResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/profiles/series\x12~\n" +
	"\fProfileTypes\x12).parca.query.v1alpha1.ProfileTypesRequest\x1a*.parca.query.v1alpha1.ProfileTypesResponse\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/profiles/types\x12m\n" +
	"\x06Labels\x12#.parca.query.v1alpha1.LabelsRequest\x1a$.parca.query.v1alpha1.LabelsResponse\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/profiles/labels\x12\x81\x01\n" +
	"\x06Values\x12#.parca.query.v1alpha1.ValuesRequest\x1a$.parca.query.v1alpha1.ValuesResponse\",\x82\xd3\xe4\x93\x02&\x12$/profiles/labels/{label_name}/values\x12\x81\x01\n" +
	"\fShareProfile\x12).parca.query.v1alpha1.ShareProfileRequest\x1a*.parca.query.v1alpha1.ShareProfileResponse\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/profiles/shareB\xe4\x01\n" +
	"\x18com.parca.query.v1alpha1B\n" +
	"QueryProtoP\x01ZJgithub.com/parca-dev/parca/gen/proto/go/parca/query/v1alpha1;queryv1alpha1\xa2\x02\x03PQX\xaa\x02\x14Parca.Query.V1alpha1\xca\x02\x14Parca\\Query\\V1alpha1\xe2\x02 Parca\\Query\\V1alpha1\\GPBMetadata\xea\x02\x16Parca::Query::V1alpha1b\x06proto3"

var (
	file_parca_query_v1alpha1_query_proto_rawDescOnce sync.Once
	file_parca_query_v1alpha1_query_proto_rawDescData []byte
)

func file_parca_query_v1alpha1_query_proto_rawDescGZIP() []byte {
	file_parca_query_v1alpha1_query_proto_rawDescOnce.Do(func() {
		file_parca_query_v1alpha1_query_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_parca_query_v1alpha1_query_proto_rawDesc), len(file_parca_query_v1alpha1_query_proto_rawDesc)))
	})
	return file_parca_query_v1alpha1_query_proto_rawDescData
}

var file_parca_query_v1alpha1_query_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_parca_query_v1alpha1_query_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_parca_query_v1alpha1_query_proto_goTypes = []any{
	(ProfileDiffSelection_Mode)(0),  // 0: parca.query.v1alpha1.ProfileDiffSelection.Mode
	(QueryRequest_Mode)(0),          // 1: parca.query.v1alpha1.QueryRequest.Mode
	(QueryRequest_ReportType)(0),    // 2: parca.query.v1alpha1.QueryRequest.ReportType
	(*ProfileTypesRequest)(nil),     // 3: parca.query.v1alpha1.ProfileTypesRequest
	(*ProfileTypesResponse)(nil),    // 4: parca.query.v1alpha1.ProfileTypesResponse
	(*ProfileType)(nil),             // 5: parca.query.v1alpha1.ProfileType
	(*QueryRangeRequest)(nil),       // 6: parca.query.v1alpha1.QueryRangeRequest
	(*QueryRangeResponse)(nil),      // 7: parca.query.v1alpha1.QueryRangeResponse
	(*MetricsSeries)(nil),           // 8: parca.query.v1alpha1.MetricsSeries
	(*MetricsSample)(nil),           // 9: parca.query.v1alpha1.MetricsSample
	(*MergeProfile)(nil),            // 10: parca.query.v1alpha1.MergeProfile
	(*SingleProfile)(nil),           // 11: parca.query.v1alpha1.SingleProfile
	(*DiffProfile)(nil),             // 12: parca.query.v1alpha1.DiffProfile
	(*ProfileDiffSelection)(nil),    // 13: parca.query.v1alpha1.ProfileDiffSelection
	(*QueryRequest)(nil),            // 14: parca.query.v1alpha1.QueryRequest
	(*Filter)(nil),                  // 15: parca.query.v1alpha1.Filter
	(*StackFilter)(nil),             // 16: parca.query.v1alpha1.StackFilter
	(*FunctionNameStackFilter)(nil), // 17: parca.query.v1alpha1.FunctionNameStackFilter
	(*FrameFilter)(nil),             // 18: parca.query.v1alpha1.FrameFilter
	(*BinaryFrameFilter)(nil),       // 19: parca.query.v1alpha1.BinaryFrameFilter
	(*RuntimeFilter)(nil),           // 20: parca.query.v1alpha1.RuntimeFilter
	(*SourceReference)(nil),         // 21: parca.query.v1alpha1.SourceReference
	(*GroupBy)(nil),                 // 22: parca.query.v1alpha1.GroupBy
	(*Top)(nil),                     // 23: parca.query.v1alpha1.Top
	(*TopNode)(nil),                 // 24: parca.query.v1alpha1.TopNode
	(*TopNodeMeta)(nil),             // 25: parca.query.v1alpha1.TopNodeMeta
	(*Flamegraph)(nil),              // 26: parca.query.v1alpha1.Flamegraph
	(*FlamegraphArrow)(nil),         // 27: parca.query.v1alpha1.FlamegraphArrow
	(*Source)(nil),                  // 28: parca.query.v1alpha1.Source
	(*FlamegraphRootNode)(nil),      // 29: parca.query.v1alpha1.FlamegraphRootNode
	(*FlamegraphNode)(nil),          // 30: parca.query.v1alpha1.FlamegraphNode
	(*FlamegraphNodeMeta)(nil),      // 31: parca.query.v1alpha1.FlamegraphNodeMeta
	(*CallgraphNode)(nil),           // 32: parca.query.v1alpha1.CallgraphNode
	(*CallgraphNodeMeta)(nil),       // 33: parca.query.v1alpha1.CallgraphNodeMeta
	(*CallgraphEdge)(nil),           // 34: parca.query.v1alpha1.CallgraphEdge
	(*Callgraph)(nil),               // 35: parca.query.v1alpha1.Callgraph
	(*QueryResponse)(nil),           // 36: parca.query.v1alpha1.QueryResponse
	(*SeriesRequest)(nil),           // 37: parca.query.v1alpha1.SeriesRequest
	(*SeriesResponse)(nil),          // 38: parca.query.v1alpha1.SeriesResponse
	(*LabelsRequest)(nil),           // 39: parca.query.v1alpha1.LabelsRequest
	(*LabelsResponse)(nil),          // 40: parca.query.v1alpha1.LabelsResponse
	(*ValuesRequest)(nil),           // 41: parca.query.v1alpha1.ValuesRequest
	(*ValuesResponse)(nil),          // 42: parca.query.v1alpha1.ValuesResponse
	(*ValueType)(nil),               // 43: parca.query.v1alpha1.ValueType
	(*ShareProfileRequest)(nil),     // 44: parca.query.v1alpha1.ShareProfileRequest
	(*ShareProfileResponse)(nil),    // 45: parca.query.v1alpha1.ShareProfileResponse
	(*TableArrow)(nil),              // 46: parca.query.v1alpha1.TableArrow
	(*ProfileMetadata)(nil),         // 47: parca.query.v1alpha1.ProfileMetadata
	(*timestamppb.Timestamp)(nil),   // 48: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),     // 49: google.protobuf.Duration
	(*v1alpha1.LabelSet)(nil),       // 50: parca.profilestore.v1alpha1.LabelSet
	(*v1alpha11.Location)(nil),      // 51: parca.metastore.v1alpha1.Location
	(*v1alpha11.Mapping)(nil),       // 52: parca.metastore.v1alpha1.Mapping
	(*v1alpha11.Function)(nil),      // 53: parca.metastore.v1alpha1.Function
	(*v1alpha11.Line)(nil),          // 54: parca.metastore.v1alpha1.Line
}
var file_parca_query_v1alpha1_query_proto_depIdxs = []int32{
	5,  // 0: parca.query.v1alpha1.ProfileTypesResponse.types:type_name -> parca.query.v1alpha1.ProfileType
	48, // 1: parca.query.v1alpha1.QueryRangeRequest.start:type_name -> google.protobuf.Timestamp
	48, // 2: parca.query.v1alpha1.QueryRangeRequest.end:type_name -> google.protobuf.Timestamp
	49, // 3: parca.query.v1alpha1.QueryRangeRequest.step:type_name -> google.protobuf.Duration
	8,  // 4: parca.query.v1alpha1.QueryRangeResponse.series:type_name -> parca.query.v1alpha1.MetricsSeries
	50, // 5: parca.query.v1alpha1.MetricsSeries.labelset:type_name -> parca.profilestore.v1alpha1.LabelSet
	9,  // 6: parca.query.v1alpha1.MetricsSeries.samples:type_name -> parca.query.v1alpha1.MetricsSample
	43, // 7: parca.query.v1alpha1.MetricsSeries.period_type:type_name -> parca.query.v1alpha1.ValueType
	43, // 8: parca.query.v1alpha1.MetricsSeries.sample_type:type_name -> parca.query.v1alpha1.ValueType
	48, // 9: parca.query.v1alpha1.MetricsSample.timestamp:type_name -> google.protobuf.Timestamp
	48, // 10: parca.query.v1alpha1.MergeProfile.start:type_name -> google.protobuf.Timestamp
	48, // 11: parca.query.v1alpha1.MergeProfile.end:type_name -> google.protobuf.Timestamp
	48, // 12: parca.query.v1alpha1.SingleProfile.time:type_name -> google.protobuf.Timestamp
	13, // 13: parca.query.v1alpha1.DiffProfile.a:type_name -> parca.query.v1alpha1.ProfileDiffSelection
	13, // 14: parca.query.v1alpha1.DiffProfile.b:type_name -> parca.query.v1alpha1.ProfileDiffSelection
	0,  // 15: parca.query.v1alpha1.ProfileDiffSelection.mode:type_name -> parca.query.v1alpha1.ProfileDiffSelection.Mode
	10, // 16: parca.query.v1alpha1.ProfileDiffSelection.merge:type_name -> parca.query.v1alpha1.MergeProfile
	11, // 17: parca.query.v1alpha1.ProfileDiffSelection.single:type_name -> parca.query.v1alpha1.SingleProfile
	1,  // 18: parca.query.v1alpha1.QueryRequest.mode:type_name -> parca.query.v1alpha1.QueryRequest.Mode
	12, // 19: parca.query.v1alpha1.QueryRequest.diff:type_name -> parca.query.v1alpha1.DiffProfile
	10, // 20: parca.query.v1alpha1.QueryRequest.merge:type_name -> parca.query.v1alpha1.MergeProfile
	11, // 21: parca.query.v1alpha1.QueryRequest.single:type_name -> parca.query.v1alpha1.SingleProfile
	2,  // 22: parca.query.v1alpha1.QueryRequest.report_type:type_name -> parca.query.v1alpha1.QueryRequest.ReportType
	22, // 23: parca.query.v1alpha1.QueryRequest.group_by:type_name -> parca.query.v1alpha1.GroupBy
	21, // 24: parca.query.v1alpha1.QueryRequest.source_reference:type_name -> parca.query.v1alpha1.SourceReference
	20, // 25: parca.query.v1alpha1.QueryRequest.runtime_filter:type_name -> parca.query.v1alpha1.RuntimeFilter
	15, // 26: parca.query.v1alpha1.QueryRequest.filter:type_name -> parca.query.v1alpha1.Filter
	16, // 27: parca.query.v1alpha1.Filter.stack_filter:type_name -> parca.query.v1alpha1.StackFilter
	18, // 28: parca.query.v1alpha1.Filter.frame_filter:type_name -> parca.query.v1alpha1.FrameFilter
	17, // 29: parca.query.v1alpha1.StackFilter.function_name_stack_filter:type_name -> parca.query.v1alpha1.FunctionNameStackFilter
	19, // 30: parca.query.v1alpha1.FrameFilter.binary_frame_filter:type_name -> parca.query.v1alpha1.BinaryFrameFilter
	24, // 31: parca.query.v1alpha1.Top.list:type_name -> parca.query.v1alpha1.TopNode
	25, // 32: parca.query.v1alpha1.TopNode.meta:type_name -> parca.query.v1alpha1.TopNodeMeta
	51, // 33: parca.query.v1alpha1.TopNodeMeta.location:type_name -> parca.metastore.v1alpha1.Location
	52, // 34: parca.query.v1alpha1.TopNodeMeta.mapping:type_name -> parca.metastore.v1alpha1.Mapping
	53, // 35: parca.query.v1alpha1.TopNodeMeta.function:type_name -> parca.metastore.v1alpha1.Function
	54, // 36: parca.query.v1alpha1.TopNodeMeta.line:type_name -> parca.metastore.v1alpha1.Line
	29, // 37: parca.query.v1alpha1.Flamegraph.root:type_name -> parca.query.v1alpha1.FlamegraphRootNode
	51, // 38: parca.query.v1alpha1.Flamegraph.locations:type_name -> parca.metastore.v1alpha1.Location
	52, // 39: parca.query.v1alpha1.Flamegraph.mapping:type_name -> parca.metastore.v1alpha1.Mapping
	53, // 40: parca.query.v1alpha1.Flamegraph.function:type_name -> parca.metastore.v1alpha1.Function
	30, // 41: parca.query.v1alpha1.FlamegraphRootNode.children:type_name -> parca.query.v1alpha1.FlamegraphNode
	31, // 42: parca.query.v1alpha1.FlamegraphNode.meta:type_name -> parca.query.v1alpha1.FlamegraphNodeMeta
	30, // 43: parca.query.v1alpha1.FlamegraphNode.children:type_name -> parca.query.v1alpha1.FlamegraphNode
	51, // 44: parca.query.v1alpha1.FlamegraphNodeMeta.location:type_name -> parca.metastore.v1alpha1.Location
	52, // 45: parca.query.v1alpha1.FlamegraphNodeMeta.mapping:type_name -> parca.metastore.v1alpha1.Mapping
	53, // 46: parca.query.v1alpha1.FlamegraphNodeMeta.function:type_name -> parca.metastore.v1alpha1.Function
	54, // 47: parca.query.v1alpha1.FlamegraphNodeMeta.line:type_name -> parca.metastore.v1alpha1.Line
	33, // 48: parca.query.v1alpha1.CallgraphNode.meta:type_name -> parca.query.v1alpha1.CallgraphNodeMeta
	51, // 49: parca.query.v1alpha1.CallgraphNodeMeta.location:type_name -> parca.metastore.v1alpha1.Location
	52, // 50: parca.query.v1alpha1.CallgraphNodeMeta.mapping:type_name -> parca.metastore.v1alpha1.Mapping
	53, // 51: parca.query.v1alpha1.CallgraphNodeMeta.function:type_name -> parca.metastore.v1alpha1.Function
	54, // 52: parca.query.v1alpha1.CallgraphNodeMeta.line:type_name -> parca.metastore.v1alpha1.Line
	32, // 53: parca.query.v1alpha1.Callgraph.nodes:type_name -> parca.query.v1alpha1.CallgraphNode
	34, // 54: parca.query.v1alpha1.Callgraph.edges:type_name -> parca.query.v1alpha1.CallgraphEdge
	26, // 55: parca.query.v1alpha1.QueryResponse.flamegraph:type_name -> parca.query.v1alpha1.Flamegraph
	23, // 56: parca.query.v1alpha1.QueryResponse.top:type_name -> parca.query.v1alpha1.Top
	35, // 57: parca.query.v1alpha1.QueryResponse.callgraph:type_name -> parca.query.v1alpha1.Callgraph
	27, // 58: parca.query.v1alpha1.QueryResponse.flamegraph_arrow:type_name -> parca.query.v1alpha1.FlamegraphArrow
	28, // 59: parca.query.v1alpha1.QueryResponse.source:type_name -> parca.query.v1alpha1.Source
	46, // 60: parca.query.v1alpha1.QueryResponse.table_arrow:type_name -> parca.query.v1alpha1.TableArrow
	47, // 61: parca.query.v1alpha1.QueryResponse.profile_metadata:type_name -> parca.query.v1alpha1.ProfileMetadata
	48, // 62: parca.query.v1alpha1.SeriesRequest.start:type_name -> google.protobuf.Timestamp
	48, // 63: parca.query.v1alpha1.SeriesRequest.end:type_name -> google.protobuf.Timestamp
	48, // 64: parca.query.v1alpha1.LabelsRequest.start:type_name -> google.protobuf.Timestamp
	48, // 65: parca.query.v1alpha1.LabelsRequest.end:type_name -> google.protobuf.Timestamp
	48, // 66: parca.query.v1alpha1.ValuesRequest.start:type_name -> google.protobuf.Timestamp
	48, // 67: parca.query.v1alpha1.ValuesRequest.end:type_name -> google.protobuf.Timestamp
	14, // 68: parca.query.v1alpha1.ShareProfileRequest.query_request:type_name -> parca.query.v1alpha1.QueryRequest
	6,  // 69: parca.query.v1alpha1.QueryService.QueryRange:input_type -> parca.query.v1alpha1.QueryRangeRequest
	14, // 70: parca.query.v1alpha1.QueryService.Query:input_type -> parca.query.v1alpha1.QueryRequest
	37, // 71: parca.query.v1alpha1.QueryService.Series:input_type -> parca.query.v1alpha1.SeriesRequest
	3,  // 72: parca.query.v1alpha1.QueryService.ProfileTypes:input_type -> parca.query.v1alpha1.ProfileTypesRequest
	39, // 73: parca.query.v1alpha1.QueryService.Labels:input_type -> parca.query.v1alpha1.LabelsRequest
	41, // 74: parca.query.v1alpha1.QueryService.Values:input_type -> parca.query.v1alpha1.ValuesRequest
	44, // 75: parca.query.v1alpha1.QueryService.ShareProfile:input_type -> parca.query.v1alpha1.ShareProfileRequest
	7,  // 76: parca.query.v1alpha1.QueryService.QueryRange:output_type -> parca.query.v1alpha1.QueryRangeResponse
	36, // 77: parca.query.v1alpha1.QueryService.Query:output_type -> parca.query.v1alpha1.QueryResponse
	38, // 78: parca.query.v1alpha1.QueryService.Series:output_type -> parca.query.v1alpha1.SeriesResponse
	4,  // 79: parca.query.v1alpha1.QueryService.ProfileTypes:output_type -> parca.query.v1alpha1.ProfileTypesResponse
	40, // 80: parca.query.v1alpha1.QueryService.Labels:output_type -> parca.query.v1alpha1.LabelsResponse
	42, // 81: parca.query.v1alpha1.QueryService.Values:output_type -> parca.query.v1alpha1.ValuesResponse
	45, // 82: parca.query.v1alpha1.QueryService.ShareProfile:output_type -> parca.query.v1alpha1.ShareProfileResponse
	76, // [76:83] is the sub-list for method output_type
	69, // [69:76] is the sub-list for method input_type
	69, // [69:69] is the sub-list for extension type_name
	69, // [69:69] is the sub-list for extension extendee
	0,  // [0:69] is the sub-list for field type_name
}

func init() { file_parca_query_v1alpha1_query_proto_init() }
func file_parca_query_v1alpha1_query_proto_init() {
	if File_parca_query_v1alpha1_query_proto != nil {
		return
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[9].OneofWrappers = []any{}
	file_parca_query_v1alpha1_query_proto_msgTypes[10].OneofWrappers = []any{
		(*ProfileDiffSelection_Merge)(nil),
		(*ProfileDiffSelection_Single)(nil),
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[11].OneofWrappers = []any{
		(*QueryRequest_Diff)(nil),
		(*QueryRequest_Merge)(nil),
		(*QueryRequest_Single)(nil),
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[12].OneofWrappers = []any{
		(*Filter_StackFilter)(nil),
		(*Filter_FrameFilter)(nil),
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[13].OneofWrappers = []any{
		(*StackFilter_FunctionNameStackFilter)(nil),
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[15].OneofWrappers = []any{
		(*FrameFilter_BinaryFrameFilter)(nil),
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[33].OneofWrappers = []any{
		(*QueryResponse_Flamegraph)(nil),
		(*QueryResponse_Pprof)(nil),
		(*QueryResponse_Top)(nil),
		(*QueryResponse_Callgraph)(nil),
		(*QueryResponse_FlamegraphArrow)(nil),
		(*QueryResponse_Source)(nil),
		(*QueryResponse_TableArrow)(nil),
		(*QueryResponse_ProfileMetadata)(nil),
	}
	file_parca_query_v1alpha1_query_proto_msgTypes[36].OneofWrappers = []any{}
	file_parca_query_v1alpha1_query_proto_msgTypes[38].OneofWrappers = []any{}
	file_parca_query_v1alpha1_query_proto_msgTypes[41].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_parca_query_v1alpha1_query_proto_rawDesc), len(file_parca_query_v1alpha1_query_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_parca_query_v1alpha1_query_proto_goTypes,
		DependencyIndexes: file_parca_query_v1alpha1_query_proto_depIdxs,
		EnumInfos:         file_parca_query_v1alpha1_query_proto_enumTypes,
		MessageInfos:      file_parca_query_v1alpha1_query_proto_msgTypes,
	}.Build()
	File_parca_query_v1alpha1_query_proto = out.File
	file_parca_query_v1alpha1_query_proto_goTypes = nil
	file_parca_query_v1alpha1_query_proto_depIdxs = nil
}
