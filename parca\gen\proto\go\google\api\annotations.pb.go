// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: google/api/annotations.proto

package annotations

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var file_google_api_annotations_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: (*HttpRule)(nil),
		Field:         72295728,
		Name:          "google.api.http",
		Tag:           "bytes,72295728,opt,name=http",
		Filename:      "google/api/annotations.proto",
	},
}

// Extension fields to descriptorpb.MethodOptions.
var (
	// See `HttpRule`.
	//
	// optional google.api.HttpRule http = 72295728;
	E_Http = &file_google_api_annotations_proto_extTypes[0]
)

var File_google_api_annotations_proto protoreflect.FileDescriptor

const file_google_api_annotations_proto_rawDesc = "" +
	"\n" +
	"\x1cgoogle/api/annotations.proto\x12\n" +
	"google.api\x1a\x15google/api/http.proto\x1a google/protobuf/descriptor.proto:K\n" +
	"\x04http\x12\x1e.google.protobuf.MethodOptions\x18\xb0ʼ\" \x01(\v2\x14.google.api.HttpRuleR\x04httpB\xae\x01\n" +
	"\x0ecom.google.apiB\x10AnnotationsProtoP\x01ZAgoogle.golang.org/genproto/googleapis/api/annotations;annotations\xa2\x02\x03GAX\xaa\x02\n" +
	"Google.Api\xca\x02\n" +
	"Google\\Api\xe2\x02\x16Google\\Api\\GPBMetadata\xea\x02\vGoogle::Apib\x06proto3"

var file_google_api_annotations_proto_goTypes = []any{
	(*descriptorpb.MethodOptions)(nil), // 0: google.protobuf.MethodOptions
	(*HttpRule)(nil),                   // 1: google.api.HttpRule
}
var file_google_api_annotations_proto_depIdxs = []int32{
	0, // 0: google.api.http:extendee -> google.protobuf.MethodOptions
	1, // 1: google.api.http:type_name -> google.api.HttpRule
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	1, // [1:2] is the sub-list for extension type_name
	0, // [0:1] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_api_annotations_proto_init() }
func file_google_api_annotations_proto_init() {
	if File_google_api_annotations_proto != nil {
		return
	}
	file_google_api_http_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_google_api_annotations_proto_rawDesc), len(file_google_api_annotations_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_google_api_annotations_proto_goTypes,
		DependencyIndexes: file_google_api_annotations_proto_depIdxs,
		ExtensionInfos:    file_google_api_annotations_proto_extTypes,
	}.Build()
	File_google_api_annotations_proto = out.File
	file_google_api_annotations_proto_goTypes = nil
	file_google_api_annotations_proto_depIdxs = nil
}
