# eBPF compilation container based on coroot-node-agent
FROM ubuntu:22.04

RUN apt-get update && apt-get install -y \
    clang \
    llvm \
    libbpf-dev \
    linux-headers-generic \
    make \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /src

# Copy eBPF source files
COPY ebpf.c vmlinux.h ./

# Compile eBPF program
RUN clang -O2 -g -target bpf -c ebpf.c -o ebpf.o

# Create output directory
RUN mkdir -p /output

# Copy compiled object to output
RUN cp ebpf.o /output/

CMD ["cp", "/output/ebpf.o", "/src/"]
