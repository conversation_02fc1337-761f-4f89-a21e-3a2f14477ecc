{"coredump-ref": "9ab16c3f657ae5d69b0ba37256d9fec367c2da02973be36b6b9bfed743253feb", "threads": [{"lwp": 155034, "frames": ["main.hello5+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:41", "main.hello4+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:36", "main.hello3+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:30", "main.hello2+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:25", "main.hello1+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:17", "main.hello+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:12", "main.main+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:47", "runtime.main+0 in /usr/local/go/src/runtime/proc.go:259", "runtime.goexit+0 in /usr/local/go/src/runtime/asm_arm64.s:1166"]}, {"lwp": 155058, "frames": ["runtime.usleep+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:142", "runtime.sysmon+0 in /usr/local/go/src/runtime/proc.go:5162", "runtime.mstart1+0 in /usr/local/go/src/runtime/proc.go:1428", "runtime.mstart0+0 in /usr/local/go/src/runtime/proc.go:1359", "runtime.mstart+0 in /usr/local/go/src/runtime/asm_arm64.s:129"]}, {"lwp": 155059, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2867", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 155060, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2867", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}], "modules": [{"ref": "ff1d01d8c89db3cbadbd6c17b3c2dce526002e157ea60ec674876b35afc8910e", "local-path": "/media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.345"}]}