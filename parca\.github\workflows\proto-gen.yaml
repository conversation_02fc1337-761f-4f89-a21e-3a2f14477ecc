name: proto-gen

on:
  pull_request:
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              ".github/workflows/proto-gen.yaml",
              ".go-version",
              "buf.gen.yaml",
              "buf.work.yaml",
              "proto/**"
            ]
          skip_after_successful_duplicate: false

  build:
    name: Proto Generate
    needs: skip-check
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: bufbuild/buf-setup-action@a47c93e0b1648d5651a065437926377d060baa99 # v1.50.0

      - name: Generate
        run:
          make proto/generate && git diff --exit-code ':!ui/packages/app/web/public/keep.go'
