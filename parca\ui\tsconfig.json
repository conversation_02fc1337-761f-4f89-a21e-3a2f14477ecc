{"extends": "./config/next.tsconfig.json", "include": ["./tests/**/*", "./.storybook/*", "./packages/**/*"], "compilerOptions": {"module": "es2020", "moduleResolution": "node", "strict": true, "isolatedModules": true, "baseUrl": ".", "target": "es2020"}, "exclude": ["node_modules", "**/*.benchmark.tsx"], "ts-node": {"esm": true, "compilerOptions": {"target": "es2020", "module": "es2020", "types": ["vite/client"]}}}