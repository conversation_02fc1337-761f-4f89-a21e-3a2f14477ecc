#!/usr/bin/env bash
#
# Copyright 2022-2025 The Parca Authors
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

set -euo pipefail

TARGET_DIR=${TARGET_DIR:-${HOME}/.local/bin}

# renovate: datasource=go depName=github.com/kubernetes/minikube
MINIKUBE_VERSION='v1.36.0'

GOOS="$(go env GOOS)"
GOARCH="$(go env GOARCH)"

if [ -e "${TARGET_DIR}/minikube" ]; then
    echo 'minikube already exists' >&2
else
    curl -LJO "https://storage.googleapis.com/minikube/releases/${MINIKUBE_VERSION}/minikube-${GOOS}-${GOARCH}"
    install "minikube-${GOOS}-${GOARCH}" "${TARGET_DIR}/minikube"
fi
