{"coredump-ref": "ed2266dd96aae211b3ee81cfa46bd1f2b0060ec6184ca7bf29be3003a034a200", "threads": [{"lwp": 7731, "frames": ["libc.so.6+0xd7d14", "node+0x14acfcf", "node+0x14ae187", "node+0x14ae4ab", "node+0xbc1a87", "node+0xbbccff", "node+0xbbd103", "V8::ExitFrame+0 in :0", "handleWriteReq+16 in node:internal/stream_base_commons:61", "writeGeneric+2 in node:internal/stream_base_commons:149", "Socket._writeGeneric+0 in node:net:0", "Socket._write+0 in node:net:0", "writeOrBuffer+24 in node:internal/streams/writable:389", "_write+47 in node:internal/streams/writable:330", "Writable.write+1 in node:internal/streams/writable:334", "value+28 in node:internal/console/constructor:289", "warn+1 in node:internal/console/constructor:368", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe02b5b", "node+0xe03663", "node+0xcdae9b", "node+0xbec133", "V8::ExitFrame+0 in :0", "trace+6 in node:internal/console/constructor:414", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe02b5b", "node+0xe03663", "node+0xcdae9b", "node+0xbec133", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:3", "add3+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:8", "test+1 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:12", "submain+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:17", "main+2 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:23", "<anonymous>+26 in /home/<USER>/opentelemetry-ebpf-profiler/tools/coredump/testsources/node/inlining.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1101", "Module._extensions..js+43 in node:internal/modules/cjs/loader:1153", "Module.load+12 in node:internal/modules/cjs/loader:981", "Module._load+65 in node:internal/modules/cjs/loader:822", "executeUserEntryPoint+0 in node:internal/modules/run_main:0", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xe02b5b", "node+0xe03663", "node+0xcdae9b", "node+0xab8c2f", "node+0xab8e4f", "node+0xaba13f", "node+0xa4642f", "node+0xb2ac47", "node+0xabbf67", "libc.so.6+0x273fb", "libc.so.6+0x274cb", "node+0xa41cb3"]}, {"lwp": 7732, "frames": ["libc.so.6+0xe5f3c", "node+0x14b3a3b", "node+0x14a292f", "node+0xb5630f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7733, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14afd73", "node+0xb51b9f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7734, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14afd73", "node+0xb51b9f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7735, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14afd73", "node+0xb51b9f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7736, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x7c8fb", "node+0x14afd73", "node+0xb51b9f", "libc.so.6+0x7d5c7", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}, {"lwp": 7737, "frames": ["libc.so.6+0x79df8", "libc.so.6+0x85a5b", "node+0x14afb5b", "node+0xbdfe0f", "libc.so.6+0x7d5c7", "libc.so.6+0xe5d9b"]}], "modules": [{"ref": "f1935c0616a48e7ec471c26886df0411beffd4e56c92539d9a6a94641950badf", "local-path": "/usr/lib/aarch64-linux-gnu/libdl.so.2"}, {"ref": "22a0986a1047cd3c9a55368fdc6bb6e5a4455aceb53ec15dbe19112d95583642", "local-path": "/usr/lib/aarch64-linux-gnu/libpthread.so.0"}, {"ref": "c4834bd79254443665af96b6d5e71d124b7f92f2eea121e61f9fff5204fc594d", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30"}, {"ref": "594545a9720b4a16973a823d18c71fbf070d3c07fb17b01df5376273e91644a1", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "fd04b635d29b5cb3faaf502a6c5cd68a623d66a736f4d0561ff280c2fa411c79", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "acfe23cf0f5f4ac35b4c314415bb31d6f52a9bd39df8be04dfb2818a571533fd", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}, {"ref": "6754f4d27557d8e76c519734eebebb0f80feb3730228a6c2f7dd06ae966914d6", "local-path": "/home/<USER>/.nvm/versions/node/v16.6.0/bin/node"}, {"ref": "e647ee3042517f06cbebbbf0f66ba25486d0722222404f1d067683767a055566", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}]}