{"name": "@parca/utilities", "version": "0.0.103", "description": "A set of reusable functions for Parca", "main": "dist/index.js", "scripts": {"test": "jest --coverage ./src/*", "prepublish": "pnpm run build", "build": "tsc", "build-swc": "swc ./src -d dist --copy-files", "watch": "tsc-watch"}, "dependencies": {"@parca/client": "workspace:*", "@rehooks/local-storage": "^2.4.4", "date-fns": "3.6.0", "date-fns-tz": "^3.1.3", "tailwindcss": "3.2.4", "tsc-watch": "6.3.1"}, "keywords": [], "author": "", "license": "ISC", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "f92c5502bce797d27d67f57a39f8af30d0d04e1e"}