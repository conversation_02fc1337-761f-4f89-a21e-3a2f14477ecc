{"name": "@parca/profile", "version": "0.19.20", "description": "Profile viewing libraries", "dependencies": {"@floating-ui/react": "^0.27.12", "@headlessui/react": "^1.7.19", "@iconify/react": "^4.0.0", "@parca/client": "workspace:*", "@parca/components": "workspace:*", "@parca/dynamicsize": "workspace:*", "@parca/hooks": "workspace:*", "@parca/icons": "workspace:*", "@parca/parser": "workspace:*", "@parca/store": "workspace:*", "@parca/utilities": "workspace:*", "@popperjs/core": "^2.11.8", "@protobuf-ts/runtime-rpc": "^2.5.0", "@storybook/preview-api": "^8.4.3", "@tanstack/react-query": "^4.0.5", "@tanstack/react-table": "^8.17.3", "@tanstack/table-core": "^8.16.0", "@types/d3": "^7.4.3", "@types/d3-scale": "^4.0.8", "@types/d3-selection": "^3.0.10", "@types/fast-levenshtein": "^0.0.4", "@types/react-beautiful-dnd": "^13.1.8", "apache-arrow": "^19.0.1", "classnames": "^2.3.1", "d3": "7.9.0", "d3-array": "^3.2.4", "d3-color": "^3.1.0", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "d3-selection": "3.0.0", "d3-shape": "^3.2.0", "fast-deep-equal": "^3.1.3", "fast-levenshtein": "^3.0.0", "framer-motion": "6.5.1", "graphviz-wasm": "3.0.2", "lodash.throttle": "^4.1.1", "react": "18.3.1", "react-beautiful-dnd": "^13.1.1", "react-contexify": "^6.0.0", "react-dom": "18.3.1", "react-draggable": "^4.4.6", "react-flame-graph": "^1.4.0", "react-inlinesvg": "^3.0.2", "react-map-interaction": "^2.1.0", "react-popper": "^2.3.0", "react-redux": "^8.0.2", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.4.0", "react-tooltip": "^5.26.3", "react-use": "^17.5.0", "tailwindcss": "3.2.4", "tsc-watch": "6.3.1", "with-alpha-hex": "^1.0.6"}, "devDependencies": {"@types/lodash.throttle": "4.1.9", "@types/react-syntax-highlighter": "15.5.13"}, "main": "dist/index.js", "scripts": {"test": "jest --coverage --config ../../../jest.config.js ./src/*", "prepublish": "pnpm run build", "build": "tsc && pnpm run compile:styles", "build-swc": "swc ./src -d dist --copy-files && pnpm run compile:styles", "watch": "tsc-watch --onCompilationComplete 'pnpm run compile:styles'", "compile:styles": "tailwindcss -o dist/styles.css --minify"}, "keywords": [], "author": "", "license": "ISC", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "f92c5502bce797d27d67f57a39f8af30d0d04e1e"}