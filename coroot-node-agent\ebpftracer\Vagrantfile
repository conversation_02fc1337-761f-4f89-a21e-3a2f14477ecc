$script = <<-SCRIPT
sudo apt update && sudo apt -y install gcc cgroup-tools
sudo snap install go --channel=1.19/stable --classic

# prevent unexpected `systemd-udevd` processes from appearing
systemctl stop systemd-udevd.service systemd-udevd-control.socket systemd-udevd-kernel.socket

sudo swapoff -a
sudo sysctl vm.overcommit_memory=1
SCRIPT

Vagrant.configure("2") do |config|
	config.vm.provider "virtualbox" do |v|
  		v.memory = 1024
  		v.cpus = 2
	end

	config.vm.provision "shell", inline: $script
	config.vm.box_check_update = false
	config.vm.synced_folder "..", "/tmp/src"

	config.vm.define "ubuntu1810" do |c|
		c.vm.box = "generic/ubuntu1810"
    end
	config.vm.define "ubuntu2004" do |c|
		c.vm.box = "generic/ubuntu2004"
    end
	config.vm.define "ubuntu2010" do |c|
		c.vm.box = "generic/ubuntu2010"
    end
    config.vm.define "ubuntu2110" do |c|
        c.vm.box = "generic/ubuntu2110"
    end
    config.vm.define "ubuntu2204" do |c|
        c.vm.box = "generic/ubuntu2204"
    end
end
