// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

// Code generated by cmd/cgo -godefs; DO NOT EDIT.
// cgo -godefs types_def.go

package support // import "go.opentelemetry.io/ebpf-profiler/support"

const (
	FrameMarkerUnknown  = 0x0
	FrameMarkerErrorBit = 0x80
	FrameMarkerPython   = 0x1
	FrameMarkerNative   = 0x3
	FrameMarkerPHP      = 0x2
	FrameMarkerPHPJIT   = 0x9
	FrameMarkerKernel   = 0x4
	FrameMarkerHotSpot  = 0x5
	FrameMarkerRuby     = 0x6
	FrameMarkerPerl     = 0x7
	FrameMarkerV8       = 0x8
	FrameMarkerDotnet   = 0xa
	FrameMarkerLuaJIT   = 0xb
	FrameMarkerGo       = 0xc
	FrameMarkerAbort    = 0xff
)

const (
	ProgUnwindStop    = 0x0
	ProgUnwindNative  = 0x1
	ProgUnwindHotspot = 0x2
	ProgUnwindPython  = 0x4
	ProgUnwindPHP     = 0x5
	ProgUnwindRuby    = 0x6
	ProgUnwindPerl    = 0x3
	ProgUnwindV8      = 0x7
	ProgUnwindDotnet  = 0x8
	ProgGoLabels      = 0x9
	ProgUnwindLuaJIT  = 0xa
)

const (
	DeltaCommandFlag = 0x8000

	MergeOpcodeNegative = 0x80
)

const (
	EventTypeGenericPID = 0x1
)

const MaxFrameUnwinds = 0x100

const (
	MetricIDBeginCumulative = 0x6f
)

const (
	BitWidthPID  = 0x20
	BitWidthPage = 0x40
)

const (
	StackDeltaBucketSmallest = 0x8
	StackDeltaBucketLargest  = 0x17

	StackDeltaPageBits = 0x10
	StackDeltaPageMask = 0xffff
)

const (
	HSTSIDIsStubBit       = 0x3f
	HSTSIDHasFrameBit     = 0x3e
	HSTSIDStackDeltaBit   = 0x38
	HSTSIDStackDeltaMask  = 0x3f
	HSTSIDStackDeltaScale = 0x8
	HSTSIDSegMapBit       = 0x0
	HSTSIDSegMapMask      = 0xffffffffffffff
)

const (
	PerfMaxStackDepth = 0x7f
)

const (
	TraceOriginUnknown  = 0x0
	TraceOriginSampling = 0x1
	TraceOriginOffCPU   = 0x2
)

const OffCPUThresholdMax = 0x3e8

type ApmIntProcInfo struct {
	Offset uint64
}
type DotnetProcInfo struct {
	Version uint32
}
type PHPProcInfo struct {
	Current_execute_data                uint64
	Jit_return_address                  uint64
	Zend_execute_data_function          uint8
	Zend_execute_data_opline            uint8
	Zend_execute_data_prev_execute_data uint8
	Zend_execute_data_this_type_info    uint8
	Zend_function_type                  uint8
	Zend_op_lineno                      uint8
	Pad_cgo_0                           [2]byte
}
type RubyProcInfo struct {
	Version                      uint32
	Current_ctx_ptr              uint64
	Vm_stack                     uint8
	Vm_stack_size                uint8
	Cfp                          uint8
	Pc                           uint8
	Iseq                         uint8
	Ep                           uint8
	Size_of_control_frame_struct uint8
	Body                         uint8
	Iseq_type                    uint8
	Iseq_encoded                 uint8
	Iseq_size                    uint8
	Size_of_value                uint8
	Running_ec                   uint16
	Pad_cgo_0                    [2]byte
}

const (
	sizeof_ApmIntProcInfo = 0x8
	sizeof_DotnetProcInfo = 0x4
	sizeof_PHPProcInfo    = 0x18
	sizeof_RubyProcInfo   = 0x20
)

const (
	CustomLabelMaxKeyLen = 0x10
	CustomLabelMaxValLen = 0x30
)

const (
	UnwindOpcodeCommand   uint8 = 0x0
	UnwindOpcodeBaseCFA   uint8 = 0x1
	UnwindOpcodeBaseSP    uint8 = 0x2
	UnwindOpcodeBaseFP    uint8 = 0x3
	UnwindOpcodeBaseLR    uint8 = 0x4
	UnwindOpcodeBaseReg   uint8 = 0x5
	UnwindOpcodeFlagDeref uint8 = 0x80

	UnwindCommandInvalid      int32 = 0x0
	UnwindCommandStop         int32 = 0x1
	UnwindCommandPLT          int32 = 0x2
	UnwindCommandSignal       int32 = 0x3
	UnwindCommandFramePointer int32 = 0x4

	UnwindDerefMask       int32 = 0x7
	UnwindDerefMultiplier int32 = 0x8
)
