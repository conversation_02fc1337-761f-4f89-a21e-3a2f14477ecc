// @generated by protobuf-ts 2.9.6 with parameter generate_dependencies
// @generated from protobuf file "parca/telemetry/v1alpha1/telemetry.proto" (package "parca.telemetry.v1alpha1", syntax proto3)
// tslint:disable
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
/**
 * ReportPanicRequest contained the info about a panic.
 *
 * @generated from protobuf message parca.telemetry.v1alpha1.ReportPanicRequest
 */
export interface ReportPanicRequest {
    /**
     * Stderr from the agent that exited with an error.
     *
     * @generated from protobuf field: string stderr = 1;
     */
    stderr: string;
    /**
     * Agent metadata.
     *
     * @generated from protobuf field: map<string, string> metadata = 2;
     */
    metadata: {
        [key: string]: string;
    };
}
/**
 * ReportPanicResponse contains the response for a ReportPanicRequest.
 *
 * @generated from protobuf message parca.telemetry.v1alpha1.ReportPanicResponse
 */
export interface ReportPanicResponse {
}
// @generated message type with reflection information, may provide speed optimized methods
class ReportPanicRequest$Type extends MessageType<ReportPanicRequest> {
    constructor() {
        super("parca.telemetry.v1alpha1.ReportPanicRequest", [
            { no: 1, name: "stderr", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "metadata", kind: "map", K: 9 /*ScalarType.STRING*/, V: { kind: "scalar", T: 9 /*ScalarType.STRING*/ } }
        ]);
    }
    create(value?: PartialMessage<ReportPanicRequest>): ReportPanicRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.stderr = "";
        message.metadata = {};
        if (value !== undefined)
            reflectionMergePartial<ReportPanicRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ReportPanicRequest): ReportPanicRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string stderr */ 1:
                    message.stderr = reader.string();
                    break;
                case /* map<string, string> metadata */ 2:
                    this.binaryReadMap2(message.metadata, reader, options);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    private binaryReadMap2(map: ReportPanicRequest["metadata"], reader: IBinaryReader, options: BinaryReadOptions): void {
        let len = reader.uint32(), end = reader.pos + len, key: keyof ReportPanicRequest["metadata"] | undefined, val: ReportPanicRequest["metadata"][any] | undefined;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case 1:
                    key = reader.string();
                    break;
                case 2:
                    val = reader.string();
                    break;
                default: throw new globalThis.Error("unknown map entry field for field parca.telemetry.v1alpha1.ReportPanicRequest.metadata");
            }
        }
        map[key ?? ""] = val ?? "";
    }
    internalBinaryWrite(message: ReportPanicRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string stderr = 1; */
        if (message.stderr !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.stderr);
        /* map<string, string> metadata = 2; */
        for (let k of globalThis.Object.keys(message.metadata))
            writer.tag(2, WireType.LengthDelimited).fork().tag(1, WireType.LengthDelimited).string(k).tag(2, WireType.LengthDelimited).string(message.metadata[k]).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.telemetry.v1alpha1.ReportPanicRequest
 */
export const ReportPanicRequest = new ReportPanicRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ReportPanicResponse$Type extends MessageType<ReportPanicResponse> {
    constructor() {
        super("parca.telemetry.v1alpha1.ReportPanicResponse", []);
    }
    create(value?: PartialMessage<ReportPanicResponse>): ReportPanicResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<ReportPanicResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ReportPanicResponse): ReportPanicResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ReportPanicResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.telemetry.v1alpha1.ReportPanicResponse
 */
export const ReportPanicResponse = new ReportPanicResponse$Type();
/**
 * @generated ServiceType for protobuf service parca.telemetry.v1alpha1.TelemetryService
 */
export const TelemetryService = new ServiceType("parca.telemetry.v1alpha1.TelemetryService", [
    { name: "ReportPanic", options: { "google.api.http": { post: "/telemetry/panic", body: "*" } }, I: ReportPanicRequest, O: ReportPanicResponse }
]);
