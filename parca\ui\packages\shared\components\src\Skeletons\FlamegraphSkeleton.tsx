// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import cx from 'classnames';

interface Props {
  isHalfScreen: boolean;
  isDarkMode: boolean;
}

export const FlameActionButtonPlaceholder = ({
  isHalfScreen,
}: {
  isHalfScreen: boolean;
}): JSX.Element => {
  return (
    <div className="ml-2 flex w-full flex-col items-start justify-between gap-2 md:flex-row md:items-end">
      <div>
        <label className="text-sm">Group</label>
        <div className="h-[38px] bg-[#f3f3f3] dark:bg-gray-900 animate-pulse w-[147px]"></div>
      </div>
      <div>
        <label className="text-sm">Sort</label>
        <div className="h-[38px] bg-[#f3f3f3] dark:bg-gray-900 animate-pulse w-[108px]"></div>
      </div>
      <div>
        <div className="h-[38px] bg-[#f3f3f3] dark:bg-gray-900 animate-pulse w-[174px]"></div>
      </div>

      {!isHalfScreen && (
        <>
          <div className="h-[38px] bg-[#f3f3f3] dark:bg-gray-900 animate-pulse w-[145px]"></div>
          <div className="h-[38px] bg-[#f3f3f3] dark:bg-gray-900 animate-pulse w-[137px]"></div>
        </>
      )}
    </div>
  );
};

const FlameGraphSkeleton = ({isHalfScreen, isDarkMode}: Props): JSX.Element => {
  return (
    <svg
      fill="none"
      height="100%"
      viewBox="0 0 2000 509"
      width={isHalfScreen ? '1455px' : '100%'}
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="shimmer" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0.599964"
            stopColor={cx(isDarkMode ? '#1f2937' : '#f3f3f3')}
            stopOpacity="1"
          >
            <animate
              attributeName="offset"
              values="-2; -2; 1"
              keyTimes="0; 0.25; 1"
              dur="2s"
              repeatCount="indefinite"
            ></animate>
          </stop>
          <stop offset="1.59996" stopColor={cx(isDarkMode ? '#374151' : '#ecebeb')} stopOpacity="1">
            <animate
              attributeName="offset"
              values="-1; -1; 2"
              keyTimes="0; 0.25; 1"
              dur="2s"
              repeatCount="indefinite"
            ></animate>
          </stop>
          <stop offset="2.59996" stopColor={cx(isDarkMode ? '#1f2937' : '#f3f3f3')} stopOpacity="1">
            <animate
              attributeName="offset"
              values="0; 0; 3"
              keyTimes="0; 0.25; 1"
              dur="2s"
              repeatCount="indefinite"
            ></animate>
          </stop>
        </linearGradient>
      </defs>

      <g fill="url(#shimmer)">
        <path d="m0 0h2000v26.6386h-2000z" />
        <path d="m0 30.6367h662.667v26h-662.667z" />
        <path d="m668.667 30.6367h662.667v26.3379h-662.667z" />
        <path d="m1337.33 30.6367h662.667v26h-662.667z" />
        <path d="m0 61.2461h63.7394v25.652h-63.7394z" />
        <path d="m70.8218 61.2461h366.856v25.652h-366.856z" />
        <path d="m445 60.9961h216v25.9072h-216z" />
        <path d="m671 60.9961h515v25.9072h-515z" />
        <path d="m1192.63 61.2461h21.2465v25.652h-21.2465z" />
        <path d="m1220.41 60.9766h90.7158v26h-90.7158z" />
        <path d="m1337 60.9961h276v25.9072h-276z" />
        <path d="m1618.98 61.2461h14.1643v25.652h-14.1643z" />
        <path d="m1637.39 61.2461h362.606v25.652h-362.606z" />
        <path d="m0 91.1914h26.9122v25.4413h-26.9122z" />
        <path d="m73 91.1172h358v25.6747h-358z" />
        <path d="m444.759 91.1914h140.227v25.4413h-140.227z" />
        <path d="m593.484 91.1914h58.0737v25.4413h-58.0737z" />
        <path d="m673 91.0469h357v25.6747h-357z" />
        <path d="m1038.24 91.1914h147.309v25.4413h-147.309z" />
        <path d="m1192.63 91.1914h15.5807v25.4413h-15.5807z" />
        <path d="m1220.41 91.1914h90.7158v25.7865h-90.7158z" />
        <path d="m1524.08 91.1914h89.2352v25.4413h-89.2352z" />
        <path d="m1424.93 91.1914h99.1502v25.4413h-99.1502z" />
        <path d="m1337 90.9766h78v25.6747h-78z" />
        <path d="m1618.98 91.4766h14.1643v25.4413h-14.1643z" />
        <path d="m1640.23 91.1914h33.9943v25.4413h-33.9943z" />
        <path d="m1682.72 91.1914h113.314v25.4413h-113.314z" />
        <path d="m1803.12 91.1914h196.884v25.4413h-196.884z" />
        <path d="m0 121.371h26.9122v25.2621h-26.9122z" />
        <path d="m73 120.977h307v25.4939h-307z" />
        <path d="m386.686 121.371h35.4108v25.2621h-35.4108z" />
        <path d="m444.759 121.371h120.397v25.2621h-120.397z" />
        <path d="m623.229 121.371h11.3314v25.2621h-11.3314z" />
        <path d="m640.227 121.371h11.3314v25.2621h-11.3314z" />
        <path d="m593 121h8v26h-8z" />
        <path d="m606.232 121.371h11.3314v25.2621h-11.3314z" />
        <path d="m673 121.402h184v25.4939h-184z" />
        <path d="m866.856 121.371h162.89v25.2621h-162.89z" />
        <path d="m1038.24 121.371h86.4023v25.2621h-86.4023z" />
        <path d="m1131.73 121.371h53.8244v25.2621h-53.8244z" />
        <path d="m1192.63 121.371h9.91502v25.2621h-9.91502z" />
        <path d="m1220.96 121.371h39.6601v25.2621h-39.6601z" />
        <path d="m1271.44 121.371h39.6882v25.6049h-39.6882z" />
        <path d="m1423.51 121.656h189.802v25.2621h-189.802z" />
        <path d="m1349.86 121.371h22.6629v25.2621h-22.6629z" />
        <path d="m1376.77 121.371h38.2436v25.2621h-38.2436z" />
        <path d="m1618.98 121.656h14.1643v25.2621h-14.1643z" />
        <path d="m1637.39 121.371h36.8272v25.2621h-36.8272z" />
        <path d="m1682.72 121.371h113.314v25.2621h-113.314z" />
        <path d="m1803.12 121.371h196.884v25.2621h-196.884z" />
        <path d="m0 151.23h26.9695v25.0895h-26.9695z" />
        <path d="m73 151.152h69v25.2615h-69z" />
        <path d="m156.139 151.23h202.981v25.0895h-202.981z" />
        <path d="m387 151.078h30v25.2615h-30z" />
        <path d="m453 151.023h112v25h-112z" />
        <path d="m445.706 151.23h2.83889v25.0895h-2.83889z" />
        <path d="m673 151.023h143v25h-143z" />
        <path d="m821.859 151.23h28.3889v25.0895h-28.3889z" />
        <path d="m868.701 151.23h82.3279v25.0895h-82.3279z" />
        <path d="m969.481 151.23h62.4556v25.0895h-62.4556z" />
        <path d="m1044.71 151.23h44.0028v25.0895h-44.0028z" />
        <path d="m1092.97 151.23h31.2278v25.0895h-31.2278z" />
        <path d="m1134.14 151.23h32.6473v25.0895h-32.6473z" />
        <path d="m1172.46 151.23h8.51668v25.0895h-8.51668z" />
        <path d="m956.707 151.23h7.09723v25.0895h-7.09723z" />
        <path d="m1192.17 151.23h9.93613v25.0895h-9.93613z" />
        <path d="m1223.56 151.23h31.2278v25.0895h-31.2278z" />
        <path d="m1424.54 151.23h65.2945v25.0895h-65.2945z" />
        <path d="m1499 151h114v25.2615h-114z" />
        <path d="m1357.99 151.23h14.1945v25.0895h-14.1945z" />
        <path d="m1376.7 151.23h38.3251v25.0895h-38.3251z" />
        <path d="m1619 151.738h14v25.2615h-14z" />
        <path d="m1640.88 151.23h9.93613v25.0895h-9.93613z" />
        <path d="m1656.49 151.23h9.93613v25.0895h-9.93613z" />
        <path d="m1691.98 151.23h76.6501v25.0895h-76.6501z" />
        <path d="m1806.96 151.23h76.6501v25.0895h-76.6501z" />
        <path d="m1890.7 151.23h76.6501v25.0895h-76.6501z" />
        <path d="m1974.45 151.23h9.93613v25.0895h-9.93613z" />
        <path d="m1990.06 151.23h9.93613v25.0895h-9.93613z" />
        <path d="m1781.41 151.23h18.4528v25.0895h-18.4528z" />
        <path d="m0 181.68h19.4693v25.016h-19.4693z" />
        <path d="m73 181h69v25.1875h-69z" />
        <path d="m156.866 181.68h96.322v25.016h-96.322z" />
        <path d="m262.88 181.68h56.3586v25.016h-56.3586z" />
        <path d="m387 181.812h30v25.1875h-30z" />
        <path d="m257.756 181.68h2.0494v25.016h-2.0494z" />
        <path d="m453.536 181.68h65.5809v25.016h-65.5809z" />
        <path d="m678 182.023h58v25h-58z" />
        <path d="m748.871 181.68h40.9881v25.016h-40.9881z" />
        <path d="m740 182.023h5v25h-5z" />
        <path d="m522.24 181.68h39.9634v25.016h-39.9634z" />
        <path d="m868.799 181.68h5.12351v25.016h-5.12351z" />
        <path d="m889.293 181.68h15.3705v25.016h-15.3705z" />
        <path d="m1134 181.473h33v25.1875h-33z" />
        <path d="m1093 181.473h30v25.1875h-30z" />
        <path d="m1045 181.473h44v25.1875h-44z" />
        <path d="m991.665 181.68h8.19762v25.016h-8.19762z" />
        <path d="m1010.16 181.68h21.5187v25.016h-21.5187z" />
        <path d="m1171.23 181.961h10.247v25.016h-10.247z" />
        <path d="m1191.83 181.68h7.17291v25.016h-7.17291z" />
        <path d="m0 211.023h18.9597v25.7087h-18.9597z" />
        <path d="m73 211.047h69v26h-69z" />
        <path d="m185 211.047h68v26h-68z" />
        <path d="m156.663 211.023h18.9597v25.7087h-18.9597z" />
        <path d="m263.323 211.023h54.8833v25.7087h-54.8833z" />
        <path d="m257.76 211.023h2.05v25.7087h-2.05z" />
        <path d="m454.009 211.023h58.8748v25.7087h-58.8748z" />
        <path d="m522.863 211.023h31.9321v25.7087h-31.9321z" />
        <path d="m677.702 211.023h57.8769v25.7087h-57.8769z" />
        <path d="m748.551 211.023h35.9236v25.7087h-35.9236z" />
        <path d="m739.57 211.023h4.98939v25.7087h-4.98939z" />
        <path d="m870.213 211.023h2.99364v25.7087h-2.99364z" />
        <path d="m890.171 211.023h7.98303v25.7087h-7.98303z" />
        <path d="m1045 211.023h34v26h-34z" />
        <path d="m1136.65 211.023h29.9363v25.7087h-29.9363z" />
        <path d="m1083.76 211.023h29.9363v25.7087h-29.9363z" />
        <path d="m1086.69 211.023h6.98515v25.7087h-6.98515z" />
        <path d="m1116.69 211.023h6.98515v25.7087h-6.98515z" />
        <path d="m1126.67 211.023h7.98303v25.7087h-7.98303z" />
        <path d="m993.963 211.023h5.98727v25.7087h-5.98727z" />
        <path d="m1010.92 211.023h20.9554v25.7087h-20.9554z" />
        <path d="m1170.57 211.312h9.97878v25.7087h-9.97878z" />
        <path d="m0 241.094h18.9597v25.7087h-18.9597z" />
        <path d="m73 241.117h69v26h-69z" />
        <path d="m157 241.094h18v26h-18z" />
        <path d="m263.323 241.094h54.8833v25.7087h-54.8833z" />
        <path d="m258.333 241.094h1.99576v25.7087h-1.99576z" />
        <path d="m454.009 241.094h49.8939v25.7087h-49.8939z" />
        <path d="m524.863 241.094h20.9554v25.7087h-20.9554z" />
        <path d="m677.702 241.094h57.8769v25.7087h-57.8769z" />
        <path d="m748.551 241.094h28.9385v25.7087h-28.9385z" />
        <path d="m739.57 241.094h4.98939v25.7087h-4.98939z" />
        <path d="m872.213 241.094h.997878v25.7087h-.997878z" />
        <path d="m892.171 241.094h3.99151v25.7087h-3.99151z" />
        <path d="m1045 241.047h34v26h-34z" />
        <path d="m1136.65 241.094h29.9363v25.7087h-29.9363z" />
        <path d="m1083.76 241.094h29.9363v25.7087h-29.9363z" />
        <path d="m1116.69 241.094h6.98515v25.7087h-6.98515z" />
        <path d="m1126.67 241.094h7.98303v25.7087h-7.98303z" />
        <path d="m994.957 241.094h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 241.094h20.9554v25.7087h-20.9554z" />
        <path d="m1170.57 241.383h9.97878v25.7087h-9.97878z" />
        <path d="m0 271.258h18.9597v25.7087h-18.9597z" />
        <path d="m72 271.234h70v26h-70z" />
        <path d="m157 271.211h18v26h-18z" />
        <path d="m263.323 271.258h15.9661v25.7087h-15.9661z" />
        <path d="m258.333 271.258h1.99576v25.7087h-1.99576z" />
        <path d="m282.283 271.258h35.9236v25.7087h-35.9236z" />
        <path d="m454.009 271.258h49.8939v25.7087h-49.8939z" />
        <path d="m524.863 271.258h12.9724v25.7087h-12.9724z" />
        <path d="m677.702 271.258h57.8769v25.7087h-57.8769z" />
        <path d="m748.551 271.258h20.9554v25.7087h-20.9554z" />
        <path d="m739.57 271.258h4.98939v25.7087h-4.98939z" />
        <path d="m1045 271.117h35v26h-35z" />
        <path d="m1136.65 271.258h29.9363v25.7087h-29.9363z" />
        <path d="m1083.76 271.258h29.9363v25.7087h-29.9363z" />
        <path d="m1116.69 271.258h6.98515v25.7087h-6.98515z" />
        <path d="m1126.67 271.258h7.98303v25.7087h-7.98303z" />
        <path d="m995.957 271.258h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 271.258h20.9554v25.7087h-20.9554z" />
        <path d="m892.171 271.258h3.99151v25.7087h-3.99151z" />
        <path d="m1170.57 271.547h9.97878v25.7087h-9.97878z" />
        <path d="m0 301.625h18.9597v25.7087h-18.9597z" />
        <path d="m73 301.578h69v26h-69z" />
        <path d="m157.682 301.625h8.98091v25.7087h-8.98091z" />
        <path d="m454.009 301.625h30.9342v25.7087h-30.9342z" />
        <path d="m486.939 301.625h16.9639v25.7087h-16.9639z" />
        <path d="m524.863 301.625h6.98515v25.7087h-6.98515z" />
        <path d="m678 301.484h59v26h-59z" />
        <path d="m749.551 301.625h11.9745v25.7087h-11.9745z" />
        <path d="m739.57 301.625h4.98939v25.7087h-4.98939z" />
        <path d="m1045 301.254h36v26h-36z" />
        <path d="m1136.65 301.625h29.9363v25.7087h-29.9363z" />
        <path d="m1083.76 301.625h29.9363v25.7087h-29.9363z" />
        <path d="m1116.69 301.625h6.98515v25.7087h-6.98515z" />
        <path d="m1126.67 301.625h7.98303v25.7087h-7.98303z" />
        <path d="m995.957 301.625h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 301.625h20.9554v25.7087h-20.9554z" />
        <path d="m892.171 301.625h3.99151v25.7087h-3.99151z" />
        <path d="m1170.57 301.914h9.97878v25.7087h-9.97878z" />
        <path d="m0 331.715h13.9703v25.7087h-13.9703z" />
        <path d="m157.682 331.715h8.98091v25.7087h-8.98091z" />
        <path d="m101 331.625h41v26h-41z" />
        <path d="m78.8809 331.715h16.9639v25.7087h-16.9639z" />
        <path d="m740.57 331.715h4.98939v25.7087h-4.98939z" />
        <path d="m1044.86 331.715h26.9427v25.7087h-26.9427z" />
        <path d="m995.957 331.715h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 331.715h20.9554v25.7087h-20.9554z" />
        <path d="m892.171 331.715h3.99151v25.7087h-3.99151z" />
        <path d="m1170.57 332.004h9.97878v25.7087h-9.97878z" />
        <path d="m0 361.891h8.98091v25.7087h-8.98091z" />
        <path d="m157.682 361.891h8.98091v25.7087h-8.98091z" />
        <path d="m101 361.711h41v26h-41z" />
        <path d="m82.8726 361.891h6.98515v25.7087h-6.98515z" />
        <path d="m1046.86 361.891h22.9512v25.7087h-22.9512z" />
        <path d="m995.957 361.891h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 361.891h20.9554v25.7087h-20.9554z" />
        <path d="m1170.57 362.18h9.97878v25.7087h-9.97878z" />
        <path d="m740.57 361.891h4.98939v25.7087h-4.98939z" />
        <path d="m0 392.25h4.98939v25.7087h-4.98939z" />
        <path d="m82.8726 392.25h6.98515v25.7087h-6.98515z" />
        <path d="m101 391.891h41v26h-41z" />
        <path d="m1048.85 392.25h16.9639v25.7087h-16.9639z" />
        <path d="m995.957 392.25h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 392.25h20.9554v25.7087h-20.9554z" />
        <path d="m1170.57 392.539h9.97878v25.7087h-9.97878z" />
        <path d="m740.57 392.25h4.98939v25.7087h-4.98939z" />
        <path d="m0 422.246h4.98939v25.7087h-4.98939z" />
        <path d="m82.8726 422.246h6.98515v25.7087h-6.98515z" />
        <path d="m1051.85 422.246h10.9767v25.7087h-10.9767z" />
        <path d="m995.957 422.246h2.99364v25.7087h-2.99364z" />
        <path d="m1010.92 422.246h20.9554v25.7087h-20.9554z" />
        <path d="m1170.57 422.535h9.97878v25.7087h-9.97878z" />
        <path d="m741.57 422.246h2.99364v25.7087h-2.99364z" />
        <path d="m0 452.242h4.98939v25.7087h-4.98939z" />
        <path d="m82.8726 452.242h2.99364v25.7087h-2.99364z" />
        <path d="m0 0h.997878v25.7087h-.997878z" transform="matrix(-1 0 0 1 998.95 452.242)" />
        <path d="m1010.92 452.242h20.9554v25.7087h-20.9554z" />
        <path d="m1170.57 452.531h9.97878v25.7087h-9.97878z" />
        <path d="m741.57 452.242h2.99364v25.7087h-2.99364z" />
        <path d="m0 482.242h4.98939v25.7087h-4.98939z" />
        <path d="m82.8726 482.242h.997878v25.7087h-.997878z" />
        <path d="m742.57 482.242h1.99576v25.7087h-1.99576z" />
        <path d="m1170.57 482.531h9.97878v25.7087h-9.97878z" />
      </g>
    </svg>
  );
};

export default FlameGraphSkeleton;
