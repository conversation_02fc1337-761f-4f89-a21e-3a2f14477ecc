// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {tryLoadAndStartRecorder} from '@alwaysmeticulous/recorder-loader';
import {createRoot} from 'react-dom/client';

import {isDevModeOrPreview} from '@parca/utilities';

import App from './App';
import reportWebVitals from './reportWebVitals';

async function startApp() {
  if (isDevModeOrPreview()) {
    // Start the Meticulous recorder before you initialise your app.
    // Note: all errors are caught and logged, so no need to surround with try/catch
    await tryLoadAndStartRecorder({
      projectId: 'lgBUqjZEtPYwCUmwxdv0IEiMsaPwDJWo9AJHvbzX',
      isProduction: false,
    } as any);
  }

  // Initialise app after the Meticulous recorder is ready: Meticulous needs to be initialised first,
  // so it can capture the initial network requests
  const container = document.getElementById('root');
  const root = createRoot(container!);
  root.render(<App />);

  // If you want to start measuring performance in your app, pass a function
  // to log results (for example: reportWebVitals(console.log))
  // or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
  reportWebVitals();
}

startApp();
