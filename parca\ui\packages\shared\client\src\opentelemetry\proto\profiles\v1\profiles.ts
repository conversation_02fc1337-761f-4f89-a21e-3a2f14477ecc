// @generated by protobuf-ts 2.9.4 with parameter generate_dependencies
// @generated from protobuf file "opentelemetry/proto/profiles/v1/profiles.proto" (package "opentelemetry.proto.profiles.v1", syntax proto3)
// tslint:disable
//
// This protofile is copied from https://github.com/open-telemetry/oteps/pull/239.
//
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { Profile } from "./alternatives/pprofextended/pprofextended";
import { KeyValue } from "../../common/v1/common";
import { InstrumentationScope } from "../../common/v1/common";
import { Resource } from "../../resource/v1/resource";
/**
 *                Relationships Diagram
 *
 * ┌──────────────────┐                LEGEND
 * │   ProfilesData   │
 * └──────────────────┘            ─────▶ embedded
 *   │
 *   │ 1-n                         ─────▷ referenced by index
 *   ▼
 * ┌──────────────────┐
 * │ ResourceProfiles │
 * └──────────────────┘
 *   │
 *   │ 1-n
 *   ▼
 * ┌──────────────────┐
 * │  ScopeProfiles   │
 * └──────────────────┘
 *   │
 *   │ 1-n
 *   ▼
 * ┌──────────────────┐
 * │ ProfileContainer │
 * └──────────────────┘
 *   │
 *   │ 1-1
 *   ▼
 * ┌──────────────────┐
 * │      Profile     │
 * └──────────────────┘
 *   │                                1-n
 *   │ 1-n         ┌───────────────────────────────────────┐
 *   ▼             │                                       ▽
 * ┌──────────────────┐   1-n   ┌──────────────┐      ┌──────────┐
 * │      Sample      │ ──────▷ │   KeyValue   │      │   Link   │
 * └──────────────────┘         └──────────────┘      └──────────┘
 *   │                    1-n       △      △
 *   │ 1-n        ┌─────────────────┘      │ 1-n
 *   ▽            │                        │
 * ┌──────────────────┐   n-1   ┌──────────────┐
 * │     Location     │ ──────▷ │   Mapping    │
 * └──────────────────┘         └──────────────┘
 *   │
 *   │ 1-n
 *   ▼
 * ┌──────────────────┐
 * │       Line       │
 * └──────────────────┘
 *   │
 *   │ 1-1
 *   ▽
 * ┌──────────────────┐
 * │     Function     │
 * └──────────────────┘
 *
 * ProfilesData represents the profiles data that can be stored in persistent storage,
 * OR can be embedded by other protocols that transfer OTLP profiles data but do not
 * implement the OTLP protocol.
 *
 * The main difference between this message and collector protocol is that
 * in this message there will not be any "control" or "metadata" specific to
 * OTLP protocol.
 *
 * When new fields are added into this message, the OTLP request MUST be updated
 * as well.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.ProfilesData
 */
export interface ProfilesData {
    /**
     * An array of ResourceProfiles.
     * For data coming from a single resource this array will typically contain
     * one element. Intermediary nodes that receive data from multiple origins
     * typically batch the data before forwarding further and in that case this
     * array will contain multiple elements.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.ResourceProfiles resource_profiles = 1;
     */
    resourceProfiles: ResourceProfiles[];
}
/**
 * A collection of ScopeProfiles from a Resource.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.ResourceProfiles
 */
export interface ResourceProfiles {
    /**
     * The resource for the profiles in this message.
     * If this field is not set then no resource info is known.
     *
     * @generated from protobuf field: opentelemetry.proto.resource.v1.Resource resource = 1;
     */
    resource?: Resource;
    /**
     * A list of ScopeProfiles that originate from a resource.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.ScopeProfiles scope_profiles = 2;
     */
    scopeProfiles: ScopeProfiles[];
    /**
     * This schema_url applies to the data in the "resource" field. It does not apply
     * to the data in the "scope_profiles" field which have their own schema_url field.
     *
     * @generated from protobuf field: string schema_url = 3;
     */
    schemaUrl: string;
}
/**
 * A collection of Profiles produced by an InstrumentationScope.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.ScopeProfiles
 */
export interface ScopeProfiles {
    /**
     * The instrumentation scope information for the profiles in this message.
     * Semantically when InstrumentationScope isn't set, it is equivalent with
     * an empty instrumentation scope name (unknown).
     *
     * @generated from protobuf field: opentelemetry.proto.common.v1.InstrumentationScope scope = 1;
     */
    scope?: InstrumentationScope;
    /**
     * A list of ProfileContainers that originate from an instrumentation scope.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.ProfileContainer profiles = 2;
     */
    profiles: ProfileContainer[];
    /**
     * This schema_url applies to all profiles and profile events in the "profiles" field.
     *
     * @generated from protobuf field: string schema_url = 3;
     */
    schemaUrl: string;
}
/**
 * A ProfileContainer represents a single profile. It wraps pprof profile with OpenTelemetry specific metadata.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.ProfileContainer
 */
export interface ProfileContainer {
    /**
     * A unique identifier for a profile. The ID is a 16-byte array. An ID with
     * all zeroes is considered invalid.
     *
     * This field is required.
     *
     * @generated from protobuf field: bytes profile_id = 1;
     */
    profileId: Uint8Array;
    /**
     * start_time_unix_nano is the start time of the profile.
     * Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.
     *
     * This field is semantically required and it is expected that end_time >= start_time.
     *
     * @generated from protobuf field: fixed64 start_time_unix_nano = 2;
     */
    startTimeUnixNano: bigint;
    /**
     * end_time_unix_nano is the end time of the profile.
     * Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.
     *
     * This field is semantically required and it is expected that end_time >= start_time.
     *
     * @generated from protobuf field: fixed64 end_time_unix_nano = 3;
     */
    endTimeUnixNano: bigint;
    /**
     * attributes is a collection of key/value pairs. Note, global attributes
     * like server name can be set using the resource API. Examples of attributes:
     *
     *     "/http/user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"
     *     "/http/server_latency": 300
     *     "abc.com/myattribute": true
     *     "abc.com/score": 10.239
     *
     * The OpenTelemetry API specification further restricts the allowed value types:
     * https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/common/README.md#attribute
     * Attribute keys MUST be unique (it is not allowed to have more than one
     * attribute with the same key).
     *
     * @generated from protobuf field: repeated opentelemetry.proto.common.v1.KeyValue attributes = 4;
     */
    attributes: KeyValue[];
    /**
     * dropped_attributes_count is the number of attributes that were discarded. Attributes
     * can be discarded because their keys are too long or because there are too many
     * attributes. If this value is 0, then no attributes were dropped.
     *
     * @generated from protobuf field: uint32 dropped_attributes_count = 5;
     */
    droppedAttributesCount: number;
    /**
     * Specifies format of the original payload. Common values are defined in semantic conventions. [required if original_payload is present]
     *
     * @generated from protobuf field: string original_payload_format = 6;
     */
    originalPayloadFormat: string;
    /**
     * Original payload can be stored in this field. This can be useful for users who want to get the original payload.
     * Formats such as JFR are highly extensible and can contain more information than what is defined in this spec.
     * Inclusion of original payload should be configurable by the user. Default behavior should be to not include the original payload.
     * If the original payload is in pprof format, it SHOULD not be included in this field.
     * The field is optional, however if it is present `profile` MUST be present and contain the same profiling information.
     *
     * @generated from protobuf field: bytes original_payload = 7;
     */
    originalPayload: Uint8Array;
    /**
     * This is a reference to a pprof profile. Required, even when original_payload is present.
     *
     * @generated from protobuf field: opentelemetry.proto.profiles.v1.alternatives.pprofextended.Profile profile = 8;
     */
    profile?: Profile;
}
// @generated message type with reflection information, may provide speed optimized methods
class ProfilesData$Type extends MessageType<ProfilesData> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.ProfilesData", [
            { no: 1, name: "resource_profiles", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => ResourceProfiles }
        ]);
    }
    create(value?: PartialMessage<ProfilesData>): ProfilesData {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.resourceProfiles = [];
        if (value !== undefined)
            reflectionMergePartial<ProfilesData>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfilesData): ProfilesData {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated opentelemetry.proto.profiles.v1.ResourceProfiles resource_profiles */ 1:
                    message.resourceProfiles.push(ResourceProfiles.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfilesData, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated opentelemetry.proto.profiles.v1.ResourceProfiles resource_profiles = 1; */
        for (let i = 0; i < message.resourceProfiles.length; i++)
            ResourceProfiles.internalBinaryWrite(message.resourceProfiles[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.ProfilesData
 */
export const ProfilesData = new ProfilesData$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ResourceProfiles$Type extends MessageType<ResourceProfiles> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.ResourceProfiles", [
            { no: 1, name: "resource", kind: "message", T: () => Resource },
            { no: 2, name: "scope_profiles", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => ScopeProfiles },
            { no: 3, name: "schema_url", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ResourceProfiles>): ResourceProfiles {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.scopeProfiles = [];
        message.schemaUrl = "";
        if (value !== undefined)
            reflectionMergePartial<ResourceProfiles>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ResourceProfiles): ResourceProfiles {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* opentelemetry.proto.resource.v1.Resource resource */ 1:
                    message.resource = Resource.internalBinaryRead(reader, reader.uint32(), options, message.resource);
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.ScopeProfiles scope_profiles */ 2:
                    message.scopeProfiles.push(ScopeProfiles.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* string schema_url */ 3:
                    message.schemaUrl = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ResourceProfiles, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* opentelemetry.proto.resource.v1.Resource resource = 1; */
        if (message.resource)
            Resource.internalBinaryWrite(message.resource, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.ScopeProfiles scope_profiles = 2; */
        for (let i = 0; i < message.scopeProfiles.length; i++)
            ScopeProfiles.internalBinaryWrite(message.scopeProfiles[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* string schema_url = 3; */
        if (message.schemaUrl !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.schemaUrl);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.ResourceProfiles
 */
export const ResourceProfiles = new ResourceProfiles$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ScopeProfiles$Type extends MessageType<ScopeProfiles> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.ScopeProfiles", [
            { no: 1, name: "scope", kind: "message", T: () => InstrumentationScope },
            { no: 2, name: "profiles", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => ProfileContainer },
            { no: 3, name: "schema_url", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ScopeProfiles>): ScopeProfiles {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.profiles = [];
        message.schemaUrl = "";
        if (value !== undefined)
            reflectionMergePartial<ScopeProfiles>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ScopeProfiles): ScopeProfiles {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* opentelemetry.proto.common.v1.InstrumentationScope scope */ 1:
                    message.scope = InstrumentationScope.internalBinaryRead(reader, reader.uint32(), options, message.scope);
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.ProfileContainer profiles */ 2:
                    message.profiles.push(ProfileContainer.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* string schema_url */ 3:
                    message.schemaUrl = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ScopeProfiles, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* opentelemetry.proto.common.v1.InstrumentationScope scope = 1; */
        if (message.scope)
            InstrumentationScope.internalBinaryWrite(message.scope, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.ProfileContainer profiles = 2; */
        for (let i = 0; i < message.profiles.length; i++)
            ProfileContainer.internalBinaryWrite(message.profiles[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* string schema_url = 3; */
        if (message.schemaUrl !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.schemaUrl);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.ScopeProfiles
 */
export const ScopeProfiles = new ScopeProfiles$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProfileContainer$Type extends MessageType<ProfileContainer> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.ProfileContainer", [
            { no: 1, name: "profile_id", kind: "scalar", T: 12 /*ScalarType.BYTES*/ },
            { no: 2, name: "start_time_unix_nano", kind: "scalar", T: 6 /*ScalarType.FIXED64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "end_time_unix_nano", kind: "scalar", T: 6 /*ScalarType.FIXED64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "attributes", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => KeyValue },
            { no: 5, name: "dropped_attributes_count", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 6, name: "original_payload_format", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "original_payload", kind: "scalar", T: 12 /*ScalarType.BYTES*/ },
            { no: 8, name: "profile", kind: "message", T: () => Profile }
        ]);
    }
    create(value?: PartialMessage<ProfileContainer>): ProfileContainer {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.profileId = new Uint8Array(0);
        message.startTimeUnixNano = 0n;
        message.endTimeUnixNano = 0n;
        message.attributes = [];
        message.droppedAttributesCount = 0;
        message.originalPayloadFormat = "";
        message.originalPayload = new Uint8Array(0);
        if (value !== undefined)
            reflectionMergePartial<ProfileContainer>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfileContainer): ProfileContainer {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bytes profile_id */ 1:
                    message.profileId = reader.bytes();
                    break;
                case /* fixed64 start_time_unix_nano */ 2:
                    message.startTimeUnixNano = reader.fixed64().toBigInt();
                    break;
                case /* fixed64 end_time_unix_nano */ 3:
                    message.endTimeUnixNano = reader.fixed64().toBigInt();
                    break;
                case /* repeated opentelemetry.proto.common.v1.KeyValue attributes */ 4:
                    message.attributes.push(KeyValue.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* uint32 dropped_attributes_count */ 5:
                    message.droppedAttributesCount = reader.uint32();
                    break;
                case /* string original_payload_format */ 6:
                    message.originalPayloadFormat = reader.string();
                    break;
                case /* bytes original_payload */ 7:
                    message.originalPayload = reader.bytes();
                    break;
                case /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.Profile profile */ 8:
                    message.profile = Profile.internalBinaryRead(reader, reader.uint32(), options, message.profile);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfileContainer, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bytes profile_id = 1; */
        if (message.profileId.length)
            writer.tag(1, WireType.LengthDelimited).bytes(message.profileId);
        /* fixed64 start_time_unix_nano = 2; */
        if (message.startTimeUnixNano !== 0n)
            writer.tag(2, WireType.Bit64).fixed64(message.startTimeUnixNano);
        /* fixed64 end_time_unix_nano = 3; */
        if (message.endTimeUnixNano !== 0n)
            writer.tag(3, WireType.Bit64).fixed64(message.endTimeUnixNano);
        /* repeated opentelemetry.proto.common.v1.KeyValue attributes = 4; */
        for (let i = 0; i < message.attributes.length; i++)
            KeyValue.internalBinaryWrite(message.attributes[i], writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* uint32 dropped_attributes_count = 5; */
        if (message.droppedAttributesCount !== 0)
            writer.tag(5, WireType.Varint).uint32(message.droppedAttributesCount);
        /* string original_payload_format = 6; */
        if (message.originalPayloadFormat !== "")
            writer.tag(6, WireType.LengthDelimited).string(message.originalPayloadFormat);
        /* bytes original_payload = 7; */
        if (message.originalPayload.length)
            writer.tag(7, WireType.LengthDelimited).bytes(message.originalPayload);
        /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.Profile profile = 8; */
        if (message.profile)
            Profile.internalBinaryWrite(message.profile, writer.tag(8, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.ProfileContainer
 */
export const ProfileContainer = new ProfileContainer$Type();
