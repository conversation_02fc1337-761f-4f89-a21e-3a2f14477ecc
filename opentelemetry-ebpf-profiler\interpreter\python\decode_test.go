// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

//nolint:lll
package python

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.opentelemetry.io/ebpf-profiler/libpf"
)

func TestAnalyzeArm64Stubs(t *testing.T) {
	val := decodeStubArgumentARM64(
		[]byte{
			0x40, 0x0a, 0x00, 0x90, 0x01, 0xd4, 0x43, 0xf9,
			0x22, 0x60, 0x17, 0x91, 0x40, 0x00, 0x40, 0xf9,
			0xa2, 0xff, 0xff, 0x17},
		0)
	assert.Equal(t, libpf.SymbolValue(1496), val, "PyEval_ReleaseLock stub test")

	val = decodeStubArgumentARM64(
		[]byte{
			0x80, 0x12, 0x00, 0xb0, 0x02, 0xd4, 0x43, 0xf9,
			0x41, 0xf4, 0x42, 0xf9, 0x61, 0x00, 0x00, 0xb4,
			0x40, 0xc0, 0x17, 0x91, 0xad, 0xe4, 0xfe, 0x17},
		0)
	assert.Equal(t, libpf.SymbolValue(1520), val, "PyGILState_GetThisThreadState test")

	// Python 3.10.12 on ARM64 Nix
	val = decodeStubArgumentARM64(
		[]byte{
			0x40, 0x1a, 0x00, 0xd0, // adrp	x0, 0xffffa0eff000 <<EMAIL>>
			0x00, 0xa0, 0x46, 0xf9, // ldr	x0, [x0, #3392]
			0x01, 0x28, 0x41, 0xf9, // ldr	x1, [x0, #592]
			0x61, 0x00, 0x00, 0xb4, // cbz	x1, 0xffffa0bb53c8 <PyGILState_GetThisThreadState+24>
			0x00, 0x5c, 0x42, 0xb9, // ldr	w0, [x0, #604]
			0x93, 0x20, 0xff, 0x17, // b	0xffffa0b7d610 <pthread_getspecific@plt>
			0x00, 0x00, 0x80, 0xd2, // mov	x0, #0x0
			0xc0, 0x03, 0x5f, 0xd6, // ret
		},
		0)
	assert.Equal(t, libpf.SymbolValue(604), val, "PyGILState_GetThisThreadState test")
}

func BenchmarkDecodeAmd64(b *testing.B) {
	for i := 0; i < b.N; i++ {
		code := []byte{
			0xf3, 0x0f, 0x1e, 0xfa, // 1bbba0: endbr64
			0x48, 0x83, 0x3d, 0x74, 0x90, 0x1e, 0x00, // 1bbba4: cmp    QWORD PTR [rip+0x1e9074],0x0        # 3a4c20 <_PyRuntime+0x240>
			0x00,       // 1bbbab:
			0x74, 0x0b, // 1bbbac: je     1bbbb9 <PyGILState_GetThisThreadState+0x19>
			0x8b, 0x3d, 0x78, 0x90, 0x1e, 0x00, // 1bbbae: mov    edi,DWORD PTR [rip+0x1e9078]        # 3a4c2c <_PyRuntime+0x24c>
			0xe9, 0xe7, 0xea, 0xe9, 0xff, // 1bbbb4: jmp    5a6a0 <pthread_getspecific@plt>
		}
		rip := uint64(0x1bbba0)
		val, _ := decodeStubArgumentAMD64(
			code,
			rip,
			0,
		)
		if val != 0x3a4c2c {
			b.Fail()
		}
	}
}

func TestAmd64DecodeStub(t *testing.T) {
	testdata := []struct {
		name          string
		code          []byte
		rip           uint64
		expected      uint64
		expectedError string
	}{
		{
			name: "3.10.16 gcc12 enable-optimizations disable-shared",
			code: []byte{
				0xf3, 0x0f, 0x1e, 0xfa, // 1bbba0: endbr64
				0x48, 0x83, 0x3d, 0x74, 0x90, 0x1e, 0x00, // 1bbba4: cmp    QWORD PTR [rip+0x1e9074],0x0        # 3a4c20 <_PyRuntime+0x240>
				0x00,       // 1bbbab:
				0x74, 0x0b, // 1bbbac: je     1bbbb9 <PyGILState_GetThisThreadState+0x19>
				0x8b, 0x3d, 0x78, 0x90, 0x1e, 0x00, // 1bbbae: mov    edi,DWORD PTR [rip+0x1e9078]        # 3a4c2c <_PyRuntime+0x24c>
				0xe9, 0xe7, 0xea, 0xe9, 0xff, // 1bbbb4: jmp    5a6a0 <pthread_getspecific@plt>
			},
			rip:      0x1bbba0,
			expected: 0x3a4c2c,
		},
		{
			name: "3.10.16 gcc12 disable-optimizations disable-shared",
			code: []byte{
				0xf3, 0x0f, 0x1e, 0xfa, // 172e50: endbr64
				0x48, 0x83, 0x3d, 0x04, 0xef, 0x24, 0x00, // 172e54: cmp    QWORD PTR [rip+0x24ef04],0x0        # 3c1d60 <_PyRuntime+0x240>
				0x00,       // 172e5b:
				0x74, 0x12, // 172e5c: je     172e70 <PyGILState_GetThisThreadState+0x20>
				0x48, 0x8d, 0x3d, 0x03, 0xef, 0x24, 0x00, // 172e5e: lea    rdi,[rip+0x24ef03]        # 3c1d68 <_PyRuntime+0x248>
				0xe9, 0x86, 0x1e, 0x01, 0x00, // 172e65: jmp    184cf0 <PyThread_tss_get>
			},
			rip:      0x172e50,
			expected: 0x3c1d68,
		},
		{
			name: "3.10.16 clang16 disable-optimizations enabled-shared",
			code: []byte{
				0x48, 0x8b, 0x05, 0x99, 0x70, 0x16, 0x00, // 1adc90: mov    rax,QWORD PTR [rip+0x167099]        # <AUTHOR> <EMAIL>
				0x48, 0x83, 0xb8, 0x40, 0x02, 0x00, 0x00, // 1adc97: cmp    QWORD PTR [rax+0x240],0x0
				0x00,       // 1adc9e:
				0x74, 0x11, // 1adc9f: je     1adcb2 <PyGILState_GetThisThreadState+0x22>
				0xbf, 0x48, 0x02, 0x00, 0x00, // 1adca1: mov    edi,0x248
				0x48, 0x03, 0x3d, 0x83, 0x70, 0x16, 0x00, // 1adca6: add    rdi,QWORD PTR [rip+0x167083]        # <AUTHOR> <EMAIL>
				0xe9, 0x2e, 0x41, 0xeb, 0xff, // 1adcad: jmp    61de0 <PyThread_tss_get@plt>
			},
			rip:      0x1adc90,
			expected: 0x248,
		},
		{
			name: "3.12.8 gcc12 disable-optimizations enabled-shared",
			code: []byte{
				0xf3, 0x0f, 0x1e, 0xfa, // 2e25d0: endbr64
				0x48, 0x8b, 0x05, 0x25, 0x27, 0x27, 0x00, // 2e25d4: mov    rax,QWORD PTR [rip+0x272725]        # <AUTHOR> <EMAIL>
				0x53,                                     // 2e25db: push   rbx
				0x48, 0x8d, 0x98, 0x08, 0x06, 0x00, 0x00, // 2e25dc: lea    rbx,[rax+0x608]
				0x48, 0x89, 0xdf, // 2e25e3: mov    rdi,rbx
				0xe8, 0x95, 0x78, 0xe2, 0xff, // 2e25e6: call   109e80 <PyThread_tss_is_created@plt>
			},
			rip:      0x2e25d0,
			expected: 0x608,
		},
		{
			name: "3.10.16 clang18 enable-optimizations enabled-shared",
			code: []byte{
				0x48, 0x8b, 0x05, 0xd9, 0x80, 0x31, 0x00, // cac50: mov    rax,QWORD PTR [rip+0x3180d9]        # <AUTHOR> <EMAIL>
				0x48, 0x83, 0xb8, 0x40, 0x02, 0x00, 0x00, // cac57: cmp    QWORD PTR [rax+0x240],0x0
				0x00,       // cac5e:
				0x74, 0x0b, // cac5f: je     cac6c <PyGILState_GetThisThreadState+0x1c>
				0x8b, 0xb8, 0x4c, 0x02, 0x00, 0x00, // cac61: mov    edi,DWORD PTR [rax+0x24c]
				0xe9, 0x24, 0x55, 0xf9, 0xff, // cac67: jmp    60190 <pthread_getspecific@plt>
			},
			rip:      0xcac50,
			expected: 0x24c,
		},
		{
			name: "3.10.16 clang18 enable-optimizations disable-shared",
			code: []byte{
				0x48, 0x83, 0x3d, 0x98, 0xc5, 0x36, 0x00, // 92000: cmp    QWORD PTR [rip+0x36c598],0x0        # 3fe5a0 <_PyRuntime+0x240>
				0x00,       // 92007:
				0x74, 0x0b, // 92008: je     92015 <PyGILState_GetThisThreadState+0x15>
				0x8b, 0x3d, 0x9c, 0xc5, 0x36, 0x00, // 9200a: mov    edi,DWORD PTR [rip+0x36c59c]        # 3fe5ac <_PyRuntime+0x24c>
				0xe9, 0x4b, 0x70, 0xfc, 0xff, // 92010: jmp    59060 <pthread_getspecific@plt>
			},
			rip:      0x92000,
			expected: 0x3fe5ac,
		},
		{
			name: "3.10.16 clang16 disable-optimizations disable-shared",
			code: []byte{
				0x48, 0x8d, 0x05, 0x69, 0x19, 0x21, 0x00, // 129bc0: lea    rax,[rip+0x211969]        # 33b530 <_PyRuntime>
				0x48, 0x83, 0xb8, 0x40, 0x02, 0x00, 0x00, // 129bc7: cmp    QWORD PTR [rax+0x240],0x0
				0x00,       // 129bce:
				0x74, 0x11, // 129bcf: je     129be2 <PyGILState_GetThisThreadState+0x22>
				0xbf, 0x48, 0x02, 0x00, 0x00, // 129bd1: mov    edi,0x248
				0x48, 0x03, 0x3d, 0x53, 0x03, 0x1e, 0x00, // 129bd6: add    rdi,QWORD PTR [rip+0x1e0353]        # 309f30 <_DYNAMIC+0x328>
				0xe9, 0x8e, 0xec, 0x00, 0x00, // 129bdd: jmp    138870 <PyThread_tss_get>
			},
			rip:      0x129bc0,
			expected: 0x248,
		},
		{
			name: "3.12.8 clang16 disable-optimizations disable-shared",
			code: []byte{
				0x53,                         // 2a20d0: push   rbx
				0xbb, 0x08, 0x06, 0x00, 0x00, // 2a20d1: mov    ebx,0x608
				0x48, 0x03, 0x1d, 0x0b, 0x1e, 0x25, 0x00, // 2a20d6: add    rbx,QWORD PTR [rip+0x251e0b]        # 4f3ee8 <_DYNAMIC+0x368>
				0x48, 0x89, 0xdf, // 2a20dd: mov    rdi,rbx
				0xe8, 0x7b, 0x41, 0x01, 0x00, // 2a20e0: call   2b6260 <PyThread_tss_is_created>
			},
			rip:      0x2a20d0,
			expected: 0x608,
		},
		{
			name: "3.10.16 clang16 disable-optimizations enabled-shared",
			code: []byte{
				0xf3, 0x0f, 0x1e, 0xfa, // 2079c0: endbr64
				0x48, 0x8b, 0x05, 0x65, 0x03, 0x18, 0x00, // 2079c4: mov    rax,QWORD PTR [rip+0x180365]        # <AUTHOR> <EMAIL>
				0x48, 0x83, 0xb8, 0x40, 0x02, 0x00, 0x00, // 2079cb: cmp    QWORD PTR [rax+0x240],0x0
				0x00,       // 2079d2:
				0x74, 0x13, // 2079d3: je     2079e8 <PyGILState_GetThisThreadState+0x28>
				0x48, 0x8d, 0xb8, 0x48, 0x02, 0x00, 0x00, // 2079d5: lea    rdi,[rax+0x248]
				0xe9, 0x8f, 0x1f, 0xe6, 0xff, // 2079dc: jmp    69970 <PyThread_tss_get@plt>
			},
			rip:      0x2079c0,
			expected: 0x248,
		},
		{
			name: "3.12.8 gcc12 disable-optimizations disable-shared",
			code: []byte{
				0xf3, 0x0f, 0x1e, 0xfa, // 2eb960: endbr64
				0x53,                                     // 2eb964: push   rbx
				0x48, 0x8d, 0x1d, 0xbc, 0x21, 0x37, 0x00, // 2eb965: lea    rbx,[rip+0x3721bc]        # 65db28 <_PyRuntime+0x608>
				0x48, 0x89, 0xdf, // 2eb96c: mov    rdi,rbx
				0xe8, 0x0c, 0x7f, 0x01, 0x00, // 2eb96f: call   303880 <PyThread_tss_is_created>
			},
			rip:      0x2eb960,
			expected: 0x65db28,
		},
		{
			name: "3.10.16 gcc12 enable-optimizations enabled-shared",
			code: []byte{
				0xf3, 0x0f, 0x1e, 0xfa, // 1c03c0: endbr64
				0x48, 0x8b, 0x05, 0x5d, 0x69, 0x1b, 0x00, // 1c03c4: mov    rax,QWORD PTR [rip+0x1b695d]        # <AUTHOR> <EMAIL>
				0x48, 0x83, 0xb8, 0x40, 0x02, 0x00, 0x00, // 1c03cb: cmp    QWORD PTR [rax+0x240],0x0
				0x00,       // 1c03d2:
				0x74, 0x0b, // 1c03d3: je     1c03e0 <PyGILState_GetThisThreadState+0x20>
				0x8b, 0xb8, 0x4c, 0x02, 0x00, 0x00, // 1c03d5: mov    edi,DWORD PTR [rax+0x24c]
				0xe9, 0x10, 0xb4, 0xe9, 0xff, // 1c03db: jmp    5b7f0 <pthread_getspecific@plt>
			},
			rip:      0x1c03c0,
			expected: 0x24c,
		},
		{
			name:          "empty code",
			code:          nil,
			expectedError: "no call/jump instructions found",
		},
		{
			name: "no call/jump instructions found",
			code: []byte{
				0x48, 0xC7, 0xC7, 0xEF, 0xEF, 0xEF, 0x00, // mov rdi, 0xefefef
			},
			expectedError: "no call/jump instructions found",
		},
		{
			name: "bad instruction",
			code: []byte{
				0x48, 0xC7, 0xC7, 0xEF, 0xEF, 0xEF, 0x00, // mov rdi, 0xefefef
				0xea, // :shrug:
			},
			expectedError: "failed to decode instruction at 0x7",
		},
		{
			name: "synthetic mov scale index",
			code: []byte{
				0x48, 0xC7, 0xC0, 0xCA, 0xCA, 0x00, 0x00, // 	mov 	rax, 0xcaca
				0xBB, 0x00, 0x00, 0x00, 0x5E, // 	mov 	ebx, 0x5e000000
				0x67, 0x48, 0x8B, 0x7C, 0x43, 0x05, // 	mov 	rdi, qword ptr [ebx + eax*2 + 5]
				0xEB, 0x00, // 	jmp 	0x14
			},
			expected: 0xCACA*2 + 0x5E000000 + 5,
		},
		{
			name: "synthetic lea scale index",
			code: []byte{
				0x48, 0xC7, 0xC0, 0xFE, 0xCA, 0x00, 0x00, // 	mov 	rax, 0xcafe
				0xBB, 0x00, 0x00, 0x00, 0x6E, // 	mov 	ebx, 0x6e000000
				0x67, 0x48, 0x8D, 0x7C, 0x43, 0x07, // 	lea 	rdi, [ebx + eax*2 + 7]
				0xE8, 0xFB, 0xFF, 0xFF, 0xFF, //	call 	0x12
			},
			expected: 0xCAFE*2 + 0x6E000000 + 7,
		},
		{
			name: "synthetic lea edi, ... scale index",
			code: []byte{
				0xB8, 0xEF, 0x00, 0x00, 0x00, // mov 	eax, 0xef
				0xBB, 0x2A, 0x00, 0x00, 0x00, // mov 	ebx, 0x2a
				0x67, 0x8D, 0x7C, 0x43, 0x07, // lea 	edi, [ebx + eax*2 + 7]
				0xEB, 0xEF, // jmp 	0
			},
			expected: 0xEF*2 + 0x2a + 7,
		},
	}

	for _, td := range testdata {
		t.Run(td.name, func(t *testing.T) {
			val, err := decodeStubArgumentAMD64(
				td.code,
				td.rip,
				0, // NULL pointer as mem
			)
			if td.expectedError != "" {
				require.Error(t, err)
				require.Contains(t, err.Error(), td.expectedError)
			} else {
				require.NoError(t, err)
			}
			assert.Equal(t, td.expected, uint64(val))
		})
	}
}

func FuzzDecodeAmd(f *testing.F) {
	f.Fuzz(func(_ *testing.T, code []byte, rip uint64) {
		_, err := decodeStubArgumentAMD64(code, rip, 0)
		if err != nil {
			return
		}
	})
}
