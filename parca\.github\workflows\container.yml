name: Container

on:
  push:
    branches:
    - main
    - release-*
  pull_request:
    branches:
    - main
    - release-*
  merge_group:
    branches:
    - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=go depName=github.com/goreleaser/goreleaser
  GORELEASER_VERSION: v1.26.2
  # renovate: datasource=npm depName=pnpm versioning=npm
  PNPM_VERSION: '10.13.1'

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              "**.go",
              ".dockerignore",
              ".github/workflows/container.yml",
              ".go-version",
              ".node-version",
              "Dockerfile*",
              "go.mod",
              "go.sum",
              "ui/**"
            ]
          skip_after_successful_duplicate: false

  build-binary:
    name: Build binary using goreleaser
    needs: skip-check
    runs-on: ubuntu-latest
    timeout-minutes: 45
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    steps:
      - name: Remove unnecessary files
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /opt/ghc
          sudo rm -rf "/usr/local/share/boost"
          sudo rm -rf "$AGENT_TOOLSDIRECTORY"
      - name: Check out code into the Go module directory
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version

      - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version-file: .node-version
          cache: 'pnpm'
          cache-dependency-path: ui/pnpm-lock.yaml

      - name: Set Tag
        run: |
          echo "goreleaser_current_tag=`git describe --match 'v*' --tags`" >> $GITHUB_ENV

      - name: Get branch name
        shell: bash
        run: echo "GITHUB_BRANCH_NAME=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - name: Build binaries
        uses: goreleaser/goreleaser-action@5fdedb94abba051217030cc86d4523cf3f02243d # v4.6.0
        with:
          distribution: goreleaser
          version: ${{ env.GORELEASER_VERSION }}
          args: build --clean --skip=validate --snapshot --debug --timeout=60m
        env:
          GORELEASER_CURRENT_TAG: "${{ env.goreleaser_current_tag }}"

      - name: Archive generated artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: parca-dist-container
          if-no-files-found: error
          path: |
            dist
            !dist/*.txt

  build-and-push-container:
    name: Container build and push (when merged)
    needs: build-binary
    runs-on: ubuntu-latest
    timeout-minutes: 30
    container:
      # https://github.com/containers/podman/tree/main/contrib/podmanimage
      image: quay.io/containers/podman:v4.9.4
      options: >-
        --device /dev/fuse:rw
        --privileged
        --security-opt label=disable
        --security-opt seccomp=unconfined
    permissions:
      id-token: write
      packages: write
      contents: read
    steps:
      - name: Install dependencies
        run: dnf install --assumeyes --repo fedora git make jq

      - name: Check out code into the Go module directory
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      # The checkout action is supposed to take care of it, but it is not enough :/
      - name: Add repository directory to the git global config as a safe directory
        run: git config --global --add safe.directory /__w/parca/parca

      - uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: parca-dist-container
          path: dist

      - name: Get branch name
        shell: bash
        run: echo "GITHUB_BRANCH_NAME=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV

      - name: Build container
        run: make container

      - name: Check images created
        run: podman images | grep 'ghcr.io/parca-dev/parca'

      - name: Install cosign
        if: ${{ github.event_name != 'pull_request' }}
        uses: sigstore/cosign-installer@398d4b0eeef1380460a10c8013a76f728fb906ac # v3.9.1

      - name: Install crane
        if: ${{ github.event_name != 'pull_request' }}
        uses: imjasonh/setup-crane@00c9e93efa4e1138c9a7a5c594acd6c75a2fbf0c # v0.3

      - name: Login to registry
        if: ${{ github.event_name != 'pull_request' }}
        run: |
          echo "${{ secrets.GITHUB_TOKEN }}" | podman login -u parca-dev --password-stdin ghcr.io
          echo "${{ secrets.QUAY_PASSWORD }}" | cosign login -u "${{ secrets.QUAY_USERNAME }}" --password-stdin quay.io

      - name: Push and sign container
        if: ${{ github.event_name != 'pull_request' }}
        run: |
          make push-container
          make sign-container
          make push-signed-quay-container
