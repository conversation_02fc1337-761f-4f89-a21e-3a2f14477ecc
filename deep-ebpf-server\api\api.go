package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/mexyusef/deep-ebpf-server/collector"
	"github.com/mexyusef/deep-ebpf-server/processor"
	"github.com/mexyusef/deep-ebpf-server/storage"
	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"k8s.io/klog"
)

// Config defines API configuration
type Config struct {
	EnableCORS  bool
	RateLimit   int
	AuthEnabled bool
}

// API handles HTTP API requests
type API struct {
	config    Config
	storage   storage.Storage
	collector *collector.Collector
	processor *processor.Processor
	upgrader  websocket.Upgrader
}

// New creates a new API instance
func New(config Config, store storage.Storage, coll *collector.Collector, proc *processor.Processor) *API {
	return &API{
		config:    config,
		storage:   store,
		collector: coll,
		processor: proc,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return config.EnableCORS // Allow all origins if CORS is enabled
			},
		},
	}
}

// Auth is a middleware for authentication (placeholder)
func (a *API) Auth(handler http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if a.config.AuthEnabled {
			// TODO: Implement authentication
			// For now, just pass through
		}
		
		// Add CORS headers if enabled
		if a.config.EnableCORS {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		}
		
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		handler(w, r)
	}
}

// Login handles user login (placeholder)
func (a *API) Login(w http.ResponseWriter, r *http.Request) {
	// TODO: Implement authentication
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status": "success",
		"token":  "placeholder-token",
	})
}

// Logout handles user logout (placeholder)
func (a *API) Logout(w http.ResponseWriter, r *http.Request) {
	// TODO: Implement authentication
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status": "success",
	})
}

// GetTraces handles GET /api/traces
func (a *API) GetTraces(w http.ResponseWriter, r *http.Request) {
	opts := a.parseQueryOptions(r)
	
	traces, err := a.storage.QueryFunctionCalls(opts)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"traces": traces,
		"count":  len(traces),
	})
}

// GetTrace handles GET /api/traces/{trace_id}
func (a *API) GetTrace(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	traceID := vars["trace_id"]
	
	trace, err := a.storage.GetFunctionCall(traceID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(trace)
}

// GetFunctions handles GET /api/functions
func (a *API) GetFunctions(w http.ResponseWriter, r *http.Request) {
	functions, err := a.storage.GetFunctionList()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"functions": functions,
		"count":     len(functions),
	})
}

// GetProcesses handles GET /api/processes
func (a *API) GetProcesses(w http.ResponseWriter, r *http.Request) {
	processes, err := a.storage.GetProcessList()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"processes": processes,
		"count":     len(processes),
	})
}

// GetLatencyMetrics handles GET /api/metrics/latency
func (a *API) GetLatencyMetrics(w http.ResponseWriter, r *http.Request) {
	functionName := r.URL.Query().Get("function")
	start, end := a.parseTimeRange(r)
	
	if functionName == "" {
		http.Error(w, "function parameter is required", http.StatusBadRequest)
		return
	}
	
	metrics, err := a.storage.GetLatencyMetrics(functionName, start, end)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// GetThroughputMetrics handles GET /api/metrics/throughput
func (a *API) GetThroughputMetrics(w http.ResponseWriter, r *http.Request) {
	functionName := r.URL.Query().Get("function")
	start, end := a.parseTimeRange(r)
	
	if functionName == "" {
		http.Error(w, "function parameter is required", http.StatusBadRequest)
		return
	}
	
	metrics, err := a.storage.GetThroughputMetrics(functionName, start, end)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// GetErrorMetrics handles GET /api/metrics/errors
func (a *API) GetErrorMetrics(w http.ResponseWriter, r *http.Request) {
	functionName := r.URL.Query().Get("function")
	start, end := a.parseTimeRange(r)
	
	if functionName == "" {
		http.Error(w, "function parameter is required", http.StatusBadRequest)
		return
	}
	
	metrics, err := a.storage.GetErrorMetrics(functionName, start, end)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// GetStatus handles GET /api/status
func (a *API) GetStatus(w http.ResponseWriter, r *http.Request) {
	status := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "unknown", // TODO: Get from build info
		"uptime":    time.Since(time.Now()).String(), // TODO: Track actual uptime
		"storage":   a.storage.GetStats(),
		"collector": a.collector.GetStats(),
		"processor": a.processor.GetStats(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// GetAgents handles GET /api/agents
func (a *API) GetAgents(w http.ResponseWriter, r *http.Request) {
	agents := a.collector.GetAgents()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"agents": agents,
		"count":  len(agents),
	})
}

// GetStats handles GET /api/stats
func (a *API) GetStats(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"storage":   a.storage.GetStats(),
		"collector": a.collector.GetStats(),
		"processor": a.processor.GetStats(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// StreamTraces handles WebSocket streaming of traces
func (a *API) StreamTraces(w http.ResponseWriter, r *http.Request) {
	conn, err := a.upgrader.Upgrade(w, r, nil)
	if err != nil {
		klog.Errorf("Failed to upgrade WebSocket connection: %v", err)
		return
	}
	defer conn.Close()
	
	// TODO: Implement real-time trace streaming
	// This would involve subscribing to trace events from the processor
	
	for {
		// Placeholder: send periodic updates
		time.Sleep(5 * time.Second)
		
		traces, err := a.storage.QueryFunctionCalls(storage.QueryOptions{
			Limit:     10,
			OrderBy:   "start_time",
			OrderDesc: true,
		})
		if err != nil {
			klog.Errorf("Failed to query traces for streaming: %v", err)
			break
		}
		
		if err := conn.WriteJSON(map[string]interface{}{
			"type":   "traces",
			"traces": traces,
		}); err != nil {
			klog.Errorf("Failed to write WebSocket message: %v", err)
			break
		}
	}
}

// StreamMetrics handles WebSocket streaming of metrics
func (a *API) StreamMetrics(w http.ResponseWriter, r *http.Request) {
	conn, err := a.upgrader.Upgrade(w, r, nil)
	if err != nil {
		klog.Errorf("Failed to upgrade WebSocket connection: %v", err)
		return
	}
	defer conn.Close()
	
	// TODO: Implement real-time metrics streaming
	
	for {
		time.Sleep(10 * time.Second)
		
		stats := a.processor.GetStats()
		
		if err := conn.WriteJSON(map[string]interface{}{
			"type":    "metrics",
			"metrics": stats,
		}); err != nil {
			klog.Errorf("Failed to write WebSocket message: %v", err)
			break
		}
	}
}

// GetConfig handles GET /api/config
func (a *API) GetConfig(w http.ResponseWriter, r *http.Request) {
	config := map[string]interface{}{
		"cors_enabled": a.config.EnableCORS,
		"rate_limit":   a.config.RateLimit,
		"auth_enabled": a.config.AuthEnabled,
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(config)
}

// UpdateConfig handles POST /api/config
func (a *API) UpdateConfig(w http.ResponseWriter, r *http.Request) {
	// TODO: Implement configuration updates
	http.Error(w, "Configuration updates not implemented", http.StatusNotImplemented)
}

// PrometheusMetrics handles GET /metrics
func (a *API) PrometheusMetrics(w http.ResponseWriter, r *http.Request) {
	// TODO: Implement Prometheus metrics export
	w.Header().Set("Content-Type", "text/plain")
	w.Write([]byte("# Prometheus metrics not implemented yet\n"))
}

// parseQueryOptions parses query parameters into QueryOptions
func (a *API) parseQueryOptions(r *http.Request) storage.QueryOptions {
	opts := storage.QueryOptions{}
	
	if functionName := r.URL.Query().Get("function"); functionName != "" {
		opts.FunctionName = functionName
	}
	
	if processName := r.URL.Query().Get("process"); processName != "" {
		opts.ProcessName = processName
	}
	
	if pidStr := r.URL.Query().Get("pid"); pidStr != "" {
		if pid, err := strconv.ParseUint(pidStr, 10, 32); err == nil {
			pid32 := uint32(pid)
			opts.PID = &pid32
		}
	}
	
	if agentID := r.URL.Query().Get("agent"); agentID != "" {
		opts.AgentID = agentID
	}
	
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			opts.Limit = limit
		}
	} else {
		opts.Limit = 100 // Default limit
	}
	
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			opts.Offset = offset
		}
	}
	
	if orderBy := r.URL.Query().Get("order_by"); orderBy != "" {
		opts.OrderBy = orderBy
	}
	
	if orderDesc := r.URL.Query().Get("order_desc"); orderDesc == "true" {
		opts.OrderDesc = true
	}
	
	start, end := a.parseTimeRange(r)
	opts.StartTime = &start
	opts.EndTime = &end
	
	return opts
}

// parseTimeRange parses start and end time from query parameters
func (a *API) parseTimeRange(r *http.Request) (time.Time, time.Time) {
	now := time.Now()
	start := now.Add(-1 * time.Hour) // Default: last hour
	end := now
	
	if startStr := r.URL.Query().Get("start"); startStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startStr); err == nil {
			start = startTime
		}
	}
	
	if endStr := r.URL.Query().Get("end"); endStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endStr); err == nil {
			end = endTime
		}
	}
	
	// Handle relative time ranges
	if timeRange := r.URL.Query().Get("timerange"); timeRange != "" {
		if duration, err := time.ParseDuration(timeRange); err == nil {
			start = now.Add(-duration)
			end = now
		}
	}
	
	return start, end
}
