// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {PillVariant} from '@parca/components';

const HealthStatus = {
  Unspecified: 'Unspecified',
  Good: 'Good',
  Bad: 'Bad',
} as const;

type HealthStatusType = (typeof HealthStatus)[keyof typeof HealthStatus];

export const getHealthStatus = (numericValue: number) => {
  const label = Object.values(HealthStatus)[numericValue];

  const colorVariants: Record<HealthStatusType, PillVariant> = {
    Unspecified: 'neutral',
    Good: 'success',
    Bad: 'danger',
  };
  return {label, colorVariant: colorVariants[label]};
};
