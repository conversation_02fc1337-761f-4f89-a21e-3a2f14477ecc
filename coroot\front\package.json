{"name": "@coroot/coroot", "version": "0.1.0", "scripts": {"build-dev": "vue-cli-service build --dest=../static --watch", "build-prod": "vue-cli-service build --dest=../static src/main.js", "lint": "vue-cli-service lint", "fmt": "prettier src --write", "fmt-check": "prettier src --check"}, "dependencies": {"@codemirror/autocomplete": "^6.3.0", "@codemirror/language": "^6.2.1", "@codemirror/lint": "^6.0.0", "@codemirror/state": "^6.1.2", "@codemirror/view": "^6.3.0", "@juggle/resize-observer": "^3.4.0", "@lezer/common": "^1.0.1", "@prometheus-io/codemirror-promql": "^0.55.1", "axios": "^1.6.0", "color-convert": "^2.0.1", "core-js": "^3.8.3", "gridstack": "^12.1.2", "highlight.js": "^11.7.0", "markdown-it": "^14.1.0", "pluralize": "^8.0.0", "sql-formatter": "^12.2.0", "uplot": "^1.6.24", "uuid": "^9.0.0", "vue": "^2.6.14", "vue-router": "^3.6.2", "vuetify": "^2.7.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@types/markdown-it": "^14.1.2", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^8.0.3", "prettier": "^3.2.4", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.5.4", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/valid-v-for": "off", "vue/require-v-for-key": "off", "vue/multi-word-component-names": "off", "vue/valid-v-slot": "off"}}, "prettier": {"singleQuote": true, "printWidth": 150, "trailingComma": "all", "tabWidth": 4, "semi": true}, "browserslist": ["defaults"]}