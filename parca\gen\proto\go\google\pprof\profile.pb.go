// Copyright 2016 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Profile is a common stacktrace profile format.
//
// Measurements represented with this format should follow the
// following conventions:
//
// - Consumers should treat unset optional fields as if they had been
//   set with their default value.
//
// - When possible, measurements should be stored in "unsampled" form
//   that is most useful to humans.  There should be enough
//   information present to determine the original sampled values.
//
// - On-disk, the serialized proto must be gzip-compressed.
//
// - The profile is represented as a set of samples, where each sample
//   references a sequence of locations, and where each location belongs
//   to a mapping.
// - There is a N->1 relationship from sample.location_id entries to
//   locations. For every sample.location_id entry there must be a
//   unique Location with that id.
// - There is an optional N->1 relationship from locations to
//   mappings. For every nonzero Location.mapping_id there must be a
//   unique Mapping with that id.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: google/pprof/profile.proto

package pprof

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Profile struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A description of the samples associated with each Sample.value.
	// For a cpu profile this might be:
	//
	//	[["cpu","nanoseconds"]] or [["wall","seconds"]] or [["syscall","count"]]
	//
	// For a heap profile, this might be:
	//
	//	[["allocations","count"], ["space","bytes"]],
	//
	// If one of the values represents the number of events represented
	// by the sample, by convention it should be at index 0 and use
	// sample_type.unit == "count".
	SampleType []*ValueType `protobuf:"bytes,1,rep,name=sample_type,json=sampleType,proto3" json:"sample_type,omitempty"`
	// The set of samples recorded in this profile.
	Sample []*Sample `protobuf:"bytes,2,rep,name=sample,proto3" json:"sample,omitempty"`
	// Mapping from address ranges to the image/binary/library mapped
	// into that address range.  mapping[0] will be the main binary.
	Mapping []*Mapping `protobuf:"bytes,3,rep,name=mapping,proto3" json:"mapping,omitempty"`
	// Useful program location
	Location []*Location `protobuf:"bytes,4,rep,name=location,proto3" json:"location,omitempty"`
	// Functions referenced by locations
	Function []*Function `protobuf:"bytes,5,rep,name=function,proto3" json:"function,omitempty"`
	// A common table for strings referenced by various messages.
	// string_table[0] must always be "".
	StringTable []string `protobuf:"bytes,6,rep,name=string_table,json=stringTable,proto3" json:"string_table,omitempty"`
	// frames with Function.function_name fully matching the following
	// regexp will be dropped from the samples, along with their successors.
	DropFrames int64 `protobuf:"varint,7,opt,name=drop_frames,json=dropFrames,proto3" json:"drop_frames,omitempty"` // Index into string table.
	// frames with Function.function_name fully matching the following
	// regexp will be kept, even if it matches drop_frames.
	KeepFrames int64 `protobuf:"varint,8,opt,name=keep_frames,json=keepFrames,proto3" json:"keep_frames,omitempty"` // Index into string table.
	// Time of collection (UTC) represented as nanoseconds past the epoch.
	TimeNanos int64 `protobuf:"varint,9,opt,name=time_nanos,json=timeNanos,proto3" json:"time_nanos,omitempty"`
	// Duration of the profile, if a duration makes sense.
	DurationNanos int64 `protobuf:"varint,10,opt,name=duration_nanos,json=durationNanos,proto3" json:"duration_nanos,omitempty"`
	// The kind of events between sampled ocurrences.
	// e.g [ "cpu","cycles" ] or [ "heap","bytes" ]
	PeriodType *ValueType `protobuf:"bytes,11,opt,name=period_type,json=periodType,proto3" json:"period_type,omitempty"`
	// The number of events between sampled occurrences.
	Period int64 `protobuf:"varint,12,opt,name=period,proto3" json:"period,omitempty"`
	// Freeform text associated to the profile.
	Comment []int64 `protobuf:"varint,13,rep,packed,name=comment,proto3" json:"comment,omitempty"` // Indices into string table.
	// Index into the string table of the type of the preferred sample
	// value. If unset, clients should default to the last sample value.
	DefaultSampleType int64 `protobuf:"varint,14,opt,name=default_sample_type,json=defaultSampleType,proto3" json:"default_sample_type,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Profile) Reset() {
	*x = Profile{}
	mi := &file_google_pprof_profile_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Profile) ProtoMessage() {}

func (x *Profile) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Profile.ProtoReflect.Descriptor instead.
func (*Profile) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{0}
}

func (x *Profile) GetSampleType() []*ValueType {
	if x != nil {
		return x.SampleType
	}
	return nil
}

func (x *Profile) GetSample() []*Sample {
	if x != nil {
		return x.Sample
	}
	return nil
}

func (x *Profile) GetMapping() []*Mapping {
	if x != nil {
		return x.Mapping
	}
	return nil
}

func (x *Profile) GetLocation() []*Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *Profile) GetFunction() []*Function {
	if x != nil {
		return x.Function
	}
	return nil
}

func (x *Profile) GetStringTable() []string {
	if x != nil {
		return x.StringTable
	}
	return nil
}

func (x *Profile) GetDropFrames() int64 {
	if x != nil {
		return x.DropFrames
	}
	return 0
}

func (x *Profile) GetKeepFrames() int64 {
	if x != nil {
		return x.KeepFrames
	}
	return 0
}

func (x *Profile) GetTimeNanos() int64 {
	if x != nil {
		return x.TimeNanos
	}
	return 0
}

func (x *Profile) GetDurationNanos() int64 {
	if x != nil {
		return x.DurationNanos
	}
	return 0
}

func (x *Profile) GetPeriodType() *ValueType {
	if x != nil {
		return x.PeriodType
	}
	return nil
}

func (x *Profile) GetPeriod() int64 {
	if x != nil {
		return x.Period
	}
	return 0
}

func (x *Profile) GetComment() []int64 {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *Profile) GetDefaultSampleType() int64 {
	if x != nil {
		return x.DefaultSampleType
	}
	return 0
}

// ValueType describes the semantics and measurement units of a value.
type ValueType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int64                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` // Index into string table.
	Unit          int64                  `protobuf:"varint,2,opt,name=unit,proto3" json:"unit,omitempty"` // Index into string table.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValueType) Reset() {
	*x = ValueType{}
	mi := &file_google_pprof_profile_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValueType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValueType) ProtoMessage() {}

func (x *ValueType) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValueType.ProtoReflect.Descriptor instead.
func (*ValueType) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{1}
}

func (x *ValueType) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ValueType) GetUnit() int64 {
	if x != nil {
		return x.Unit
	}
	return 0
}

// Each Sample records values encountered in some program
// context. The program context is typically a stack trace, perhaps
// augmented with auxiliary information like the thread-id, some
// indicator of a higher level request being handled etc.
type Sample struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ids recorded here correspond to a Profile.location.id.
	// The leaf is at location_id[0].
	LocationId []uint64 `protobuf:"varint,1,rep,packed,name=location_id,json=locationId,proto3" json:"location_id,omitempty"`
	// The type and unit of each value is defined by the corresponding
	// entry in Profile.sample_type. All samples must have the same
	// number of values, the same as the length of Profile.sample_type.
	// When aggregating multiple samples into a single sample, the
	// result has a list of values that is the element-wise sum of the
	// lists of the originals.
	Value []int64 `protobuf:"varint,2,rep,packed,name=value,proto3" json:"value,omitempty"`
	// label includes additional context for this sample. It can include
	// things like a thread id, allocation size, etc
	Label         []*Label `protobuf:"bytes,3,rep,name=label,proto3" json:"label,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Sample) Reset() {
	*x = Sample{}
	mi := &file_google_pprof_profile_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sample) ProtoMessage() {}

func (x *Sample) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sample.ProtoReflect.Descriptor instead.
func (*Sample) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{2}
}

func (x *Sample) GetLocationId() []uint64 {
	if x != nil {
		return x.LocationId
	}
	return nil
}

func (x *Sample) GetValue() []int64 {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Sample) GetLabel() []*Label {
	if x != nil {
		return x.Label
	}
	return nil
}

type Label struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Key   int64                  `protobuf:"varint,1,opt,name=key,proto3" json:"key,omitempty"` // Index into string table
	// At most one of the following must be present
	Str int64 `protobuf:"varint,2,opt,name=str,proto3" json:"str,omitempty"` // Index into string table
	Num int64 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	// Should only be present when num is present.
	// Specifies the units of num.
	// Use arbitrary string (for example, "requests") as a custom count unit.
	// If no unit is specified, consumer may apply heuristic to deduce the unit.
	// Consumers may also  interpret units like "bytes" and "kilobytes" as memory
	// units and units like "seconds" and "nanoseconds" as time units,
	// and apply appropriate unit conversions to these.
	NumUnit       int64 `protobuf:"varint,4,opt,name=num_unit,json=numUnit,proto3" json:"num_unit,omitempty"` // Index into string table
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Label) Reset() {
	*x = Label{}
	mi := &file_google_pprof_profile_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{3}
}

func (x *Label) GetKey() int64 {
	if x != nil {
		return x.Key
	}
	return 0
}

func (x *Label) GetStr() int64 {
	if x != nil {
		return x.Str
	}
	return 0
}

func (x *Label) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Label) GetNumUnit() int64 {
	if x != nil {
		return x.NumUnit
	}
	return 0
}

type Mapping struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique nonzero id for the mapping.
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Address at which the binary (or DLL) is loaded into memory.
	MemoryStart uint64 `protobuf:"varint,2,opt,name=memory_start,json=memoryStart,proto3" json:"memory_start,omitempty"`
	// The limit of the address range occupied by this mapping.
	MemoryLimit uint64 `protobuf:"varint,3,opt,name=memory_limit,json=memoryLimit,proto3" json:"memory_limit,omitempty"`
	// Offset in the binary that corresponds to the first mapped address.
	FileOffset uint64 `protobuf:"varint,4,opt,name=file_offset,json=fileOffset,proto3" json:"file_offset,omitempty"`
	// The object this entry is loaded from.  This can be a filename on
	// disk for the main binary and shared libraries, or virtual
	// abstractions like "[vdso]".
	Filename int64 `protobuf:"varint,5,opt,name=filename,proto3" json:"filename,omitempty"` // Index into string table
	// A string that uniquely identifies a particular program version
	// with high probability. E.g., for binaries generated by GNU tools,
	// it could be the contents of the .note.gnu.build-id field.
	BuildId int64 `protobuf:"varint,6,opt,name=build_id,json=buildId,proto3" json:"build_id,omitempty"` // Index into string table
	// The following fields indicate the resolution of symbolic info.
	HasFunctions    bool `protobuf:"varint,7,opt,name=has_functions,json=hasFunctions,proto3" json:"has_functions,omitempty"`
	HasFilenames    bool `protobuf:"varint,8,opt,name=has_filenames,json=hasFilenames,proto3" json:"has_filenames,omitempty"`
	HasLineNumbers  bool `protobuf:"varint,9,opt,name=has_line_numbers,json=hasLineNumbers,proto3" json:"has_line_numbers,omitempty"`
	HasInlineFrames bool `protobuf:"varint,10,opt,name=has_inline_frames,json=hasInlineFrames,proto3" json:"has_inline_frames,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Mapping) Reset() {
	*x = Mapping{}
	mi := &file_google_pprof_profile_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mapping) ProtoMessage() {}

func (x *Mapping) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mapping.ProtoReflect.Descriptor instead.
func (*Mapping) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{4}
}

func (x *Mapping) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Mapping) GetMemoryStart() uint64 {
	if x != nil {
		return x.MemoryStart
	}
	return 0
}

func (x *Mapping) GetMemoryLimit() uint64 {
	if x != nil {
		return x.MemoryLimit
	}
	return 0
}

func (x *Mapping) GetFileOffset() uint64 {
	if x != nil {
		return x.FileOffset
	}
	return 0
}

func (x *Mapping) GetFilename() int64 {
	if x != nil {
		return x.Filename
	}
	return 0
}

func (x *Mapping) GetBuildId() int64 {
	if x != nil {
		return x.BuildId
	}
	return 0
}

func (x *Mapping) GetHasFunctions() bool {
	if x != nil {
		return x.HasFunctions
	}
	return false
}

func (x *Mapping) GetHasFilenames() bool {
	if x != nil {
		return x.HasFilenames
	}
	return false
}

func (x *Mapping) GetHasLineNumbers() bool {
	if x != nil {
		return x.HasLineNumbers
	}
	return false
}

func (x *Mapping) GetHasInlineFrames() bool {
	if x != nil {
		return x.HasInlineFrames
	}
	return false
}

// Describes function and line table debug information.
type Location struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique nonzero id for the location.  A profile could use
	// instruction addresses or any integer sequence as ids.
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The id of the corresponding profile.Mapping for this location.
	// It can be unset if the mapping is unknown or not applicable for
	// this profile type.
	MappingId uint64 `protobuf:"varint,2,opt,name=mapping_id,json=mappingId,proto3" json:"mapping_id,omitempty"`
	// The instruction address for this location, if available.  It
	// should be within [Mapping.memory_start...Mapping.memory_limit]
	// for the corresponding mapping. A non-leaf address may be in the
	// middle of a call instruction. It is up to display tools to find
	// the beginning of the instruction if necessary.
	Address uint64 `protobuf:"varint,3,opt,name=address,proto3" json:"address,omitempty"`
	// Multiple line indicates this location has inlined functions,
	// where the last entry represents the caller into which the
	// preceding entries were inlined.
	//
	// E.g., if memcpy() is inlined into printf:
	//
	//	line[0].function_name == "memcpy"
	//	line[1].function_name == "printf"
	Line []*Line `protobuf:"bytes,4,rep,name=line,proto3" json:"line,omitempty"`
	// Provides an indication that multiple symbols map to this location's
	// address, for example due to identical code folding by the linker. In that
	// case the line information above represents one of the multiple
	// symbols. This field must be recomputed when the symbolization state of the
	// profile changes.
	IsFolded      bool `protobuf:"varint,5,opt,name=is_folded,json=isFolded,proto3" json:"is_folded,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Location) Reset() {
	*x = Location{}
	mi := &file_google_pprof_profile_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{5}
}

func (x *Location) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Location) GetMappingId() uint64 {
	if x != nil {
		return x.MappingId
	}
	return 0
}

func (x *Location) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *Location) GetLine() []*Line {
	if x != nil {
		return x.Line
	}
	return nil
}

func (x *Location) GetIsFolded() bool {
	if x != nil {
		return x.IsFolded
	}
	return false
}

type Line struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The id of the corresponding profile.Function for this line.
	FunctionId uint64 `protobuf:"varint,1,opt,name=function_id,json=functionId,proto3" json:"function_id,omitempty"`
	// Line number in source code.
	Line          int64 `protobuf:"varint,2,opt,name=line,proto3" json:"line,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Line) Reset() {
	*x = Line{}
	mi := &file_google_pprof_profile_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Line) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Line) ProtoMessage() {}

func (x *Line) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Line.ProtoReflect.Descriptor instead.
func (*Line) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{6}
}

func (x *Line) GetFunctionId() uint64 {
	if x != nil {
		return x.FunctionId
	}
	return 0
}

func (x *Line) GetLine() int64 {
	if x != nil {
		return x.Line
	}
	return 0
}

type Function struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique nonzero id for the function.
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Name of the function, in human-readable form if available.
	Name int64 `protobuf:"varint,2,opt,name=name,proto3" json:"name,omitempty"` // Index into string table
	// Name of the function, as identified by the system.
	// For instance, it can be a C++ mangled name.
	SystemName int64 `protobuf:"varint,3,opt,name=system_name,json=systemName,proto3" json:"system_name,omitempty"` // Index into string table
	// Source file containing the function.
	Filename int64 `protobuf:"varint,4,opt,name=filename,proto3" json:"filename,omitempty"` // Index into string table
	// Line number in source file.
	StartLine     int64 `protobuf:"varint,5,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Function) Reset() {
	*x = Function{}
	mi := &file_google_pprof_profile_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Function) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Function) ProtoMessage() {}

func (x *Function) ProtoReflect() protoreflect.Message {
	mi := &file_google_pprof_profile_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Function.ProtoReflect.Descriptor instead.
func (*Function) Descriptor() ([]byte, []int) {
	return file_google_pprof_profile_proto_rawDescGZIP(), []int{7}
}

func (x *Function) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Function) GetName() int64 {
	if x != nil {
		return x.Name
	}
	return 0
}

func (x *Function) GetSystemName() int64 {
	if x != nil {
		return x.SystemName
	}
	return 0
}

func (x *Function) GetFilename() int64 {
	if x != nil {
		return x.Filename
	}
	return 0
}

func (x *Function) GetStartLine() int64 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

var File_google_pprof_profile_proto protoreflect.FileDescriptor

const file_google_pprof_profile_proto_rawDesc = "" +
	"\n" +
	"\x1agoogle/pprof/profile.proto\x12\x12perftools.profiles\"\xf5\x04\n" +
	"\aProfile\x12>\n" +
	"\vsample_type\x18\x01 \x03(\v2\x1d.perftools.profiles.ValueTypeR\n" +
	"sampleType\x122\n" +
	"\x06sample\x18\x02 \x03(\v2\x1a.perftools.profiles.SampleR\x06sample\x125\n" +
	"\amapping\x18\x03 \x03(\v2\x1b.perftools.profiles.MappingR\amapping\x128\n" +
	"\blocation\x18\x04 \x03(\v2\x1c.perftools.profiles.LocationR\blocation\x128\n" +
	"\bfunction\x18\x05 \x03(\v2\x1c.perftools.profiles.FunctionR\bfunction\x12!\n" +
	"\fstring_table\x18\x06 \x03(\tR\vstringTable\x12\x1f\n" +
	"\vdrop_frames\x18\a \x01(\x03R\n" +
	"dropFrames\x12\x1f\n" +
	"\vkeep_frames\x18\b \x01(\x03R\n" +
	"keepFrames\x12\x1d\n" +
	"\n" +
	"time_nanos\x18\t \x01(\x03R\ttimeNanos\x12%\n" +
	"\x0eduration_nanos\x18\n" +
	" \x01(\x03R\rdurationNanos\x12>\n" +
	"\vperiod_type\x18\v \x01(\v2\x1d.perftools.profiles.ValueTypeR\n" +
	"periodType\x12\x16\n" +
	"\x06period\x18\f \x01(\x03R\x06period\x12\x18\n" +
	"\acomment\x18\r \x03(\x03R\acomment\x12.\n" +
	"\x13default_sample_type\x18\x0e \x01(\x03R\x11defaultSampleType\"3\n" +
	"\tValueType\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x03R\x04type\x12\x12\n" +
	"\x04unit\x18\x02 \x01(\x03R\x04unit\"p\n" +
	"\x06Sample\x12\x1f\n" +
	"\vlocation_id\x18\x01 \x03(\x04R\n" +
	"locationId\x12\x14\n" +
	"\x05value\x18\x02 \x03(\x03R\x05value\x12/\n" +
	"\x05label\x18\x03 \x03(\v2\x19.perftools.profiles.LabelR\x05label\"X\n" +
	"\x05Label\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12\x10\n" +
	"\x03str\x18\x02 \x01(\x03R\x03str\x12\x10\n" +
	"\x03num\x18\x03 \x01(\x03R\x03num\x12\x19\n" +
	"\bnum_unit\x18\x04 \x01(\x03R\anumUnit\"\xd7\x02\n" +
	"\aMapping\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12!\n" +
	"\fmemory_start\x18\x02 \x01(\x04R\vmemoryStart\x12!\n" +
	"\fmemory_limit\x18\x03 \x01(\x04R\vmemoryLimit\x12\x1f\n" +
	"\vfile_offset\x18\x04 \x01(\x04R\n" +
	"fileOffset\x12\x1a\n" +
	"\bfilename\x18\x05 \x01(\x03R\bfilename\x12\x19\n" +
	"\bbuild_id\x18\x06 \x01(\x03R\abuildId\x12#\n" +
	"\rhas_functions\x18\a \x01(\bR\fhasFunctions\x12#\n" +
	"\rhas_filenames\x18\b \x01(\bR\fhasFilenames\x12(\n" +
	"\x10has_line_numbers\x18\t \x01(\bR\x0ehasLineNumbers\x12*\n" +
	"\x11has_inline_frames\x18\n" +
	" \x01(\bR\x0fhasInlineFrames\"\x9e\x01\n" +
	"\bLocation\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"mapping_id\x18\x02 \x01(\x04R\tmappingId\x12\x18\n" +
	"\aaddress\x18\x03 \x01(\x04R\aaddress\x12,\n" +
	"\x04line\x18\x04 \x03(\v2\x18.perftools.profiles.LineR\x04line\x12\x1b\n" +
	"\tis_folded\x18\x05 \x01(\bR\bisFolded\";\n" +
	"\x04Line\x12\x1f\n" +
	"\vfunction_id\x18\x01 \x01(\x04R\n" +
	"functionId\x12\x12\n" +
	"\x04line\x18\x02 \x01(\x03R\x04line\"\x8a\x01\n" +
	"\bFunction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\x03R\x04name\x12\x1f\n" +
	"\vsystem_name\x18\x03 \x01(\x03R\n" +
	"systemName\x12\x1a\n" +
	"\bfilename\x18\x04 \x01(\x03R\bfilename\x12\x1d\n" +
	"\n" +
	"start_line\x18\x05 \x01(\x03R\tstartLineB\xc5\x01\n" +
	"\x16com.perftools.profilesB\fProfileProtoP\x01Z4github.com/parca-dev/parca/gen/proto/go/google/pprof\xa2\x02\x03PPX\xaa\x02\x12Perftools.Profiles\xca\x02\x12Perftools\\Profiles\xe2\x02\x1ePerftools\\Profiles\\GPBMetadata\xea\x02\x13Perftools::Profilesb\x06proto3"

var (
	file_google_pprof_profile_proto_rawDescOnce sync.Once
	file_google_pprof_profile_proto_rawDescData []byte
)

func file_google_pprof_profile_proto_rawDescGZIP() []byte {
	file_google_pprof_profile_proto_rawDescOnce.Do(func() {
		file_google_pprof_profile_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_google_pprof_profile_proto_rawDesc), len(file_google_pprof_profile_proto_rawDesc)))
	})
	return file_google_pprof_profile_proto_rawDescData
}

var file_google_pprof_profile_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_google_pprof_profile_proto_goTypes = []any{
	(*Profile)(nil),   // 0: perftools.profiles.Profile
	(*ValueType)(nil), // 1: perftools.profiles.ValueType
	(*Sample)(nil),    // 2: perftools.profiles.Sample
	(*Label)(nil),     // 3: perftools.profiles.Label
	(*Mapping)(nil),   // 4: perftools.profiles.Mapping
	(*Location)(nil),  // 5: perftools.profiles.Location
	(*Line)(nil),      // 6: perftools.profiles.Line
	(*Function)(nil),  // 7: perftools.profiles.Function
}
var file_google_pprof_profile_proto_depIdxs = []int32{
	1, // 0: perftools.profiles.Profile.sample_type:type_name -> perftools.profiles.ValueType
	2, // 1: perftools.profiles.Profile.sample:type_name -> perftools.profiles.Sample
	4, // 2: perftools.profiles.Profile.mapping:type_name -> perftools.profiles.Mapping
	5, // 3: perftools.profiles.Profile.location:type_name -> perftools.profiles.Location
	7, // 4: perftools.profiles.Profile.function:type_name -> perftools.profiles.Function
	1, // 5: perftools.profiles.Profile.period_type:type_name -> perftools.profiles.ValueType
	3, // 6: perftools.profiles.Sample.label:type_name -> perftools.profiles.Label
	6, // 7: perftools.profiles.Location.line:type_name -> perftools.profiles.Line
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_google_pprof_profile_proto_init() }
func file_google_pprof_profile_proto_init() {
	if File_google_pprof_profile_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_google_pprof_profile_proto_rawDesc), len(file_google_pprof_profile_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_pprof_profile_proto_goTypes,
		DependencyIndexes: file_google_pprof_profile_proto_depIdxs,
		MessageInfos:      file_google_pprof_profile_proto_msgTypes,
	}.Build()
	File_google_pprof_profile_proto = out.File
	file_google_pprof_profile_proto_goTypes = nil
	file_google_pprof_profile_proto_depIdxs = nil
}
