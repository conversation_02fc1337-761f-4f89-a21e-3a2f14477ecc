{"name": "ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"lint": "eslint --ext .ts,.tsx,.js packages/*", "fix": "eslint --fix --ext .ts,.tsx,.js packages/*", "type-check": "tsc --noEmit", "test": "vitest --run", "build": "lerna run build", "build-swc-lerna": "lerna run build-swc", "watch": "lerna run --parallel watch", "watch-parca-dev": "lerna run --parallel --include-dependencies --scope @parca/web watch", "publish:ci": "lerna publish --yes --no-verify-access", "benchmark": "NODE_ENV=production BROWSERSLIST='Chrome > 100' GENERATE_SOURCEMAP=false ts-node --experimental-specifier-resolution=node scripts/run-benchmark.mts", "prettier": "prettier --write --ignore-path ../.prettierignore 'packages/**/*.{js,jsx,ts,tsx,md,mdx}'", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "chromatic"}, "dependencies": {"@parca/react-benchmark": "^5.4.1", "command-line-args": "^5.2.1", "not-a-log": "^1.0.1", "postcss-loader": "^8.1.1", "react": "18.3.1", "react-dom": "18.3.1", "react-popper": "^2.3.0", "tailwindcss": "3.2.4"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/node": "7.28.0", "@babel/plugin-proposal-export-default-from": "7.27.1", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@babel/preset-env": "7.28.0", "@chromatic-com/storybook": "1.9.0", "@ianvs/prettier-plugin-sort-imports": "3.7.2", "@mdx-js/loader": "2.3.0", "@next/bundle-analyzer": "12.3.7", "@next/eslint-plugin-next": "12.3.7", "@next/mdx": "12.3.7", "@storybook/addon-actions": "8.6.14", "@storybook/addon-docs": "8.6.14", "@storybook/addon-essentials": "8.6.14", "@storybook/addon-interactions": "8.6.14", "@storybook/addon-links": "8.6.14", "@storybook/addon-mdx-gfm": "8.6.14", "@storybook/addon-outline": "8.6.14", "@storybook/node-logger": "8.6.14", "@storybook/react": "8.6.14", "@storybook/react-vite": "8.6.14", "@storybook/test": "8.6.14", "@swc/cli": "0.7.8", "@swc/core": "1.12.11", "@swc/jest": "0.2.39", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "13.4.0", "@types/command-line-args": "5.2.3", "@types/jest": "29.5.14", "@types/node": "18.19.115", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "arg": "5.0.2", "chromatic": "11.29.0", "css-loader": "6.11.0", "dev-kong": "0.11.0", "eslint": "8.45.0", "eslint-config-prettier": "9.1.0", "eslint-config-react-app": "7.0.1", "eslint-config-standard-with-typescript": "22.0.0", "eslint-plugin-import": "2.32.0", "eslint-plugin-jest": "26.9.0", "eslint-plugin-jest-dom": "4.0.3", "eslint-plugin-n": "15.7.0", "eslint-plugin-prettier": "5.5.1", "eslint-plugin-promise": "6.6.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-standard": "5.0.0", "eslint-plugin-storybook": "0.12.0", "eslint-plugin-typescript-enum": "2.1.0", "execa": "6.1.0", "fs-extra": "10.1.0", "glob-promise": "5.0.1", "globby": "13.2.2", "identity-obj-proxy": "3.0.0", "internal-ip": "7.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "lerna": "8.2.3", "local-web-server": "5.4.0", "next-transpile-modules": "9.1.0", "ora": "6.3.1", "plop": "3.1.2", "pm2": "5.4.3", "postcss": "8.5.6", "prettier": "3.6.2", "prettier-plugin-tailwindcss": "^0.4.0", "react-is": "18.3.1", "react-test-renderer": "18.3.1", "replace-in-files": "3.0.0", "rimraf": "3.0.2", "sass": "1.89.2", "sass-loader": "13.3.3", "storybook": "8.6.14", "storybook-dark-mode": "4.0.2", "style-loader": "3.3.4", "ts-jest": "29.4.0", "ts-node": "10.9.2", "tsc-watch": "6.3.1", "typescript": "5.8.3", "vite": "5.4.19", "vitest": "1.6.1"}, "eslintConfig": {"overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}]}, "resolutions": {"fork-ts-checker-webpack-plugin": "^8.0.0"}}