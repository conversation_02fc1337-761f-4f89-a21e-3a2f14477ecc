package storage

import (
	"time"
)

// FunctionCall represents a complete function call
type FunctionCall struct {
	ID           string                 `json:"id"`
	AgentID      string                 `json:"agent_id"`
	PID          uint32                 `json:"pid"`
	ProcessName  string                 `json:"process_name"`
	FunctionName string                 `json:"function_name"`
	FunctionAddr uint64                 `json:"function_addr"`
	StartTime    time.Time              `json:"start_time"`
	EndTime      time.Time              `json:"end_time"`
	Duration     time.Duration          `json:"duration"`
	Arguments    []uint64               `json:"arguments"`
	ReturnValue  uint64                 `json:"return_value"`
	StackTrace   []uint64               `json:"stack_trace"`
	Error        string                 `json:"error,omitempty"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// FunctionMetrics represents aggregated metrics for a function
type FunctionMetrics struct {
	FunctionName  string        `json:"function_name"`
	CallCount     uint64        `json:"call_count"`
	ErrorCount    uint64        `json:"error_count"`
	TotalDuration time.Duration `json:"total_duration"`
	MinDuration   time.Duration `json:"min_duration"`
	MaxDuration   time.Duration `json:"max_duration"`
	AvgDuration   time.Duration `json:"avg_duration"`
	P50Duration   time.Duration `json:"p50_duration"`
	P95Duration   time.Duration `json:"p95_duration"`
	P99Duration   time.Duration `json:"p99_duration"`
	LastSeen      time.Time     `json:"last_seen"`
	WindowStart   time.Time     `json:"window_start"`
	WindowEnd     time.Time     `json:"window_end"`
}

// QueryOptions defines options for querying data
type QueryOptions struct {
	StartTime     *time.Time
	EndTime       *time.Time
	FunctionName  string
	ProcessName   string
	PID           *uint32
	AgentID       string
	Limit         int
	Offset        int
	OrderBy       string
	OrderDesc     bool
}

// Storage interface defines the storage operations
type Storage interface {
	// Function calls
	StoreFunctionCall(call *FunctionCall) error
	GetFunctionCall(id string) (*FunctionCall, error)
	QueryFunctionCalls(opts QueryOptions) ([]*FunctionCall, error)
	DeleteFunctionCalls(before time.Time) error

	// Function metrics
	StoreFunctionMetrics(metrics *FunctionMetrics) error
	GetFunctionMetrics(functionName string, start, end time.Time) ([]*FunctionMetrics, error)
	QueryFunctionMetrics(opts QueryOptions) ([]*FunctionMetrics, error)

	// Aggregated queries
	GetFunctionList() ([]string, error)
	GetProcessList() ([]string, error)
	GetLatencyMetrics(functionName string, start, end time.Time) (*LatencyMetrics, error)
	GetThroughputMetrics(functionName string, start, end time.Time) (*ThroughputMetrics, error)
	GetErrorMetrics(functionName string, start, end time.Time) (*ErrorMetrics, error)

	// System operations
	GetStats() map[string]interface{}
	Close() error
}

// LatencyMetrics represents latency statistics
type LatencyMetrics struct {
	FunctionName string                   `json:"function_name"`
	StartTime    time.Time                `json:"start_time"`
	EndTime      time.Time                `json:"end_time"`
	CallCount    uint64                   `json:"call_count"`
	MinLatency   time.Duration            `json:"min_latency"`
	MaxLatency   time.Duration            `json:"max_latency"`
	AvgLatency   time.Duration            `json:"avg_latency"`
	Percentiles  map[string]time.Duration `json:"percentiles"`
	Histogram    []HistogramBucket        `json:"histogram"`
}

// ThroughputMetrics represents throughput statistics
type ThroughputMetrics struct {
	FunctionName string              `json:"function_name"`
	StartTime    time.Time           `json:"start_time"`
	EndTime      time.Time           `json:"end_time"`
	TotalCalls   uint64              `json:"total_calls"`
	CallsPerSec  float64             `json:"calls_per_sec"`
	TimeSeries   []ThroughputPoint   `json:"time_series"`
}

// ErrorMetrics represents error statistics
type ErrorMetrics struct {
	FunctionName string         `json:"function_name"`
	StartTime    time.Time      `json:"start_time"`
	EndTime      time.Time      `json:"end_time"`
	TotalCalls   uint64         `json:"total_calls"`
	ErrorCalls   uint64         `json:"error_calls"`
	ErrorRate    float64        `json:"error_rate"`
	TimeSeries   []ErrorPoint   `json:"time_series"`
}

// HistogramBucket represents a latency histogram bucket
type HistogramBucket struct {
	LowerBound time.Duration `json:"lower_bound"`
	UpperBound time.Duration `json:"upper_bound"`
	Count      uint64        `json:"count"`
}

// ThroughputPoint represents a point in throughput time series
type ThroughputPoint struct {
	Timestamp time.Time `json:"timestamp"`
	CallCount uint64    `json:"call_count"`
	Rate      float64   `json:"rate"`
}

// ErrorPoint represents a point in error time series
type ErrorPoint struct {
	Timestamp  time.Time `json:"timestamp"`
	TotalCalls uint64    `json:"total_calls"`
	ErrorCalls uint64    `json:"error_calls"`
	ErrorRate  float64   `json:"error_rate"`
}
