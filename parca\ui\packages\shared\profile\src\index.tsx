// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useLabelNames} from './MatchersInput';
import ProfileExplorer, {getExpressionAsAString} from './ProfileExplorer';
import ProfileTypeSelector from './ProfileTypeSelector';
import CustomSelect from './SimpleMatchers/Select';

export * from './ProfileFlameGraph';
export * from './ProfileSource';
export * from './ProfileView';
export * from './ProfileViewWithData';
export * from './utils';
export * from './ProfileTypeSelector';
export * from './SourceView';
export * from './ProfileMetricsGraph';

export const DEFAULT_PROFILE_EXPLORER_PARAM_VALUES = {
  dashboard_items: 'flamegraph',
};

export {ProfileExplorer, ProfileTypeSelector, getExpressionAsAString, CustomSelect, useLabelNames};
