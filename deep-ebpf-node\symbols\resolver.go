package symbols

import (
	"bufio"
	"debug/elf"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync"

	"k8s.io/klog/v2"
)

// Symbol represents a resolved symbol
type Symbol struct {
	Name    string `json:"name"`
	Address uint64 `json:"address"`
	Size    uint64 `json:"size"`
	Type    string `json:"type"`
	File    string `json:"file"`
	Line    int    `json:"line"`
}

// SymbolResolver provides symbol resolution capabilities
type SymbolResolver struct {
	kernelSymbols map[uint64]*Symbol
	binaryCache   map[string]map[uint64]*Symbol
	mutex         sync.RWMutex
	loaded        bool
}

// NewSymbolResolver creates a new symbol resolver
func NewSymbolResolver() *SymbolResolver {
	return &SymbolResolver{
		kernelSymbols: make(map[uint64]*Symbol),
		binaryCache:   make(map[string]map[uint64]*Symbol),
	}
}

// LoadKernelSymbols loads kernel symbols from /proc/kallsyms
func (sr *SymbolResolver) LoadKernelSymbols() error {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	if sr.loaded {
		return nil
	}

	klog.Infoln("Loading kernel symbols from /proc/kallsyms...")

	file, err := os.Open("/proc/kallsyms")
	if err != nil {
		return fmt.Errorf("failed to open /proc/kallsyms: %w", err)
	}
	defer file.Close()

	count := 0
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		parts := strings.Fields(line)
		if len(parts) < 3 {
			continue
		}

		// Parse address
		addr, err := strconv.ParseUint(parts[0], 16, 64)
		if err != nil || addr == 0 {
			continue
		}

		// Parse symbol type and name
		symbolType := parts[1]
		symbolName := parts[2]

		// Skip certain symbol types
		if symbolType == "U" || symbolType == "u" {
			continue
		}

		symbol := &Symbol{
			Name:    symbolName,
			Address: addr,
			Type:    symbolType,
		}

		sr.kernelSymbols[addr] = symbol
		count++
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading /proc/kallsyms: %w", err)
	}

	sr.loaded = true
	klog.Infof("Loaded %d kernel symbols", count)
	return nil
}

// ResolveKernelAddress resolves a kernel address to a symbol
func (sr *SymbolResolver) ResolveKernelAddress(addr uint64) *Symbol {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	// Direct lookup first
	if symbol, exists := sr.kernelSymbols[addr]; exists {
		return symbol
	}

	// Find closest symbol (for addresses within functions)
	var closest *Symbol
	var closestAddr uint64

	for symbolAddr, symbol := range sr.kernelSymbols {
		if symbolAddr <= addr && symbolAddr > closestAddr {
			closest = symbol
			closestAddr = symbolAddr
		}
	}

	if closest != nil && addr-closestAddr < 0x1000 { // Within 4KB
		// Create a copy with the actual address
		result := *closest
		result.Address = addr
		if addr != closestAddr {
			result.Name = fmt.Sprintf("%s+0x%x", closest.Name, addr-closestAddr)
		}
		return &result
	}

	return nil
}

// ResolveBinaryAddress resolves an address within a specific binary
func (sr *SymbolResolver) ResolveBinaryAddress(binaryPath string, addr uint64) *Symbol {
	sr.mutex.RLock()
	cache, exists := sr.binaryCache[binaryPath]
	sr.mutex.RUnlock()

	if !exists {
		// Load symbols for this binary
		if err := sr.loadBinarySymbols(binaryPath); err != nil {
			klog.V(2).Infof("Failed to load symbols for %s: %v", binaryPath, err)
			return nil
		}

		sr.mutex.RLock()
		cache = sr.binaryCache[binaryPath]
		sr.mutex.RUnlock()
	}

	if cache == nil {
		return nil
	}

	// Direct lookup
	if symbol, exists := cache[addr]; exists {
		return symbol
	}

	// Find closest symbol
	var closest *Symbol
	var closestAddr uint64

	for symbolAddr, symbol := range cache {
		if symbolAddr <= addr && symbolAddr > closestAddr {
			closest = symbol
			closestAddr = symbolAddr
		}
	}

	if closest != nil && addr-closestAddr < closest.Size {
		// Create a copy with the actual address
		result := *closest
		result.Address = addr
		if addr != closestAddr {
			result.Name = fmt.Sprintf("%s+0x%x", closest.Name, addr-closestAddr)
		}
		return &result
	}

	return nil
}

// loadBinarySymbols loads symbols from an ELF binary
func (sr *SymbolResolver) loadBinarySymbols(binaryPath string) error {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	// Check if already loaded
	if _, exists := sr.binaryCache[binaryPath]; exists {
		return nil
	}

	klog.V(2).Infof("Loading symbols from %s", binaryPath)

	file, err := elf.Open(binaryPath)
	if err != nil {
		return fmt.Errorf("failed to open ELF file: %w", err)
	}
	defer file.Close()

	symbols := make(map[uint64]*Symbol)

	// Load dynamic symbols
	if dynsyms, err := file.DynamicSymbols(); err == nil {
		sr.processELFSymbols(dynsyms, symbols, "dynamic")
	}

	// Load regular symbols
	if syms, err := file.Symbols(); err == nil {
		sr.processELFSymbols(syms, symbols, "static")
	}

	sr.binaryCache[binaryPath] = symbols
	klog.V(2).Infof("Loaded %d symbols from %s", len(symbols), binaryPath)
	return nil
}

// processELFSymbols processes ELF symbols and adds them to the symbol map
func (sr *SymbolResolver) processELFSymbols(syms []elf.Symbol, symbols map[uint64]*Symbol, symbolType string) {
	for _, sym := range syms {
		if sym.Value == 0 || sym.Name == "" {
			continue
		}

		// Skip certain symbol types
		if sym.Info&0xf == 0 { // STT_NOTYPE
			continue
		}

		symbol := &Symbol{
			Name:    sym.Name,
			Address: sym.Value,
			Size:    sym.Size,
			Type:    symbolType,
		}

		symbols[sym.Value] = symbol
	}
}

// ResolveAddress resolves an address to a symbol (kernel or userspace)
func (sr *SymbolResolver) ResolveAddress(addr uint64, binaryPath string) *Symbol {
	// Check if it's a kernel address
	if addr >= 0xffffffff00000000 {
		return sr.ResolveKernelAddress(addr)
	}

	// Try to resolve as userspace address
	if binaryPath != "" {
		return sr.ResolveBinaryAddress(binaryPath, addr)
	}

	return nil
}

// GetFunctionName returns just the function name for an address
func (sr *SymbolResolver) GetFunctionName(addr uint64, binaryPath string) string {
	symbol := sr.ResolveAddress(addr, binaryPath)
	if symbol != nil {
		return symbol.Name
	}

	// Return a generic name if symbol not found
	if addr >= 0xffffffff00000000 {
		return fmt.Sprintf("kernel_func_0x%x", addr)
	}
	return fmt.Sprintf("func_0x%x", addr)
}

// GetSymbolInfo returns detailed symbol information
func (sr *SymbolResolver) GetSymbolInfo(addr uint64, binaryPath string) map[string]interface{} {
	symbol := sr.ResolveAddress(addr, binaryPath)
	if symbol == nil {
		return map[string]interface{}{
			"name":    sr.GetFunctionName(addr, binaryPath),
			"address": fmt.Sprintf("0x%x", addr),
			"type":    "unknown",
		}
	}

	return map[string]interface{}{
		"name":    symbol.Name,
		"address": fmt.Sprintf("0x%x", symbol.Address),
		"size":    symbol.Size,
		"type":    symbol.Type,
		"file":    symbol.File,
		"line":    symbol.Line,
	}
}

// ListKernelSymbols returns a list of kernel symbols matching a pattern
func (sr *SymbolResolver) ListKernelSymbols(pattern string) []*Symbol {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	var matches []*Symbol
	for _, symbol := range sr.kernelSymbols {
		if pattern == "" || strings.Contains(symbol.Name, pattern) {
			matches = append(matches, symbol)
		}
	}

	// Sort by address
	sort.Slice(matches, func(i, j int) bool {
		return matches[i].Address < matches[j].Address
	})

	return matches
}

// ListBinarySymbols returns a list of symbols from a binary matching a pattern
func (sr *SymbolResolver) ListBinarySymbols(binaryPath, pattern string) []*Symbol {
	sr.mutex.RLock()
	cache, exists := sr.binaryCache[binaryPath]
	sr.mutex.RUnlock()

	if !exists {
		if err := sr.loadBinarySymbols(binaryPath); err != nil {
			return nil
		}
		sr.mutex.RLock()
		cache = sr.binaryCache[binaryPath]
		sr.mutex.RUnlock()
	}

	var matches []*Symbol
	for _, symbol := range cache {
		if pattern == "" || strings.Contains(symbol.Name, pattern) {
			matches = append(matches, symbol)
		}
	}

	// Sort by address
	sort.Slice(matches, func(i, j int) bool {
		return matches[i].Address < matches[j].Address
	})

	return matches
}

// GetStats returns statistics about loaded symbols
func (sr *SymbolResolver) GetStats() map[string]interface{} {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	binaryCount := len(sr.binaryCache)
	totalBinarySymbols := 0
	for _, cache := range sr.binaryCache {
		totalBinarySymbols += len(cache)
	}

	return map[string]interface{}{
		"kernel_symbols_loaded": sr.loaded,
		"kernel_symbols_count":  len(sr.kernelSymbols),
		"binaries_cached":       binaryCount,
		"binary_symbols_count":  totalBinarySymbols,
	}
}

// ClearCache clears the symbol cache
func (sr *SymbolResolver) ClearCache() {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	sr.binaryCache = make(map[string]map[uint64]*Symbol)
	klog.Infoln("Symbol cache cleared")
}

// PreloadBinarySymbols preloads symbols for a binary
func (sr *SymbolResolver) PreloadBinarySymbols(binaryPath string) error {
	return sr.loadBinarySymbols(binaryPath)
}

// IsKernelAddress checks if an address is in kernel space
func (sr *SymbolResolver) IsKernelAddress(addr uint64) bool {
	return addr >= 0xffffffff00000000
}

// IsUserAddress checks if an address is in user space
func (sr *SymbolResolver) IsUserAddress(addr uint64) bool {
	return addr < 0xffffffff00000000 && addr >= 0x400000
}

// FormatAddress formats an address with symbol information
func (sr *SymbolResolver) FormatAddress(addr uint64, binaryPath string) string {
	symbol := sr.ResolveAddress(addr, binaryPath)
	if symbol != nil {
		if symbol.Address == addr {
			return fmt.Sprintf("%s (0x%x)", symbol.Name, addr)
		}
		return fmt.Sprintf("%s (0x%x)", symbol.Name, addr)
	}

	space := "user"
	if sr.IsKernelAddress(addr) {
		space = "kernel"
	}
	return fmt.Sprintf("0x%x (%s space)", addr, space)
}
