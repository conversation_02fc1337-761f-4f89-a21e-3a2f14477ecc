---
sidebar_position: 8
---

# JVM

The following inspections are based on the JVM metrics automatically collected by `coroot-node-agent` for each JVM running on the node. 
So, you don't need to configure anything!

* JVM availability: checks that every JVM is up and running
* JVM safepoints: detects situations when a java application has been stopped for a significant amount of time due to safepoint operations

<img alt="JVM" src="/img/docs/jvm.png" class="card w-1200"/>
