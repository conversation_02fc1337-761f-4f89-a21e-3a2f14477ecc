worker_processes 1;
master_process off;
#daemon off;
events {
    worker_connections 1024;
}
http {
    lua_package_path "$prefix/lua/?.lua;;";
    server {
        listen 8080 reuseport;
        location /fib {
            default_type text/plain;
            content_by_lua_block {
                local fib = require "fib"
                local u = require "util"
                local function f()
                    ngx.say(fib.calc(32))
                end
                u.run_duration(.1, f)
            }
        }
        location /comp {
            default_type text/plain;
            content_by_lua_block {
                local t = require "input-text"
                local c = require "comp"
                local u = require "util"
                local function f()
                    local text = t.gen()
                    res = c.comp(text)
                    ngx.say("Comp: " .. #text .. "->" .. #res)
                end
                u.run_duration(.1, f)
            }
        }
        location /ffi {
            default_type text/plain;
            content_by_lua_block {
                local q = require "qsort"
                local u = require "util"
                local function f()
                    local text = q.sort(100)
                    c.comp(text)
                end
                u.run_duration(.1, f)
            }
        }
        location /pcall {
            default_type text/plain;
            content_by_lua_block {
                local u = require "util"
                local n = require "nested-pcall"
                local function f()
                    n.call()
                end
                u.run_duration(.1, f)
                ngx.say("pcall")
            }
        }
    }
}