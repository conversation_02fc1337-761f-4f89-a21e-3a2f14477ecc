package main

import (
	"embed"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"syscall"

	"github.com/mexyusef/deep-ebpf-server/api"
	"github.com/mexyusef/deep-ebpf-server/collector"
	"github.com/mexyusef/deep-ebpf-server/config"
	"github.com/mexyusef/deep-ebpf-server/storage"
	"github.com/mexyusef/deep-ebpf-server/processor"
	"github.com/mexyusef/deep-ebpf-server/common"
	"github.com/gorilla/mux"
	"golang.org/x/term"
	"gopkg.in/alecthomas/kingpin.v2"
	"k8s.io/klog"
)

const Edition = "Deep-eBPF"

var version = "unknown"

//go:embed static
var static embed.FS

func main() {
	kingpin.Command("run", "Run Deep-eBPF server").Default()
	cmdSetAdminPassword := kingpin.Command("set-admin-password", "Set password for the default Admin user")

	cmd := kingpin.Parse()

	klog.Infof("edition: %s", Edition)
	klog.Infof("version: %s", version)

	cfg, err := config.Load()
	if err != nil {
		klog.Exitf("failed to load config: %s", err)
	}

	if err = common.CreateDirectoryIfNotExists(cfg.DataDir); err != nil {
		klog.Exitln(err)
	}

	// Initialize storage layer
	var store storage.Storage
	if cfg.Storage.Type == "memory" {
		klog.Infoln("storage type: memory")
		store = storage.NewMemoryStorage()
	} else {
		klog.Infoln("storage type: persistent")
		store, err = storage.NewPersistentStorage(cfg.DataDir)
		if err != nil {
			klog.Exitln(err)
		}
	}

	switch cmd {
	case cmdSetAdminPassword.FullCommand():
		err = setAdminPassword()
		if err != nil {
			fmt.Println("Failed to set admin password:", err)
		} else {
			fmt.Println("Admin password set successfully.")
		}
		return
	}

	// Initialize function trace processor
	processorConfig := processor.Config{
		BufferSize:       cfg.Processor.BufferSize,
		CorrelationTTL:   cfg.Processor.CorrelationTTL,
		AggregationWindow: cfg.Processor.AggregationWindow,
	}
	proc := processor.New(processorConfig, store)

	// Initialize collector for agent data
	collectorConfig := collector.Config{
		ListenAddress:    cfg.Collector.ListenAddress,
		BufferSize:      cfg.Collector.BufferSize,
		AgentTimeout:    cfg.Collector.AgentTimeout,
		EnableDiscovery: cfg.Collector.EnableDiscovery,
	}
	coll := collector.New(collectorConfig, proc)

	// Setup graceful shutdown
	go func() {
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM)
		<-ch
		klog.Infoln("Shutting down...")
		coll.Close()
		proc.Close()
		store.Close()
		os.Exit(0)
	}()

	// Start collector
	go func() {
		klog.Infof("Starting collector on %s", cfg.Collector.ListenAddress)
		if err := coll.Start(); err != nil {
			klog.Exitf("Failed to start collector: %v", err)
		}
	}()

	// Start processor
	go func() {
		klog.Infoln("Starting function trace processor")
		if err := proc.Start(); err != nil {
			klog.Exitf("Failed to start processor: %v", err)
		}
	}()

	// Initialize API
	apiConfig := api.Config{
		EnableCORS:   cfg.API.EnableCORS,
		RateLimit:    cfg.API.RateLimit,
		AuthEnabled:  cfg.API.AuthEnabled,
	}
	apiHandler := api.New(apiConfig, store, coll, proc)

	instanceUuid := common.GetInstanceUuid(cfg.DataDir)

	// Setup HTTP router
	router := mux.NewRouter()
	
	// Debug endpoints
	router.PathPrefix("/debug/pprof/").Handler(http.DefaultServeMux)
	router.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}).Methods(http.MethodGet)

	// Collector endpoints (for agents to send data)
	router.HandleFunc("/v1/function-traces", coll.FunctionTraces).Methods(http.MethodPost)
	router.HandleFunc("/v1/agent-status", coll.AgentStatus).Methods(http.MethodPost)
	router.HandleFunc("/v1/agent-config", coll.AgentConfig).Methods(http.MethodGet)

	// API endpoints
	r := router
	if cfg.UrlBasePath != "/" {
		r = router.PathPrefix(cfg.UrlBasePath).Subrouter()
	}

	// Authentication endpoints
	r.HandleFunc("/api/login", apiHandler.Login).Methods(http.MethodPost)
	r.HandleFunc("/api/logout", apiHandler.Logout).Methods(http.MethodPost)

	// Function tracing API endpoints
	r.HandleFunc("/api/traces", apiHandler.Auth(apiHandler.GetTraces)).Methods(http.MethodGet)
	r.HandleFunc("/api/traces/{trace_id}", apiHandler.Auth(apiHandler.GetTrace)).Methods(http.MethodGet)
	r.HandleFunc("/api/functions", apiHandler.Auth(apiHandler.GetFunctions)).Methods(http.MethodGet)
	r.HandleFunc("/api/processes", apiHandler.Auth(apiHandler.GetProcesses)).Methods(http.MethodGet)

	// Performance metrics API endpoints
	r.HandleFunc("/api/metrics/latency", apiHandler.Auth(apiHandler.GetLatencyMetrics)).Methods(http.MethodGet)
	r.HandleFunc("/api/metrics/throughput", apiHandler.Auth(apiHandler.GetThroughputMetrics)).Methods(http.MethodGet)
	r.HandleFunc("/api/metrics/errors", apiHandler.Auth(apiHandler.GetErrorMetrics)).Methods(http.MethodGet)

	// System status API endpoints
	r.HandleFunc("/api/status", apiHandler.Auth(apiHandler.GetStatus)).Methods(http.MethodGet)
	r.HandleFunc("/api/agents", apiHandler.Auth(apiHandler.GetAgents)).Methods(http.MethodGet)
	r.HandleFunc("/api/stats", apiHandler.Auth(apiHandler.GetStats)).Methods(http.MethodGet)

	// Real-time streaming endpoints
	r.HandleFunc("/api/stream/traces", apiHandler.Auth(apiHandler.StreamTraces)).Methods(http.MethodGet)
	r.HandleFunc("/api/stream/metrics", apiHandler.Auth(apiHandler.StreamMetrics)).Methods(http.MethodGet)

	// Configuration endpoints
	r.HandleFunc("/api/config", apiHandler.Auth(apiHandler.GetConfig)).Methods(http.MethodGet)
	r.HandleFunc("/api/config", apiHandler.Auth(apiHandler.UpdateConfig)).Methods(http.MethodPost)

	// Prometheus metrics endpoint
	r.HandleFunc("/metrics", apiHandler.PrometheusMetrics).Methods(http.MethodGet)

	// Static files and web UI
	if cfg.DeveloperMode {
		r.PathPrefix("/static/").Handler(http.StripPrefix(cfg.UrlBasePath+"static/", http.FileServer(http.Dir("./static"))))
	} else {
		r.PathPrefix("/static/").Handler(http.StripPrefix(cfg.UrlBasePath, http.FileServer(common.NewStaticFSWrapper(static))))
	}

	// Serve index.html for SPA
	indexHtml := readIndexHtml(cfg.UrlBasePath, version, instanceUuid, cfg.DeveloperMode)
	r.PathPrefix("").HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		_, _ = w.Write(indexHtml)
	})

	router.PathPrefix("").Handler(http.RedirectHandler(cfg.UrlBasePath, http.StatusMovedPermanently))

	// Start HTTP server
	klog.Infof("Starting Deep-eBPF server on %s", cfg.ListenAddress)
	klog.Infof("Web interface available at: http://%s%s", cfg.ListenAddress, cfg.UrlBasePath)
	
	if err := http.ListenAndServe(cfg.ListenAddress, router); err != nil {
		klog.Exitf("Failed to start HTTP server: %v", err)
	}
}

func setAdminPassword() error {
	fmt.Print("Enter new admin password: ")
	password, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		return err
	}
	fmt.Println()
	
	// TODO: Implement password setting logic
	fmt.Printf("Setting password: %s\n", string(password))
	return nil
}

func readIndexHtml(basePath, version, instanceUuid string, devMode bool) []byte {
	// TODO: Implement index.html template rendering
	// This would read the index.html template and inject configuration
	html := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>Deep-eBPF Server</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <base href="%s">
</head>
<body>
    <div id="app">
        <h1>Deep-eBPF Server</h1>
        <p>Version: %s</p>
        <p>Instance: %s</p>
        <p>Function tracing dashboard loading...</p>
    </div>
    <script>
        window.CONFIG = {
            basePath: "%s",
            version: "%s",
            instanceUuid: "%s",
            devMode: %t
        };
    </script>
</body>
</html>`, basePath, version, instanceUuid, basePath, version, instanceUuid, devMode)
	
	return []byte(html)
}
