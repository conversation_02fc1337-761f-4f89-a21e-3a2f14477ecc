// Copyright 2022-2025 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package config

import (
	"bytes"
	"errors"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	commonconfig "github.com/prometheus/common/config"
	"github.com/prometheus/common/model"
	"github.com/prometheus/prometheus/discovery"
	_ "github.com/prometheus/prometheus/discovery/install" // Imported for registration side-effect
	"github.com/prometheus/prometheus/discovery/targetgroup"
	"github.com/prometheus/prometheus/model/relabel"
	"github.com/thanos-io/objstore/client"
	"gopkg.in/yaml.v3"
)

const (
	pprofMemory     string = "memory"
	pprofBlock      string = "block"
	pprofGoroutine  string = "goroutine"
	pprofMutex      string = "mutex"
	pprofProcessCPU string = "process_cpu"
)

// Config holds all the configuration information for Parca.
type Config struct {
	ObjectStorage *ObjectStorage  `yaml:"object_storage,omitempty"`
	ScrapeConfigs []*ScrapeConfig `yaml:"scrape_configs,omitempty"`
}

type ObjectStorage struct {
	Bucket *client.BucketConfig `yaml:"bucket,omitempty"`
}

// Validate returns an error if the config is not valid.
func (c *Config) Validate() error {
	if err := validation.ValidateStruct(c,
		validation.Field(&c.ObjectStorage, validation.Required, ObjectStorageValid),
		validation.Field(&c.ScrapeConfigs, ScrapeConfigsValid),
	); err != nil {
		return err
	}

	return nil
}

func trueValue() *bool {
	a := true
	return &a
}

func DefaultScrapeConfig() ScrapeConfig {
	return ScrapeConfig{
		ScrapeInterval: model.Duration(time.Second * 10),
		ScrapeTimeout:  model.Duration(time.Second * 0),
		Scheme:         "http",
		ProfilingConfig: &ProfilingConfig{
			PprofConfig: PprofConfig{
				pprofMemory: &PprofProfilingConfig{
					Enabled: trueValue(),
					Path:    "/debug/pprof/allocs",
				},
				pprofBlock: &PprofProfilingConfig{
					Enabled: trueValue(),
					Path:    "/debug/pprof/block",
				},
				pprofGoroutine: &PprofProfilingConfig{
					Enabled: trueValue(),
					Path:    "/debug/pprof/goroutine",
				},
				pprofMutex: &PprofProfilingConfig{
					Enabled: trueValue(),
					Path:    "/debug/pprof/mutex",
				},
				pprofProcessCPU: &PprofProfilingConfig{
					Enabled: trueValue(),
					Delta:   true,
					Path:    "/debug/pprof/profile",
				},
			},
		},
	}
}

func (c Config) String() string {
	b, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Sprintf("<error creating config string: %s>", err)
	}
	return string(b)
}

// SetDirectory joins any relative file paths with dir.
func (c *Config) SetDirectory(dir string) {
	for _, c := range c.ScrapeConfigs {
		c.SetDirectory(dir)
	}
}

// Load parses the YAML input s into a Config.
func Load(s string) (*Config, error) {
	cfg := &Config{}

	dec := yaml.NewDecoder(bytes.NewBuffer([]byte(s)))
	dec.KnownFields(true)
	if err := dec.Decode(cfg); err != nil {
		return nil, err
	}

	return cfg, nil
}

// LoadFile parses the given YAML file into a Config.
func LoadFile(filename string) (*Config, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	cfg, err := Load(string(content))
	if err != nil {
		return nil, fmt.Errorf("parsing YAML file %s: %v", filename, err)
	}
	cfg.SetDirectory(filepath.Dir(filename))
	return cfg, nil
}

// ScrapeConfig configures a scraping unit for conprof.
type ScrapeConfig struct {
	// Name of the section in the config
	JobName string `yaml:"job_name"`
	// A set of query parameters with which the target is scraped.
	Params url.Values `yaml:"params,omitempty"`
	// How frequently to scrape the targets of this scrape config.
	ScrapeInterval model.Duration `yaml:"scrape_interval,omitempty"`
	// The timeout for scraping targets of this config.
	ScrapeTimeout model.Duration `yaml:"scrape_timeout,omitempty"`
	// The URL scheme with which to fetch metrics from targets.
	Scheme string `yaml:"scheme,omitempty"`

	// NormalizedAddresses can be set to true if the addresses returned by the
	// endpoints have already been normalized.
	NormalizedAddresses bool `yaml:"normalized_addresses,omitempty"`

	ProfilingConfig *ProfilingConfig `yaml:"profiling_config,omitempty"`

	RelabelConfigs []*relabel.Config `yaml:"relabel_configs,omitempty"`
	// We cannot do proper Go type embedding below as the parser will then parse
	// values arbitrarily into the overflow maps of further-down types.
	ServiceDiscoveryConfigs discovery.Configs             `yaml:"-"`
	HTTPClientConfig        commonconfig.HTTPClientConfig `yaml:",inline"`
}

// SetDirectory joins any relative file paths with dir.
func (c *ScrapeConfig) SetDirectory(dir string) {
	c.ServiceDiscoveryConfigs.SetDirectory(dir)
	c.HTTPClientConfig.SetDirectory(dir)
}

// ServiceDiscoveryConfig configures lists of different service discovery mechanisms.
type ServiceDiscoveryConfig struct {
	// List of labeled target groups for this job.
	StaticConfigs []*targetgroup.Group `yaml:"static_configs,omitempty"`
}

type ProfilingConfig struct {
	PprofConfig PprofConfig `yaml:"pprof_config,omitempty"`
	PprofPrefix string      `yaml:"path_prefix,omitempty"`
}

type PprofConfig map[string]*PprofProfilingConfig

// UnmarshalYAML implements the yaml.Unmarshaler interface.
func (c *ScrapeConfig) UnmarshalYAML(unmarshal func(interface{}) error) error {
	defaults := DefaultScrapeConfig()
	unmarshalled := ScrapeConfig{
		ScrapeInterval: defaults.ScrapeInterval,
		ScrapeTimeout:  defaults.ScrapeTimeout,
		Scheme:         defaults.Scheme,
	}
	if err := discovery.UnmarshalYAMLWithInlineConfigs(&unmarshalled, unmarshal); err != nil {
		return err
	}

	if unmarshalled.ProfilingConfig == nil {
		unmarshalled.ProfilingConfig = defaults.ProfilingConfig
	} else if unmarshalled.ProfilingConfig.PprofConfig == nil {
		unmarshalled.ProfilingConfig.PprofConfig = defaults.ProfilingConfig.PprofConfig
	} else {
		// Merge unmarshalled config with defaults
		for pt, pc := range defaults.ProfilingConfig.PprofConfig {
			// nothing set yet so simply use the default
			if unmarshalled.ProfilingConfig.PprofConfig[pt] == nil {
				unmarshalled.ProfilingConfig.PprofConfig[pt] = pc
				continue
			}
			if unmarshalled.ProfilingConfig.PprofConfig[pt].Enabled == nil {
				unmarshalled.ProfilingConfig.PprofConfig[pt].Enabled = trueValue()
			}
			if unmarshalled.ProfilingConfig.PprofConfig[pt].Path == "" {
				unmarshalled.ProfilingConfig.PprofConfig[pt].Path = pc.Path
			}
		}
	}

	// If path prefix is specified, add to PprofConfig path
	if unmarshalled.ProfilingConfig.PprofPrefix != "" {
		for pt := range unmarshalled.ProfilingConfig.PprofConfig {
			unmarshalled.ProfilingConfig.PprofConfig[pt].Path = filepath.Join(unmarshalled.ProfilingConfig.PprofPrefix, unmarshalled.ProfilingConfig.PprofConfig[pt].Path)
		}
	}

	*c = unmarshalled

	if len(c.JobName) == 0 {
		return errors.New("job_name is empty")
	}

	// The UnmarshalYAML method of HTTPClientConfig is not being called because it's not a pointer.
	// We cannot make it a pointer as the parser panics for inlined pointer structs.
	// Thus we just do its validation here.
	if err := c.HTTPClientConfig.Validate(); err != nil {
		return err
	}

	// Check for users putting URLs in target groups.
	if len(c.RelabelConfigs) == 0 {
		if err := checkStaticTargets(c.ServiceDiscoveryConfigs); err != nil {
			return err
		}
	}

	for _, rlcfg := range c.RelabelConfigs {
		if rlcfg == nil {
			return errors.New("empty or null target relabeling rule in scrape config")
		}
	}

	// Validate the scrape and timeout internal configuration. When /debug/pprof/profile scraping
	// is enabled we need to make sure there is enough time to complete the scrape.
	if c.ScrapeTimeout == 0 {
		c.ScrapeTimeout = c.ScrapeInterval + model.Duration(3*time.Second)
	}
	if c.ScrapeTimeout <= c.ScrapeInterval {
		return fmt.Errorf("scrape timeout must be greater than the interval: %v", c.JobName)
	}

	if cfg, ok := c.ProfilingConfig.PprofConfig[pprofProcessCPU]; ok {
		if *cfg.Enabled && c.ScrapeTimeout < model.Duration(time.Second*2) {
			return fmt.Errorf("%v scrape_timeout must be at least 2 seconds in %v", pprofProcessCPU, c.JobName)
		}
	}

	return nil
}

func checkStaticTargets(configs discovery.Configs) error {
	for _, cfg := range configs {
		sc, ok := cfg.(discovery.StaticConfig)
		if !ok {
			continue
		}
		for _, tg := range sc {
			for _, t := range tg.Targets {
				if err := CheckTargetAddress(t[model.AddressLabel]); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

type PprofProfilingConfig struct {
	Enabled        *bool        `yaml:"enabled,omitempty"`
	Path           string       `yaml:"path,omitempty"`
	Delta          bool         `yaml:"delta,omitempty"`
	KeepSampleType []SampleType `yaml:"keep_sample_type,omitempty"`
	Seconds        int          `yaml:"seconds,omitempty"`
}

type SampleType struct {
	Type string `yaml:"type,omitempty"`
	Unit string `yaml:"unit,omitempty"`
}

// CheckTargetAddress checks if target address is valid.
func CheckTargetAddress(address model.LabelValue) error {
	// For now check for a URL, we may want to expand this later.
	if strings.Contains(string(address), "/") {
		return fmt.Errorf("%q is not a valid hostname", address)
	}
	return nil
}
