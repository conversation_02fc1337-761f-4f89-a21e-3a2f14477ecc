// @generated by protobuf-ts 2.9.4 with parameter generate_dependencies
// @generated from protobuf file "opentelemetry/proto/collector/profiles/v1/profiles_service.proto" (package "opentelemetry.proto.collector.profiles.v1", syntax proto3)
// tslint:disable
//
// Copyright 2019, OpenTelemetry Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//
// // This protofile is copied from
// https://github.com/open-telemetry/opentelemetry-proto-profile/blob/154f8715345b18bac436e4c55e014272cb0fd723/opentelemetry/proto/collector/profiles/v1
//
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { ResourceProfiles } from "../../../profiles/v1/profiles";
/**
 * @generated from protobuf message opentelemetry.proto.collector.profiles.v1.ExportProfilesServiceRequest
 */
export interface ExportProfilesServiceRequest {
    /**
     * An array of ResourceProfiles.
     * For data coming from a single resource this array will typically contain one
     * element. Intermediary nodes (such as OpenTelemetry Collector) that receive
     * data from multiple origins typically batch the data before forwarding further and
     * in that case this array will contain multiple elements.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.ResourceProfiles resource_profiles = 1;
     */
    resourceProfiles: ResourceProfiles[];
}
/**
 * @generated from protobuf message opentelemetry.proto.collector.profiles.v1.ExportProfilesServiceResponse
 */
export interface ExportProfilesServiceResponse {
    /**
     * The details of a partially successful export request.
     *
     * If the request is only partially accepted
     * (i.e. when the server accepts only parts of the data and rejects the rest)
     * the server MUST initialize the `partial_success` field and MUST
     * set the `rejected_<signal>` with the number of items it rejected.
     *
     * Servers MAY also make use of the `partial_success` field to convey
     * warnings/suggestions to senders even when the request was fully accepted.
     * In such cases, the `rejected_<signal>` MUST have a value of `0` and
     * the `error_message` MUST be non-empty.
     *
     * A `partial_success` message with an empty value (rejected_<signal> = 0 and
     * `error_message` = "") is equivalent to it not being set/present. Senders
     * SHOULD interpret it the same way as in the full success case.
     *
     * @generated from protobuf field: opentelemetry.proto.collector.profiles.v1.ExportProfilesPartialSuccess partial_success = 1;
     */
    partialSuccess?: ExportProfilesPartialSuccess;
}
/**
 * @generated from protobuf message opentelemetry.proto.collector.profiles.v1.ExportProfilesPartialSuccess
 */
export interface ExportProfilesPartialSuccess {
    /**
     * The number of rejected profiles.
     *
     * A `rejected_<signal>` field holding a `0` value indicates that the
     * request was fully accepted.
     *
     * @generated from protobuf field: int64 rejected_profiles = 1;
     */
    rejectedProfiles: bigint;
    /**
     * A developer-facing human-readable message in English. It should be used
     * either to explain why the server rejected parts of the data during a partial
     * success or to convey warnings/suggestions during a full success. The message
     * should offer guidance on how users can address such issues.
     *
     * error_message is an optional field. An error_message with an empty value
     * is equivalent to it not being set.
     *
     * @generated from protobuf field: string error_message = 2;
     */
    errorMessage: string;
}
// @generated message type with reflection information, may provide speed optimized methods
class ExportProfilesServiceRequest$Type extends MessageType<ExportProfilesServiceRequest> {
    constructor() {
        super("opentelemetry.proto.collector.profiles.v1.ExportProfilesServiceRequest", [
            { no: 1, name: "resource_profiles", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => ResourceProfiles }
        ]);
    }
    create(value?: PartialMessage<ExportProfilesServiceRequest>): ExportProfilesServiceRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.resourceProfiles = [];
        if (value !== undefined)
            reflectionMergePartial<ExportProfilesServiceRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ExportProfilesServiceRequest): ExportProfilesServiceRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated opentelemetry.proto.profiles.v1.ResourceProfiles resource_profiles */ 1:
                    message.resourceProfiles.push(ResourceProfiles.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ExportProfilesServiceRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated opentelemetry.proto.profiles.v1.ResourceProfiles resource_profiles = 1; */
        for (let i = 0; i < message.resourceProfiles.length; i++)
            ResourceProfiles.internalBinaryWrite(message.resourceProfiles[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.collector.profiles.v1.ExportProfilesServiceRequest
 */
export const ExportProfilesServiceRequest = new ExportProfilesServiceRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ExportProfilesServiceResponse$Type extends MessageType<ExportProfilesServiceResponse> {
    constructor() {
        super("opentelemetry.proto.collector.profiles.v1.ExportProfilesServiceResponse", [
            { no: 1, name: "partial_success", kind: "message", T: () => ExportProfilesPartialSuccess }
        ]);
    }
    create(value?: PartialMessage<ExportProfilesServiceResponse>): ExportProfilesServiceResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<ExportProfilesServiceResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ExportProfilesServiceResponse): ExportProfilesServiceResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* opentelemetry.proto.collector.profiles.v1.ExportProfilesPartialSuccess partial_success */ 1:
                    message.partialSuccess = ExportProfilesPartialSuccess.internalBinaryRead(reader, reader.uint32(), options, message.partialSuccess);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ExportProfilesServiceResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* opentelemetry.proto.collector.profiles.v1.ExportProfilesPartialSuccess partial_success = 1; */
        if (message.partialSuccess)
            ExportProfilesPartialSuccess.internalBinaryWrite(message.partialSuccess, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.collector.profiles.v1.ExportProfilesServiceResponse
 */
export const ExportProfilesServiceResponse = new ExportProfilesServiceResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ExportProfilesPartialSuccess$Type extends MessageType<ExportProfilesPartialSuccess> {
    constructor() {
        super("opentelemetry.proto.collector.profiles.v1.ExportProfilesPartialSuccess", [
            { no: 1, name: "rejected_profiles", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "error_message", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ExportProfilesPartialSuccess>): ExportProfilesPartialSuccess {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.rejectedProfiles = 0n;
        message.errorMessage = "";
        if (value !== undefined)
            reflectionMergePartial<ExportProfilesPartialSuccess>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ExportProfilesPartialSuccess): ExportProfilesPartialSuccess {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 rejected_profiles */ 1:
                    message.rejectedProfiles = reader.int64().toBigInt();
                    break;
                case /* string error_message */ 2:
                    message.errorMessage = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ExportProfilesPartialSuccess, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 rejected_profiles = 1; */
        if (message.rejectedProfiles !== 0n)
            writer.tag(1, WireType.Varint).int64(message.rejectedProfiles);
        /* string error_message = 2; */
        if (message.errorMessage !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.errorMessage);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.collector.profiles.v1.ExportProfilesPartialSuccess
 */
export const ExportProfilesPartialSuccess = new ExportProfilesPartialSuccess$Type();
/**
 * @generated ServiceType for protobuf service opentelemetry.proto.collector.profiles.v1.ProfilesService
 */
export const ProfilesService = new ServiceType("opentelemetry.proto.collector.profiles.v1.ProfilesService", [
    { name: "Export", options: {}, I: ExportProfilesServiceRequest, O: ExportProfilesServiceResponse }
]);
