// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {Select, useURLState} from '@parca/components';

import {
  FIELD_CUMULATIVE,
  FIELD_DIFF,
  FIELD_FUNCTION_NAME,
} from '../../../ProfileFlameGraph/FlameGraphArrow';
import {useProfileViewContext} from '../../context/ProfileViewContext';

const SortByDropdown = (): React.JSX.Element => {
  const [storeSortBy, setStoreSortBy] = useURLState('sort_by', {
    defaultValue: FIELD_FUNCTION_NAME,
  });

  const {compareMode} = useProfileViewContext();

  return (
    <div>
      <label className="text-sm">Sort by</label>
      <Select
        className="!px-3"
        items={[
          {
            key: FIELD_FUNCTION_NAME,
            disabled: false,
            element: {
              active: <>Function</>,
              expanded: (
                <>
                  <span>Function</span>
                </>
              ),
            },
          },
          {
            key: FIELD_CUMULATIVE,
            disabled: false,
            element: {
              active: <>Cumulative</>,
              expanded: (
                <>
                  <span>Cumulative</span>
                </>
              ),
            },
          },
          {
            key: FIELD_DIFF,
            disabled: !compareMode,
            element: {
              active: <>Diff</>,
              expanded: (
                <>
                  <span>Diff</span>
                </>
              ),
            },
          },
        ]}
        selectedKey={storeSortBy as string}
        onSelection={key => setStoreSortBy(key)}
        placeholder={'Sort By'}
        primary={false}
        disabled={false}
        id="h-sort-by-filter"
      />
    </div>
  );
};

export default SortByDropdown;
