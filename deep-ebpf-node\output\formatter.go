package output

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/mexyusef/deep-ebpf-node/ebpftracer"
	"github.com/mexyusef/deep-ebpf-node/runtime"
	"github.com/mexyusef/deep-ebpf-node/symbols"
)

// Formatter interface for different output formats
type Formatter interface {
	FormatEvent(event ebpftracer.Event) error
}

// HumanFormatter provides human-readable output matching client requirements with runtime detection and symbol resolution
type HumanFormatter struct {
	output          *os.File
	runtimeDetector *runtime.Detector
	symbolResolver  *symbols.SymbolResolver
}

// NewHumanFormatter creates a new human-readable formatter
func NewHumanFormatter() *HumanFormatter {
	formatter := &HumanFormatter{
		output:          os.Stdout,
		runtimeDetector: runtime.NewDetector(),
		symbolResolver:  symbols.NewSymbolResolver(),
	}

	// Load kernel symbols in background
	go func() {
		if err := formatter.symbolResolver.LoadKernelSymbols(); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to load kernel symbols: %v\n", err)
		}
	}()

	return formatter
}

// FormatEvent formats an event in human-readable format
func (f *HumanFormatter) FormatEvent(event ebpftracer.Event) error {
	// Only format function tracing events
	if event.Type != ebpftracer.EventTypeFunctionEntry && 
	   event.Type != ebpftracer.EventTypeFunctionExit {
		return nil
	}

	if event.FunctionData == nil {
		return nil
	}

	// Format timestamp
	timestamp := time.Unix(0, int64(event.Timestamp)).Format("15:04:05.000000")
	
	// Detect runtime for this process
	runtimeInfo := f.runtimeDetector.DetectRuntime(event.Pid)
	processName := runtimeInfo.MainModule
	if processName == "" {
		processName = fmt.Sprintf("pid_%d", event.Pid)
	}

	// Determine event type string
	eventTypeStr := "entry"
	if event.Type == ebpftracer.EventTypeFunctionExit {
		eventTypeStr = "exit"
	}

	// Resolve function name using symbol resolver
	binaryPath := ""
	if runtimeInfo.Interpreter != "" {
		binaryPath = runtimeInfo.Interpreter
	}

	functionName := f.symbolResolver.GetFunctionName(event.FunctionData.FunctionAddr, binaryPath)

	// Fallback to provided name if symbol resolution didn't work
	if functionName == fmt.Sprintf("func_0x%x", event.FunctionData.FunctionAddr) && event.FunctionData.FunctionName != "" {
		functionName = event.FunctionData.FunctionName
	}
	
	// Determine function space (kernel vs user)
	functionSpace := "user"
	if f.symbolResolver.IsKernelAddress(event.FunctionData.FunctionAddr) {
		functionSpace = "kernel"
	}

	// Format runtime information
	runtimeStr := string(runtimeInfo.Runtime)
	if runtimeInfo.Version != "" {
		runtimeStr += " " + runtimeInfo.Version
	}

	// Format the output according to client requirements with enhanced runtime detection
	output := fmt.Sprintf(`┌─ [%s] %s:%d/%d on CPU %d [%s]
├─ Function: %s [%s]
├─ Address:  %s
├─ PID:     %d
├─ Binary:  %s
├─ Runtime: %s`,
		timestamp, processName, event.Pid, event.Pid, 0, eventTypeStr,
		functionName, functionSpace,
		f.symbolResolver.FormatAddress(event.FunctionData.FunctionAddr, binaryPath),
		event.Pid, processName, runtimeStr)
	
	// Add duration for exit events
	if event.Type == ebpftracer.EventTypeFunctionExit && event.Duration > 0 {
		output += fmt.Sprintf("\n├─ Duration: %s", formatDuration(event.Duration))
	}
	
	// Add arguments
	if len(event.FunctionData.Arguments) > 0 {
		output += "\n├─ Arguments:"
		registers := []string{"RDI", "RSI", "RDX", "RCX", "R8", "R9"}
		for i, arg := range event.FunctionData.Arguments {
			if i >= len(registers) {
				break
			}
			if i == len(event.FunctionData.Arguments)-1 {
				output += fmt.Sprintf("\n│  └─ [%d] arg%d=0x%x (%s)", i, i, arg, registers[i])
			} else {
				output += fmt.Sprintf("\n│  ├─ [%d] arg%d=0x%x (%s)", i, i, arg, registers[i])
			}
		}
	}
	
	// Add return value for exit events
	if event.Type == ebpftracer.EventTypeFunctionExit {
		output += fmt.Sprintf("\n├─ Return: 0x%x", event.FunctionData.ReturnValue)
	}
	
	// Add memory analysis (simplified)
	if len(event.FunctionData.Arguments) > 0 {
		output += "\n├─ Memory:"
		for i, arg := range event.FunctionData.Arguments {
			if arg > 0x1000 && arg < 0x7fffffffffff { // Likely pointer
				output += fmt.Sprintf("\n│  ├─ Ptr[%d]: 0x%x (user space)", i, arg)
			}
		}
	}
	
	// Add stack trace if available
	if event.FunctionData.StackDepth > 0 {
		output += fmt.Sprintf("\n├─ Stack ID: %d (call stack available)", event.FunctionData.StackDepth)
		if event.FunctionData.StackDepth <= 5 { // Show first few stack entries
			output += "\n├─ Stack Trace:"
			for i := uint32(0); i < event.FunctionData.StackDepth && i < 5; i++ {
				if i < uint32(len(event.FunctionData.StackTrace)) {
					if i == event.FunctionData.StackDepth-1 || i == 4 {
						output += fmt.Sprintf("\n│  └─ [%d] 0x%x", i, event.FunctionData.StackTrace[i])
					} else {
						output += fmt.Sprintf("\n│  ├─ [%d] 0x%x", i, event.FunctionData.StackTrace[i])
					}
				}
			}
		}
	}
	
	// Close the box
	output += "\n└─────────────────────────────────────────────────────────────────────────────────\n"
	
	// Write to output
	_, err := f.output.WriteString(output)
	return err
}

// formatDuration formats duration in a human-readable way
func formatDuration(d time.Duration) string {
	if d < time.Microsecond {
		return fmt.Sprintf("%dns", d.Nanoseconds())
	} else if d < time.Millisecond {
		return fmt.Sprintf("%.3fµs", float64(d.Nanoseconds())/1000.0)
	} else if d < time.Second {
		return fmt.Sprintf("%.3fms", float64(d.Nanoseconds())/1000000.0)
	} else {
		return fmt.Sprintf("%.3fs", d.Seconds())
	}
}

// JSONFormatter provides JSON output
type JSONFormatter struct {
	output *os.File
}

// NewJSONFormatter creates a new JSON formatter
func NewJSONFormatter() *JSONFormatter {
	return &JSONFormatter{
		output: os.Stdout,
	}
}

// FormatEvent formats an event as JSON
func (f *JSONFormatter) FormatEvent(event ebpftracer.Event) error {
	// Only format function tracing events
	if event.Type != ebpftracer.EventTypeFunctionEntry && 
	   event.Type != ebpftracer.EventTypeFunctionExit {
		return nil
	}

	jsonEvent := map[string]interface{}{
		"timestamp":   time.Unix(0, int64(event.Timestamp)).Format(time.RFC3339Nano),
		"type":        event.Type.String(),
		"pid":         event.Pid,
		"duration_ns": event.Duration.Nanoseconds(),
	}
	
	if event.FunctionData != nil {
		jsonEvent["function"] = map[string]interface{}{
			"name":         event.FunctionData.FunctionName,
			"address":      fmt.Sprintf("0x%x", event.FunctionData.FunctionAddr),
			"return_addr":  fmt.Sprintf("0x%x", event.FunctionData.ReturnAddr),
			"arguments":    formatArgumentsAsStrings(event.FunctionData.Arguments),
			"return_value": fmt.Sprintf("0x%x", event.FunctionData.ReturnValue),
			"stack_depth":  event.FunctionData.StackDepth,
		}
		
		if len(event.FunctionData.StackTrace) > 0 {
			stackTrace := make([]string, len(event.FunctionData.StackTrace))
			for i, addr := range event.FunctionData.StackTrace {
				stackTrace[i] = fmt.Sprintf("0x%x", addr)
			}
			jsonEvent["function"].(map[string]interface{})["stack_trace"] = stackTrace
		}
	}
	
	data, err := json.Marshal(jsonEvent)
	if err != nil {
		return err
	}
	
	_, err = f.output.Write(append(data, '\n'))
	return err
}

// formatArgumentsAsStrings converts arguments to string representation
func formatArgumentsAsStrings(args []uint64) []string {
	result := make([]string, len(args))
	for i, arg := range args {
		result[i] = fmt.Sprintf("0x%x", arg)
	}
	return result
}

// ServerFormatter sends data to deep-ebpf-server
type ServerFormatter struct {
	endpoint string
	agentID  string
}

// NewServerFormatter creates a new server formatter
func NewServerFormatter(endpoint, agentID string) *ServerFormatter {
	return &ServerFormatter{
		endpoint: endpoint,
		agentID:  agentID,
	}
}

// FormatEvent sends an event to the deep-ebpf-server
func (f *ServerFormatter) FormatEvent(event ebpftracer.Event) error {
	// Only format function tracing events
	if event.Type != ebpftracer.EventTypeFunctionEntry &&
	   event.Type != ebpftracer.EventTypeFunctionExit {
		return nil
	}

	if event.FunctionData == nil {
		return nil
	}

	// Convert to server format
	trace := map[string]interface{}{
		"agent_id":      f.agentID,
		"timestamp":     time.Unix(0, int64(event.Timestamp)).Format(time.RFC3339Nano),
		"type":          event.Type.String(),
		"pid":           event.Pid,
		"process_name":  fmt.Sprintf("pid_%d", event.Pid),
		"function_name": event.FunctionData.FunctionName,
		"function_addr": event.FunctionData.FunctionAddr,
		"duration":      event.Duration.Nanoseconds(),
		"arguments":     event.FunctionData.Arguments,
		"return_value":  event.FunctionData.ReturnValue,
		"stack_trace":   event.FunctionData.StackTrace,
		"metadata":      map[string]interface{}{
			"stack_depth": event.FunctionData.StackDepth,
		},
	}

	// Send to server (simplified - in real implementation would batch and handle errors)
	traces := []interface{}{trace}
	data, err := json.Marshal(traces)
	if err != nil {
		return err
	}

	// TODO: Implement HTTP POST to server
	// For now, just print that we would send it
	fmt.Printf("Would send to %s: %s\n", f.endpoint, string(data))

	return nil
}

// OTLPFormatter provides OpenTelemetry output (placeholder)
type OTLPFormatter struct {
	endpoint string
}

// NewOTLPFormatter creates a new OTLP formatter
func NewOTLPFormatter(endpoint string) *OTLPFormatter {
	return &OTLPFormatter{
		endpoint: endpoint,
	}
}

// FormatEvent formats an event for OTLP export (placeholder)
func (f *OTLPFormatter) FormatEvent(event ebpftracer.Event) error {
	// TODO: Implement OTLP export
	// This would convert function trace events to OpenTelemetry spans
	return fmt.Errorf("OTLP export not implemented yet")
}

// FileFormatter wraps another formatter and writes to a file
type FileFormatter struct {
	underlying Formatter
	file       *os.File
}

// NewFileFormatter creates a formatter that writes to a file
func NewFileFormatter(filename string, underlying Formatter) (*FileFormatter, error) {
	file, err := os.Create(filename)
	if err != nil {
		return nil, err
	}
	
	// Update the underlying formatter to use the file
	switch f := underlying.(type) {
	case *HumanFormatter:
		f.output = file
	case *JSONFormatter:
		f.output = file
	}
	
	return &FileFormatter{
		underlying: underlying,
		file:       file,
	}, nil
}

// FormatEvent formats an event using the underlying formatter
func (f *FileFormatter) FormatEvent(event ebpftracer.Event) error {
	return f.underlying.FormatEvent(event)
}

// Close closes the file
func (f *FileFormatter) Close() error {
	return f.file.Close()
}
