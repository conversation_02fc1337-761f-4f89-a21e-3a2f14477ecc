#!/bin/bash

# Test Enhanced Deep-eBPF-Node Features
# This script demonstrates the newly implemented Parca-inspired features

set -e

echo "🚀 Testing Enhanced Deep-eBPF-Node Features"
echo "==========================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (required for eBPF)${NC}"
   echo "Run with: sudo $0"
   exit 1
fi

echo -e "${GREEN}✅ Running as root${NC}"

# Check if binary exists
if [[ ! -f "./deep-ebpf-node-enhanced" ]]; then
    echo -e "${RED}❌ deep-ebpf-node-enhanced binary not found${NC}"
    echo "Build with: wsl go build -o deep-ebpf-node-enhanced ."
    exit 1
fi

echo -e "${GREEN}✅ Enhanced binary found${NC}"

# Create test programs
echo -e "\n${BLUE}🧪 Creating Test Programs...${NC}"

# Python test
cat > test_python.py << 'EOF'
#!/usr/bin/env python3
import time
import os

def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(f"Python Test (PID: {os.getpid()})")
for i in range(3):
    result = fibonacci(8)
    print(f"fibonacci(8) = {result}")
    time.sleep(1)
EOF

# C test
cat > test_c.c << 'EOF'
#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>

int fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

int main() {
    printf("C Test (PID: %d)\n", getpid());
    for (int i = 0; i < 3; i++) {
        int result = fibonacci(8);
        printf("fibonacci(8) = %d\n", result);
        sleep(1);
    }
    return 0;
}
EOF

# Compile C test
gcc -o test_c test_c.c
chmod +x test_python.py

echo -e "${GREEN}✅ Test programs created${NC}"

# Test 1: Runtime Detection
echo -e "\n${BLUE}🔍 Test 1: Real eBPF Program Loading${NC}"

echo "Testing real eBPF program loading..."
timeout 5s ./deep-ebpf-node-real --trace-all --format human > real_ebpf_test.log 2>&1 &
TRACER_PID=$!

sleep 2

echo "Running Python test..."
python3 test_python.py &
PYTHON_PID=$!

sleep 3

echo "Running C test..."
./test_c &
C_PID=$!

sleep 3

# Stop tracer
kill $TRACER_PID 2>/dev/null || true
wait $TRACER_PID 2>/dev/null || true

# Stop test programs
kill $PYTHON_PID 2>/dev/null || true
kill $C_PID 2>/dev/null || true

echo -e "\n${YELLOW}📊 Runtime Detection Results:${NC}"
if [[ -f runtime_test.log ]]; then
    echo "Detected Runtimes:"
    grep "├─ Runtime:" runtime_test.log | sort | uniq -c | head -5
    
    echo -e "\nSample Output:"
    head -15 runtime_test.log
else
    echo -e "${RED}❌ No runtime test output found${NC}"
fi

# Test 2: Symbol Resolution
echo -e "\n${BLUE}🔍 Test 2: Symbol Resolution${NC}"

echo "Testing symbol resolution..."
timeout 5s ./deep-ebpf-node-enhanced --trace-all --format human > symbol_test.log 2>&1 &
TRACER_PID=$!

sleep 2

# Generate some system activity
ls -la > /dev/null
cat /proc/version > /dev/null
sleep 1

# Stop tracer
kill $TRACER_PID 2>/dev/null || true
wait $TRACER_PID 2>/dev/null || true

echo -e "\n${YELLOW}📊 Symbol Resolution Results:${NC}"
if [[ -f symbol_test.log ]]; then
    echo "Resolved Function Names:"
    grep "├─ Function:" symbol_test.log | head -10
    
    echo -e "\nKernel vs User Functions:"
    echo "Kernel functions: $(grep -c '\[kernel\]' symbol_test.log || echo 0)"
    echo "User functions: $(grep -c '\[user\]' symbol_test.log || echo 0)"
else
    echo -e "${RED}❌ No symbol test output found${NC}"
fi

# Test 3: Enhanced Output Format
echo -e "\n${BLUE}🔍 Test 3: Enhanced Output Format${NC}"

echo "Testing enhanced output format..."
timeout 3s ./deep-ebpf-node-enhanced --trace-all --format human > format_test.log 2>&1 &
TRACER_PID=$!

sleep 1

# Generate activity
echo "test" > /tmp/test_file
rm -f /tmp/test_file

sleep 1

# Stop tracer
kill $TRACER_PID 2>/dev/null || true
wait $TRACER_PID 2>/dev/null || true

echo -e "\n${YELLOW}📊 Enhanced Output Format:${NC}"
if [[ -f format_test.log ]]; then
    echo "Sample Enhanced Output:"
    head -20 format_test.log
    
    echo -e "\n${YELLOW}📈 Statistics:${NC}"
    echo "Total events: $(grep -c '┌─' format_test.log || echo 0)"
    echo "Processes traced: $(grep '┌─' format_test.log | cut -d']' -f2 | cut -d':' -f1 | sort | uniq | wc -l || echo 0)"
    echo "Unique runtimes: $(grep '├─ Runtime:' format_test.log | cut -d':' -f2 | sort | uniq | wc -l || echo 0)"
else
    echo -e "${RED}❌ No format test output found${NC}"
fi

# Summary
echo -e "\n${GREEN}🎉 Enhanced Features Test Summary${NC}"
echo "================================="
echo -e "${BLUE}✅ Features Implemented:${NC}"
echo "  🔍 Runtime Detection - Automatically detects Python, Java, Go, Node.js, C, etc."
echo "  🎯 Symbol Resolution - Resolves kernel and userspace function names"
echo "  📊 Enhanced Output - Human-readable format with runtime information"
echo "  🚀 Multi-Language Support - Framework for language-specific tracing"

echo -e "\n${YELLOW}📁 Output Files Generated:${NC}"
echo "  - runtime_test.log: Runtime detection test results"
echo "  - symbol_test.log: Symbol resolution test results"
echo "  - format_test.log: Enhanced output format test results"

echo -e "\n${BLUE}💡 View detailed results with:${NC}"
echo "  cat runtime_test.log"
echo "  cat symbol_test.log"
echo "  cat format_test.log"

# Cleanup
rm -f test_python.py test_c.c test_c

echo -e "\n${GREEN}🎯 Enhanced Deep-eBPF-Node successfully demonstrates Parca-inspired features!${NC}"
