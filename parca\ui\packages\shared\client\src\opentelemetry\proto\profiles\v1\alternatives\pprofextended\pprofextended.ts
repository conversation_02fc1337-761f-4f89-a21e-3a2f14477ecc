// @generated by protobuf-ts 2.9.4 with parameter generate_dependencies
// @generated from protobuf file "opentelemetry/proto/profiles/v1/alternatives/pprofextended/pprofextended.proto" (package "opentelemetry.proto.profiles.v1.alternatives.pprofextended", syntax proto3)
// tslint:disable
//
// Profile is a common stacktrace profile format.
//
// Measurements represented with this format should follow the
// following conventions:
//
// - Consumers should treat unset optional fields as if they had been
//   set with their default value.
//
// - When possible, measurements should be stored in "unsampled" form
//   that is most useful to humans.  There should be enough
//   information present to determine the original sampled values.
//
// - On-disk, the serialized proto must be gzip-compressed.
//
// - The profile is represented as a set of samples, where each sample
//   references a sequence of locations, and where each location belongs
//   to a mapping.
// - There is a N->1 relationship from sample.location_id entries to
//   locations. For every sample.location_id entry there must be a
//   unique Location with that index.
// - There is an optional N->1 relationship from locations to
//   mappings. For every nonzero Location.mapping_id there must be a
//   unique Mapping with that index.
//
//
// This protofile is copied from https://github.com/open-telemetry/oteps/pull/239.
//
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { KeyValue } from "../../../../common/v1/common";
/**
 * Represents a complete profile, including sample types, samples,
 * mappings to binaries, locations, functions, string table, and additional metadata.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Profile
 */
export interface Profile {
    /**
     * A description of the samples associated with each Sample.value.
     * For a cpu profile this might be:
     *   [["cpu","nanoseconds"]] or [["wall","seconds"]] or [["syscall","count"]]
     * For a heap profile, this might be:
     *   [["allocations","count"], ["space","bytes"]],
     * If one of the values represents the number of events represented
     * by the sample, by convention it should be at index 0 and use
     * sample_type.unit == "count".
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType sample_type = 1;
     */
    sampleType: ValueType[];
    /**
     * The set of samples recorded in this profile.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Sample sample = 2;
     */
    sample: Sample[];
    /**
     * Mapping from address ranges to the image/binary/library mapped
     * into that address range.  mapping[0] will be the main binary.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Mapping mapping = 3;
     */
    mapping: Mapping[];
    /**
     * Locations referenced by samples via location_indices.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Location location = 4;
     */
    location: Location[];
    /**
     * Array of locations referenced by samples.
     *
     * @generated from protobuf field: repeated int64 location_indices = 15;
     */
    locationIndices: bigint[];
    /**
     * Functions referenced by locations.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Function function = 5;
     */
    function: Function[];
    /**
     * Lookup table for attributes.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.common.v1.KeyValue attribute_table = 16;
     */
    attributeTable: KeyValue[];
    /**
     * Represents a mapping between Attribute Keys and Units.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.AttributeUnit attribute_units = 17;
     */
    attributeUnits: AttributeUnit[];
    /**
     * Lookup table for links.
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Link link_table = 18;
     */
    linkTable: Link[];
    /**
     * A common table for strings referenced by various messages.
     * string_table[0] must always be "".
     *
     * @generated from protobuf field: repeated string string_table = 6;
     */
    stringTable: string[];
    /**
     * frames with Function.function_name fully matching the following
     * regexp will be dropped from the samples, along with their successors.
     *
     * @generated from protobuf field: int64 drop_frames = 7;
     */
    dropFrames: bigint; // Index into string table.
    /**
     * frames with Function.function_name fully matching the following
     * regexp will be kept, even if it matches drop_frames.
     *
     * @generated from protobuf field: int64 keep_frames = 8;
     */
    keepFrames: bigint; // Index into string table.
    /**
     * The following fields are informational, do not affect
     * interpretation of results.
     * Time of collection (UTC) represented as nanoseconds past the epoch.
     *
     * @generated from protobuf field: int64 time_nanos = 9;
     */
    timeNanos: bigint;
    /**
     * Duration of the profile, if a duration makes sense.
     *
     * @generated from protobuf field: int64 duration_nanos = 10;
     */
    durationNanos: bigint;
    /**
     * The kind of events between sampled occurrences.
     * e.g [ "cpu","cycles" ] or [ "heap","bytes" ]
     *
     * @generated from protobuf field: opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType period_type = 11;
     */
    periodType?: ValueType;
    /**
     * The number of events between sampled occurrences.
     *
     * @generated from protobuf field: int64 period = 12;
     */
    period: bigint;
    /**
     * Free-form text associated with the profile. The text is displayed as is
     * to the user by the tools that read profiles (e.g. by pprof). This field
     * should not be used to store any machine-readable information, it is only
     * for human-friendly content. The profile must stay functional if this field
     * is cleaned.
     *
     * @generated from protobuf field: repeated int64 comment = 13;
     */
    comment: bigint[]; // Indices into string table.
    /**
     * Index into the string table of the type of the preferred sample
     * value. If unset, clients should default to the last sample value.
     *
     * @generated from protobuf field: int64 default_sample_type = 14;
     */
    defaultSampleType: bigint;
}
/**
 * Represents a mapping between Attribute Keys and Units.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.AttributeUnit
 */
export interface AttributeUnit {
    /**
     * Index into string table.
     *
     * @generated from protobuf field: int64 attribute_key = 1;
     */
    attributeKey: bigint;
    /**
     * Index into string table.
     *
     * @generated from protobuf field: int64 unit = 2;
     */
    unit: bigint;
}
/**
 * A pointer from a profile Sample to a trace Span.
 * Connects a profile sample to a trace span, identified by unique trace and span IDs.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Link
 */
export interface Link {
    /**
     * A unique identifier of a trace that this linked span is part of. The ID is a
     * 16-byte array.
     *
     * @generated from protobuf field: bytes trace_id = 1;
     */
    traceId: Uint8Array;
    /**
     * A unique identifier for the linked span. The ID is an 8-byte array.
     *
     * @generated from protobuf field: bytes span_id = 2;
     */
    spanId: Uint8Array;
}
/**
 * ValueType describes the type and units of a value, with an optional aggregation temporality.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType
 */
export interface ValueType {
    /**
     * @generated from protobuf field: int64 type = 1;
     */
    type: bigint; // Index into string table.
    /**
     * @generated from protobuf field: int64 unit = 2;
     */
    unit: bigint; // Index into string table.
    /**
     * @generated from protobuf field: opentelemetry.proto.profiles.v1.alternatives.pprofextended.AggregationTemporality aggregation_temporality = 3;
     */
    aggregationTemporality: AggregationTemporality;
}
/**
 * Each Sample records values encountered in some program
 * context. The program context is typically a stack trace, perhaps
 * augmented with auxiliary information like the thread-id, some
 * indicator of a higher level request being handled etc.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Sample
 */
export interface Sample {
    /**
     * The indices recorded here correspond to locations in Profile.location.
     * The leaf is at location_index[0]. [deprecated, superseded by locations_start_index / locations_length]
     *
     * @generated from protobuf field: repeated uint64 location_index = 1;
     */
    locationIndex: bigint[];
    /**
     * locations_start_index along with locations_length refers to to a slice of locations in Profile.location.
     * Supersedes location_index.
     *
     * @generated from protobuf field: uint64 locations_start_index = 7;
     */
    locationsStartIndex: bigint;
    /**
     * locations_length along with locations_start_index refers to a slice of locations in Profile.location.
     * Supersedes location_index.
     *
     * @generated from protobuf field: uint64 locations_length = 8;
     */
    locationsLength: bigint;
    /**
     * A 128bit id that uniquely identifies this stacktrace, globally. Index into string table. [optional]
     *
     * @generated from protobuf field: uint32 stacktrace_id_index = 9;
     */
    stacktraceIdIndex: number;
    /**
     * The type and unit of each value is defined by the corresponding
     * entry in Profile.sample_type. All samples must have the same
     * number of values, the same as the length of Profile.sample_type.
     * When aggregating multiple samples into a single sample, the
     * result has a list of values that is the element-wise sum of the
     * lists of the originals.
     *
     * @generated from protobuf field: repeated int64 value = 2;
     */
    value: bigint[];
    /**
     * label includes additional context for this sample. It can include
     * things like a thread id, allocation size, etc.
     *
     * NOTE: While possible, having multiple values for the same label key is
     * strongly discouraged and should never be used. Most tools (e.g. pprof) do
     * not have good (or any) support for multi-value labels. And an even more
     * discouraged case is having a string label and a numeric label of the same
     * name on a sample.  Again, possible to express, but should not be used.
     * [deprecated, superseded by attributes]
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Label label = 3;
     */
    label: Label[];
    /**
     * References to attributes in Profile.attribute_table. [optional]
     *
     * @generated from protobuf field: repeated uint64 attributes = 10;
     */
    attributes: bigint[];
    /**
     * Reference to link in Profile.link_table. [optional]
     *
     * @generated from protobuf field: uint64 link = 12;
     */
    link: bigint;
    /**
     * Timestamps associated with Sample represented in ms. These timestamps are expected
     * to fall within the Profile's time range. [optional]
     *
     * @generated from protobuf field: repeated uint64 timestamps = 13;
     */
    timestamps: bigint[];
}
/**
 * Provides additional context for a sample,
 * such as thread ID or allocation size, with optional units. [deprecated]
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Label
 */
export interface Label {
    /**
     * @generated from protobuf field: int64 key = 1;
     */
    key: bigint; // Index into string table
    /**
     * At most one of the following must be present
     *
     * @generated from protobuf field: int64 str = 2;
     */
    str: bigint; // Index into string table
    /**
     * @generated from protobuf field: int64 num = 3;
     */
    num: bigint;
    /**
     * Should only be present when num is present.
     * Specifies the units of num.
     * Use arbitrary string (for example, "requests") as a custom count unit.
     * If no unit is specified, consumer may apply heuristic to deduce the unit.
     * Consumers may also  interpret units like "bytes" and "kilobytes" as memory
     * units and units like "seconds" and "nanoseconds" as time units,
     * and apply appropriate unit conversions to these.
     *
     * @generated from protobuf field: int64 num_unit = 4;
     */
    numUnit: bigint; // Index into string table
}
/**
 * Describes the mapping of a binary in memory, including its address range,
 * file offset, and metadata like build ID
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Mapping
 */
export interface Mapping {
    /**
     * Unique nonzero id for the mapping. [deprecated]
     *
     * @generated from protobuf field: uint64 id = 1;
     */
    id: bigint;
    /**
     * Address at which the binary (or DLL) is loaded into memory.
     *
     * @generated from protobuf field: uint64 memory_start = 2;
     */
    memoryStart: bigint;
    /**
     * The limit of the address range occupied by this mapping.
     *
     * @generated from protobuf field: uint64 memory_limit = 3;
     */
    memoryLimit: bigint;
    /**
     * Offset in the binary that corresponds to the first mapped address.
     *
     * @generated from protobuf field: uint64 file_offset = 4;
     */
    fileOffset: bigint;
    /**
     * The object this entry is loaded from.  This can be a filename on
     * disk for the main binary and shared libraries, or virtual
     * abstractions like "[vdso]".
     *
     * @generated from protobuf field: int64 filename = 5;
     */
    filename: bigint; // Index into string table
    /**
     * A string that uniquely identifies a particular program version
     * with high probability. E.g., for binaries generated by GNU tools,
     * it could be the contents of the .note.gnu.build-id field.
     *
     * @generated from protobuf field: int64 build_id = 6;
     */
    buildId: bigint; // Index into string table
    /**
     * Specifies the kind of build id. See BuildIdKind enum for more details [optional]
     *
     * @generated from protobuf field: opentelemetry.proto.profiles.v1.alternatives.pprofextended.BuildIdKind build_id_kind = 11;
     */
    buildIdKind: BuildIdKind;
    /**
     * References to attributes in Profile.attribute_table. [optional]
     *
     * @generated from protobuf field: repeated uint64 attributes = 12;
     */
    attributes: bigint[];
    /**
     * The following fields indicate the resolution of symbolic info.
     *
     * @generated from protobuf field: bool has_functions = 7;
     */
    hasFunctions: boolean;
    /**
     * @generated from protobuf field: bool has_filenames = 8;
     */
    hasFilenames: boolean;
    /**
     * @generated from protobuf field: bool has_line_numbers = 9;
     */
    hasLineNumbers: boolean;
    /**
     * @generated from protobuf field: bool has_inline_frames = 10;
     */
    hasInlineFrames: boolean;
}
/**
 * Describes function and line table debug information.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Location
 */
export interface Location {
    /**
     * Unique nonzero id for the location.  A profile could use
     * instruction addresses or any integer sequence as ids. [deprecated]
     *
     * @generated from protobuf field: uint64 id = 1;
     */
    id: bigint;
    /**
     * The index of the corresponding profile.Mapping for this location.
     * It can be unset if the mapping is unknown or not applicable for
     * this profile type.
     *
     * @generated from protobuf field: uint64 mapping_index = 2;
     */
    mappingIndex: bigint;
    /**
     * The instruction address for this location, if available.  It
     * should be within [Mapping.memory_start...Mapping.memory_limit]
     * for the corresponding mapping. A non-leaf address may be in the
     * middle of a call instruction. It is up to display tools to find
     * the beginning of the instruction if necessary.
     *
     * @generated from protobuf field: uint64 address = 3;
     */
    address: bigint;
    /**
     * Multiple line indicates this location has inlined functions,
     * where the last entry represents the caller into which the
     * preceding entries were inlined.
     *
     * E.g., if memcpy() is inlined into printf:
     *    line[0].function_name == "memcpy"
     *    line[1].function_name == "printf"
     *
     * @generated from protobuf field: repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Line line = 4;
     */
    line: Line[];
    /**
     * Provides an indication that multiple symbols map to this location's
     * address, for example due to identical code folding by the linker. In that
     * case the line information above represents one of the multiple
     * symbols. This field must be recomputed when the symbolization state of the
     * profile changes.
     *
     * @generated from protobuf field: bool is_folded = 5;
     */
    isFolded: boolean;
    /**
     * Type of frame (e.g. kernel, native, python, hotspot, php). Index into string table.
     *
     * @generated from protobuf field: uint32 type_index = 6;
     */
    typeIndex: number;
    /**
     * References to attributes in Profile.attribute_table. [optional]
     *
     * @generated from protobuf field: repeated uint64 attributes = 7;
     */
    attributes: bigint[];
}
/**
 * Details a specific line in a source code, linked to a function.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Line
 */
export interface Line {
    /**
     * The index of the corresponding profile.Function for this line.
     *
     * @generated from protobuf field: uint64 function_index = 1;
     */
    functionIndex: bigint;
    /**
     * Line number in source code.
     *
     * @generated from protobuf field: int64 line = 2;
     */
    line: bigint;
    /**
     * Column number in source code.
     *
     * @generated from protobuf field: int64 column = 3;
     */
    column: bigint;
}
/**
 * Describes a function, including its human-readable name, system name,
 * source file, and starting line number in the source.
 *
 * @generated from protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Function
 */
export interface Function {
    /**
     * Unique nonzero id for the function. [deprecated]
     *
     * @generated from protobuf field: uint64 id = 1;
     */
    id: bigint;
    /**
     * Name of the function, in human-readable form if available.
     *
     * @generated from protobuf field: int64 name = 2;
     */
    name: bigint; // Index into string table
    /**
     * Name of the function, as identified by the system.
     * For instance, it can be a C++ mangled name.
     *
     * @generated from protobuf field: int64 system_name = 3;
     */
    systemName: bigint; // Index into string table
    /**
     * Source file containing the function.
     *
     * @generated from protobuf field: int64 filename = 4;
     */
    filename: bigint; // Index into string table
    /**
     * Line number in source file.
     *
     * @generated from protobuf field: int64 start_line = 5;
     */
    startLine: bigint;
}
/**
 * Specifies the method of aggregating metric values, either DELTA (change since last report)
 * or CUMULATIVE (total since a fixed start time).
 *
 * @generated from protobuf enum opentelemetry.proto.profiles.v1.alternatives.pprofextended.AggregationTemporality
 */
export enum AggregationTemporality {
    /**
     * UNSPECIFIED is the default AggregationTemporality, it MUST not be used.
     *
     * @generated from protobuf enum value: AGGREGATION_TEMPORALITY_UNSPECIFIED = 0;
     */
    UNSPECIFIED = 0,
    /**
     * * DELTA is an AggregationTemporality for a profiler which reports
     * changes since last report time. Successive metrics contain aggregation of
     * values from continuous and non-overlapping intervals.
     * The values for a DELTA metric are based only on the time interval
     * associated with one measurement cycle. There is no dependency on
     * previous measurements like is the case for CUMULATIVE metrics.
     * For example, consider a system measuring the number of requests that
     * it receives and reports the sum of these requests every second as a
     * DELTA metric:
     * 1. The system starts receiving at time=t_0.
     * 2. A request is received, the system measures 1 request.
     * 3. A request is received, the system measures 1 request.
     * 4. A request is received, the system measures 1 request.
     * 5. The 1 second collection cycle ends. A metric is exported for the
     * number of requests received over the interval of time t_0 to
     * t_0+1 with a value of 3.
     * 6. A request is received, the system measures 1 request.
     * 7. A request is received, the system measures 1 request.
     * 8. The 1 second collection cycle ends. A metric is exported for the
     * number of requests received over the interval of time t_0+1 to
     * t_0+2 with a value of 2.
     *
     * @generated from protobuf enum value: AGGREGATION_TEMPORALITY_DELTA = 1;
     */
    DELTA = 1,
    /**
     * * CUMULATIVE is an AggregationTemporality for a profiler which
     * reports changes since a fixed start time. This means that current values
     * of a CUMULATIVE metric depend on all previous measurements since the
     * start time. Because of this, the sender is required to retain this state
     * in some form. If this state is lost or invalidated, the CUMULATIVE metric
     * values MUST be reset and a new fixed start time following the last
     * reported measurement time sent MUST be used.
     * For example, consider a system measuring the number of requests that
     * it receives and reports the sum of these requests every second as a
     * CUMULATIVE metric:
     * 1. The system starts receiving at time=t_0.
     * 2. A request is received, the system measures 1 request.
     * 3. A request is received, the system measures 1 request.
     * 4. A request is received, the system measures 1 request.
     * 5. The 1 second collection cycle ends. A metric is exported for the
     * number of requests received over the interval of time t_0 to
     * t_0+1 with a value of 3.
     * 6. A request is received, the system measures 1 request.
     * 7. A request is received, the system measures 1 request.
     * 8. The 1 second collection cycle ends. A metric is exported for the
     * number of requests received over the interval of time t_0 to
     * t_0+2 with a value of 5.
     * 9. The system experiences a fault and loses state.
     * 10. The system recovers and resumes receiving at time=t_1.
     * 11. A request is received, the system measures 1 request.
     * 12. The 1 second collection cycle ends. A metric is exported for the
     * number of requests received over the interval of time t_1 to
     * t_0+1 with a value of 1.
     * Note: Even though, when reporting changes since last report time, using
     * CUMULATIVE is valid, it is not recommended.
     *
     * @generated from protobuf enum value: AGGREGATION_TEMPORALITY_CUMULATIVE = 2;
     */
    CUMULATIVE = 2
}
/**
 * Indicates the semantics of the build_id field.
 *
 * @generated from protobuf enum opentelemetry.proto.profiles.v1.alternatives.pprofextended.BuildIdKind
 */
export enum BuildIdKind {
    /**
     * Linker-generated build ID, stored in the ELF binary notes.
     *
     * @generated from protobuf enum value: BUILD_ID_LINKER = 0;
     */
    BUILD_ID_LINKER = 0,
    /**
     * Build ID based on the content hash of the binary. Currently no particular
     * hashing approach is standardized, so a given producer needs to define it
     * themselves and thus unlike BUILD_ID_LINKER this kind of hash is producer-specific.
     * We may choose to provide a standardized stable hash recommendation later.
     *
     * @generated from protobuf enum value: BUILD_ID_BINARY_HASH = 1;
     */
    BUILD_ID_BINARY_HASH = 1
}
// @generated message type with reflection information, may provide speed optimized methods
class Profile$Type extends MessageType<Profile> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Profile", [
            { no: 1, name: "sample_type", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => ValueType },
            { no: 2, name: "sample", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Sample },
            { no: 3, name: "mapping", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Mapping },
            { no: 4, name: "location", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location },
            { no: 15, name: "location_indices", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "function", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Function },
            { no: 16, name: "attribute_table", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => KeyValue },
            { no: 17, name: "attribute_units", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => AttributeUnit },
            { no: 18, name: "link_table", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Link },
            { no: 6, name: "string_table", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "drop_frames", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "keep_frames", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "time_nanos", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "duration_nanos", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 11, name: "period_type", kind: "message", T: () => ValueType },
            { no: 12, name: "period", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 13, name: "comment", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 14, name: "default_sample_type", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Profile>): Profile {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.sampleType = [];
        message.sample = [];
        message.mapping = [];
        message.location = [];
        message.locationIndices = [];
        message.function = [];
        message.attributeTable = [];
        message.attributeUnits = [];
        message.linkTable = [];
        message.stringTable = [];
        message.dropFrames = 0n;
        message.keepFrames = 0n;
        message.timeNanos = 0n;
        message.durationNanos = 0n;
        message.period = 0n;
        message.comment = [];
        message.defaultSampleType = 0n;
        if (value !== undefined)
            reflectionMergePartial<Profile>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Profile): Profile {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType sample_type */ 1:
                    message.sampleType.push(ValueType.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Sample sample */ 2:
                    message.sample.push(Sample.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Mapping mapping */ 3:
                    message.mapping.push(Mapping.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Location location */ 4:
                    message.location.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated int64 location_indices */ 15:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.locationIndices.push(reader.int64().toBigInt());
                    else
                        message.locationIndices.push(reader.int64().toBigInt());
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Function function */ 5:
                    message.function.push(Function.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated opentelemetry.proto.common.v1.KeyValue attribute_table */ 16:
                    message.attributeTable.push(KeyValue.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.AttributeUnit attribute_units */ 17:
                    message.attributeUnits.push(AttributeUnit.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Link link_table */ 18:
                    message.linkTable.push(Link.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated string string_table */ 6:
                    message.stringTable.push(reader.string());
                    break;
                case /* int64 drop_frames */ 7:
                    message.dropFrames = reader.int64().toBigInt();
                    break;
                case /* int64 keep_frames */ 8:
                    message.keepFrames = reader.int64().toBigInt();
                    break;
                case /* int64 time_nanos */ 9:
                    message.timeNanos = reader.int64().toBigInt();
                    break;
                case /* int64 duration_nanos */ 10:
                    message.durationNanos = reader.int64().toBigInt();
                    break;
                case /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType period_type */ 11:
                    message.periodType = ValueType.internalBinaryRead(reader, reader.uint32(), options, message.periodType);
                    break;
                case /* int64 period */ 12:
                    message.period = reader.int64().toBigInt();
                    break;
                case /* repeated int64 comment */ 13:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.comment.push(reader.int64().toBigInt());
                    else
                        message.comment.push(reader.int64().toBigInt());
                    break;
                case /* int64 default_sample_type */ 14:
                    message.defaultSampleType = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Profile, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType sample_type = 1; */
        for (let i = 0; i < message.sampleType.length; i++)
            ValueType.internalBinaryWrite(message.sampleType[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Sample sample = 2; */
        for (let i = 0; i < message.sample.length; i++)
            Sample.internalBinaryWrite(message.sample[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Mapping mapping = 3; */
        for (let i = 0; i < message.mapping.length; i++)
            Mapping.internalBinaryWrite(message.mapping[i], writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Location location = 4; */
        for (let i = 0; i < message.location.length; i++)
            Location.internalBinaryWrite(message.location[i], writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* repeated int64 location_indices = 15; */
        if (message.locationIndices.length) {
            writer.tag(15, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.locationIndices.length; i++)
                writer.int64(message.locationIndices[i]);
            writer.join();
        }
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Function function = 5; */
        for (let i = 0; i < message.function.length; i++)
            Function.internalBinaryWrite(message.function[i], writer.tag(5, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.common.v1.KeyValue attribute_table = 16; */
        for (let i = 0; i < message.attributeTable.length; i++)
            KeyValue.internalBinaryWrite(message.attributeTable[i], writer.tag(16, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.AttributeUnit attribute_units = 17; */
        for (let i = 0; i < message.attributeUnits.length; i++)
            AttributeUnit.internalBinaryWrite(message.attributeUnits[i], writer.tag(17, WireType.LengthDelimited).fork(), options).join();
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Link link_table = 18; */
        for (let i = 0; i < message.linkTable.length; i++)
            Link.internalBinaryWrite(message.linkTable[i], writer.tag(18, WireType.LengthDelimited).fork(), options).join();
        /* repeated string string_table = 6; */
        for (let i = 0; i < message.stringTable.length; i++)
            writer.tag(6, WireType.LengthDelimited).string(message.stringTable[i]);
        /* int64 drop_frames = 7; */
        if (message.dropFrames !== 0n)
            writer.tag(7, WireType.Varint).int64(message.dropFrames);
        /* int64 keep_frames = 8; */
        if (message.keepFrames !== 0n)
            writer.tag(8, WireType.Varint).int64(message.keepFrames);
        /* int64 time_nanos = 9; */
        if (message.timeNanos !== 0n)
            writer.tag(9, WireType.Varint).int64(message.timeNanos);
        /* int64 duration_nanos = 10; */
        if (message.durationNanos !== 0n)
            writer.tag(10, WireType.Varint).int64(message.durationNanos);
        /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType period_type = 11; */
        if (message.periodType)
            ValueType.internalBinaryWrite(message.periodType, writer.tag(11, WireType.LengthDelimited).fork(), options).join();
        /* int64 period = 12; */
        if (message.period !== 0n)
            writer.tag(12, WireType.Varint).int64(message.period);
        /* repeated int64 comment = 13; */
        if (message.comment.length) {
            writer.tag(13, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.comment.length; i++)
                writer.int64(message.comment[i]);
            writer.join();
        }
        /* int64 default_sample_type = 14; */
        if (message.defaultSampleType !== 0n)
            writer.tag(14, WireType.Varint).int64(message.defaultSampleType);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Profile
 */
export const Profile = new Profile$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AttributeUnit$Type extends MessageType<AttributeUnit> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.AttributeUnit", [
            { no: 1, name: "attribute_key", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "unit", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<AttributeUnit>): AttributeUnit {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.attributeKey = 0n;
        message.unit = 0n;
        if (value !== undefined)
            reflectionMergePartial<AttributeUnit>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: AttributeUnit): AttributeUnit {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 attribute_key */ 1:
                    message.attributeKey = reader.int64().toBigInt();
                    break;
                case /* int64 unit */ 2:
                    message.unit = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: AttributeUnit, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 attribute_key = 1; */
        if (message.attributeKey !== 0n)
            writer.tag(1, WireType.Varint).int64(message.attributeKey);
        /* int64 unit = 2; */
        if (message.unit !== 0n)
            writer.tag(2, WireType.Varint).int64(message.unit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.AttributeUnit
 */
export const AttributeUnit = new AttributeUnit$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Link$Type extends MessageType<Link> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Link", [
            { no: 1, name: "trace_id", kind: "scalar", T: 12 /*ScalarType.BYTES*/ },
            { no: 2, name: "span_id", kind: "scalar", T: 12 /*ScalarType.BYTES*/ }
        ]);
    }
    create(value?: PartialMessage<Link>): Link {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.traceId = new Uint8Array(0);
        message.spanId = new Uint8Array(0);
        if (value !== undefined)
            reflectionMergePartial<Link>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Link): Link {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bytes trace_id */ 1:
                    message.traceId = reader.bytes();
                    break;
                case /* bytes span_id */ 2:
                    message.spanId = reader.bytes();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Link, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bytes trace_id = 1; */
        if (message.traceId.length)
            writer.tag(1, WireType.LengthDelimited).bytes(message.traceId);
        /* bytes span_id = 2; */
        if (message.spanId.length)
            writer.tag(2, WireType.LengthDelimited).bytes(message.spanId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Link
 */
export const Link = new Link$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ValueType$Type extends MessageType<ValueType> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType", [
            { no: 1, name: "type", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "unit", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "aggregation_temporality", kind: "enum", T: () => ["opentelemetry.proto.profiles.v1.alternatives.pprofextended.AggregationTemporality", AggregationTemporality, "AGGREGATION_TEMPORALITY_"] }
        ]);
    }
    create(value?: PartialMessage<ValueType>): ValueType {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.type = 0n;
        message.unit = 0n;
        message.aggregationTemporality = 0;
        if (value !== undefined)
            reflectionMergePartial<ValueType>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ValueType): ValueType {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 type */ 1:
                    message.type = reader.int64().toBigInt();
                    break;
                case /* int64 unit */ 2:
                    message.unit = reader.int64().toBigInt();
                    break;
                case /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.AggregationTemporality aggregation_temporality */ 3:
                    message.aggregationTemporality = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ValueType, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 type = 1; */
        if (message.type !== 0n)
            writer.tag(1, WireType.Varint).int64(message.type);
        /* int64 unit = 2; */
        if (message.unit !== 0n)
            writer.tag(2, WireType.Varint).int64(message.unit);
        /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.AggregationTemporality aggregation_temporality = 3; */
        if (message.aggregationTemporality !== 0)
            writer.tag(3, WireType.Varint).int32(message.aggregationTemporality);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.ValueType
 */
export const ValueType = new ValueType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Sample$Type extends MessageType<Sample> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Sample", [
            { no: 1, name: "location_index", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "locations_start_index", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 8, name: "locations_length", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 9, name: "stacktrace_id_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 2, name: "value", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "label", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Label },
            { no: 10, name: "attributes", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 12, name: "link", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 13, name: "timestamps", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Sample>): Sample {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locationIndex = [];
        message.locationsStartIndex = 0n;
        message.locationsLength = 0n;
        message.stacktraceIdIndex = 0;
        message.value = [];
        message.label = [];
        message.attributes = [];
        message.link = 0n;
        message.timestamps = [];
        if (value !== undefined)
            reflectionMergePartial<Sample>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Sample): Sample {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated uint64 location_index */ 1:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.locationIndex.push(reader.uint64().toBigInt());
                    else
                        message.locationIndex.push(reader.uint64().toBigInt());
                    break;
                case /* uint64 locations_start_index */ 7:
                    message.locationsStartIndex = reader.uint64().toBigInt();
                    break;
                case /* uint64 locations_length */ 8:
                    message.locationsLength = reader.uint64().toBigInt();
                    break;
                case /* uint32 stacktrace_id_index */ 9:
                    message.stacktraceIdIndex = reader.uint32();
                    break;
                case /* repeated int64 value */ 2:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.value.push(reader.int64().toBigInt());
                    else
                        message.value.push(reader.int64().toBigInt());
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Label label */ 3:
                    message.label.push(Label.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated uint64 attributes */ 10:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.attributes.push(reader.uint64().toBigInt());
                    else
                        message.attributes.push(reader.uint64().toBigInt());
                    break;
                case /* uint64 link */ 12:
                    message.link = reader.uint64().toBigInt();
                    break;
                case /* repeated uint64 timestamps */ 13:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.timestamps.push(reader.uint64().toBigInt());
                    else
                        message.timestamps.push(reader.uint64().toBigInt());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Sample, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated uint64 location_index = 1; */
        if (message.locationIndex.length) {
            writer.tag(1, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.locationIndex.length; i++)
                writer.uint64(message.locationIndex[i]);
            writer.join();
        }
        /* uint64 locations_start_index = 7; */
        if (message.locationsStartIndex !== 0n)
            writer.tag(7, WireType.Varint).uint64(message.locationsStartIndex);
        /* uint64 locations_length = 8; */
        if (message.locationsLength !== 0n)
            writer.tag(8, WireType.Varint).uint64(message.locationsLength);
        /* uint32 stacktrace_id_index = 9; */
        if (message.stacktraceIdIndex !== 0)
            writer.tag(9, WireType.Varint).uint32(message.stacktraceIdIndex);
        /* repeated int64 value = 2; */
        if (message.value.length) {
            writer.tag(2, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.value.length; i++)
                writer.int64(message.value[i]);
            writer.join();
        }
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Label label = 3; */
        for (let i = 0; i < message.label.length; i++)
            Label.internalBinaryWrite(message.label[i], writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* repeated uint64 attributes = 10; */
        if (message.attributes.length) {
            writer.tag(10, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.attributes.length; i++)
                writer.uint64(message.attributes[i]);
            writer.join();
        }
        /* uint64 link = 12; */
        if (message.link !== 0n)
            writer.tag(12, WireType.Varint).uint64(message.link);
        /* repeated uint64 timestamps = 13; */
        if (message.timestamps.length) {
            writer.tag(13, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.timestamps.length; i++)
                writer.uint64(message.timestamps[i]);
            writer.join();
        }
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Sample
 */
export const Sample = new Sample$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Label$Type extends MessageType<Label> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Label", [
            { no: 1, name: "key", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "str", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "num", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "num_unit", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Label>): Label {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.key = 0n;
        message.str = 0n;
        message.num = 0n;
        message.numUnit = 0n;
        if (value !== undefined)
            reflectionMergePartial<Label>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Label): Label {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 key */ 1:
                    message.key = reader.int64().toBigInt();
                    break;
                case /* int64 str */ 2:
                    message.str = reader.int64().toBigInt();
                    break;
                case /* int64 num */ 3:
                    message.num = reader.int64().toBigInt();
                    break;
                case /* int64 num_unit */ 4:
                    message.numUnit = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Label, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 key = 1; */
        if (message.key !== 0n)
            writer.tag(1, WireType.Varint).int64(message.key);
        /* int64 str = 2; */
        if (message.str !== 0n)
            writer.tag(2, WireType.Varint).int64(message.str);
        /* int64 num = 3; */
        if (message.num !== 0n)
            writer.tag(3, WireType.Varint).int64(message.num);
        /* int64 num_unit = 4; */
        if (message.numUnit !== 0n)
            writer.tag(4, WireType.Varint).int64(message.numUnit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Label
 */
export const Label = new Label$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Mapping$Type extends MessageType<Mapping> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Mapping", [
            { no: 1, name: "id", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "memory_start", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "memory_limit", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "file_offset", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "filename", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 6, name: "build_id", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 11, name: "build_id_kind", kind: "enum", T: () => ["opentelemetry.proto.profiles.v1.alternatives.pprofextended.BuildIdKind", BuildIdKind] },
            { no: 12, name: "attributes", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 7, name: "has_functions", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 8, name: "has_filenames", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 9, name: "has_line_numbers", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 10, name: "has_inline_frames", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<Mapping>): Mapping {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = 0n;
        message.memoryStart = 0n;
        message.memoryLimit = 0n;
        message.fileOffset = 0n;
        message.filename = 0n;
        message.buildId = 0n;
        message.buildIdKind = 0;
        message.attributes = [];
        message.hasFunctions = false;
        message.hasFilenames = false;
        message.hasLineNumbers = false;
        message.hasInlineFrames = false;
        if (value !== undefined)
            reflectionMergePartial<Mapping>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Mapping): Mapping {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* uint64 id */ 1:
                    message.id = reader.uint64().toBigInt();
                    break;
                case /* uint64 memory_start */ 2:
                    message.memoryStart = reader.uint64().toBigInt();
                    break;
                case /* uint64 memory_limit */ 3:
                    message.memoryLimit = reader.uint64().toBigInt();
                    break;
                case /* uint64 file_offset */ 4:
                    message.fileOffset = reader.uint64().toBigInt();
                    break;
                case /* int64 filename */ 5:
                    message.filename = reader.int64().toBigInt();
                    break;
                case /* int64 build_id */ 6:
                    message.buildId = reader.int64().toBigInt();
                    break;
                case /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.BuildIdKind build_id_kind */ 11:
                    message.buildIdKind = reader.int32();
                    break;
                case /* repeated uint64 attributes */ 12:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.attributes.push(reader.uint64().toBigInt());
                    else
                        message.attributes.push(reader.uint64().toBigInt());
                    break;
                case /* bool has_functions */ 7:
                    message.hasFunctions = reader.bool();
                    break;
                case /* bool has_filenames */ 8:
                    message.hasFilenames = reader.bool();
                    break;
                case /* bool has_line_numbers */ 9:
                    message.hasLineNumbers = reader.bool();
                    break;
                case /* bool has_inline_frames */ 10:
                    message.hasInlineFrames = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Mapping, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* uint64 id = 1; */
        if (message.id !== 0n)
            writer.tag(1, WireType.Varint).uint64(message.id);
        /* uint64 memory_start = 2; */
        if (message.memoryStart !== 0n)
            writer.tag(2, WireType.Varint).uint64(message.memoryStart);
        /* uint64 memory_limit = 3; */
        if (message.memoryLimit !== 0n)
            writer.tag(3, WireType.Varint).uint64(message.memoryLimit);
        /* uint64 file_offset = 4; */
        if (message.fileOffset !== 0n)
            writer.tag(4, WireType.Varint).uint64(message.fileOffset);
        /* int64 filename = 5; */
        if (message.filename !== 0n)
            writer.tag(5, WireType.Varint).int64(message.filename);
        /* int64 build_id = 6; */
        if (message.buildId !== 0n)
            writer.tag(6, WireType.Varint).int64(message.buildId);
        /* opentelemetry.proto.profiles.v1.alternatives.pprofextended.BuildIdKind build_id_kind = 11; */
        if (message.buildIdKind !== 0)
            writer.tag(11, WireType.Varint).int32(message.buildIdKind);
        /* repeated uint64 attributes = 12; */
        if (message.attributes.length) {
            writer.tag(12, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.attributes.length; i++)
                writer.uint64(message.attributes[i]);
            writer.join();
        }
        /* bool has_functions = 7; */
        if (message.hasFunctions !== false)
            writer.tag(7, WireType.Varint).bool(message.hasFunctions);
        /* bool has_filenames = 8; */
        if (message.hasFilenames !== false)
            writer.tag(8, WireType.Varint).bool(message.hasFilenames);
        /* bool has_line_numbers = 9; */
        if (message.hasLineNumbers !== false)
            writer.tag(9, WireType.Varint).bool(message.hasLineNumbers);
        /* bool has_inline_frames = 10; */
        if (message.hasInlineFrames !== false)
            writer.tag(10, WireType.Varint).bool(message.hasInlineFrames);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Mapping
 */
export const Mapping = new Mapping$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Location$Type extends MessageType<Location> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Location", [
            { no: 1, name: "id", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "mapping_index", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "address", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "line", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Line },
            { no: 5, name: "is_folded", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "type_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 7, name: "attributes", kind: "scalar", repeat: 1 /*RepeatType.PACKED*/, T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Location>): Location {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = 0n;
        message.mappingIndex = 0n;
        message.address = 0n;
        message.line = [];
        message.isFolded = false;
        message.typeIndex = 0;
        message.attributes = [];
        if (value !== undefined)
            reflectionMergePartial<Location>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Location): Location {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* uint64 id */ 1:
                    message.id = reader.uint64().toBigInt();
                    break;
                case /* uint64 mapping_index */ 2:
                    message.mappingIndex = reader.uint64().toBigInt();
                    break;
                case /* uint64 address */ 3:
                    message.address = reader.uint64().toBigInt();
                    break;
                case /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Line line */ 4:
                    message.line.push(Line.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* bool is_folded */ 5:
                    message.isFolded = reader.bool();
                    break;
                case /* uint32 type_index */ 6:
                    message.typeIndex = reader.uint32();
                    break;
                case /* repeated uint64 attributes */ 7:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.attributes.push(reader.uint64().toBigInt());
                    else
                        message.attributes.push(reader.uint64().toBigInt());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Location, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* uint64 id = 1; */
        if (message.id !== 0n)
            writer.tag(1, WireType.Varint).uint64(message.id);
        /* uint64 mapping_index = 2; */
        if (message.mappingIndex !== 0n)
            writer.tag(2, WireType.Varint).uint64(message.mappingIndex);
        /* uint64 address = 3; */
        if (message.address !== 0n)
            writer.tag(3, WireType.Varint).uint64(message.address);
        /* repeated opentelemetry.proto.profiles.v1.alternatives.pprofextended.Line line = 4; */
        for (let i = 0; i < message.line.length; i++)
            Line.internalBinaryWrite(message.line[i], writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* bool is_folded = 5; */
        if (message.isFolded !== false)
            writer.tag(5, WireType.Varint).bool(message.isFolded);
        /* uint32 type_index = 6; */
        if (message.typeIndex !== 0)
            writer.tag(6, WireType.Varint).uint32(message.typeIndex);
        /* repeated uint64 attributes = 7; */
        if (message.attributes.length) {
            writer.tag(7, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.attributes.length; i++)
                writer.uint64(message.attributes[i]);
            writer.join();
        }
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Location
 */
export const Location = new Location$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Line$Type extends MessageType<Line> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Line", [
            { no: 1, name: "function_index", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "line", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "column", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Line>): Line {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functionIndex = 0n;
        message.line = 0n;
        message.column = 0n;
        if (value !== undefined)
            reflectionMergePartial<Line>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Line): Line {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* uint64 function_index */ 1:
                    message.functionIndex = reader.uint64().toBigInt();
                    break;
                case /* int64 line */ 2:
                    message.line = reader.int64().toBigInt();
                    break;
                case /* int64 column */ 3:
                    message.column = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Line, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* uint64 function_index = 1; */
        if (message.functionIndex !== 0n)
            writer.tag(1, WireType.Varint).uint64(message.functionIndex);
        /* int64 line = 2; */
        if (message.line !== 0n)
            writer.tag(2, WireType.Varint).int64(message.line);
        /* int64 column = 3; */
        if (message.column !== 0n)
            writer.tag(3, WireType.Varint).int64(message.column);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Line
 */
export const Line = new Line$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Function$Type extends MessageType<Function> {
    constructor() {
        super("opentelemetry.proto.profiles.v1.alternatives.pprofextended.Function", [
            { no: 1, name: "id", kind: "scalar", T: 4 /*ScalarType.UINT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "name", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "system_name", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "filename", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "start_line", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Function>): Function {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = 0n;
        message.name = 0n;
        message.systemName = 0n;
        message.filename = 0n;
        message.startLine = 0n;
        if (value !== undefined)
            reflectionMergePartial<Function>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Function): Function {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* uint64 id */ 1:
                    message.id = reader.uint64().toBigInt();
                    break;
                case /* int64 name */ 2:
                    message.name = reader.int64().toBigInt();
                    break;
                case /* int64 system_name */ 3:
                    message.systemName = reader.int64().toBigInt();
                    break;
                case /* int64 filename */ 4:
                    message.filename = reader.int64().toBigInt();
                    break;
                case /* int64 start_line */ 5:
                    message.startLine = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Function, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* uint64 id = 1; */
        if (message.id !== 0n)
            writer.tag(1, WireType.Varint).uint64(message.id);
        /* int64 name = 2; */
        if (message.name !== 0n)
            writer.tag(2, WireType.Varint).int64(message.name);
        /* int64 system_name = 3; */
        if (message.systemName !== 0n)
            writer.tag(3, WireType.Varint).int64(message.systemName);
        /* int64 filename = 4; */
        if (message.filename !== 0n)
            writer.tag(4, WireType.Varint).int64(message.filename);
        /* int64 start_line = 5; */
        if (message.startLine !== 0n)
            writer.tag(5, WireType.Varint).int64(message.startLine);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message opentelemetry.proto.profiles.v1.alternatives.pprofextended.Function
 */
export const Function = new Function$Type();
