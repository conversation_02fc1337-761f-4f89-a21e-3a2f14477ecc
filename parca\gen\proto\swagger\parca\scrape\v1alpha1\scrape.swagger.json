{"swagger": "2.0", "info": {"title": "parca/scrape/v1alpha1/scrape.proto", "version": "version not set"}, "tags": [{"name": "ScrapeService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/targets": {"get": {"summary": "Targets returns the set of scrape targets that are configured", "operationId": "ScrapeService_Targets", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1alpha1TargetsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "state", "description": "state is the state of targets to returns\n\n - STATE_ANY_UNSPECIFIED: STATE_ANY_UNSPECIFIED unspecified\n - STATE_ACTIVE: STATE_ACTIVE target active state\n - STATE_DROPPED: STATE_DROPPED target dropped state", "in": "query", "required": false, "type": "string", "enum": ["STATE_ANY_UNSPECIFIED", "STATE_ACTIVE", "STATE_DROPPED"], "default": "STATE_ANY_UNSPECIFIED"}], "tags": ["ScrapeService"]}}}, "definitions": {"TargetHealth": {"type": "string", "enum": ["HEALTH_UNKNOWN_UNSPECIFIED", "HEALTH_GOOD", "HEALTH_BAD"], "default": "HEALTH_UNKNOWN_UNSPECIFIED", "description": "- HEALTH_UNKNOWN_UNSPECIFIED: HEALTH_UNKNOWN_UNSPECIFIED unspecified\n - HEALTH_GOOD: HEALTH_GOOD healthy target\n - HEALTH_BAD: HEALTH_BAD unhealthy target", "title": "Health are the possible health values of a target"}, "profilestorev1alpha1Label": {"type": "object", "properties": {"name": {"type": "string", "title": "name is the label name"}, "value": {"type": "string", "title": "value is the value for the label name"}}, "title": "Label is a key value pair of identifiers"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "scrapev1alpha1Targets": {"type": "object", "properties": {"targets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1Target"}, "title": "targets is a list of targets"}}, "title": "Targets is a list of targets"}, "v1alpha1LabelSet": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/profilestorev1alpha1Label"}, "title": "labels are the grouping of labels"}}, "title": "LabelSet is a group of labels"}, "v1alpha1Target": {"type": "object", "properties": {"discoveredLabels": {"$ref": "#/definitions/v1alpha1LabelSet", "title": "discovered_labels are the set of labels for the target that have been discovered"}, "labels": {"$ref": "#/definitions/v1alpha1LabelSet", "title": "labels are the set of labels given for the target"}, "lastError": {"type": "string", "title": "last_error is the error message most recently received from a scrape attempt"}, "lastScrape": {"type": "string", "format": "date-time", "title": "last_scrape is the time stamp the last scrape request was performed"}, "lastScrapeDuration": {"type": "string", "title": "last_scrape_duration is the duration of the last scrape request"}, "url": {"type": "string", "title": "url is the url of the target"}, "health": {"$ref": "#/definitions/TargetHealth", "title": "health indicates the current health of the target"}}, "title": "Target is the scrape target representation"}, "v1alpha1TargetsRequestState": {"type": "string", "enum": ["STATE_ANY_UNSPECIFIED", "STATE_ACTIVE", "STATE_DROPPED"], "default": "STATE_ANY_UNSPECIFIED", "description": "- STATE_ANY_UNSPECIFIED: STATE_ANY_UNSPECIFIED unspecified\n - STATE_ACTIVE: STATE_ACTIVE target active state\n - STATE_DROPPED: STATE_DROPPED target dropped state", "title": "State represents the current state of a target"}, "v1alpha1TargetsResponse": {"type": "object", "properties": {"targets": {"type": "object", "additionalProperties": {"$ref": "#/definitions/scrapev1alpha1Targets"}, "title": "targets is the mapping of targets"}}, "title": "TargetsResponse is the set of targets for the given requested state"}}}