// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {TimeObject, TimeUnits, convertTime, formatDuration} from '@parca/utilities';

const LastScrapeCell = ({
  lastScrape,
  lastScrapeDuration,
}: {
  lastScrape: TimeObject;
  lastScrapeDuration: TimeObject;
}) => {
  const nowInNanoseconds = convertTime(
    new Date().getTime(),
    TimeUnits.Milliseconds,
    TimeUnits.Nanos
  );
  return (
    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-200">
      <p>Last Scrape: {formatDuration(lastScrape, nowInNanoseconds)} ago</p>
      <p>Duration: {formatDuration(lastScrapeDuration)}</p>
    </td>
  );
};

export default LastScrapeCell;
