{"__inputs": [{"name": "DS_PROMETHEUS", "label": "prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.3.6"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "iteration": 1645011639708, "links": [], "liveNow": false, "panels": [{"datasource": {"uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "wps"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": false, "expr": "rate(parca_tsdb_head_profiles_appended_total[5m])", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Sam<PERSON> Appended", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 11, "title": "Write (gRPC)", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum by(grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.profilestore.v1alpha1.ProfileStoreService\",grpc_method=\"WriteRaw\"}[5m])) > 0", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC Write Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 10}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum by(grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.profilestore.v1alpha1.ProfileStoreService\",grpc_method=\"WriteRaw\",grpc_code=~\"Aborted|Unavailable|Internal|Unknown|Unimplemented|DataLoss\"}[5m]))", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC Write Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 10}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.5, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.profilestore.v1alpha1.ProfileStoreService\",grpc_method=\"WriteRaw\"}[5m])))", "interval": "", "legendFormat": "p50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.90, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.profilestore.v1alpha1.ProfileStoreService\",grpc_method=\"WriteRaw\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p90", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.95, sum by (le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.profilestore.v1alpha1.ProfileStoreService\",grpc_method=\"WriteRaw\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p95", "refId": "C"}], "title": "gRPC Write Duration", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "$datasource"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 13, "panels": [], "title": "Query", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "These are the requests that query the overview metrics for profiles over a given time range.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum by (grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"QueryRange\"}[5m])) > 0", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC QueryRange Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum by(grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"QueryRange\",grpc_code=~\"Aborted|Unavailable|Internal|Unknown|Unimplemented|DataLoss\"}[5m]))", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC QueryRange Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0.1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.5, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"QueryRange\"}[5m])))", "interval": "", "legendFormat": "p50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.90, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"QueryRange\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p90", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.95, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"QueryRange\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p95", "refId": "C"}], "title": "gRPC QueryRange Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "These are requests that show the actual profiles.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 27}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum by(grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"Query\"}[5m])) > 0", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC Query Requests", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 27}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum by(grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"Query\",grpc_code=~\"Aborted|Unavailable|Internal|Unknown|Unimplemented|DataLoss\"}[5m]))", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC Query Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 27}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.5, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"Query\"}[5m])))", "interval": "", "legendFormat": "p50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.90, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"Query\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p90", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "histogram_quantile(0.95, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=\"Query\"}[5m])))   ", "hide": false, "interval": "", "legendFormat": "p95", "refId": "C"}], "title": "gRPC Query Duration", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "These are the requests that return label names and values, usually used in UIs for label suggestions.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 35}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": false, "expr": "sum by (grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=~\"Labels|Values\"}[5m])) > 0", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC Labels Requests", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 35}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum by(grpc_code) (rate(grpc_server_handled_total{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=~\"Labels|Values\",grpc_code=~\"Aborted|Unavailable|Internal|Unknown|Unimplemented|DataLoss\"}[5m]))", "interval": "", "legendFormat": "{{ grpc_code }}", "refId": "A"}], "title": "gRPC Labels Errors", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 35}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.5, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=~\"Labels|Values\"}[5m])))", "interval": "", "legendFormat": "p50", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.90, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=~\"Labels|Values\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p90", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.95, sum by(le) (rate(grpc_server_handling_seconds_bucket{grpc_service=\"parca.query.v1alpha1.QueryService\",grpc_method=~\"Labels|Values\"}[5m])))", "hide": false, "interval": "", "legendFormat": "p95", "refId": "C"}], "title": "gRPC Labels Duration", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 43}, "id": 9, "title": "Internals", "type": "row"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 14, "x": 0, "y": 44}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "increase(parca_tsdb_head_truncated_chunks_total[5m])", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Truncated Chunks", "type": "timeseries"}, {"datasource": {"uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 14, "y": 44}, "id": 25, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.6", "targets": [{"exemplar": true, "expr": "time() - parca_tsdb_head_min_time", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "min timestamp", "type": "stat"}, {"datasource": {"uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 19, "y": 44}, "id": 26, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.6", "targets": [{"exemplar": true, "expr": "time() - parca_tsdb_head_max_time", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "max timestamp", "type": "stat"}], "refresh": "30s", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "prometheus", "value": "prometheus"}, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Parca", "uid": "iWwypvN7k", "version": 2, "weekStart": ""}