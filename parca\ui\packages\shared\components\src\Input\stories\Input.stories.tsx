// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Input from '..';

export default {
  component: Input,
  title: 'Components/Input',
  argTypes: {type: {control: {type: 'select'}, options: ['string', 'number']}},
};

export const Default = {args: {}};
export const TypeNumber = {args: {type: 'number', defaultValue: '1'}};
export const WithAction = {args: {type: 'number', defaultValue: '1', onAction: () => {}}};
