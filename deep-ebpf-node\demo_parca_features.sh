#!/bin/bash

# Deep-eBPF-Node Enhanced Demo with Parca Features
# This script demonstrates the newly implemented Parca-inspired features:
# 1. Runtime Detection (Python, Java, Go, Node.js, Ruby, .NET, etc.)
# 2. Symbol Resolution (kernel and userspace)
# 3. Enhanced Human-Readable Output

set -e

echo "🚀 Deep-eBPF-Node Enhanced Demo with Parca Features"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (required for eBPF)${NC}"
   echo "Run with: sudo $0"
   exit 1
fi

echo -e "${GREEN}✅ Running as root${NC}"

# Build the enhanced tracer
echo -e "\n${BLUE}📦 Building Enhanced Deep-eBPF-Node...${NC}"
cd /mnt/c/github-current/ebpf-tracing/deep-ebpf-node

# Clean and build
make clean
if ! make build; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build successful${NC}"

# Create test programs for different runtimes
echo -e "\n${BLUE}🧪 Creating Multi-Language Test Programs...${NC}"

# Create Python test
cat > test_python.py << 'EOF'
#!/usr/bin/env python3
import time
import os

def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def main():
    print(f"Python Test Program (PID: {os.getpid()})")
    for i in range(5):
        result = fibonacci(10)
        print(f"fibonacci(10) = {result}")
        time.sleep(1)
    print("Python test completed")

if __name__ == "__main__":
    main()
EOF

# Create Node.js test
cat > test_nodejs.js << 'EOF'
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

function main() {
    console.log(`Node.js Test Program (PID: ${process.pid})`);
    for (let i = 0; i < 5; i++) {
        const result = fibonacci(10);
        console.log(`fibonacci(10) = ${result}`);
        // Sleep for 1 second
        const start = Date.now();
        while (Date.now() - start < 1000) {}
    }
    console.log("Node.js test completed");
}

main();
EOF

# Create Go test
cat > test_go.go << 'EOF'
package main

import (
    "fmt"
    "os"
    "time"
)

func fibonacci(n int) int {
    if n <= 1 {
        return n
    }
    return fibonacci(n-1) + fibonacci(n-2)
}

func main() {
    fmt.Printf("Go Test Program (PID: %d)\n", os.Getpid())
    for i := 0; i < 5; i++ {
        result := fibonacci(10)
        fmt.Printf("fibonacci(10) = %d\n", result)
        time.Sleep(1 * time.Second)
    }
    fmt.Println("Go test completed")
}
EOF

# Create Java test
cat > TestJava.java << 'EOF'
public class TestJava {
    public static int fibonacci(int n) {
        if (n <= 1) return n;
        return fibonacci(n-1) + fibonacci(n-2);
    }
    
    public static void main(String[] args) {
        System.out.println("Java Test Program (PID: " + ProcessHandle.current().pid() + ")");
        for (int i = 0; i < 5; i++) {
            int result = fibonacci(10);
            System.out.println("fibonacci(10) = " + result);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                break;
            }
        }
        System.out.println("Java test completed");
    }
}
EOF

# Create C test
cat > test_c.c << 'EOF'
#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>

int fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

int main() {
    printf("C Test Program (PID: %d)\n", getpid());
    for (int i = 0; i < 5; i++) {
        int result = fibonacci(10);
        printf("fibonacci(10) = %d\n", result);
        sleep(1);
    }
    printf("C test completed\n");
    return 0;
}
EOF

# Compile test programs
echo -e "\n${BLUE}🔨 Compiling Test Programs...${NC}"

# Compile C
gcc -o test_c test_c.c
echo -e "${GREEN}✅ C program compiled${NC}"

# Compile Go
if command -v go &> /dev/null; then
    go build -o test_go test_go.go
    echo -e "${GREEN}✅ Go program compiled${NC}"
else
    echo -e "${YELLOW}⚠️  Go not available, skipping Go test${NC}"
fi

# Compile Java
if command -v javac &> /dev/null; then
    javac TestJava.java
    echo -e "${GREEN}✅ Java program compiled${NC}"
else
    echo -e "${YELLOW}⚠️  Java not available, skipping Java test${NC}"
fi

# Make Python and Node.js executable
chmod +x test_python.py test_nodejs.js

echo -e "\n${BLUE}🎯 Starting Enhanced Deep-eBPF-Node Tracer...${NC}"

# Start the tracer in background with enhanced features
./deep-ebpf-node --trace-all --format human > enhanced_demo_output.log 2>&1 &
TRACER_PID=$!

echo -e "${GREEN}✅ Tracer started (PID: $TRACER_PID)${NC}"
sleep 2

# Function to run test and show runtime detection
run_test() {
    local name=$1
    local command=$2
    local expected_runtime=$3
    
    echo -e "\n${BLUE}🧪 Testing $name Runtime Detection...${NC}"
    echo "Command: $command"
    
    # Run the test program
    eval "$command" &
    TEST_PID=$!
    
    # Let it run for a few seconds
    sleep 3
    
    # Kill the test program
    kill $TEST_PID 2>/dev/null || true
    wait $TEST_PID 2>/dev/null || true
    
    echo -e "${GREEN}✅ $name test completed${NC}"
}

# Run tests for different runtimes
echo -e "\n${BLUE}🎪 Running Multi-Language Runtime Detection Tests...${NC}"

# Test C runtime
run_test "C/Native" "./test_c" "C/native"

# Test Python runtime
if command -v python3 &> /dev/null; then
    run_test "Python" "python3 test_python.py" "Python"
else
    echo -e "${YELLOW}⚠️  Python3 not available, skipping Python test${NC}"
fi

# Test Node.js runtime
if command -v node &> /dev/null; then
    run_test "Node.js" "node test_nodejs.js" "Node.js"
else
    echo -e "${YELLOW}⚠️  Node.js not available, skipping Node.js test${NC}"
fi

# Test Go runtime
if [[ -f test_go ]]; then
    run_test "Go" "./test_go" "Go"
fi

# Test Java runtime
if command -v java &> /dev/null && [[ -f TestJava.class ]]; then
    run_test "Java" "java TestJava" "Java"
fi

# Let the tracer capture some system activity
echo -e "\n${BLUE}⏱️  Capturing System Activity for 10 seconds...${NC}"
sleep 10

# Stop the tracer
echo -e "\n${BLUE}🛑 Stopping Tracer...${NC}"
kill $TRACER_PID 2>/dev/null || true
wait $TRACER_PID 2>/dev/null || true

echo -e "${GREEN}✅ Tracer stopped${NC}"

# Analyze the results
echo -e "\n${BLUE}📊 Analyzing Enhanced Output...${NC}"

if [[ -f enhanced_demo_output.log ]]; then
    echo -e "\n${YELLOW}📈 Runtime Detection Results:${NC}"
    
    # Count different runtimes detected
    echo "Runtime Distribution:"
    grep "├─ Runtime:" enhanced_demo_output.log | sort | uniq -c | while read count runtime; do
        echo "  $runtime: $count events"
    done
    
    echo -e "\n${YELLOW}🔍 Symbol Resolution Results:${NC}"
    
    # Show resolved function names
    echo "Function Names Resolved:"
    grep "├─ Function:" enhanced_demo_output.log | head -10 | while read line; do
        echo "  $line"
    done
    
    echo -e "\n${YELLOW}🎯 Process Detection Results:${NC}"
    
    # Show detected processes
    echo "Processes Traced:"
    grep "┌─" enhanced_demo_output.log | cut -d']' -f2 | cut -d':' -f1 | sort | uniq -c | head -10 | while read count process; do
        echo "  $process: $count events"
    done
    
    echo -e "\n${YELLOW}📋 Sample Enhanced Output:${NC}"
    echo "First 20 lines of enhanced output:"
    head -20 enhanced_demo_output.log
    
    echo -e "\n${GREEN}✅ Enhanced output saved to: enhanced_demo_output.log${NC}"
    echo -e "${BLUE}💡 View full output with: cat enhanced_demo_output.log${NC}"
    
    # Show statistics
    total_events=$(grep -c "┌─" enhanced_demo_output.log)
    kernel_events=$(grep -c "\[kernel\]" enhanced_demo_output.log)
    user_events=$(grep -c "\[user\]" enhanced_demo_output.log)
    
    echo -e "\n${YELLOW}📊 Statistics:${NC}"
    echo "  Total Events: $total_events"
    echo "  Kernel Events: $kernel_events"
    echo "  User Events: $user_events"
    echo "  Runtime Types: $(grep "├─ Runtime:" enhanced_demo_output.log | cut -d':' -f2 | sort | uniq | wc -l)"
    echo "  Unique Processes: $(grep "┌─" enhanced_demo_output.log | cut -d']' -f2 | cut -d':' -f1 | sort | uniq | wc -l)"
    
else
    echo -e "${RED}❌ No output file found${NC}"
fi

# Cleanup
echo -e "\n${BLUE}🧹 Cleaning up...${NC}"
rm -f test_python.py test_nodejs.js test_go.go TestJava.java TestJava.class test_c.c test_c test_go

echo -e "\n${GREEN}🎉 Enhanced Deep-eBPF-Node Demo Completed!${NC}"
echo -e "${BLUE}📝 Key Features Demonstrated:${NC}"
echo "  ✅ Multi-Language Runtime Detection (Python, Java, Go, Node.js, C)"
echo "  ✅ Advanced Symbol Resolution (kernel and userspace)"
echo "  ✅ Enhanced Human-Readable Output Format"
echo "  ✅ Process Detection and Classification"
echo "  ✅ Kernel vs User Space Function Identification"
echo ""
echo -e "${YELLOW}🔍 This demonstrates the successful integration of Parca-inspired features!${NC}"
