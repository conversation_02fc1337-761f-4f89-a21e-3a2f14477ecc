package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/mexyusef/deep-ebpf-server/processor"
	"github.com/gorilla/websocket"
	"k8s.io/klog"
)

// Config defines collector configuration
type Config struct {
	ListenAddress   string
	BufferSize      int
	AgentTimeout    time.Duration
	EnableDiscovery bool
	MaxAgents       int
}

// Agent represents a connected deep-ebpf-node agent
type Agent struct {
	ID          string    `json:"id"`
	Address     string    `json:"address"`
	Hostname    string    `json:"hostname"`
	Version     string    `json:"version"`
	LastSeen    time.Time `json:"last_seen"`
	Status      string    `json:"status"`
	TracesCount uint64    `json:"traces_count"`
}

// Use processor's FunctionTrace type to avoid duplication
type FunctionTrace = processor.FunctionTrace

// Collector manages data collection from deep-ebpf-node agents
type Collector struct {
	config    Config
	processor *processor.Processor

	agents     map[string]*Agent
	agentsLock sync.RWMutex

	traceBuffer chan *FunctionTrace
	upgrader    websocket.Upgrader

	ctx    context.Context
	cancel context.CancelFunc
}

// New creates a new collector instance
func New(config Config, proc *processor.Processor) *Collector {
	ctx, cancel := context.WithCancel(context.Background())

	return &Collector{
		config:    config,
		processor: proc,
		agents:    make(map[string]*Agent),
		traceBuffer: make(chan *FunctionTrace, config.BufferSize),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for now
			},
		},
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start starts the collector
func (c *Collector) Start() error {
	// Start trace processing goroutine
	go c.processTraces()

	// Start agent cleanup goroutine
	go c.cleanupAgents()

	klog.Infof("Collector started, listening for agents")
	return nil
}

// Close stops the collector
func (c *Collector) Close() {
	c.cancel()
	close(c.traceBuffer)
	klog.Infof("Collector stopped")
}

// FunctionTraces handles function trace data from agents
func (c *Collector) FunctionTraces(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get agent ID from headers or generate one
	agentID := r.Header.Get("X-Agent-ID")
	if agentID == "" {
		agentID = fmt.Sprintf("agent-%s", r.RemoteAddr)
	}

	// Update agent status
	c.updateAgent(agentID, r.RemoteAddr, r.Header.Get("X-Agent-Hostname"), r.Header.Get("X-Agent-Version"))

	// Parse traces from request body
	var traces []FunctionTrace
	if err := json.NewDecoder(r.Body).Decode(&traces); err != nil {
		klog.Errorf("Failed to decode traces from agent %s: %v", agentID, err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Add agent ID to traces and buffer them
	for i := range traces {
		traces[i].AgentID = agentID
		select {
		case c.traceBuffer <- &traces[i]:
		default:
			klog.Warningf("Trace buffer full, dropping trace from agent %s", agentID)
		}
	}

	// Update agent trace count
	c.agentsLock.Lock()
	if agent, exists := c.agents[agentID]; exists {
		agent.TracesCount += uint64(len(traces))
		agent.LastSeen = time.Now()
	}
	c.agentsLock.Unlock()

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))

	klog.V(2).Infof("Received %d traces from agent %s", len(traces), agentID)
}

// AgentStatus handles agent status updates
func (c *Collector) AgentStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	agentID := r.Header.Get("X-Agent-ID")
	if agentID == "" {
		http.Error(w, "Missing X-Agent-ID header", http.StatusBadRequest)
		return
	}

	var status map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&status); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	c.updateAgent(agentID, r.RemoteAddr, r.Header.Get("X-Agent-Hostname"), r.Header.Get("X-Agent-Version"))

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

// AgentConfig provides configuration to agents
func (c *Collector) AgentConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	config := map[string]interface{}{
		"collection_interval": "5s",
		"buffer_size":        1000,
		"compression":        true,
		"sample_rate":        1,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(config)
}

// GetAgents returns the list of connected agents
func (c *Collector) GetAgents() []*Agent {
	c.agentsLock.RLock()
	defer c.agentsLock.RUnlock()

	agents := make([]*Agent, 0, len(c.agents))
	for _, agent := range c.agents {
		agents = append(agents, agent)
	}
	return agents
}

// GetStats returns collector statistics
func (c *Collector) GetStats() map[string]interface{} {
	c.agentsLock.RLock()
	defer c.agentsLock.RUnlock()

	totalTraces := uint64(0)
	activeAgents := 0
	for _, agent := range c.agents {
		totalTraces += agent.TracesCount
		if time.Since(agent.LastSeen) < c.config.AgentTimeout {
			activeAgents++
		}
	}

	return map[string]interface{}{
		"total_agents":    len(c.agents),
		"active_agents":   activeAgents,
		"total_traces":    totalTraces,
		"buffer_size":     len(c.traceBuffer),
		"buffer_capacity": cap(c.traceBuffer),
	}
}

// updateAgent updates agent information
func (c *Collector) updateAgent(id, address, hostname, version string) {
	c.agentsLock.Lock()
	defer c.agentsLock.Unlock()

	agent, exists := c.agents[id]
	if !exists {
		agent = &Agent{
			ID:      id,
			Address: address,
			Status:  "active",
		}
		c.agents[id] = agent
		klog.Infof("New agent connected: %s from %s", id, address)
	}

	agent.LastSeen = time.Now()
	if hostname != "" {
		agent.Hostname = hostname
	}
	if version != "" {
		agent.Version = version
	}
}

// processTraces processes traces from the buffer
func (c *Collector) processTraces() {
	for {
		select {
		case trace := <-c.traceBuffer:
			if trace == nil {
				return // Channel closed
			}
			
			// Send trace to processor
			if err := c.processor.ProcessTrace(trace); err != nil {
				klog.Errorf("Failed to process trace: %v", err)
			}

		case <-c.ctx.Done():
			return
		}
	}
}

// cleanupAgents removes inactive agents
func (c *Collector) cleanupAgents() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.agentsLock.Lock()
			for id, agent := range c.agents {
				if time.Since(agent.LastSeen) > c.config.AgentTimeout*2 {
					delete(c.agents, id)
					klog.Infof("Removed inactive agent: %s", id)
				} else if time.Since(agent.LastSeen) > c.config.AgentTimeout {
					agent.Status = "inactive"
				} else {
					agent.Status = "active"
				}
			}
			c.agentsLock.Unlock()

		case <-c.ctx.Done():
			return
		}
	}
}
