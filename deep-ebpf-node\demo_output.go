package main

import (
	"fmt"
	"time"

	"github.com/mexyusef/deep-ebpf-node/ebpftracer"
	"github.com/mexyusef/deep-ebpf-node/output"
)

func main() {
	fmt.Println("🎯 Deep-eBPF Expected Output Format Demonstration")
	fmt.Println("================================================")
	fmt.Println()

	// Create a mock function entry event
	entryEvent := ebpftracer.Event{
		Type:      ebpftracer.EventTypeFunctionEntry,
		Pid:       947,
		Timestamp: uint64(time.Now().UnixNano()),
		FunctionData: &ebpftracer.FunctionData{
			FunctionName: "fibonacci",
			FunctionAddr: 0x401234,
			Arguments:    []uint64{10, 0x7fff12345678, 0, 0, 0, 0},
			StackTrace:   []uint64{0x401234, 0x401180, 0x4010a0, 0x7f8b2c0e7083, 0x40108e},
			StackDepth:   5,
		},
	}

	// Create a mock function exit event
	exitEvent := ebpftracer.Event{
		Type:      ebpftracer.EventTypeFunctionExit,
		Pid:       947,
		Timestamp: uint64(time.Now().Add(29943 * time.Nanosecond).UnixNano()),
		Duration:  29943 * time.Nanosecond,
		FunctionData: &ebpftracer.FunctionData{
			FunctionName: "fibonacci",
			FunctionAddr: 0x401234,
			ReturnAddr:   0x401180,
			Arguments:    []uint64{10, 0x7fff12345678, 0, 0, 0, 0},
			ReturnValue:  55, // fibonacci(10) = 55
			StackTrace:   []uint64{0x401234, 0x401180, 0x4010a0, 0x7f8b2c0e7083, 0x40108e},
			StackDepth:   5,
		},
	}

	// Create human formatter
	formatter := output.NewHumanFormatter()

	fmt.Println("📝 Function Entry Event:")
	fmt.Println("------------------------")
	if err := formatter.FormatEvent(entryEvent); err != nil {
		fmt.Printf("Error formatting entry event: %v\n", err)
	}

	fmt.Println()
	fmt.Println("📝 Function Exit Event:")
	fmt.Println("-----------------------")
	if err := formatter.FormatEvent(exitEvent); err != nil {
		fmt.Printf("Error formatting exit event: %v\n", err)
	}

	fmt.Println()
	fmt.Println("✅ This demonstrates the exact output format specified in DEEP_EBPF_IMPLEMENTATION_PLAN.md")
	fmt.Println("✅ Including the new fields: PID, Binary, Runtime")
	fmt.Println("✅ Shows function name, address, duration, arguments, memory analysis, and stack ID")
	fmt.Println("✅ Human-readable format with proper Unicode box drawing characters")
}
