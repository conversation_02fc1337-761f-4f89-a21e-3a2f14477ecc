{"coredump-ref": "502f91cf2e60531bd30c2506f5760b03f1442413ee6ee83af06278824c2453ca", "threads": [{"lwp": 43768, "frames": ["libc-2.31.so+0x4618b", "libc-2.31.so+0x25858", "libjvm.so+0x244002", "libjvm.so+0xf2fa83", "libjvm.so+0xf3036e", "libjvm.so+0xf303a1", "libjvm.so+0xdcd7fd", "libc-2.31.so+0x4620f", "libpthread-2.31.so+0xacd5", "libjli.so+0x904e", "libjli.so+0x5d80", "libjli.so+0x76d4", "java+0x12b2", "libc-2.31.so+0x270b2", "java+0x135d"]}, {"lwp": 43771, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe072c", "libjvm.so+0x712151", "libjvm.so+0x7132c2", "libjvm.so+0x601a4e", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43770, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xf5efdf", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43774, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0x76b18a", "libjvm.so+0x76b397", "libjvm.so+0x601a4e", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43773, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0x7152ab", "libjvm.so+0x601a4e", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43779, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe072c", "libjvm.so+0xcdd989", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43775, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xf3623c", "libjvm.so+0xf36e47", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43772, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xf5efdf", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43776, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe085a", "libjvm.so+0x9028cb", "void java.lang.ref.Reference.waitForReferencePendingList()+0 in Reference.java:0", "void java.lang.ref.Reference.processPendingReferences()+0 in Reference.java:243", "void java.lang.ref.Reference$ReferenceHandler.run()+0 in Reference.java:215", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x83dbb2", "libjvm.so+0x8f89a3", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43780, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xbd446a", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43777, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc31b9a", "libjvm.so+0xc0416c", "libjvm.so+0xe50735", "libjvm.so+0x8fa0b9", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove()+0 in ReferenceQueue.java:176", "void java.lang.ref.Finalizer$FinalizerThread.run()+17 in Finalizer.java:171", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x83dbb2", "libjvm.so+0x8f89a3", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43778, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xdcd310", "libjvm.so+0xc1ae14", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43783, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xe45ffa", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43786, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc31d45", "libjvm.so+0xc03e24", "libjvm.so+0xe50735", "libjvm.so+0x8fa0b9", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "void jdk.internal.ref.CleanerImpl.run()+12 in CleanerImpl.java:140", "void java.lang.Thread.run()+1 in Thread.java:831", "void jdk.internal.misc.InnocuousThread.run()+2 in InnocuousThread.java:134", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x83dbb2", "libjvm.so+0x8f89a3", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43788, "frames": ["libpthread-2.31.so+0x133f4", "libpthread-2.31.so+0x134e7", "libjvm.so+0xcdd059", "libjvm.so+0xf5efdf", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43769, "frames": ["libjvm.so+0x59d4e4", "libjvm.so+0x8f5c9a", "libjvm.so+0x8c5ec4", "libjava.so+0x168a9", "libjava.so+0x16631", "libjava.so+0xeb1a", "void java.io.FileOutputStream.writeBytes(byte[], int, int, boolean)+0 in FileOutputStream.java:0", "void java.io.FileOutputStream.write(byte[], int, int)+0 in FileOutputStream.java:347", "void java.io.BufferedOutputStream.flushBuffer()+1 in BufferedOutputStream.java:81", "void java.io.BufferedOutputStream.flush()+0 in BufferedOutputStream.java:142", "void java.io.PrintStream.write(byte[], int, int)+4 in PrintStream.java:570", "void sun.nio.cs.StreamEncoder.writeBytes()+11 in StreamEncoder.java:242", "void sun.nio.cs.StreamEncoder.implFlushBuffer()+1 in StreamEncoder.java:321", "void sun.nio.cs.StreamEncoder.flushBuffer()+2 in StreamEncoder.java:110", "void java.io.OutputStreamWriter.flushBuffer()+0 in OutputStreamWriter.java:178", "void java.io.PrintStream.writeln(java.lang.String)+5 in PrintStream.java:723", "void java.io.PrintStream.println(java.lang.String)+1 in PrintStream.java:1028", "void HelloWorld.main(java.lang.String[])+0 in HelloWorld.java:4", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x83c424", "libjvm.so+0x8d0734", "libjvm.so+0x8d2f62", "libjli.so+0x4a4d", "libjli.so+0x841c", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43784, "frames": ["libpthread-2.31.so+0x10376", "libjvm.so+0xc3239a", "libjvm.so+0xbe072c", "libjvm.so+0xbf69e1", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43785, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe072c", "libjvm.so+0xea7974", "libjvm.so+0xea7a58", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43782, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe085a", "libjvm.so+0x5e5565", "libjvm.so+0x5e83b0", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}, {"lwp": 43781, "frames": ["libpthread-2.31.so+0x107b1", "libjvm.so+0xc323df", "libjvm.so+0xbe085a", "libjvm.so+0x5e5565", "libjvm.so+0x5e83b0", "libjvm.so+0xea8a5a", "libjvm.so+0xead720", "libjvm.so+0xc290ae", "libpthread-2.31.so+0x9608", "libc-2.31.so+0x122292"]}], "modules": null}