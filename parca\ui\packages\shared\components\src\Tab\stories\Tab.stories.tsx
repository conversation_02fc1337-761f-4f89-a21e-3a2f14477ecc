// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Tab from '..';

export default {
  component: Tab,
  title: 'Components/Tab',
  argTypes: {defaultTabIndex: {control: {type: 'select'}, options: [0, 1, 2]}},
};

const tabs = ['Tab 1', 'Tab 2', 'Tab 3'];
const panels = ['content 1', 'content 2', 'content 3'].map(text => (
  <div
    key={text}
    className="flex justify-center items-center h-32 text-gray-700 dark:text-gray-300"
  >
    {text}
  </div>
));
export const Default = {args: {tabs, panels}};
export const CustomTagIndex = {args: {tabs, panels, defaultTabIndex: 1}};
