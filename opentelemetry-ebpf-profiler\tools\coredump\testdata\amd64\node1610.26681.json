{"coredump-ref": "c74393e19c4d0d51d1f0a2bf1f2046b9e2cbe45952d8f42087109ab5c973613e", "threads": [{"lwp": 26681, "frames": ["node+0x9e7c50", "node+0x7ec150", "V8::ExitFrame+0 in :0", "writeSync+15 in node:fs:873", "SyncWriteStream._write+0 in node:internal/fs/sync_write_stream:0", "writeOrBuffer+24 in node:internal/streams/writable:389", "_write+47 in node:internal/streams/writable:330", "Writable.write+1 in node:internal/streams/writable:334", "value+28 in node:internal/console/constructor:289", "log+1 in node:internal/console/constructor:363", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xb8ee01", "node+0xb8fad4", "node+0xa0650f", "node+0x905af6", "V8::ExitFrame+0 in :0", "<anonymous>+11 in /home/<USER>/optimyze/node/hello.js:12", "Module._compile+46 in node:internal/modules/cjs/loader:1101", "Module._extensions..js+43 in node:internal/modules/cjs/loader:1153", "Module.load+12 in node:internal/modules/cjs/loader:981", "Module._load+65 in node:internal/modules/cjs/loader:822", "executeUserEntryPoint+7 in node:internal/modules/run_main:79", "<anonymous>+16 in node:internal/main/run_main_module:17", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xb8ee01", "node+0xb8fad4", "node+0xa0650f", "node+0x798836", "node+0x798b22", "node+0x799f39", "node+0x709d93", "node+0x8271d9", "node+0x82761a", "node+0x79c575", "ld-musl-x86_64.so.1+0x1ca02", "node+0x7034a3"]}, {"lwp": 26683, "frames": ["ld-musl-x86_64.so.1+0x55413", "ld-musl-x86_64.so.1+0x5272c", "ld-musl-x86_64.so.1+0x51d29", "ld-musl-x86_64.so.1+0x52b36", "libuv.so.1.0.0+0x185fa", "node+0x8571da", "ld-musl-x86_64.so.1+0x53220", "ld-musl-x86_64.so.1+0x553df"]}, {"lwp": 26684, "frames": ["ld-musl-x86_64.so.1+0x55413", "ld-musl-x86_64.so.1+0x5272c", "ld-musl-x86_64.so.1+0x51d29", "ld-musl-x86_64.so.1+0x52b36", "libuv.so.1.0.0+0x185fa", "node+0x8571da", "ld-musl-x86_64.so.1+0x53220", "ld-musl-x86_64.so.1+0x553df"]}, {"lwp": 26685, "frames": ["ld-musl-x86_64.so.1+0x55413", "ld-musl-x86_64.so.1+0x5272c", "ld-musl-x86_64.so.1+0x51d29", "ld-musl-x86_64.so.1+0x52b36", "libuv.so.1.0.0+0x185fa", "node+0x8571da", "ld-musl-x86_64.so.1+0x53220", "ld-musl-x86_64.so.1+0x553df"]}, {"lwp": 26682, "frames": ["ld-musl-x86_64.so.1+0x55413", "ld-musl-x86_64.so.1+0x5272c", "ld-musl-x86_64.so.1+0x1fdbc", "libuv.so.1.0.0+0x1cdbf", "libuv.so.1.0.0+0xd8ee", "node+0x85bab9", "ld-musl-x86_64.so.1+0x53220", "ld-musl-x86_64.so.1+0x553df"]}, {"lwp": 26687, "frames": ["ld-musl-x86_64.so.1+0x55413", "ld-musl-x86_64.so.1+0x5272c", "ld-musl-x86_64.so.1+0x51d29", "ld-musl-x86_64.so.1+0x54f64", "libuv.so.1.0.0+0x184cc", "node+0x8f8de4", "ld-musl-x86_64.so.1+0x53220", "ld-musl-x86_64.so.1+0x553df"]}, {"lwp": 26686, "frames": ["ld-musl-x86_64.so.1+0x55413", "ld-musl-x86_64.so.1+0x5272c", "ld-musl-x86_64.so.1+0x51d29", "ld-musl-x86_64.so.1+0x52b36", "libuv.so.1.0.0+0x185fa", "node+0x8571da", "ld-musl-x86_64.so.1+0x53220", "ld-musl-x86_64.so.1+0x553df"]}], "modules": null}