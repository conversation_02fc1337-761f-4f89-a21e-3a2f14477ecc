// @generated by protobuf-ts 2.9.6 with parameter generate_dependencies
// @generated from protobuf file "parca/telemetry/v1alpha1/telemetry.proto" (package "parca.telemetry.v1alpha1", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { TelemetryService } from "./telemetry";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { ReportPanicResponse } from "./telemetry";
import type { ReportPanicRequest } from "./telemetry";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * TelemetryService is the service that provides APIs to send information about the
 * Agents, such as unhandled panics and other relevant runtime data.
 *
 * @generated from protobuf service parca.telemetry.v1alpha1.TelemetryService
 */
export interface ITelemetryServiceClient {
    /**
     * ReportPanic receives information from an Agent that panic'ed.
     *
     * @generated from protobuf rpc: ReportPanic(parca.telemetry.v1alpha1.ReportPanicRequest) returns (parca.telemetry.v1alpha1.ReportPanicResponse);
     */
    reportPanic(input: ReportPanicRequest, options?: RpcOptions): UnaryCall<ReportPanicRequest, ReportPanicResponse>;
}
/**
 * TelemetryService is the service that provides APIs to send information about the
 * Agents, such as unhandled panics and other relevant runtime data.
 *
 * @generated from protobuf service parca.telemetry.v1alpha1.TelemetryService
 */
export class TelemetryServiceClient implements ITelemetryServiceClient, ServiceInfo {
    typeName = TelemetryService.typeName;
    methods = TelemetryService.methods;
    options = TelemetryService.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * ReportPanic receives information from an Agent that panic'ed.
     *
     * @generated from protobuf rpc: ReportPanic(parca.telemetry.v1alpha1.ReportPanicRequest) returns (parca.telemetry.v1alpha1.ReportPanicResponse);
     */
    reportPanic(input: ReportPanicRequest, options?: RpcOptions): UnaryCall<ReportPanicRequest, ReportPanicResponse> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<ReportPanicRequest, ReportPanicResponse>("unary", this._transport, method, opt, input);
    }
}
