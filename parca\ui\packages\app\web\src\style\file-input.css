#one {
  margin: 0 auto;
  margin-top: 50px;
  box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.2);
}
.it .btn-orange {
  background-color: transparent;
  border-color: #777 !important;
  color: #777;
  text-align: left;
  width: 100%;
}
.it input.form-control {
  height: 54px;
  border: none;
  margin-bottom: 0px;
  border-radius: 0px;
  border-bottom: 1px solid #ddd;
  box-shadow: none;
}
.it .form-control:focus {
  border-color: #117a8b;
  box-shadow: none;
  outline: none;
}
.fileUpload {
  position: relative;
  overflow: hidden;
  margin: 10px;
}
.fileUpload input.upload {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
  font-size: 20px;
  cursor: pointer;
  opacity: 0;
  filter: alpha(opacity=0);
}
.it .btn-new,
.it .btn-next {
  margin: 30px 0px;
  border-radius: 0px;
  background-color: #333;
  color: #f5f5f5;
  font-size: 16px;
  width: 155px;
}
.it .btn-next {
  background-color: #17a2b8;
  color: #fff;
}
.it .btn-next:hover {
  background-color: #117a8b;
  color: #fff;
}
.it .btn-check {
  cursor: pointer;
  line-height: 54px;
  color: red;
}
.it .uploadDoc {
  margin-bottom: 20px;
}
.it .uploadDoc {
  margin-bottom: 20px;
}
.it .btn-orange img {
  width: 30px;
}
.sharing-form p {
  font-size: 16px;
  text-align: center;
  margin: 30px 0px;
}
.it #uploader .docErr {
  position: absolute;
  right: auto;
  left: 10px;
  top: -56px;
  padding: 10px;
  font-size: 15px;
  background-color: #fff;
  color: red;
  box-shadow: 0px 0px 7px 2px rgba(0, 0, 0, 0.2);
  display: none;
}
.it #uploader .docErr:after {
  content: '\f0d7';
  display: inline-block;
  font-family: FontAwesome;
  font-size: 50px;
  color: #fff;
  position: absolute;
  left: 30px;
  bottom: -40px;
  text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.2);
}
.sharing-banner {
  margin-top: 20px;
}
