// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

export function binarySearchClosest(sortedArray: number[], seekElement: number): number {
  if (sortedArray.length === 1) {
    return 0;
  }

  let startIndex = 0;
  let endIndex: number = sortedArray.length - 1;
  while (startIndex <= endIndex) {
    if (endIndex - startIndex === 1) {
      const distanceToStart = seekElement - sortedArray[startIndex];
      const distanceToEnd = sortedArray[endIndex] - seekElement;
      if (distanceToStart < distanceToEnd) {
        return startIndex;
      }
      if (distanceToStart > distanceToEnd) {
        return endIndex;
      }
    }
    const mid = startIndex + Math.floor((endIndex - startIndex) / 2);
    const guess = sortedArray[mid];
    if (guess > seekElement) {
      endIndex = mid;
    } else {
      startIndex = mid;
    }
  }

  return -1;
}
