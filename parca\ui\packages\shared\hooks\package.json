{"name": "@parca/hooks", "version": "0.0.94", "description": "A library containing React hooks used in the Parca UI", "main": "dist/index.js", "scripts": {"test": "jest --coverage --config ../../../jest.config.js ./src/*", "prepublish": "pnpm run build", "build": "tsc", "build-swc": "swc ./src -d dist --copy-files", "watch": "tsc-watch"}, "dependencies": {"@parca/client": "workspace:*", "@parca/store": "workspace:*", "@parca/utilities": "workspace:*", "@rehooks/local-storage": "^2.4.4", "@types/lodash": "^4.17.0", "lodash": "^4.17.21", "react": "18.3.1", "react-dom": "18.3.1", "tsc-watch": "6.3.1"}, "keywords": [], "author": "", "license": "ISC", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "gitHead": "f92c5502bce797d27d67f57a39f8af30d0d04e1e"}