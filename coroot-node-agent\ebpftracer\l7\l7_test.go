package l7

import (
	"bytes"
	"encoding/binary"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
)

func TestParseHttp(t *testing.T) {
	m, p := ParseHttp([]byte(`HEAD /1 HTTP/1.1\nHost: 127.0.0.1\nUser-Agent: curl/8.0.1\nAccept: */*\n\nxzxxxxxxzx`))
	assert.Equal(t, "HEAD", m)
	assert.Equal(t, "/1", p)

	m, p = ParseHttp([]byte(`GET /too-long-uri`))
	assert.Equal(t, "GET", m)
	assert.Equal(t, "/too-long-uri...", p)
}

func Test_parseMemcached(t *testing.T) {
	cmd, items := ParseMemcached(append([]byte(`incr 1111 2222`), '\r', '\n'))
	assert.Equal(t, "incr", cmd)
	assert.Equal(t, []string{"1111"}, items)

	cmd, items = ParseMemcached(append([]byte(`gets 1111 2222 3333`), '\r', '\n'))
	assert.Equal(t, "gets", cmd)
	assert.Equal(t, []string{"1111", "2222", "3333"}, items)
}

func TestParseRedis(t *testing.T) {
	cmd, args := ParseRedis([]byte{
		'*', '3', '\r', '\n',
		'$', '4', '\r', '\n',
		'L', 'L', 'E', 'N', '\r', '\n',
		'$', '6', '\r', '\n',
		'm', 'y', 'l', 'i', 's', 't', '\r', '\n',
		'$', '2', '\r', '\n',
		'x', 'y', '\r', '\n',
	})
	assert.Equal(t, "LLEN", cmd)
	assert.Equal(t, "mylist ...", args)

	cmd, args = ParseRedis([]byte{
		'*', '2', '\r', '\n',
		'$', '8', '\r', '\n',
		'S', 'M', 'E', 'M', 'B', 'E', 'R', 'S', '\r', '\n',
		'$', '6', '\r', '\n',
		'm', 'y', 'l', 'i', 's', 't', '\r', '\n',
	})

	assert.Equal(t, "SMEMBERS", cmd)
	assert.Equal(t, "mylist", args)
}

type mongoHeader struct {
	MessageLength int32
	RequestID     int32
	ResponseTo    int32
	OpCode        int32
	Flags         int32
	SectionKind   uint8
}

func TestParseMongo(t *testing.T) {
	buf := bytes.NewBuffer(nil)
	v := bson.M{"a": "bssssssssssssssssssssssssssssssssssssssssss"}
	data, err := bson.Marshal(v)
	assert.NoError(t, err)

	h := mongoHeader{
		MessageLength: 16 + 4 + 1 + int32(len(data)),
		OpCode:        MongoOpMSG,
	}

	assert.NoError(t, binary.Write(buf, binary.LittleEndian, h))
	_, err = buf.Write(data)
	assert.NoError(t, err)

	payload := buf.Bytes()

	assert.Equal(t, `{"a": "bssssssssssssssssssssssssssssssssssssssssss"}`, ParseMongo(payload))
	assert.Equal(t, `<truncated>`, ParseMongo(payload[:20]))

	dataSize := binary.LittleEndian.Uint32(data)

	binary.LittleEndian.PutUint32(payload[mongoHeaderLength+mongoSectionKindLength:], dataSize+1)
	assert.Equal(t, `<truncated>`, ParseMongo(payload))
}

func TestParseClickHouse(t *testing.T) {
	payload := []byte{
		0x1, 0x24, 0x65, 0x38, 0x30, 0x63, 0x38, 0x31, 0x39, 0x62, 0x2d, 0x63, 0x33, 0x65, 0x33, 0x2d, 0x34, 0x66, 0x39,
		0x35, 0x2d, 0x38, 0x30, 0x62, 0x66, 0x2d, 0x39, 0x31, 0x39, 0x34, 0x66, 0x64, 0x37, 0x32, 0x33, 0x35, 0x62, 0x31,
		0x1, 0x0, 0x24, 0x65, 0x38, 0x30, 0x63, 0x38, 0x31, 0x39, 0x62, 0x2d, 0x63, 0x33, 0x65, 0x33, 0x2d, 0x34, 0x66,
		0x39, 0x35, 0x2d, 0x38, 0x30, 0x62, 0x66, 0x2d, 0x39, 0x31, 0x39, 0x34, 0x66, 0x64, 0x37, 0x32, 0x33, 0x35, 0x62,
		0x31, 0x11, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x33, 0x2e, 0x31, 0x32, 0x32, 0x3a, 0x35, 0x36, 0x39, 0x34, 0x30,
		0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75, 0x73,
		0x65, 0x2f, 0x63, 0x68, 0x2d, 0x67, 0x6f, 0x0, 0x3e, 0xbc, 0xa9, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
		0x2, 0x1, 0xd5, 0x2, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54, 0x20, 0x49, 0x4e, 0x54, 0x4f, 0x20, 0x22, 0x6f, 0x74, 0x65,
		0x6c, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
		0x64, 0x22, 0x20, 0x28, 0x22, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x2c, 0x22, 0x54, 0x72,
		0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x2c, 0x22, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x2c, 0x22, 0x50, 0x61,
		0x72, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x2c, 0x22, 0x54, 0x72, 0x61, 0x63, 0x65, 0x53,
		0x74, 0x61, 0x74, 0x65, 0x22, 0x2c, 0x22, 0x53, 0x70, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2c, 0x22, 0x53,
		0x70, 0x61, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x2c, 0x22, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
		0x6d, 0x65, 0x22, 0x2c, 0x22, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
		0x75, 0x74, 0x65, 0x73, 0x22, 0x2c, 0x22, 0x53, 0x70, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
		0x65, 0x73, 0x22, 0x2c, 0x22, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2c, 0x22, 0x53, 0x74, 0x61,
		0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x2c, 0x22, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73,
		0x73, 0x61, 0x67, 0x65, 0x22, 0x2c, 0x22, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
		0x74, 0x61, 0x6d, 0x70, 0x22, 0x2c, 0x22, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x22,
		0x2c, 0x22, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
		0x22, 0x2c, 0x22, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x2c, 0x22,
		0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x2c, 0x22, 0x4c, 0x69, 0x6e, 0x6b,
		0x73, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x2c, 0x22, 0x4c, 0x69, 0x6e, 0x6b,
		0x73, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x22, 0x29, 0x20, 0x56, 0x41, 0x4c, 0x55,
		0x45, 0x53, 0x0, 0x2, 0x0, 0xd5, 0xf4, 0x35, 0x76, 0x56, 0xdc, 0x39, 0x41, 0xc6, 0xda, 0xe5, 0x71, 0xb, 0xaf, 0x8d,
		0xe1, 0x82, 0x14, 0x0, 0x0, 0x0, 0xa, 0x0, 0x0, 0x0, 0xa0, 0x1, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
	}

	assert.Equal(t,
		`INSERT INTO "otel_traces_distributed" ("Timestamp","TraceId","SpanId","ParentSpanId","TraceState","SpanName","SpanKind","ServiceName","ResourceAttributes","SpanAttributes","Duration","StatusCode","StatusMessage","Events.Timestamp","Events.Name","Events.Attributes","Links.TraceId","Links.SpanId","Links.TraceState","Links.Attributes") VALUES`,
		ParseClickhouse(payload),
	)

	payload = []byte{
		0x1, 0x0, 0x1, 0x0, 0x0, 0x11, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x33, 0x2e, 0x31, 0x32, 0x32, 0x3a, 0x34, 0x38,
		0x33, 0x34, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x1d, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x2d,
		0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x2d, 0x36, 0x34, 0x36, 0x63, 0x36, 0x35, 0x64, 0x34, 0x62, 0x2d, 0x67, 0x67,
		0x68, 0x70, 0x67, 0x2c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2d, 0x67, 0x6f, 0x2f, 0x32,
		0x2e, 0x38, 0x2e, 0x33, 0x20, 0x28, 0x6c, 0x76, 0x3a, 0x67, 0x6f, 0x2f, 0x31, 0x2e, 0x32, 0x33, 0x2e, 0x34, 0x3b,
		0x20, 0x6f, 0x73, 0x3a, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x29, 0x2, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
		0x0, 0x2, 0x1, 0xe8, 0x8, 0x20, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x20, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
		0x6d, 0x70, 0x2c, 0x20, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x2c, 0x20, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64,
		0x2c, 0x20, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x2c, 0x20, 0x53, 0x70, 0x61,
		0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x2c, 0x20, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x2c,
		0x20, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2c, 0x20, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
		0x64, 0x65, 0x2c, 0x20, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2c, 0x20,
		0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2c,
		0x20, 0x53, 0x70, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2c, 0x20, 0x45, 0x76,
		0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2c, 0x20, 0x45, 0x76, 0x65,
		0x6e, 0x74, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x2c, 0x20, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x41, 0x74,
		0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x20, 0x46, 0x52, 0x4f, 0x4d, 0x20, 0x6f, 0x74, 0x65, 0x6c, 0x5f,
		0x74, 0x72, 0x61, 0x63, 0x65, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x64, 0x20,
		0x57, 0x48, 0x45, 0x52, 0x45, 0x20, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x20, 0x49,
		0x4e, 0x20, 0x28, 0x5b, 0x27, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x2f, 0x63, 0x6f,
		0x72, 0x6f, 0x6f, 0x74, 0x2d, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x27, 0x2c, 0x20, 0x27, 0x2f, 0x73, 0x79, 0x73,
		0x74, 0x65, 0x6d, 0x2e, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x2f, 0x6b, 0x33, 0x73, 0x2d, 0x61, 0x67, 0x65, 0x6e, 0x74,
		0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x27, 0x2c, 0x20, 0x27, 0x2f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
		0x2e, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x2f, 0x6b, 0x33, 0x73, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x27,
		0x5d, 0x29, 0x20, 0x41, 0x4e, 0x44, 0x20, 0x28, 0x53, 0x70, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
		0x74, 0x65, 0x73, 0x5b, 0x27, 0x6e, 0x65, 0x74, 0x2e, 0x70, 0x65, 0x65, 0x72, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x27,
		0x5d, 0x20, 0x49, 0x4e, 0x20, 0x28, 0x5b, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x33, 0x2e, 0x38, 0x34, 0x27,
		0x2c, 0x20, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x31, 0x2e, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27, 0x31, 0x30,
		0x2e, 0x34, 0x32, 0x2e, 0x30, 0x2e, 0x31, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e,
		0x35, 0x2e, 0x36, 0x39, 0x27, 0x5d, 0x29, 0x20, 0x4f, 0x52, 0x20, 0x28, 0x53, 0x70, 0x61, 0x6e, 0x41, 0x74, 0x74,
		0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x5b, 0x27, 0x6e, 0x65, 0x74, 0x2e, 0x70, 0x65, 0x65, 0x72, 0x2e, 0x6e,
		0x61, 0x6d, 0x65, 0x27, 0x5d, 0x2c, 0x20, 0x53, 0x70, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
		0x65, 0x73, 0x5b, 0x27, 0x6e, 0x65, 0x74, 0x2e, 0x70, 0x65, 0x65, 0x72, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x27, 0x5d,
		0x29, 0x20, 0x49, 0x4e, 0x20, 0x28, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x33, 0x2e, 0x38, 0x34, 0x27,
		0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x39, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e,
		0x33, 0x2e, 0x38, 0x34, 0x27, 0x2c, 0x20, 0x27, 0x30, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34,
		0x32, 0x2e, 0x33, 0x2e, 0x38, 0x34, 0x27, 0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x30, 0x27, 0x29, 0x2c, 0x20, 0x28,
		0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x33, 0x2e, 0x38, 0x34, 0x27, 0x2c, 0x20, 0x27, 0x38, 0x31, 0x32, 0x33,
		0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x31, 0x2e, 0x37, 0x33, 0x27, 0x2c, 0x20,
		0x27, 0x30, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x31, 0x2e, 0x37, 0x33, 0x27,
		0x2c, 0x20, 0x27, 0x38, 0x31, 0x32, 0x33, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e,
		0x31, 0x2e, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x39, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31,
		0x30, 0x2e, 0x34, 0x32, 0x2e, 0x31, 0x2e, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x30, 0x27, 0x29,
		0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x30, 0x2e, 0x31, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27,
		0x30, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x30, 0x2e, 0x31, 0x37, 0x33, 0x27,
		0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x39, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e,
		0x30, 0x2e, 0x31, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x30, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27,
		0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x30, 0x2e, 0x31, 0x37, 0x33, 0x27, 0x2c, 0x20, 0x27, 0x38, 0x31, 0x32, 0x33,
		0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x35, 0x2e, 0x36, 0x39, 0x27, 0x2c, 0x20,
		0x27, 0x39, 0x30, 0x30, 0x30, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x35, 0x2e,
		0x36, 0x39, 0x27, 0x2c, 0x20, 0x27, 0x38, 0x31, 0x32, 0x33, 0x27, 0x29, 0x2c, 0x20, 0x28, 0x27, 0x31, 0x30, 0x2e,
		0x34, 0x32, 0x2e, 0x35, 0x2e, 0x36, 0x39, 0x27, 0x2c, 0x20, 0x27, 0x39, 0x30, 0x30, 0x39, 0x27, 0x29, 0x2c, 0x20,
		0x28, 0x27, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x35, 0x2e, 0x36, 0x39, 0x27, 0x2c, 0x20, 0x27, 0x30, 0x27, 0x29,
		0x29, 0x29, 0x20, 0x41, 0x4e, 0x44, 0x20, 0x54, 0x69, 0x6d, 0x0,
	}
	assert.Equal(t,
		`SELECT Timestamp, TraceId, SpanId, ParentSpanId, SpanName, ServiceName, Duration, StatusCode, StatusMessage, ResourceAttributes, SpanAttributes, Events.Timestamp, Events.Name, Events.Attributes FROM otel_traces_distributed WHERE ServiceName IN (['/k8s/coroot/coroot-coroot', '/system.slice/k3s-agent.service', '/system.slice/k3s.service']) AND (SpanAttributes['net.peer.name'] IN (['10.42.3.84', '10.42.1.73', '10.42.0.173', '10.42.5.69']) OR (SpanAttributes['net.peer.name'], SpanAttributes['net.peer.port']) IN (('10.42.3.84', '9009'), ('10.42.3.84', '0'), ('10.42.3.84', '9000'), ('10.42.3.84', '8123'), ('10.42.1.73', '0'), ('10.42.1.73', '8123'), ('10.42.1.73', '9009'), ('10.42.1.73', '9000'), ('10.42.0.173', '0'), ('10.42.0.173', '9009'), ('10.42.0.173', '9000'), ('10.42.0.173', '8123'), ('10.42.5.69', '9000'), ('10.42.5.69', '8123'), ('10.42.5.69', '9009'), ('10.42.5.69', '0'))) AND Tim...<TRUNCATED>`,
		ParseClickhouse(payload),
	)

	payload = []byte{ // ClientQuerySecondary, parsing doesn't work
		0x01, 0x00, 0x02, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x24, 0x32, 0x34, 0x65, 0x61, 0x36, 0x33, 0x61,
		0x64, 0x2d, 0x32, 0x61, 0x34, 0x64, 0x2d, 0x34, 0x66, 0x32, 0x64, 0x2d, 0x38, 0x34, 0x65, 0x31, 0x2d, 0x34, 0x62,
		0x65, 0x62, 0x36, 0x35, 0x36, 0x61, 0x36, 0x30, 0x32, 0x32, 0x11, 0x31, 0x30, 0x2e, 0x34, 0x32, 0x2e, 0x30, 0x2e,
		0x32, 0x34, 0x33, 0x3a, 0x35, 0x38, 0x34, 0x37, 0x34, 0x78, 0xc3, 0x92, 0x37, 0xa3, 0x35, 0x06, 0x00, 0x01, 0x00,
		0x00, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x63, 0x68, 0x2d, 0x67, 0x6f, 0x00,
		0x3e, 0xbc, 0xa9, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x5f,
		0x6c, 0x6f, 0x67, 0x73, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x00, 0x04, 0x6e, 0x6f, 0x6e, 0x65, 0x00, 0x01, 0x00,
		0x00, 0x02, 0x01, 0xd0, 0x02, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54, 0x20, 0x49, 0x4e, 0x54, 0x4f, 0x20, 0x63, 0x6f,
		0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x62, 0x31, 0x66, 0x35, 0x62, 0x77, 0x6b, 0x67, 0x2e, 0x6f, 0x74, 0x65, 0x6c, 0x5f,
		0x74, 0x72, 0x61, 0x63, 0x65, 0x73, 0x20, 0x28, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2c, 0x20,
		0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x2c, 0x20, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x2c, 0x20, 0x50, 0x61,
		0x72, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x2c, 0x20, 0x54, 0x72, 0x61, 0x63, 0x65, 0x53, 0x74,
		0x61, 0x74, 0x65, 0x2c, 0x20, 0x53, 0x70, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x2c, 0x20, 0x53, 0x70, 0x61, 0x6e,
		0x4b, 0x69, 0x6e, 0x64, 0x2c, 0x20, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x2c, 0x20,
		0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2c,
		0x20, 0x53, 0x70, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x2c, 0x20, 0x44, 0x75,
		0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2c, 0x20, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x2c,
		0x20, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2c, 0x20, 0x60, 0x45, 0x76,
		0x65, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x60, 0x2c, 0x20, 0x60, 0x45,
		0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x60, 0x2c, 0x20, 0x60, 0x45, 0x76, 0x65, 0x6e, 0x74,
		0x73, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x60, 0x2c, 0x20, 0x60, 0x4c, 0x69, 0x6e,
		0x6b, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x60, 0x2c, 0x20, 0x60, 0x4c, 0x69, 0x6e, 0x6b, 0x73,
		0x2e, 0x53, 0x70, 0x61, 0x6e, 0x49, 0x64, 0x60, 0x2c, 0x20, 0x60, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x54, 0x72,
		0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x60, 0x2c, 0x20, 0x60, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x41,
		0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x60, 0x29, 0x20, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53, 0x00,
		0x02, 0x00, 0xa7, 0x83, 0xac, 0x6c, 0xd5, 0x5c, 0x7a, 0x7c, 0xb5, 0xac, 0x46, 0xbd, 0xdb, 0x86, 0xe2, 0x14, 0x82,
		0x14, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0xa0, 0x01, 0x00, 0x02, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00,
	}

	assert.Equal(t,
		``,
		ParseClickhouse(payload),
	)

}

func TestParseZookeeper(t *testing.T) {
	payload := []byte{
		0x0, 0x0, 0x1, 0x4a, 0x0, 0xcc, 0x17, 0x68, 0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x5, 0x0, 0xff, 0xff, 0xff, 0xff,
		0x0, 0x0, 0x0, 0x83, 0x2f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x74, 0x61, 0x62,
		0x6c, 0x65, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x64, 0x2d, 0x31, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x5f,
		0x33, 0x6b, 0x75, 0x71, 0x38, 0x62, 0x33, 0x7a, 0x2f, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65,
		0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6c, 0x69,
		0x63, 0x61, 0x73, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x2d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75,
		0x73, 0x65, 0x2d, 0x73, 0x68, 0x61, 0x72, 0x64, 0x2d, 0x31, 0x2d, 0x30, 0x2f, 0x6d, 0x69, 0x6e, 0x5f, 0x75, 0x6e,
		0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x69,
		0x6d, 0x65, 0x0, 0x0, 0x0, 0x1, 0x30, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x5, 0x0, 0xff, 0xff, 0xff, 0xff, 0x0,
		0x0, 0x0, 0x81, 0x2f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x74, 0x61, 0x62, 0x6c,
		0x65, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x64, 0x2d, 0x31, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x33,
		0x6b, 0x75, 0x71, 0x38, 0x62, 0x33, 0x7a, 0x2f, 0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x73,
		0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63,
		0x61, 0x73, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x2d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68, 0x6f, 0x75, 0x73,
		0x65, 0x2d, 0x73, 0x68, 0x61, 0x72, 0x64, 0x2d, 0x31, 0x2d, 0x30, 0x2f, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x6f,
		0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x0,
		0x0, 0x0, 0xa, 0x31, 0x37, 0x33, 0x34, 0x36, 0x30, 0x37, 0x32, 0x34, 0x39, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
		0xff, 0xff, 0x1, 0xff, 0xff, 0xff, 0xff,
	}
	op, arg := ParseZookeeper(payload)
	assert.Equal(t, "multi(setData, ...)", op)
	assert.Equal(t, "/clickhouse/tables/shard-1/coroot_3kuq8b3z/otel_traces_trace_id_ts/replicas/coroot-clickhouse-shard-1-0/min_unprocessed_insert_time", arg)

	payload = []byte{
		0x0, 0x0, 0x0, 0x53, 0x0, 0xce, 0x16, 0x90, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x46, 0x2f, 0x63, 0x6c, 0x69, 0x63,
		0x6b, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x64,
		0x2d, 0x31, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x33, 0x6b, 0x75, 0x71, 0x38, 0x62, 0x33, 0x7a, 0x2f,
		0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69,
		0x64, 0x5f, 0x74, 0x73, 0x2f, 0x6c, 0x6f, 0x67, 0x0,
	}
	op, arg = ParseZookeeper(payload)

	assert.Equal(t, "getData", op)
	assert.Equal(t, "/clickhouse/tables/shard-1/coroot_3kuq8b3z/otel_traces_trace_id_ts/log", arg)

	payload = []byte{
		0x0, 0x0, 0x0, 0x53, 0x0, 0xce, 0x16, 0x90, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x46, 0x2f, 0x63, 0x6c, 0x69, 0x63,
		0x6b, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x64,
		0x2d, 0x31, 0x2f, 0x63, 0x6f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x33, 0x6b, 0x75, 0x71, 0x38, 0x62, 0x33, 0x7a, 0x2f,
		0x6f, 0x74, 0x65, 0x6c, 0x5f, 0x74,
	}
	op, arg = ParseZookeeper(payload)

	assert.Equal(t, "getData", op)
	assert.Equal(t, "/clickhouse/tables/shard-1/coroot_3kuq8b3z/otel_t...<TRUNCATED>", arg)
}
