# Deep-eBPF Server Makefile
# Based on coroot server build system

BINARY := deep-ebpf-server
GO := go
DOCKER := docker

# Version information
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "unknown")
COMMIT ?= $(shell git rev-parse HEAD 2>/dev/null || echo "unknown")
DATE ?= $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Build flags
LDFLAGS := -X main.version=$(VERSION)
BUILD_FLAGS := -ldflags "$(LDFLAGS)"

.PHONY: all server clean test check-deps help

# Default target
all: server

# Build server
server:
	@echo "Building Deep-eBPF server..."
	$(GO) mod tidy
	$(GO) build $(BUILD_FLAGS) -o $(BINARY) ./main.go
	@echo "Server built successfully: $(BINARY)"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(BINARY)
	$(GO) clean

# Run tests
test:
	@echo "Running tests..."
	$(GO) test ./...

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@which $(GO) > /dev/null || (echo "Go is not installed" && exit 1)
	@echo "Dependencies OK"

# Install dependencies
deps:
	@echo "Installing Go dependencies..."
	$(GO) mod download

# Format code
fmt:
	@echo "Formatting code..."
	$(GO) fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run || echo "golangci-lint not installed, skipping"

# Run the server
run: server
	@echo "Running deep-ebpf-server..."
	./$(BINARY) --help

# Run with development configuration
dev: server
	@echo "Running in development mode..."
	./$(BINARY) --dev --listen :8080 --data-dir ./dev-data

# Run with custom config
run-config: server
	@echo "Usage: make run-config CONFIG=config.yaml"
	@if [ -z "$(CONFIG)" ]; then echo "Please specify CONFIG: make run-config CONFIG=config.yaml"; exit 1; fi
	./$(BINARY) --config $(CONFIG)

# Generate example configuration
config:
	@echo "Generating example configuration..."
	@echo "# Deep-eBPF Server Configuration" > config.example.yaml
	@echo "" >> config.example.yaml
	@echo "# Server settings" >> config.example.yaml
	@echo "listen_address: \":8080\"" >> config.example.yaml
	@echo "url_base_path: \"/\"" >> config.example.yaml
	@echo "data_dir: \"./data\"" >> config.example.yaml
	@echo "developer_mode: false" >> config.example.yaml
	@echo "" >> config.example.yaml
	@echo "# Storage configuration" >> config.example.yaml
	@echo "storage:" >> config.example.yaml
	@echo "  type: \"persistent\"" >> config.example.yaml
	@echo "  retention_days: 30" >> config.example.yaml
	@echo "  compression: true" >> config.example.yaml
	@echo "  max_memory_mb: 1024" >> config.example.yaml
	@echo "  flush_interval: \"5m\"" >> config.example.yaml
	@echo "" >> config.example.yaml
	@echo "# Data collector configuration" >> config.example.yaml
	@echo "collector:" >> config.example.yaml
	@echo "  listen_address: \":8081\"" >> config.example.yaml
	@echo "  buffer_size: 10000" >> config.example.yaml
	@echo "  agent_timeout: \"30s\"" >> config.example.yaml
	@echo "  enable_discovery: true" >> config.example.yaml
	@echo "  max_agents: 1000" >> config.example.yaml
	@echo "" >> config.example.yaml
	@echo "# Function trace processor configuration" >> config.example.yaml
	@echo "processor:" >> config.example.yaml
	@echo "  buffer_size: 50000" >> config.example.yaml
	@echo "  correlation_ttl: \"5m\"" >> config.example.yaml
	@echo "  aggregation_window: \"1m\"" >> config.example.yaml
	@echo "  max_concurrency: 10" >> config.example.yaml
	@echo "" >> config.example.yaml
	@echo "# API configuration" >> config.example.yaml
	@echo "api:" >> config.example.yaml
	@echo "  enable_cors: true" >> config.example.yaml
	@echo "  rate_limit: 1000" >> config.example.yaml
	@echo "  auth_enabled: false" >> config.example.yaml
	@echo "Example configuration written to config.example.yaml"

# Install the binary
install: server
	@echo "Installing $(BINARY)..."
	sudo cp $(BINARY) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(BINARY)

# Uninstall the binary
uninstall:
	@echo "Uninstalling $(BINARY)..."
	sudo rm -f /usr/local/bin/$(BINARY)

# Check system requirements
check-system:
	@echo "Checking system requirements..."
	@echo "Go version: $(shell $(GO) version)"
	@echo "Available memory: $(shell free -h | grep Mem | awk '{print $$2}' || echo 'unknown')"
	@echo "Available disk space: $(shell df -h . | tail -1 | awk '{print $$4}' || echo 'unknown')"
	@echo "Network interfaces:"
	@ip addr show 2>/dev/null | grep inet || echo "Could not list network interfaces"

# Generate documentation
docs:
	@echo "Generating documentation..."
	$(GO) doc -all ./... > docs/API.md

# Create release
release: clean server
	@echo "Creating release $(VERSION)..."
	mkdir -p release
	cp $(BINARY) release/
	cp README.md release/
	cp config.example.yaml release/
	tar -czf release/deep-ebpf-server-$(VERSION).tar.gz -C release .
	@echo "Release created: release/deep-ebpf-server-$(VERSION).tar.gz"

# Docker build
docker:
	@echo "Building Docker image..."
	$(DOCKER) build -t deep-ebpf-server:$(VERSION) .
	$(DOCKER) tag deep-ebpf-server:$(VERSION) deep-ebpf-server:latest

# Docker run
docker-run: docker
	@echo "Running Docker container..."
	$(DOCKER) run -p 8080:8080 -p 8081:8081 -v $(PWD)/data:/data deep-ebpf-server:latest

# Performance test
perf-test: server
	@echo "Running performance tests..."
	@echo "TODO: Implement performance tests"

# Load test
load-test: server
	@echo "Running load tests..."
	@echo "TODO: Implement load tests with multiple agents"

# Integration test
integration-test: server
	@echo "Running integration tests..."
	@echo "Starting server in background..."
	./$(BINARY) --dev --listen :18080 --data-dir ./test-data &
	SERVER_PID=$$!; \
	sleep 5; \
	echo "Testing health endpoint..."; \
	curl -f http://localhost:18080/health || (kill $$SERVER_PID; exit 1); \
	echo "Testing API endpoints..."; \
	curl -f http://localhost:18080/api/status || (kill $$SERVER_PID; exit 1); \
	echo "Stopping server..."; \
	kill $$SERVER_PID; \
	rm -rf ./test-data; \
	echo "Integration tests passed"

# Benchmark
benchmark:
	@echo "Running benchmarks..."
	$(GO) test -bench=. ./...

# Show help
help:
	@echo "Deep-eBPF Server - Available targets:"
	@echo ""
	@echo "  all              - Build server (default)"
	@echo "  server           - Build server binary"
	@echo "  clean            - Clean build artifacts"
	@echo "  test             - Run tests"
	@echo "  check-deps       - Check dependencies"
	@echo "  deps             - Install Go dependencies"
	@echo "  fmt              - Format code"
	@echo "  lint             - Lint code"
	@echo "  run              - Run the server (shows help)"
	@echo "  dev              - Run in development mode"
	@echo "  run-config       - Run with custom config (make run-config CONFIG=config.yaml)"
	@echo "  config           - Generate example configuration"
	@echo "  install          - Install binary to /usr/local/bin"
	@echo "  uninstall        - Remove installed binary"
	@echo "  check-system     - Check system requirements"
	@echo "  docs             - Generate documentation"
	@echo "  release          - Create release package"
	@echo "  docker           - Build Docker image"
	@echo "  docker-run       - Run Docker container"
	@echo "  perf-test        - Run performance tests"
	@echo "  load-test        - Run load tests"
	@echo "  integration-test - Run integration tests"
	@echo "  benchmark        - Run benchmarks"
	@echo "  help             - Show this help message"
	@echo ""
	@echo "Usage examples:"
	@echo "  1. make check-system    # Verify system requirements"
	@echo "  2. make check-deps      # Check dependencies"
	@echo "  3. make server          # Build server"
	@echo "  4. make config          # Generate example config"
	@echo "  5. make dev             # Run in development mode"
	@echo ""
	@echo "For more information, see README.md"
