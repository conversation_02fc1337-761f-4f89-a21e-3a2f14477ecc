syntax = "proto3";

package parca.debuginfo.v1alpha1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/parca-dev/parca/gen/go/debuginfo";

// DebuginfoService is a service that allows storage of debug info
service DebuginfoService {
  // Upload ingests debug info for a given build_id
  rpc Upload(stream UploadRequest) returns (UploadResponse) {
    option (google.api.http) = {
      post: "/upload"
      body: "*"
    };
  }

  // ShouldInitiateUpload returns whether an upload for a given build_id should be initiated or not.
  rpc ShouldInitiateUpload(ShouldInitiateUploadRequest) returns (ShouldInitiateUploadResponse) {
    option (google.api.http) = {
      post: "/shouldinitiateupload"
      body: "*"
    };
  }

  // InitiateUpload returns a strategy and information to upload debug info for a given build_id.
  rpc InitiateUpload(InitiateUploadRequest) returns (InitiateUploadResponse) {
    option (google.api.http) = {
      post: "/initiateupload"
      body: "*"
    };
  }

  // MarkUploadFinished marks the upload as finished for a given build_id.
  rpc MarkUploadFinished(MarkUploadFinishedRequest) returns (MarkUploadFinishedResponse) {
    option (google.api.http) = {
      post: "/markuploadfinished"
      body: "*"
    };
  }
}

// Types of debuginfo.
enum DebuginfoType {
  // The default type that the API always supported. This type is expected to
  // contain debuginfos for symbolizaton purposes.
  DEBUGINFO_TYPE_DEBUGINFO_UNSPECIFIED = 0;
  // The type to identify executables. This is meant to be used for
  // disassembling so it is expected to contain executable `.text` section.
  DEBUGINFO_TYPE_EXECUTABLE = 1;
  // The type to identify a source tarball. This is expected to contain
  // multiple source files that debuginfo references. It is meant to show code
  // with profiling data inline.
  DEBUGINFO_TYPE_SOURCES = 2;
}

// ShouldInitiateUploadRequest is the request for ShouldInitiateUpload.
message ShouldInitiateUploadRequest {
  // The build_id of the debuginfo.
  string build_id = 1;
  // Hash of the debuginfo to upload.
  string hash = 2;
  // Force uploading even if valid debuginfos are already available.
  bool force = 3;
  // Type of debuginfo to propose uploading.
  DebuginfoType type = 4;
  // Type of build ID.
  BuildIDType build_id_type = 5;
}

// BuildIDType is the type of build ID.
enum BuildIDType {
  // The build ID is unknown.
  BUILD_ID_TYPE_UNKNOWN_UNSPECIFIED = 0;
  // The build ID is a GNU build ID.
  BUILD_ID_TYPE_GNU = 1;
  // The build ID is an opaque hash.
  BUILD_ID_TYPE_HASH = 2;
  // The build ID is a Go build ID.
  BUILD_ID_TYPE_GO = 3;
}

// ShouldInitiateUploadResponse is the response for ShouldInitiateUpload.
message ShouldInitiateUploadResponse {
  // Whether an upload should be initiated or not.
  bool should_initiate_upload = 1;
  // Reason for why an upload should be initiated or not.
  string reason = 2;
}

// InitiateUploadRequest is the request to initiate an upload.
message InitiateUploadRequest {
  // The build_id of the debug info to upload.
  string build_id = 1;
  // The size of the debug info to upload.
  int64 size = 2;
  // Hash of the debuginfo to upload.
  string hash = 3;
  // Force uploading even if valid debuginfos are already available.
  bool force = 4;
  // Type of debuginfo to propose uploading.
  DebuginfoType type = 5;
  // Type of build ID.
  BuildIDType build_id_type = 6;
}

// InitiateUploadResponse is the response to an InitiateUploadRequest.
message InitiateUploadResponse {
  // UploadInstructions contains the instructions for the client to upload the debuginfo.
  UploadInstructions upload_instructions = 1;
}

// UploadInstructions contains the instructions for the client to upload debuginfo.
message UploadInstructions {
  // The build ID of the debuginfo to upload.
  string build_id = 1;

  // The strategy to use for uploading.
  enum UploadStrategy {
    // The upload is not allowed.
    UPLOAD_STRATEGY_UNSPECIFIED = 0;
    // The upload is allowed and should be done via the Upload RPC.
    UPLOAD_STRATEGY_GRPC = 1;
    // The upload is allowed and should be done via a returned signed URL.
    UPLOAD_STRATEGY_SIGNED_URL = 2;
  }

  // The upload_id to use for uploading.
  string upload_id = 2;
  // The strategy to use for uploading.
  UploadStrategy upload_strategy = 3;
  // The signed url to use for uploading using a PUT request when the upload
  // strategy is SIGNED_STRATEGY_URL.
  string signed_url = 4;
  // Type of debuginfo the upload instructions are for.
  DebuginfoType type = 5;
}

// MarkUploadFinishedRequest is the request to mark an upload as finished.
message MarkUploadFinishedRequest {
  // The build_id of the debug info to mark as finished.
  string build_id = 1;
  // The upload_id of the debug info to mark as finished.
  string upload_id = 2;
  // The type of debuginfo upload to mark as finished.
  DebuginfoType type = 3;
}

// MarkUploadFinishedResponse is the response to a MarkUploadFinishedRequest.
message MarkUploadFinishedResponse {}

// UploadRequest upload debug info
message UploadRequest {
  // data contains either the upload info metadata or the debug info
  oneof data {
    // info is the metadata for the debug info
    UploadInfo info = 1;

    // chunk_data is the raw bytes of the debug info
    bytes chunk_data = 2;
  }
}

// UploadInfo contains the build_id and other metadata for the debug data
message UploadInfo {
  // build_id is a unique identifier for the debug data
  string build_id = 1;
  // upload_id is a unique identifier for the upload
  string upload_id = 2;
  // the type of debuginfo that's being uploaded
  DebuginfoType type = 3;
}

// UploadResponse returns the build_id and the size of the uploaded debug info
message UploadResponse {
  // build_id is a unique identifier for the debug data
  string build_id = 1;

  // size is the number of bytes of the debug info
  uint64 size = 2;
}

// Debuginfo contains metadata about a debuginfo file.
message Debuginfo {
  // BuildID is the build ID of the debuginfo.
  string build_id = 1;

  // Source is the source of the debuginfo.
  enum Source {
    // To understand when no source is set we have the unknown source.
    SOURCE_UNKNOWN_UNSPECIFIED = 0;
    // The debuginfo was uploaded by a user/agent.
    SOURCE_UPLOAD = 1;
    // The debuginfo is available from the configured debuginfod server(s).
    SOURCE_DEBUGINFOD = 2;
  }

  // Source is the source of the debuginfo.
  Source source = 2;

  // DebuginfoUpload is the debuginfo upload metadata.
  DebuginfoUpload upload = 3;

  // Quality is the quality of the debuginfo. This is set asynchonously by the
  // symbolizer when the debuginfo is actually used.
  DebuginfoQuality quality = 4;

  // The debuginfod servers this piece of debuginfo is available at.
  repeated string debuginfod_servers = 5;

  // The type of debuginfo.
  DebuginfoType type = 6;
}

// DebuginfoUpload contains metadata about a debuginfo upload.
message DebuginfoUpload {
  // UploadID is the ID of the debuginfo upload.
  string id = 1;

  // Hash is the hash of the debuginfo.
  string hash = 2;

  // The state of the debuginfo upload.
  enum State {
    // To understand when no upload state is set we have the unknown state.
    STATE_UNKNOWN_UNSPECIFIED = 0;
    // The debuginfo is currently being uploaded.
    STATE_UPLOADING = 1;
    // The debuginfo has been uploaded successfully.
    STATE_UPLOADED = 2;
  }

  // State is the current state of the debuginfo upload.
  State state = 3;

  // StartedAt is the time the debuginfo upload was started.
  google.protobuf.Timestamp started_at = 4;

  // FinishedAt is the time the debuginfo upload was finished.
  google.protobuf.Timestamp finished_at = 5;
}

// DebuginfoQuality is the quality of the debuginfo.
message DebuginfoQuality {
  // The debuginfo file is not a valid ELF file.
  bool not_valid_elf = 1;
  // Whether the debuginfo contains dwarf information.
  bool has_dwarf = 2;
  // Whether the debuginfo contains Go's pclntab.
  bool has_go_pclntab = 3;
  // Whether the debuginfo contains symtab.
  bool has_symtab = 4;
  // Whether the debuginfo contains dynsym.
  bool has_dynsym = 5;
}
