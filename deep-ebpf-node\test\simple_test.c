#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>

// Simple test functions for deep-ebpf-node validation
int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fi<PERSON><PERSON>ci(n - 2);
}

int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

void test_function_calls() {
    printf("Testing function calls for eBPF tracing...\n");
    
    for (int i = 0; i < 10; i++) {
        int fib_result = fibonacci(i + 5);
        int fact_result = factorial(i + 1);
        
        printf("Iteration %d: fibonacci(%d) = %d, factorial(%d) = %d\n", 
               i, i + 5, fib_result, i + 1, fact_result);
        
        // Sleep to make tracing easier to observe
        usleep(500000); // 0.5 seconds
    }
}

int main(int argc, char *argv[]) {
    printf("Deep-eBPF Node Test Program\n");
    printf("PID: %d\n", getpid());
    printf("This program will make function calls that should be traced by deep-ebpf-node\n");
    printf("Run: sudo ./deep-ebpf-node --target-pid %d --format human\n", getpid());
    printf("\n");
    
    // Give user time to start the tracer
    printf("Starting in 5 seconds...\n");
    sleep(5);
    
    test_function_calls();
    
    printf("\nTest completed. Check the tracer output for function call traces.\n");
    return 0;
}
