/* roboto-v30-latin */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 100;
    src: url('../fonts/roboto-100.eot');
    src: local(''),
    url('../fonts/roboto-100.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-100.woff2') format('woff2'),
    url('../fonts/roboto-100.woff') format('woff'),
    url('../fonts/roboto-100.ttf') format('truetype'),
    url('../fonts/roboto-100.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 100;
    src: url('../fonts/roboto-100italic.eot');
    src: local(''),
    url('../fonts/roboto-100italic.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-100italic.woff2') format('woff2'),
    url('../fonts/roboto-100italic.woff') format('woff'),
    url('../fonts/roboto-100italic.ttf') format('truetype'),
    url('../fonts/roboto-100italic.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: url('../fonts/roboto-300.eot');
    src: local(''),
    url('../fonts/roboto-300.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-300.woff2') format('woff2'),
    url('../fonts/roboto-300.woff') format('woff'),
    url('../fonts/roboto-300.ttf') format('truetype'),
    url('../fonts/roboto-300.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/roboto-regular.eot');
    src: local(''),
    url('../fonts/roboto-regular.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-regular.woff2') format('woff2'),
    url('../fonts/roboto-regular.woff') format('woff'),
    url('../fonts/roboto-regular.ttf') format('truetype'),
    url('../fonts/roboto-regular.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 300;
    src: url('../fonts/roboto-300italic.eot');
    src: local(''),
    url('../fonts/roboto-300italic.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-300italic.woff2') format('woff2'),
    url('../fonts/roboto-300italic.woff') format('woff'),
    url('../fonts/roboto-300italic.ttf') format('truetype'),
    url('../fonts/roboto-300italic.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 400;
    src: url('../fonts/roboto-italic.eot');
    src: local(''),
    url('../fonts/roboto-italic.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-italic.woff2') format('woff2'),
    url('../fonts/roboto-italic.woff') format('woff'),
    url('../fonts/roboto-italic.ttf') format('truetype'),
    url('../fonts/roboto-italic.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: url('../fonts/roboto-500.eot');
    src: local(''),
    url('../fonts/roboto-500.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-500.woff2') format('woff2'),
    url('../fonts/roboto-500.woff') format('woff'),
    url('../fonts/roboto-500.ttf') format('truetype'),
    url('../fonts/roboto-500.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 500;
    src: url('../fonts/roboto-500italic.eot');
    src: local(''),
    url('../fonts/roboto-500italic.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-500italic.woff2') format('woff2'),
    url('../fonts/roboto-500italic.woff') format('woff'),
    url('../fonts/roboto-500italic.ttf') format('truetype'),
    url('../fonts/roboto-500italic.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/roboto-700.eot');
    src: local(''),
    url('../fonts/roboto-700.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-700.woff2') format('woff2'),
    url('../fonts/roboto-700.woff') format('woff'),
    url('../fonts/roboto-700.ttf') format('truetype'),
    url('../fonts/roboto-700.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 700;
    src: url('../fonts/roboto-700italic.eot');
    src: local(''),
    url('../fonts/roboto-700italic.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-700italic.woff2') format('woff2'),
    url('../fonts/roboto-700italic.woff') format('woff'),
    url('../fonts/roboto-700italic.ttf') format('truetype'),
    url('../fonts/roboto-700italic.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 900;
    src: url('../fonts/roboto-900.eot');
    src: local(''),
    url('../fonts/roboto-900.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-900.woff2') format('woff2'),
    url('../fonts/roboto-900.woff') format('woff'),
    url('../fonts/roboto-900.ttf') format('truetype'),
    url('../fonts/roboto-900.svg#Roboto') format('svg');
}
@font-face {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 900;
    src: url('../fonts/roboto-900italic.eot');
    src: local(''),
    url('../fonts/roboto-900italic.eot?#iefix') format('embedded-opentype'),
    url('../fonts/roboto-900italic.woff2') format('woff2'),
    url('../fonts/roboto-900italic.woff') format('woff'),
    url('../fonts/roboto-900italic.ttf') format('truetype'),
    url('../fonts/roboto-900italic.svg#Roboto') format('svg');
}