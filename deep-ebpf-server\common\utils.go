package common

import (
	"crypto/rand"
	"embed"
	"encoding/json"
	"fmt"
	"io/fs"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// CreateDirectoryIfNotExists creates a directory if it doesn't exist
func CreateDirectoryIfNotExists(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	}
	return nil
}

// GetInstanceUuid generates or retrieves a unique instance UUID
func GetInstanceUuid(dataDir string) string {
	uuidFile := filepath.Join(dataDir, "instance.uuid")
	
	// Try to read existing UUID
	if data, err := os.ReadFile(uuidFile); err == nil {
		return strings.TrimSpace(string(data))
	}
	
	// Generate new UUID
	uuid := generateUUID()
	
	// Save UUID to file
	if err := os.WriteFile(uuidFile, []byte(uuid), 0644); err == nil {
		return uuid
	}
	
	// Return generated UUID even if we couldn't save it
	return uuid
}

// generateUUID generates a simple UUID
func generateUUID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:])
}

// StaticFSWrapper wraps an embedded filesystem for HTTP serving
type StaticFSWrapper struct {
	fs embed.FS
}

// NewStaticFSWrapper creates a new static filesystem wrapper
func NewStaticFSWrapper(fs embed.FS) *StaticFSWrapper {
	return &StaticFSWrapper{fs: fs}
}

// Open implements http.FileSystem
func (w *StaticFSWrapper) Open(name string) (http.File, error) {
	// Remove leading slash and add static prefix
	name = strings.TrimPrefix(name, "/")
	if !strings.HasPrefix(name, "static/") {
		name = "static/" + name
	}
	
	file, err := w.fs.Open(name)
	if err != nil {
		return nil, err
	}
	
	return &StaticFile{File: file}, nil
}

// StaticFile wraps an embedded file for HTTP serving
type StaticFile struct {
	fs.File
}

// Readdir implements http.File
func (f *StaticFile) Readdir(count int) ([]os.FileInfo, error) {
	return nil, fmt.Errorf("readdir not supported")
}

// Stat implements http.File
func (f *StaticFile) Stat() (os.FileInfo, error) {
	return f.File.Stat()
}

// FormatBytes formats bytes in human-readable format
func FormatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := uint64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// FormatDuration formats duration in human-readable format
func FormatDuration(d int64) string {
	if d < 1000 {
		return fmt.Sprintf("%dns", d)
	} else if d < 1000000 {
		return fmt.Sprintf("%.1fµs", float64(d)/1000.0)
	} else if d < 1000000000 {
		return fmt.Sprintf("%.1fms", float64(d)/1000000.0)
	} else {
		return fmt.Sprintf("%.1fs", float64(d)/1000000000.0)
	}
}

// ValidatePort validates if a port number is valid
func ValidatePort(port int) bool {
	return port > 0 && port <= 65535
}

// ParseAddress parses an address string and validates it
func ParseAddress(addr string) (string, error) {
	if addr == "" {
		return "", fmt.Errorf("address cannot be empty")
	}
	
	// Basic validation - should contain a colon for host:port
	if !strings.Contains(addr, ":") {
		return "", fmt.Errorf("address must be in format host:port")
	}
	
	return addr, nil
}

// EnsureTrailingSlash ensures a path has a trailing slash
func EnsureTrailingSlash(path string) string {
	if !strings.HasSuffix(path, "/") {
		return path + "/"
	}
	return path
}

// RemoveTrailingSlash removes trailing slash from a path
func RemoveTrailingSlash(path string) string {
	return strings.TrimSuffix(path, "/")
}

// SafeFilename creates a safe filename from a string
func SafeFilename(name string) string {
	// Replace unsafe characters
	unsafe := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", " "}
	safe := name
	for _, char := range unsafe {
		safe = strings.ReplaceAll(safe, char, "_")
	}
	return safe
}

// Contains checks if a slice contains a string
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// RemoveDuplicates removes duplicate strings from a slice
func RemoveDuplicates(slice []string) []string {
	seen := make(map[string]bool)
	result := []string{}
	
	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// GetEnvOrDefault gets an environment variable or returns a default value
func GetEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// FileExists checks if a file exists
func FileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// DirExists checks if a directory exists
func DirExists(dirname string) bool {
	info, err := os.Stat(dirname)
	return !os.IsNotExist(err) && info.IsDir()
}

// CreateTempFile creates a temporary file with the given content
func CreateTempFile(content []byte, suffix string) (string, error) {
	tmpFile, err := os.CreateTemp("", "*"+suffix)
	if err != nil {
		return "", err
	}
	defer tmpFile.Close()
	
	if _, err := tmpFile.Write(content); err != nil {
		os.Remove(tmpFile.Name())
		return "", err
	}
	
	return tmpFile.Name(), nil
}

// CleanupTempFile removes a temporary file
func CleanupTempFile(filename string) {
	os.Remove(filename)
}

// GetFileSize returns the size of a file in bytes
func GetFileSize(filename string) (int64, error) {
	info, err := os.Stat(filename)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// TruncateString truncates a string to a maximum length
func TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

// PadString pads a string to a minimum length
func PadString(s string, minLen int, padChar rune) string {
	if len(s) >= minLen {
		return s
	}
	padding := strings.Repeat(string(padChar), minLen-len(s))
	return s + padding
}

// IsValidJSON checks if a string is valid JSON
func IsValidJSON(s string) bool {
	var js interface{}
	return json.Unmarshal([]byte(s), &js) == nil
}

// Min returns the minimum of two integers
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Max returns the maximum of two integers
func Max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// Clamp clamps a value between min and max
func Clamp(value, min, max int) int {
	if value < min {
		return min
	}
	if value > max {
		return max
	}
	return value
}
