package storage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// PersistentStorage implements file-based persistent storage
type PersistentStorage struct {
	dataDir string
	
	// In-memory cache for fast access
	cache *MemoryStorage
	
	// File handles and locks
	callsFile   *os.File
	metricsFile *os.File
	fileLock    sync.Mutex
	
	// Background flush
	flushInterval time.Duration
	stopChan      chan struct{}
}

// NewPersistentStorage creates a new persistent storage instance
func NewPersistentStorage(dataDir string) (*PersistentStorage, error) {
	// Create data directory if it doesn't exist
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}
	
	// Initialize in-memory cache
	cache := NewMemoryStorage()
	
	storage := &PersistentStorage{
		dataDir:       dataDir,
		cache:         cache,
		flushInterval: 5 * time.Minute,
		stopChan:      make(chan struct{}),
	}
	
	// Open data files
	if err := storage.openFiles(); err != nil {
		return nil, fmt.Errorf("failed to open data files: %w", err)
	}
	
	// Load existing data
	if err := storage.loadData(); err != nil {
		return nil, fmt.Errorf("failed to load existing data: %w", err)
	}
	
	// Start background flush
	go storage.backgroundFlush()
	
	return storage, nil
}

// openFiles opens the data files
func (p *PersistentStorage) openFiles() error {
	var err error
	
	// Open function calls file
	callsPath := filepath.Join(p.dataDir, "function_calls.jsonl")
	p.callsFile, err = os.OpenFile(callsPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open calls file: %w", err)
	}
	
	// Open metrics file
	metricsPath := filepath.Join(p.dataDir, "function_metrics.jsonl")
	p.metricsFile, err = os.OpenFile(metricsPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open metrics file: %w", err)
	}
	
	return nil
}

// loadData loads existing data from files
func (p *PersistentStorage) loadData() error {
	// Load function calls
	if err := p.loadFunctionCalls(); err != nil {
		return fmt.Errorf("failed to load function calls: %w", err)
	}
	
	// Load metrics
	if err := p.loadFunctionMetrics(); err != nil {
		return fmt.Errorf("failed to load function metrics: %w", err)
	}
	
	return nil
}

// loadFunctionCalls loads function calls from file
func (p *PersistentStorage) loadFunctionCalls() error {
	callsPath := filepath.Join(p.dataDir, "function_calls.jsonl")
	
	file, err := os.Open(callsPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // File doesn't exist yet, that's OK
		}
		return err
	}
	defer file.Close()
	
	decoder := json.NewDecoder(file)
	for decoder.More() {
		var call FunctionCall
		if err := decoder.Decode(&call); err != nil {
			// Log error but continue loading
			continue
		}
		
		// Add to cache
		p.cache.StoreFunctionCall(&call)
	}
	
	return nil
}

// loadFunctionMetrics loads function metrics from file
func (p *PersistentStorage) loadFunctionMetrics() error {
	metricsPath := filepath.Join(p.dataDir, "function_metrics.jsonl")
	
	file, err := os.Open(metricsPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // File doesn't exist yet, that's OK
		}
		return err
	}
	defer file.Close()
	
	decoder := json.NewDecoder(file)
	for decoder.More() {
		var metrics FunctionMetrics
		if err := decoder.Decode(&metrics); err != nil {
			// Log error but continue loading
			continue
		}
		
		// Add to cache
		p.cache.StoreFunctionMetrics(&metrics)
	}
	
	return nil
}

// backgroundFlush periodically flushes data to disk
func (p *PersistentStorage) backgroundFlush() {
	ticker := time.NewTicker(p.flushInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			p.flush()
		case <-p.stopChan:
			p.flush() // Final flush
			return
		}
	}
}

// flush flushes buffered data to disk
func (p *PersistentStorage) flush() {
	p.fileLock.Lock()
	defer p.fileLock.Unlock()
	
	if p.callsFile != nil {
		p.callsFile.Sync()
	}
	if p.metricsFile != nil {
		p.metricsFile.Sync()
	}
}

// StoreFunctionCall stores a function call
func (p *PersistentStorage) StoreFunctionCall(call *FunctionCall) error {
	// Store in cache first
	if err := p.cache.StoreFunctionCall(call); err != nil {
		return err
	}
	
	// Write to file
	p.fileLock.Lock()
	defer p.fileLock.Unlock()
	
	data, err := json.Marshal(call)
	if err != nil {
		return err
	}
	
	_, err = p.callsFile.Write(append(data, '\n'))
	return err
}

// GetFunctionCall retrieves a function call by ID
func (p *PersistentStorage) GetFunctionCall(id string) (*FunctionCall, error) {
	return p.cache.GetFunctionCall(id)
}

// QueryFunctionCalls queries function calls with options
func (p *PersistentStorage) QueryFunctionCalls(opts QueryOptions) ([]*FunctionCall, error) {
	return p.cache.QueryFunctionCalls(opts)
}

// DeleteFunctionCalls deletes function calls before a given time
func (p *PersistentStorage) DeleteFunctionCalls(before time.Time) error {
	// Delete from cache
	if err := p.cache.DeleteFunctionCalls(before); err != nil {
		return err
	}
	
	// TODO: Implement file compaction to remove deleted records
	// For now, we just delete from cache
	
	return nil
}

// StoreFunctionMetrics stores function metrics
func (p *PersistentStorage) StoreFunctionMetrics(metrics *FunctionMetrics) error {
	// Store in cache first
	if err := p.cache.StoreFunctionMetrics(metrics); err != nil {
		return err
	}
	
	// Write to file
	p.fileLock.Lock()
	defer p.fileLock.Unlock()
	
	data, err := json.Marshal(metrics)
	if err != nil {
		return err
	}
	
	_, err = p.metricsFile.Write(append(data, '\n'))
	return err
}

// GetFunctionMetrics retrieves function metrics for a time range
func (p *PersistentStorage) GetFunctionMetrics(functionName string, start, end time.Time) ([]*FunctionMetrics, error) {
	return p.cache.GetFunctionMetrics(functionName, start, end)
}

// QueryFunctionMetrics queries function metrics with options
func (p *PersistentStorage) QueryFunctionMetrics(opts QueryOptions) ([]*FunctionMetrics, error) {
	return p.cache.QueryFunctionMetrics(opts)
}

// GetFunctionList returns a list of all function names
func (p *PersistentStorage) GetFunctionList() ([]string, error) {
	return p.cache.GetFunctionList()
}

// GetProcessList returns a list of all process names
func (p *PersistentStorage) GetProcessList() ([]string, error) {
	return p.cache.GetProcessList()
}

// GetLatencyMetrics calculates latency metrics for a function
func (p *PersistentStorage) GetLatencyMetrics(functionName string, start, end time.Time) (*LatencyMetrics, error) {
	return p.cache.GetLatencyMetrics(functionName, start, end)
}

// GetThroughputMetrics calculates throughput metrics for a function
func (p *PersistentStorage) GetThroughputMetrics(functionName string, start, end time.Time) (*ThroughputMetrics, error) {
	return p.cache.GetThroughputMetrics(functionName, start, end)
}

// GetErrorMetrics calculates error metrics for a function
func (p *PersistentStorage) GetErrorMetrics(functionName string, start, end time.Time) (*ErrorMetrics, error) {
	return p.cache.GetErrorMetrics(functionName, start, end)
}

// GetStats returns storage statistics
func (p *PersistentStorage) GetStats() map[string]interface{} {
	stats := p.cache.GetStats()
	stats["type"] = "persistent"
	stats["data_dir"] = p.dataDir
	
	// Add file sizes
	if callsInfo, err := p.callsFile.Stat(); err == nil {
		stats["calls_file_size"] = callsInfo.Size()
	}
	if metricsInfo, err := p.metricsFile.Stat(); err == nil {
		stats["metrics_file_size"] = metricsInfo.Size()
	}
	
	return stats
}

// Close closes the storage
func (p *PersistentStorage) Close() error {
	// Stop background flush
	close(p.stopChan)
	
	// Close files
	p.fileLock.Lock()
	defer p.fileLock.Unlock()
	
	if p.callsFile != nil {
		p.callsFile.Close()
	}
	if p.metricsFile != nil {
		p.metricsFile.Close()
	}
	
	return nil
}
