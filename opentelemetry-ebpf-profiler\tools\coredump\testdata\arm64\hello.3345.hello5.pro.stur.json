{"coredump-ref": "77ca3d48ef63837014f381a9e7986bcd6d95bd87856f7c40faaf1a55039a063a", "threads": [{"lwp": 3662, "frames": ["main.hello5+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:40", "main.hello4+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:36", "main.hello3+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:30", "main.hello2+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:25", "main.hello1+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:17", "main.hello+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:12", "main.main+0 in /media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.go:47", "runtime.main+0 in /usr/local/go/src/runtime/proc.go:259", "runtime.goexit+0 in /usr/local/go/src/runtime/asm_arm64.s:1166"]}, {"lwp": 3664, "frames": ["runtime.usleep+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:142", "runtime.sysmon+0 in /usr/local/go/src/runtime/proc.go:5162", "runtime.mstart1+0 in /usr/local/go/src/runtime/proc.go:1428", "runtime.mstart0+0 in /usr/local/go/src/runtime/proc.go:1359", "runtime.mstart+0 in /usr/local/go/src/runtime/asm_arm64.s:129"]}, {"lwp": 3665, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2867", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 3666, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.startlockedm+0 in /usr/local/go/src/runtime/proc.go:2471", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3241", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 3667, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2867", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}], "modules": [{"ref": "bb87b9bba238372aad8a65bacbf088583cf7a580daa23abfed2467a19307c707", "local-path": "/media/psf/devel/prodfiler/utils/coredump/testsources/go/hello.3345"}]}