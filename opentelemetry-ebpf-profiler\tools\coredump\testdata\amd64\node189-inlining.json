{"coredump-ref": "136df05d67bcb15743e7107d0cbd9078a168e42b62efe2017fc0e7bbb2c0479f", "threads": [{"lwp": 17272, "frames": ["node+0xefb26a", "node+0xe70c1d", "node+0xb54ea0", "node+0xb5ac91", "node+0xb5c255", "node+0x9eb4f7", "node+0xefd2a7", "node+0x10a7bc2", "V8::ExitFrame+0 in :0", "trace+6 in node:internal/console/constructor:427", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xb1dd4d", "node+0xb1ef67", "node+0x99f5ad", "node+0x897b4a", "V8::ExitFrame+0 in :0", "add+1 in /home/<USER>/optimyze/node/test.js:3", "add3+1 in /home/<USER>/optimyze/node/test.js:8", "test+1 in /home/<USER>/optimyze/node/test.js:12", "submain+2 in /home/<USER>/optimyze/node/test.js:17", "main+2 in /home/<USER>/optimyze/node/test.js:23", "<anonymous>+26 in /home/<USER>/optimyze/node/test.js:27", "Module._compile+46 in node:internal/modules/cjs/loader:1119", "Module._extensions..js+45 in node:internal/modules/cjs/loader:1173", "Module.load+12 in node:internal/modules/cjs/loader:997", "Module._load+68 in node:internal/modules/cjs/loader:838", "executeUserEntryPoint+0 in node:internal/modules/run_main:0", "<anonymous>+0 in node:internal/main/run_main_module:0", "V8::InternalFrame+0 in :0", "V8::EntryFrame+0 in :0", "node+0xb1dd4d", "node+0xb1ef67", "node+0x99f5ad", "node+0x72803d", "node+0x728146", "node+0x729cc6", "node+0x69c422", "node+0x7ba27d", "node+0x7ba6af", "node+0x72928f", "node+0x72c764", "ld-musl-x86_64.so.1+0x1ca21", "node+0x66659c"]}, {"lwp": 17276, "frames": ["ld-musl-x86_64.so.1+0x554a3", "ld-musl-x86_64.so.1+0x526f9", "ld-musl-x86_64.so.1+0x51cf4", "ld-musl-x86_64.so.1+0x52b03", "libuv.so.1.0.0+0x1aeb4", "node+0x7e45aa", "ld-musl-x86_64.so.1+0x531f4", "ld-musl-x86_64.so.1+0x5546f"]}, {"lwp": 17275, "frames": ["ld-musl-x86_64.so.1+0x554a3", "ld-musl-x86_64.so.1+0x526f9", "ld-musl-x86_64.so.1+0x51cf4", "ld-musl-x86_64.so.1+0x52b03", "libuv.so.1.0.0+0x1aeb4", "node+0x7e45aa", "ld-musl-x86_64.so.1+0x531f4", "ld-musl-x86_64.so.1+0x5546f"]}, {"lwp": 17273, "frames": ["ld-musl-x86_64.so.1+0x554a3", "ld-musl-x86_64.so.1+0x526f9", "ld-musl-x86_64.so.1+0x1fddb", "libuv.so.1.0.0+0x1f5f2", "libuv.so.1.0.0+0x1021b", "node+0x7e93c7", "ld-musl-x86_64.so.1+0x531f4", "ld-musl-x86_64.so.1+0x5546f"]}, {"lwp": 17278, "frames": ["ld-musl-x86_64.so.1+0x554a3", "ld-musl-x86_64.so.1+0x526f9", "ld-musl-x86_64.so.1+0x51cf4", "ld-musl-x86_64.so.1+0x55005", "libuv.so.1.0.0+0x1ad86", "node+0x887560", "ld-musl-x86_64.so.1+0x531f4", "ld-musl-x86_64.so.1+0x5546f"]}, {"lwp": 17277, "frames": ["ld-musl-x86_64.so.1+0x554a3", "ld-musl-x86_64.so.1+0x526f9", "ld-musl-x86_64.so.1+0x51cf4", "ld-musl-x86_64.so.1+0x52b03", "libuv.so.1.0.0+0x1aeb4", "node+0x7e45aa", "ld-musl-x86_64.so.1+0x531f4", "ld-musl-x86_64.so.1+0x5546f"]}, {"lwp": 17274, "frames": ["ld-musl-x86_64.so.1+0x554a3", "ld-musl-x86_64.so.1+0x526f9", "ld-musl-x86_64.so.1+0x51cf4", "ld-musl-x86_64.so.1+0x52b03", "libuv.so.1.0.0+0x1aeb4", "node+0x7e45aa", "ld-musl-x86_64.so.1+0x531f4", "ld-musl-x86_64.so.1+0x5546f"]}], "modules": [{"ref": "5ab1ef5271e278d73e0bf67f8ffd5316ebdd7f65ba80d78e8ab73d2a081abe0a", "local-path": "/usr/lib/libgcc_s.so.1"}, {"ref": "5437ef6d210060ffd1155a4fbb3efe900e81588f0bb6a7e06935ec9bf27668b4", "local-path": "/usr/lib/libstdc++.so.6.0.30"}, {"ref": "9cbc38e234cde3798aca950f3f63880e162383aefe376b60bad87915802fd41f", "local-path": "/usr/lib/libuv.so.1.0.0"}, {"ref": "1b8ef46271445700050eb307b887fc73cc1b687ed43e83151d2c696cbdf3e054", "local-path": "/usr/lib/libicuuc.so.71.1"}, {"ref": "a3a410820b3d773d81a75a7dcc9d2346a5dad03e92cf82b35c92c2c13098aeb2", "local-path": "/usr/lib/libnghttp2.so.14.23.0"}, {"ref": "9bdf749ca620aac88e30575deac05d6f076ed01ebfcd4c59a461c9b08192eabd", "local-path": "/usr/lib/libcares.so.2.5.1"}, {"ref": "465eee61ae9960b541c86df05007b18abb2758aaa539790d4e247d9ca6c2f1f2", "local-path": "/lib/libcrypto.so.3"}, {"ref": "ed4af2df45ae11c1427cdedb3e8709689be255f82fe238086fef9525a07db39c", "local-path": "/lib/libz.so.1.2.12"}, {"ref": "cd3409849cb7fb1d938cb3d52a39951d2d3084af994b7a92246affe19ab2a5e4", "local-path": "/usr/lib/libicui18n.so.71.1"}, {"ref": "d69e8d7e2f8570f8b736c6061f9dc0bfe969733a17f3f5518ad33890dc7928c4", "local-path": "/lib/libssl.so.3"}, {"ref": "703e21e00df635be9406c653ab153ea6cc31d16b24eedbf8147f589da6355917", "local-path": "/usr/lib/libbrotlienc.so.1.0.9"}, {"ref": "77d4e17277f51885a677d16771620f0a8fa5696f5e05db3e6cc89c2d4df53f94", "local-path": "/usr/lib/libbrotlidec.so.1.0.9"}, {"ref": "2a973a89a5d64b0fe31c1bce2339d3774b683c526513b7b0bbaa487d4d0c3d3b", "local-path": "/lib/ld-musl-x86_64.so.1"}, {"ref": "8ec76ae6d3acc71c81eaa12797f71d02a2f42c39b15d310c4e3619f69d11e6ab", "local-path": "/usr/lib/debug/lib/ld-musl-x86_64.so.1.debug"}, {"ref": "877aef4b82e3c6dd8b54b18ed742f97a4d7c37229b5ab50a50eedac20456b404", "local-path": "/usr/bin/node"}, {"ref": "363684f4c2cc049e17e9e784d6369b4f590c11e08d69538e01a2d90dc0914ef5", "local-path": "/usr/lib/libbrotlicommon.so.1.0.9"}]}