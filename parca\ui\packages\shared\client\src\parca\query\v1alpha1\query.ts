// @generated by protobuf-ts 2.9.6 with parameter generate_dependencies
// @generated from protobuf file "parca/query/v1alpha1/query.proto" (package "parca.query.v1alpha1", syntax proto3)
// tslint:disable
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { Unknown<PERSON>ieldHand<PERSON> } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { Line } from "../../metastore/v1alpha1/metastore";
import { Function } from "../../metastore/v1alpha1/metastore";
import { Mapping } from "../../metastore/v1alpha1/metastore";
import { Location } from "../../metastore/v1alpha1/metastore";
import { LabelSet } from "../../profilestore/v1alpha1/profilestore";
import { Duration } from "../../../google/protobuf/duration";
import { Timestamp } from "../../../google/protobuf/timestamp";
/**
 * ProfileTypesRequest is the request to retrieve the list of available profile types.
 *
 * @generated from protobuf message parca.query.v1alpha1.ProfileTypesRequest
 */
export interface ProfileTypesRequest {
}
/**
 * ProfileTypesResponse is the response to retrieve the list of available profile types.
 *
 * @generated from protobuf message parca.query.v1alpha1.ProfileTypesResponse
 */
export interface ProfileTypesResponse {
    /**
     * types is the list of available profile types.
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.ProfileType types = 1;
     */
    types: ProfileType[];
}
/**
 * ProfileType is the type of a profile as well as the units the profile type is available in.
 *
 * @generated from protobuf message parca.query.v1alpha1.ProfileType
 */
export interface ProfileType {
    /**
     * name is the name of the profile type.
     *
     * @generated from protobuf field: string name = 1;
     */
    name: string;
    /**
     * sample_type is the type of the samples in the profile.
     *
     * @generated from protobuf field: string sample_type = 2;
     */
    sampleType: string;
    /**
     * sample_unit is the unit of the samples in the profile.
     *
     * @generated from protobuf field: string sample_unit = 3;
     */
    sampleUnit: string;
    /**
     * period_type is the type of the periods in the profile.
     *
     * @generated from protobuf field: string period_type = 4;
     */
    periodType: string;
    /**
     * period_unit is the unit of the periods in the profile.
     *
     * @generated from protobuf field: string period_unit = 5;
     */
    periodUnit: string;
    /**
     * delta describes whether the profile is a delta profile.
     *
     * @generated from protobuf field: bool delta = 6;
     */
    delta: boolean;
}
/**
 * QueryRangeRequest is the request for a set of profiles matching a query over a time window
 *
 * @generated from protobuf message parca.query.v1alpha1.QueryRangeRequest
 */
export interface QueryRangeRequest {
    /**
     * query is the query string to match profiles against
     *
     * @generated from protobuf field: string query = 1;
     */
    query: string;
    /**
     * start is the start of the query time window
     *
     * @generated from protobuf field: google.protobuf.Timestamp start = 2;
     */
    start?: Timestamp;
    /**
     * end is the end of the query time window
     *
     * @generated from protobuf field: google.protobuf.Timestamp end = 3;
     */
    end?: Timestamp;
    /**
     * limit is the max number of profiles to include in the response
     *
     * @generated from protobuf field: uint32 limit = 4;
     */
    limit: number;
    /**
     * step is the duration of each sample returned.
     *
     * @generated from protobuf field: google.protobuf.Duration step = 5;
     */
    step?: Duration;
    /**
     * sum_by is the set of labels to sum by
     *
     * @generated from protobuf field: repeated string sum_by = 6;
     */
    sumBy: string[];
}
/**
 * QueryRangeResponse is the set of matching profile values
 *
 * @generated from protobuf message parca.query.v1alpha1.QueryRangeResponse
 */
export interface QueryRangeResponse {
    /**
     * series is the set of metrics series that satisfy the query range request
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.MetricsSeries series = 1;
     */
    series: MetricsSeries[];
}
/**
 * MetricsSeries is a set of labels and corresponding sample values
 *
 * @generated from protobuf message parca.query.v1alpha1.MetricsSeries
 */
export interface MetricsSeries {
    /**
     * labelset is the set of key value pairs
     *
     * @generated from protobuf field: parca.profilestore.v1alpha1.LabelSet labelset = 1;
     */
    labelset?: LabelSet;
    /**
     * samples is the set of top-level cumulative values of the corresponding profiles
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.MetricsSample samples = 2;
     */
    samples: MetricsSample[];
    /**
     * period_type is the value type of profile period
     *
     * @generated from protobuf field: parca.query.v1alpha1.ValueType period_type = 3;
     */
    periodType?: ValueType;
    /**
     * sample_type is the value type of profile sample
     *
     * @generated from protobuf field: parca.query.v1alpha1.ValueType sample_type = 4;
     */
    sampleType?: ValueType;
}
/**
 * MetricsSample is a cumulative value and timestamp of a profile
 *
 * @generated from protobuf message parca.query.v1alpha1.MetricsSample
 */
export interface MetricsSample {
    /**
     * timestamp is the time the profile was ingested
     *
     * @generated from protobuf field: google.protobuf.Timestamp timestamp = 1;
     */
    timestamp?: Timestamp;
    /**
     * value is the cumulative value for the profile
     *
     * @generated from protobuf field: int64 value = 2;
     */
    value: bigint;
    /**
     * value_per_second is the calculated per second average in the steps duration
     *
     * @generated from protobuf field: double value_per_second = 3;
     */
    valuePerSecond: number;
    /**
     * duration is the normalized aggregated duration the metric samples has been observed over.
     *
     * @generated from protobuf field: int64 duration = 4;
     */
    duration: bigint;
}
/**
 * MergeProfile contains parameters for a merge request
 *
 * @generated from protobuf message parca.query.v1alpha1.MergeProfile
 */
export interface MergeProfile {
    /**
     * query is the query string to match profiles for merge
     *
     * @generated from protobuf field: string query = 1;
     */
    query: string;
    /**
     * start is the beginning of the evaluation time window
     *
     * @generated from protobuf field: google.protobuf.Timestamp start = 2;
     */
    start?: Timestamp;
    /**
     * end is the end of the evaluation time window
     *
     * @generated from protobuf field: google.protobuf.Timestamp end = 3;
     */
    end?: Timestamp;
}
/**
 * SingleProfile contains parameters for a single profile query request
 *
 * @generated from protobuf message parca.query.v1alpha1.SingleProfile
 */
export interface SingleProfile {
    /**
     * time is the point in time to perform the profile request
     *
     * @generated from protobuf field: google.protobuf.Timestamp time = 1;
     */
    time?: Timestamp;
    /**
     * query is the query string to retrieve the profile
     *
     * @generated from protobuf field: string query = 2;
     */
    query: string;
}
/**
 * DiffProfile contains parameters for a profile diff request
 *
 * @generated from protobuf message parca.query.v1alpha1.DiffProfile
 */
export interface DiffProfile {
    /**
     * a is the first profile to diff
     *
     * @generated from protobuf field: parca.query.v1alpha1.ProfileDiffSelection a = 1;
     */
    a?: ProfileDiffSelection;
    /**
     * b is the second profile to diff
     *
     * @generated from protobuf field: parca.query.v1alpha1.ProfileDiffSelection b = 2;
     */
    b?: ProfileDiffSelection;
    /**
     * absolute diffing, by default comparisons are relative
     *
     * @generated from protobuf field: optional bool absolute = 3;
     */
    absolute?: boolean;
}
/**
 * ProfileDiffSelection contains the parameters of a diff selection
 *
 * @generated from protobuf message parca.query.v1alpha1.ProfileDiffSelection
 */
export interface ProfileDiffSelection {
    /**
     * mode is the selection of the diff mode
     *
     * @generated from protobuf field: parca.query.v1alpha1.ProfileDiffSelection.Mode mode = 1;
     */
    mode: ProfileDiffSelection_Mode;
    /**
     * @generated from protobuf oneof: options
     */
    options: {
        oneofKind: "merge";
        /**
         * merge contains options for a merge request
         *
         * @generated from protobuf field: parca.query.v1alpha1.MergeProfile merge = 2;
         */
        merge: MergeProfile;
    } | {
        oneofKind: "single";
        /**
         * single contains options for a single profile request
         *
         * @generated from protobuf field: parca.query.v1alpha1.SingleProfile single = 3;
         */
        single: SingleProfile;
    } | {
        oneofKind: undefined;
    };
}
/**
 * Mode specifies the type of diff
 *
 * @generated from protobuf enum parca.query.v1alpha1.ProfileDiffSelection.Mode
 */
export enum ProfileDiffSelection_Mode {
    /**
     * MODE_SINGLE_UNSPECIFIED default unspecified
     *
     * @generated from protobuf enum value: MODE_SINGLE_UNSPECIFIED = 0;
     */
    SINGLE_UNSPECIFIED = 0,
    /**
     * MODE_MERGE merge profile
     *
     * @generated from protobuf enum value: MODE_MERGE = 1;
     */
    MERGE = 1
}
/**
 * QueryRequest is a request for a profile query
 *
 * @generated from protobuf message parca.query.v1alpha1.QueryRequest
 */
export interface QueryRequest {
    /**
     * mode indicates the type of query performed
     *
     * @generated from protobuf field: parca.query.v1alpha1.QueryRequest.Mode mode = 1;
     */
    mode: QueryRequest_Mode;
    /**
     * @generated from protobuf oneof: options
     */
    options: {
        oneofKind: "diff";
        /**
         * diff contains the diff query options
         *
         * @generated from protobuf field: parca.query.v1alpha1.DiffProfile diff = 2;
         */
        diff: DiffProfile;
    } | {
        oneofKind: "merge";
        /**
         * merge contains the merge query options
         *
         * @generated from protobuf field: parca.query.v1alpha1.MergeProfile merge = 3;
         */
        merge: MergeProfile;
    } | {
        oneofKind: "single";
        /**
         * single contains the single query options
         *
         * @generated from protobuf field: parca.query.v1alpha1.SingleProfile single = 4;
         */
        single: SingleProfile;
    } | {
        oneofKind: undefined;
    };
    /**
     * report_type is the type of report to return
     *
     * @generated from protobuf field: parca.query.v1alpha1.QueryRequest.ReportType report_type = 5;
     */
    reportType: QueryRequest_ReportType;
    /**
     * filter_query is the query string to filter the profile samples
     *
     * @deprecated
     * @generated from protobuf field: optional string filter_query = 6 [deprecated = true];
     */
    filterQuery?: string;
    /**
     * node_trim_threshold is the threshold % where the nodes with Value less than this will be removed from the report
     *
     * @generated from protobuf field: optional float node_trim_threshold = 7;
     */
    nodeTrimThreshold?: number;
    /**
     * group_by indicates the fields to group by
     *
     * @generated from protobuf field: optional parca.query.v1alpha1.GroupBy group_by = 8;
     */
    groupBy?: GroupBy;
    /**
     * source information about the source requested, required if source report is requested
     *
     * @generated from protobuf field: optional parca.query.v1alpha1.SourceReference source_reference = 9;
     */
    sourceReference?: SourceReference;
    /**
     * which runtime frames to filter out, often interpreter frames like python or ruby are not super useful by default
     *
     * @deprecated
     * @generated from protobuf field: optional parca.query.v1alpha1.RuntimeFilter runtime_filter = 10 [deprecated = true];
     */
    runtimeFilter?: RuntimeFilter;
    /**
     * invert_call_stack inverts the call stacks in the flamegraph
     *
     * @generated from protobuf field: optional bool invert_call_stack = 11;
     */
    invertCallStack?: boolean;
    /**
     * a set of filter to apply to the query request
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.Filter filter = 12;
     */
    filter: Filter[];
    /**
     * sandwich_by_function is a function name to use for sandwich view functionality
     *
     * @generated from protobuf field: optional string sandwich_by_function = 13;
     */
    sandwichByFunction?: string;
}
/**
 * Mode is the type of query request
 *
 * @generated from protobuf enum parca.query.v1alpha1.QueryRequest.Mode
 */
export enum QueryRequest_Mode {
    /**
     * MODE_SINGLE_UNSPECIFIED query unspecified
     *
     * @generated from protobuf enum value: MODE_SINGLE_UNSPECIFIED = 0;
     */
    SINGLE_UNSPECIFIED = 0,
    /**
     * MODE_DIFF is a diff query
     *
     * @generated from protobuf enum value: MODE_DIFF = 1;
     */
    DIFF = 1,
    /**
     * MODE_MERGE is a merge query
     *
     * @generated from protobuf enum value: MODE_MERGE = 2;
     */
    MERGE = 2
}
/**
 * ReportType is the type of report to return
 *
 * @generated from protobuf enum parca.query.v1alpha1.QueryRequest.ReportType
 */
export enum QueryRequest_ReportType {
    /**
     * REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED unspecified
     *
     * @deprecated
     * @generated from protobuf enum value: REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED = 0 [deprecated = true];
     */
    FLAMEGRAPH_UNSPECIFIED = 0,
    /**
     * REPORT_TYPE_PPROF unspecified
     *
     * @generated from protobuf enum value: REPORT_TYPE_PPROF = 1;
     */
    PPROF = 1,
    /**
     * REPORT_TYPE_TOP unspecified
     *
     * @generated from protobuf enum value: REPORT_TYPE_TOP = 2;
     */
    TOP = 2,
    /**
     * REPORT_TYPE_CALLGRAPH unspecified
     *
     * @generated from protobuf enum value: REPORT_TYPE_CALLGRAPH = 3;
     */
    CALLGRAPH = 3,
    /**
     * REPORT_TYPE_FLAMEGRAPH_TABLE unspecified
     *
     * @generated from protobuf enum value: REPORT_TYPE_FLAMEGRAPH_TABLE = 4;
     */
    FLAMEGRAPH_TABLE = 4,
    /**
     * REPORT_TYPE_FLAMEGRAPH_ARROW unspecified
     *
     * @generated from protobuf enum value: REPORT_TYPE_FLAMEGRAPH_ARROW = 5;
     */
    FLAMEGRAPH_ARROW = 5,
    /**
     * REPORT_TYPE_SOURCE contains source code annotated with profiling information
     *
     * @generated from protobuf enum value: REPORT_TYPE_SOURCE = 6;
     */
    SOURCE = 6,
    /**
     * REPORT_TYPE_TABLE_ARROW unspecified
     *
     * @generated from protobuf enum value: REPORT_TYPE_TABLE_ARROW = 7;
     */
    TABLE_ARROW = 7,
    /**
     * REPORT_TYPE_PROFILE_METADATA contains metadata about the profile i.e. binaries, labels
     *
     * @generated from protobuf enum value: REPORT_TYPE_PROFILE_METADATA = 8;
     */
    PROFILE_METADATA = 8,
    /**
     * REPORT_TYPE_FLAMECHART contains flamechart representation of the report
     *
     * @generated from protobuf enum value: REPORT_TYPE_FLAMECHART = 9;
     */
    FLAMECHART = 9
}
/**
 * Filter to apply to the query request
 *
 * @generated from protobuf message parca.query.v1alpha1.Filter
 */
export interface Filter {
    /**
     * @generated from protobuf oneof: filter
     */
    filter: {
        oneofKind: "stackFilter";
        /**
         * stack_filter is a filter for filtering by stacks
         *
         * @generated from protobuf field: parca.query.v1alpha1.StackFilter stack_filter = 1;
         */
        stackFilter: StackFilter;
    } | {
        oneofKind: "frameFilter";
        /**
         * frame_filter is a filter for filtering by frames
         *
         * @generated from protobuf field: parca.query.v1alpha1.FrameFilter frame_filter = 2;
         */
        frameFilter: FrameFilter;
    } | {
        oneofKind: undefined;
    };
}
/**
 * StackFilter is a filter for filtering by stacks
 *
 * @generated from protobuf message parca.query.v1alpha1.StackFilter
 */
export interface StackFilter {
    /**
     * @generated from protobuf oneof: filter
     */
    filter: {
        oneofKind: "functionNameStackFilter";
        /**
         * function_name_stack_filter is the function name to filter by
         *
         * @generated from protobuf field: parca.query.v1alpha1.FunctionNameStackFilter function_name_stack_filter = 1;
         */
        functionNameStackFilter: FunctionNameStackFilter;
    } | {
        oneofKind: undefined;
    };
}
/**
 * FunctionNameStackFilter is a filter for filtering by function name
 *
 * @generated from protobuf message parca.query.v1alpha1.FunctionNameStackFilter
 */
export interface FunctionNameStackFilter {
    /**
     * function_to_filter is the function name to filter by
     *
     * @generated from protobuf field: string function_to_filter = 1;
     */
    functionToFilter: string;
    /**
     * exclude determines whether to exclude stacks matching the function
     *
     * @generated from protobuf field: bool exclude = 2;
     */
    exclude: boolean;
}
/**
 * FrameFilter is a filter for filtering by frames
 *
 * @generated from protobuf message parca.query.v1alpha1.FrameFilter
 */
export interface FrameFilter {
    /**
     * @generated from protobuf oneof: filter
     */
    filter: {
        oneofKind: "binaryFrameFilter";
        /**
         * binary_frame_filter is the list of binary names to filter by
         *
         * @generated from protobuf field: parca.query.v1alpha1.BinaryFrameFilter binary_frame_filter = 1;
         */
        binaryFrameFilter: BinaryFrameFilter;
    } | {
        oneofKind: undefined;
    };
}
/**
 * BinaryFrameFilter is a filter for filtering by binaries
 *
 * @generated from protobuf message parca.query.v1alpha1.BinaryFrameFilter
 */
export interface BinaryFrameFilter {
    /**
     * include_binaries is the list of binaries to filter by
     *
     * @generated from protobuf field: repeated string include_binaries = 1;
     */
    includeBinaries: string[];
}
/**
 * RuntimeFilter configures which runtimes to filter frames out for.
 *
 * @generated from protobuf message parca.query.v1alpha1.RuntimeFilter
 */
export interface RuntimeFilter {
    /**
     * Whether to show frames of the python runtime.
     *
     * @generated from protobuf field: bool show_python = 1;
     */
    showPython: boolean;
    /**
     * Whether to show frames of the ruby runtime.
     *
     * @generated from protobuf field: bool show_ruby = 2;
     */
    showRuby: boolean;
    /**
     * Whether to only show interpreted frames.
     *
     * @generated from protobuf field: bool show_interpreted_only = 3;
     */
    showInterpretedOnly: boolean;
}
/**
 * SourceReference contains a reference to source code.
 *
 * @generated from protobuf message parca.query.v1alpha1.SourceReference
 */
export interface SourceReference {
    /**
     * The build ID to request the source of.
     *
     * @generated from protobuf field: string build_id = 1;
     */
    buildId: string;
    /**
     * The filename requested.
     *
     * @generated from protobuf field: string filename = 2;
     */
    filename: string;
    /**
     * Whether to perform a full query or just retrieve the source.
     *
     * @generated from protobuf field: bool source_only = 3;
     */
    sourceOnly: boolean;
}
/**
 * GroupBy encapsulates the repeated fields to group by
 *
 * @generated from protobuf message parca.query.v1alpha1.GroupBy
 */
export interface GroupBy {
    /**
     * the names of the fields to group by.
     * special fields are the ones prefixed with "labels." which are grouping by pprof labels.
     *
     * @generated from protobuf field: repeated string fields = 1;
     */
    fields: string[];
}
/**
 * Top is the top report type
 *
 * @generated from protobuf message parca.query.v1alpha1.Top
 */
export interface Top {
    /**
     * list are the list of ordered elements of the table
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.TopNode list = 1;
     */
    list: TopNode[];
    /**
     * reported is the number of lines reported
     *
     * @generated from protobuf field: int32 reported = 2;
     */
    reported: number;
    /**
     * total is the number of lines that exist in the report
     * Use total from the top level query response instead.
     *
     * @deprecated
     * @generated from protobuf field: int32 total = 3 [deprecated = true];
     */
    total: number;
    /**
     * unit is the unit represented by top table
     *
     * @generated from protobuf field: string unit = 4;
     */
    unit: string;
}
/**
 * TopNode is a node entry in a top list
 *
 * @generated from protobuf message parca.query.v1alpha1.TopNode
 */
export interface TopNode {
    /**
     * meta is the metadata about the node
     *
     * @generated from protobuf field: parca.query.v1alpha1.TopNodeMeta meta = 1;
     */
    meta?: TopNodeMeta;
    /**
     * cumulative is the cumulative value of the node
     *
     * @generated from protobuf field: int64 cumulative = 2;
     */
    cumulative: bigint;
    /**
     * flat is the flat value of the node
     *
     * @generated from protobuf field: int64 flat = 3;
     */
    flat: bigint;
    /**
     * diff is the diff value between two profiles
     *
     * @generated from protobuf field: int64 diff = 4;
     */
    diff: bigint;
}
/**
 * TopNodeMeta is the metadata for a given node
 *
 * @generated from protobuf message parca.query.v1alpha1.TopNodeMeta
 */
export interface TopNodeMeta {
    /**
     * location is the location for the code
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Location location = 1;
     */
    location?: Location;
    /**
     * mapping is the mapping into code
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Mapping mapping = 2;
     */
    mapping?: Mapping;
    /**
     * function is the function information
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Function function = 3;
     */
    function?: Function;
    /**
     * line is the line location
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Line line = 4;
     */
    line?: Line;
}
/**
 * Flamegraph is the flame graph report type
 *
 * @generated from protobuf message parca.query.v1alpha1.Flamegraph
 */
export interface Flamegraph {
    /**
     * root is the root of the flame graph
     *
     * @generated from protobuf field: parca.query.v1alpha1.FlamegraphRootNode root = 1;
     */
    root?: FlamegraphRootNode;
    /**
     * total is the total weight of the flame graph
     * Use total from the top level query response instead.
     *
     * @deprecated
     * @generated from protobuf field: int64 total = 2 [deprecated = true];
     */
    total: bigint;
    /**
     * unit is the unit represented by the flame graph
     *
     * @generated from protobuf field: string unit = 3;
     */
    unit: string;
    /**
     * height is the max height of the graph
     *
     * @generated from protobuf field: int32 height = 4;
     */
    height: number;
    /**
     * string_table holds all deduplicated strings used in the meta data.
     *
     * @generated from protobuf field: repeated string string_table = 5;
     */
    stringTable: string[];
    /**
     * locations deduplicated by their ID to be referenced by nodes.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Location locations = 6;
     */
    locations: Location[];
    /**
     * mapping deduplicated by their ID to be referenced by nodes.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Mapping mapping = 7;
     */
    mapping: Mapping[];
    /**
     * function deduplicated by their ID to be referenced by nodes.
     *
     * @generated from protobuf field: repeated parca.metastore.v1alpha1.Function function = 8;
     */
    function: Function[];
    /**
     * untrimmed_total is the total weight of the flame graph before trimming.
     * Use trimmed instead.
     *
     * @deprecated
     * @generated from protobuf field: int64 untrimmed_total = 9 [deprecated = true];
     */
    untrimmedTotal: bigint;
    /**
     * trimmed is the amount of cumulative value trimmed from the flame graph.
     *
     * @generated from protobuf field: int64 trimmed = 10;
     */
    trimmed: bigint;
}
/**
 * Flamegraph is the flame graph report type
 *
 * @generated from protobuf message parca.query.v1alpha1.FlamegraphArrow
 */
export interface FlamegraphArrow {
    /**
     * record is the arrow record containing the actual flamegraph data
     *
     * @generated from protobuf field: bytes record = 1;
     */
    record: Uint8Array;
    /**
     * unit is the unit represented by the flame graph
     *
     * @generated from protobuf field: string unit = 2;
     */
    unit: string;
    /**
     * height is the max height of the graph
     *
     * @generated from protobuf field: int32 height = 3;
     */
    height: number;
    /**
     * trimmed is the amount of cumulative value trimmed from the flame graph.
     *
     * @generated from protobuf field: int64 trimmed = 4;
     */
    trimmed: bigint;
}
/**
 * Source is the result of the source report type.
 *
 * @generated from protobuf message parca.query.v1alpha1.Source
 */
export interface Source {
    /**
     * An arrow record that contains a row per source code line with value and diff columns for flat and cumulative.
     *
     * @generated from protobuf field: bytes record = 1;
     */
    record: Uint8Array;
    /**
     * The actual source file content.
     *
     * @generated from protobuf field: string source = 2;
     */
    source: string;
    /**
     * The unit of the values in the record.
     *
     * @generated from protobuf field: string unit = 3;
     */
    unit: string;
}
/**
 * FlamegraphRootNode is a root node of a flame graph
 *
 * @generated from protobuf message parca.query.v1alpha1.FlamegraphRootNode
 */
export interface FlamegraphRootNode {
    /**
     * cumulative is the cumulative value of the graph
     *
     * @generated from protobuf field: int64 cumulative = 1;
     */
    cumulative: bigint;
    /**
     * diff is the diff
     *
     * @generated from protobuf field: int64 diff = 2;
     */
    diff: bigint;
    /**
     * children are the list of the children of the root node
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.FlamegraphNode children = 3;
     */
    children: FlamegraphNode[];
}
/**
 * FlamegraphNode represents a node in the graph
 *
 * @generated from protobuf message parca.query.v1alpha1.FlamegraphNode
 */
export interface FlamegraphNode {
    /**
     * meta is the metadata about the node
     *
     * @generated from protobuf field: parca.query.v1alpha1.FlamegraphNodeMeta meta = 1;
     */
    meta?: FlamegraphNodeMeta;
    /**
     * cumulative is the cumulative value of the node
     *
     * @generated from protobuf field: int64 cumulative = 2;
     */
    cumulative: bigint;
    /**
     * diff is the diff
     *
     * @generated from protobuf field: int64 diff = 3;
     */
    diff: bigint;
    /**
     * children are the child nodes
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.FlamegraphNode children = 4;
     */
    children: FlamegraphNode[];
}
/**
 * FlamegraphNodeMeta is the metadata for a given node
 *
 * @generated from protobuf message parca.query.v1alpha1.FlamegraphNodeMeta
 */
export interface FlamegraphNodeMeta {
    /**
     * location is the location for the code
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Location location = 1;
     */
    location?: Location;
    /**
     * mapping is the mapping into code
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Mapping mapping = 2;
     */
    mapping?: Mapping;
    /**
     * function is the function information
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Function function = 3;
     */
    function?: Function;
    /**
     * line is the line location
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Line line = 4;
     */
    line?: Line;
    /**
     * location_index has the index to the deduplicated location in the location table.
     *
     * @generated from protobuf field: uint32 location_index = 5;
     */
    locationIndex: number;
    /**
     * line_index is the line index within the referenced location.
     *
     * @generated from protobuf field: uint32 line_index = 6;
     */
    lineIndex: number;
}
/**
 * CallgraphNode represents a node in the graph
 *
 * @generated from protobuf message parca.query.v1alpha1.CallgraphNode
 */
export interface CallgraphNode {
    /**
     * id is the unique id of the node
     *
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * meta is the metadata about the node
     *
     * @generated from protobuf field: parca.query.v1alpha1.CallgraphNodeMeta meta = 2;
     */
    meta?: CallgraphNodeMeta;
    /**
     * cumulative is the cumulative value of the node
     *
     * @generated from protobuf field: int64 cumulative = 3;
     */
    cumulative: bigint;
    /**
     * flat is the flat value of the node
     *
     * @generated from protobuf field: int64 flat = 4;
     */
    flat: bigint;
}
/**
 * TopNodeMeta is the metadata for a given node
 *
 * @generated from protobuf message parca.query.v1alpha1.CallgraphNodeMeta
 */
export interface CallgraphNodeMeta {
    /**
     * location is the location for the code
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Location location = 1;
     */
    location?: Location;
    /**
     * mapping is the mapping into code
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Mapping mapping = 2;
     */
    mapping?: Mapping;
    /**
     * function is the function information
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Function function = 3;
     */
    function?: Function;
    /**
     * line is the line location
     *
     * @generated from protobuf field: parca.metastore.v1alpha1.Line line = 4;
     */
    line?: Line;
}
/**
 * CallgraphEdge represents an edge in the graph
 *
 * @generated from protobuf message parca.query.v1alpha1.CallgraphEdge
 */
export interface CallgraphEdge {
    /**
     * id is the unique id of the edge
     *
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * source represents the id of the source node
     *
     * @generated from protobuf field: string source = 2;
     */
    source: string;
    /**
     * target represents the id of the target node
     *
     * @generated from protobuf field: string target = 3;
     */
    target: string;
    /**
     * cumulative is the cumulative value of the edge
     *
     * @generated from protobuf field: int64 cumulative = 4;
     */
    cumulative: bigint;
    /**
     * is_collapsed indicates if the edge is collapsed
     *
     * @generated from protobuf field: bool is_collapsed = 5;
     */
    isCollapsed: boolean;
}
/**
 * Callgraph is the callgraph report type
 *
 * @generated from protobuf message parca.query.v1alpha1.Callgraph
 */
export interface Callgraph {
    /**
     * nodes are the nodes in the callgraph
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.CallgraphNode nodes = 1;
     */
    nodes: CallgraphNode[];
    /**
     * edges are the edges connecting nodes in the callgraph
     *
     * @generated from protobuf field: repeated parca.query.v1alpha1.CallgraphEdge edges = 2;
     */
    edges: CallgraphEdge[];
    /**
     * cumulative is the total cumulative value of the callgraph
     * Use total from the top level query response instead.
     *
     * @deprecated
     * @generated from protobuf field: int64 cumulative = 3 [deprecated = true];
     */
    cumulative: bigint;
}
/**
 * QueryResponse is the returned report for the given query
 *
 * @generated from protobuf message parca.query.v1alpha1.QueryResponse
 */
export interface QueryResponse {
    /**
     * @generated from protobuf oneof: report
     */
    report: {
        oneofKind: "flamegraph";
        /**
         * flamegraph is a flamegraph representation of the report
         *
         * @generated from protobuf field: parca.query.v1alpha1.Flamegraph flamegraph = 5;
         */
        flamegraph: Flamegraph;
    } | {
        oneofKind: "pprof";
        /**
         * pprof is a pprof profile as compressed bytes
         *
         * @generated from protobuf field: bytes pprof = 6;
         */
        pprof: Uint8Array;
    } | {
        oneofKind: "top";
        /**
         * top is a top list representation of the report
         *
         * @generated from protobuf field: parca.query.v1alpha1.Top top = 7;
         */
        top: Top;
    } | {
        oneofKind: "callgraph";
        /**
         * callgraph is a callgraph nodes and edges representation of the report
         *
         * @generated from protobuf field: parca.query.v1alpha1.Callgraph callgraph = 8;
         */
        callgraph: Callgraph;
    } | {
        oneofKind: "flamegraphArrow";
        /**
         * flamegraph_arrow is a flamegraph encoded as a arrow record
         *
         * @generated from protobuf field: parca.query.v1alpha1.FlamegraphArrow flamegraph_arrow = 11;
         */
        flamegraphArrow: FlamegraphArrow;
    } | {
        oneofKind: "source";
        /**
         * source is the source report type result
         *
         * @generated from protobuf field: parca.query.v1alpha1.Source source = 12;
         */
        source: Source;
    } | {
        oneofKind: "tableArrow";
        /**
         * table_arrow is a table encoded as a arrow record
         *
         * @generated from protobuf field: parca.query.v1alpha1.TableArrow table_arrow = 13;
         */
        tableArrow: TableArrow;
    } | {
        oneofKind: "profileMetadata";
        /**
         * profile_metadata contains metadata about the profile i.e. binaries, labels
         *
         * @generated from protobuf field: parca.query.v1alpha1.ProfileMetadata profile_metadata = 14;
         */
        profileMetadata: ProfileMetadata;
    } | {
        oneofKind: undefined;
    };
    /**
     * total is the total number of samples shown in the report.
     *
     * @generated from protobuf field: int64 total = 9;
     */
    total: bigint;
    /**
     * filtered is the number of samples filtered out of the report.
     *
     * @generated from protobuf field: int64 filtered = 10;
     */
    filtered: bigint;
}
/**
 * SeriesRequest is unimplemented
 *
 * @generated from protobuf message parca.query.v1alpha1.SeriesRequest
 */
export interface SeriesRequest {
    /**
     * match ...
     *
     * @generated from protobuf field: repeated string match = 1;
     */
    match: string[];
    /**
     * start ...
     *
     * @generated from protobuf field: google.protobuf.Timestamp start = 2;
     */
    start?: Timestamp;
    /**
     * end ...
     *
     * @generated from protobuf field: google.protobuf.Timestamp end = 3;
     */
    end?: Timestamp;
}
/**
 * SeriesResponse is unimplemented
 *
 * @generated from protobuf message parca.query.v1alpha1.SeriesResponse
 */
export interface SeriesResponse {
}
/**
 * LabelsRequest are the request values for labels
 *
 * @generated from protobuf message parca.query.v1alpha1.LabelsRequest
 */
export interface LabelsRequest {
    /**
     * match are the set of matching strings
     *
     * @generated from protobuf field: repeated string match = 1;
     */
    match: string[];
    /**
     * start is the start of the time window to perform the query
     *
     * @generated from protobuf field: google.protobuf.Timestamp start = 2;
     */
    start?: Timestamp;
    /**
     * end is the end of the time window to perform the query
     *
     * @generated from protobuf field: google.protobuf.Timestamp end = 3;
     */
    end?: Timestamp;
    /**
     * profile_type is the type of profile to filter by
     *
     * @generated from protobuf field: optional string profile_type = 4;
     */
    profileType?: string;
}
/**
 * LabelsResponse is the set of matching label names
 *
 * @generated from protobuf message parca.query.v1alpha1.LabelsResponse
 */
export interface LabelsResponse {
    /**
     * / label_names are the set of matching label names
     *
     * @generated from protobuf field: repeated string label_names = 1;
     */
    labelNames: string[];
    /**
     * warnings is unimplemented
     *
     * @generated from protobuf field: repeated string warnings = 2;
     */
    warnings: string[];
}
/**
 * ValuesRequest are the request values for a values request
 *
 * @generated from protobuf message parca.query.v1alpha1.ValuesRequest
 */
export interface ValuesRequest {
    /**
     * label_name is the label name to match values against
     *
     * @generated from protobuf field: string label_name = 1;
     */
    labelName: string;
    /**
     * match are the set of matching strings to match values against
     *
     * @generated from protobuf field: repeated string match = 2;
     */
    match: string[];
    /**
     * start is the start of the time window to perform the query
     *
     * @generated from protobuf field: google.protobuf.Timestamp start = 3;
     */
    start?: Timestamp;
    /**
     * end is the end of the time window to perform the query
     *
     * @generated from protobuf field: google.protobuf.Timestamp end = 4;
     */
    end?: Timestamp;
    /**
     * profile_type is the type of profile to filter by
     *
     * @generated from protobuf field: optional string profile_type = 5;
     */
    profileType?: string;
}
/**
 * ValuesResponse are the set of matching values
 *
 * @generated from protobuf message parca.query.v1alpha1.ValuesResponse
 */
export interface ValuesResponse {
    /**
     * label_values are the set of matching label values
     *
     * @generated from protobuf field: repeated string label_values = 1;
     */
    labelValues: string[];
    /**
     * warnings is unimplemented
     *
     * @generated from protobuf field: repeated string warnings = 2;
     */
    warnings: string[];
}
/**
 * ValueType represents a value, including its type and unit
 *
 * @generated from protobuf message parca.query.v1alpha1.ValueType
 */
export interface ValueType {
    /**
     * type is the type of the value
     *
     * @generated from protobuf field: string type = 1;
     */
    type: string;
    /**
     * unit is the unit of the value
     *
     * @generated from protobuf field: string unit = 2;
     */
    unit: string;
}
/**
 * ShareProfileRequest represents the query denoting the profile and a description about the profile
 *
 * @generated from protobuf message parca.query.v1alpha1.ShareProfileRequest
 */
export interface ShareProfileRequest {
    /**
     * QueryRequest that refers to the profile to be shared
     *
     * @generated from protobuf field: parca.query.v1alpha1.QueryRequest query_request = 1;
     */
    queryRequest?: QueryRequest;
    /**
     * description about the profile
     *
     * @generated from protobuf field: optional string description = 2;
     */
    description?: string;
}
/**
 * ShareProfileResponse represents the shared link of a profile
 *
 * @generated from protobuf message parca.query.v1alpha1.ShareProfileResponse
 */
export interface ShareProfileResponse {
    /**
     * link to access the profile
     *
     * @generated from protobuf field: string link = 1;
     */
    link: string;
}
/**
 * TableArrow has the table encoded as a arrow record
 *
 * @generated from protobuf message parca.query.v1alpha1.TableArrow
 */
export interface TableArrow {
    /**
     * record is the arrow record containing the actual table data
     *
     * @generated from protobuf field: bytes record = 1;
     */
    record: Uint8Array;
    /**
     * unit is the unit represented by the flame graph
     *
     * @generated from protobuf field: string unit = 2;
     */
    unit: string;
}
/**
 * ProfileMetadata contains metadata about the profile i.e. binaries, labels
 *
 * @generated from protobuf message parca.query.v1alpha1.ProfileMetadata
 */
export interface ProfileMetadata {
    /**
     * mapping_files is the list of binaries in the profile
     *
     * @generated from protobuf field: repeated string mapping_files = 1;
     */
    mappingFiles: string[];
    /**
     * labels is the list of labels in the profile
     *
     * @generated from protobuf field: repeated string labels = 2;
     */
    labels: string[];
}
// @generated message type with reflection information, may provide speed optimized methods
class ProfileTypesRequest$Type extends MessageType<ProfileTypesRequest> {
    constructor() {
        super("parca.query.v1alpha1.ProfileTypesRequest", []);
    }
    create(value?: PartialMessage<ProfileTypesRequest>): ProfileTypesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<ProfileTypesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfileTypesRequest): ProfileTypesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfileTypesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ProfileTypesRequest
 */
export const ProfileTypesRequest = new ProfileTypesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProfileTypesResponse$Type extends MessageType<ProfileTypesResponse> {
    constructor() {
        super("parca.query.v1alpha1.ProfileTypesResponse", [
            { no: 1, name: "types", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => ProfileType }
        ]);
    }
    create(value?: PartialMessage<ProfileTypesResponse>): ProfileTypesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.types = [];
        if (value !== undefined)
            reflectionMergePartial<ProfileTypesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfileTypesResponse): ProfileTypesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.query.v1alpha1.ProfileType types */ 1:
                    message.types.push(ProfileType.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfileTypesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.query.v1alpha1.ProfileType types = 1; */
        for (let i = 0; i < message.types.length; i++)
            ProfileType.internalBinaryWrite(message.types[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ProfileTypesResponse
 */
export const ProfileTypesResponse = new ProfileTypesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProfileType$Type extends MessageType<ProfileType> {
    constructor() {
        super("parca.query.v1alpha1.ProfileType", [
            { no: 1, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "sample_type", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "sample_unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "period_type", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "period_unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "delta", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<ProfileType>): ProfileType {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.sampleType = "";
        message.sampleUnit = "";
        message.periodType = "";
        message.periodUnit = "";
        message.delta = false;
        if (value !== undefined)
            reflectionMergePartial<ProfileType>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfileType): ProfileType {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string name */ 1:
                    message.name = reader.string();
                    break;
                case /* string sample_type */ 2:
                    message.sampleType = reader.string();
                    break;
                case /* string sample_unit */ 3:
                    message.sampleUnit = reader.string();
                    break;
                case /* string period_type */ 4:
                    message.periodType = reader.string();
                    break;
                case /* string period_unit */ 5:
                    message.periodUnit = reader.string();
                    break;
                case /* bool delta */ 6:
                    message.delta = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfileType, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string name = 1; */
        if (message.name !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.name);
        /* string sample_type = 2; */
        if (message.sampleType !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.sampleType);
        /* string sample_unit = 3; */
        if (message.sampleUnit !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.sampleUnit);
        /* string period_type = 4; */
        if (message.periodType !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.periodType);
        /* string period_unit = 5; */
        if (message.periodUnit !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.periodUnit);
        /* bool delta = 6; */
        if (message.delta !== false)
            writer.tag(6, WireType.Varint).bool(message.delta);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ProfileType
 */
export const ProfileType = new ProfileType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class QueryRangeRequest$Type extends MessageType<QueryRangeRequest> {
    constructor() {
        super("parca.query.v1alpha1.QueryRangeRequest", [
            { no: 1, name: "query", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start", kind: "message", T: () => Timestamp },
            { no: 3, name: "end", kind: "message", T: () => Timestamp },
            { no: 4, name: "limit", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 5, name: "step", kind: "message", T: () => Duration },
            { no: 6, name: "sum_by", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<QueryRangeRequest>): QueryRangeRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.query = "";
        message.limit = 0;
        message.sumBy = [];
        if (value !== undefined)
            reflectionMergePartial<QueryRangeRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: QueryRangeRequest): QueryRangeRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string query */ 1:
                    message.query = reader.string();
                    break;
                case /* google.protobuf.Timestamp start */ 2:
                    message.start = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.start);
                    break;
                case /* google.protobuf.Timestamp end */ 3:
                    message.end = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.end);
                    break;
                case /* uint32 limit */ 4:
                    message.limit = reader.uint32();
                    break;
                case /* google.protobuf.Duration step */ 5:
                    message.step = Duration.internalBinaryRead(reader, reader.uint32(), options, message.step);
                    break;
                case /* repeated string sum_by */ 6:
                    message.sumBy.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: QueryRangeRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string query = 1; */
        if (message.query !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.query);
        /* google.protobuf.Timestamp start = 2; */
        if (message.start)
            Timestamp.internalBinaryWrite(message.start, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp end = 3; */
        if (message.end)
            Timestamp.internalBinaryWrite(message.end, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* uint32 limit = 4; */
        if (message.limit !== 0)
            writer.tag(4, WireType.Varint).uint32(message.limit);
        /* google.protobuf.Duration step = 5; */
        if (message.step)
            Duration.internalBinaryWrite(message.step, writer.tag(5, WireType.LengthDelimited).fork(), options).join();
        /* repeated string sum_by = 6; */
        for (let i = 0; i < message.sumBy.length; i++)
            writer.tag(6, WireType.LengthDelimited).string(message.sumBy[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.QueryRangeRequest
 */
export const QueryRangeRequest = new QueryRangeRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class QueryRangeResponse$Type extends MessageType<QueryRangeResponse> {
    constructor() {
        super("parca.query.v1alpha1.QueryRangeResponse", [
            { no: 1, name: "series", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => MetricsSeries }
        ]);
    }
    create(value?: PartialMessage<QueryRangeResponse>): QueryRangeResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.series = [];
        if (value !== undefined)
            reflectionMergePartial<QueryRangeResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: QueryRangeResponse): QueryRangeResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.query.v1alpha1.MetricsSeries series */ 1:
                    message.series.push(MetricsSeries.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: QueryRangeResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.query.v1alpha1.MetricsSeries series = 1; */
        for (let i = 0; i < message.series.length; i++)
            MetricsSeries.internalBinaryWrite(message.series[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.QueryRangeResponse
 */
export const QueryRangeResponse = new QueryRangeResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class MetricsSeries$Type extends MessageType<MetricsSeries> {
    constructor() {
        super("parca.query.v1alpha1.MetricsSeries", [
            { no: 1, name: "labelset", kind: "message", T: () => LabelSet },
            { no: 2, name: "samples", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => MetricsSample },
            { no: 3, name: "period_type", kind: "message", T: () => ValueType },
            { no: 4, name: "sample_type", kind: "message", T: () => ValueType }
        ]);
    }
    create(value?: PartialMessage<MetricsSeries>): MetricsSeries {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.samples = [];
        if (value !== undefined)
            reflectionMergePartial<MetricsSeries>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: MetricsSeries): MetricsSeries {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.profilestore.v1alpha1.LabelSet labelset */ 1:
                    message.labelset = LabelSet.internalBinaryRead(reader, reader.uint32(), options, message.labelset);
                    break;
                case /* repeated parca.query.v1alpha1.MetricsSample samples */ 2:
                    message.samples.push(MetricsSample.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* parca.query.v1alpha1.ValueType period_type */ 3:
                    message.periodType = ValueType.internalBinaryRead(reader, reader.uint32(), options, message.periodType);
                    break;
                case /* parca.query.v1alpha1.ValueType sample_type */ 4:
                    message.sampleType = ValueType.internalBinaryRead(reader, reader.uint32(), options, message.sampleType);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: MetricsSeries, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.profilestore.v1alpha1.LabelSet labelset = 1; */
        if (message.labelset)
            LabelSet.internalBinaryWrite(message.labelset, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated parca.query.v1alpha1.MetricsSample samples = 2; */
        for (let i = 0; i < message.samples.length; i++)
            MetricsSample.internalBinaryWrite(message.samples[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.ValueType period_type = 3; */
        if (message.periodType)
            ValueType.internalBinaryWrite(message.periodType, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.ValueType sample_type = 4; */
        if (message.sampleType)
            ValueType.internalBinaryWrite(message.sampleType, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.MetricsSeries
 */
export const MetricsSeries = new MetricsSeries$Type();
// @generated message type with reflection information, may provide speed optimized methods
class MetricsSample$Type extends MessageType<MetricsSample> {
    constructor() {
        super("parca.query.v1alpha1.MetricsSample", [
            { no: 1, name: "timestamp", kind: "message", T: () => Timestamp },
            { no: 2, name: "value", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "value_per_second", kind: "scalar", T: 1 /*ScalarType.DOUBLE*/ },
            { no: 4, name: "duration", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<MetricsSample>): MetricsSample {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.value = 0n;
        message.valuePerSecond = 0;
        message.duration = 0n;
        if (value !== undefined)
            reflectionMergePartial<MetricsSample>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: MetricsSample): MetricsSample {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* google.protobuf.Timestamp timestamp */ 1:
                    message.timestamp = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.timestamp);
                    break;
                case /* int64 value */ 2:
                    message.value = reader.int64().toBigInt();
                    break;
                case /* double value_per_second */ 3:
                    message.valuePerSecond = reader.double();
                    break;
                case /* int64 duration */ 4:
                    message.duration = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: MetricsSample, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* google.protobuf.Timestamp timestamp = 1; */
        if (message.timestamp)
            Timestamp.internalBinaryWrite(message.timestamp, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* int64 value = 2; */
        if (message.value !== 0n)
            writer.tag(2, WireType.Varint).int64(message.value);
        /* double value_per_second = 3; */
        if (message.valuePerSecond !== 0)
            writer.tag(3, WireType.Bit64).double(message.valuePerSecond);
        /* int64 duration = 4; */
        if (message.duration !== 0n)
            writer.tag(4, WireType.Varint).int64(message.duration);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.MetricsSample
 */
export const MetricsSample = new MetricsSample$Type();
// @generated message type with reflection information, may provide speed optimized methods
class MergeProfile$Type extends MessageType<MergeProfile> {
    constructor() {
        super("parca.query.v1alpha1.MergeProfile", [
            { no: 1, name: "query", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start", kind: "message", T: () => Timestamp },
            { no: 3, name: "end", kind: "message", T: () => Timestamp }
        ]);
    }
    create(value?: PartialMessage<MergeProfile>): MergeProfile {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.query = "";
        if (value !== undefined)
            reflectionMergePartial<MergeProfile>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: MergeProfile): MergeProfile {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string query */ 1:
                    message.query = reader.string();
                    break;
                case /* google.protobuf.Timestamp start */ 2:
                    message.start = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.start);
                    break;
                case /* google.protobuf.Timestamp end */ 3:
                    message.end = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.end);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: MergeProfile, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string query = 1; */
        if (message.query !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.query);
        /* google.protobuf.Timestamp start = 2; */
        if (message.start)
            Timestamp.internalBinaryWrite(message.start, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp end = 3; */
        if (message.end)
            Timestamp.internalBinaryWrite(message.end, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.MergeProfile
 */
export const MergeProfile = new MergeProfile$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SingleProfile$Type extends MessageType<SingleProfile> {
    constructor() {
        super("parca.query.v1alpha1.SingleProfile", [
            { no: 1, name: "time", kind: "message", T: () => Timestamp },
            { no: 2, name: "query", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<SingleProfile>): SingleProfile {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.query = "";
        if (value !== undefined)
            reflectionMergePartial<SingleProfile>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SingleProfile): SingleProfile {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* google.protobuf.Timestamp time */ 1:
                    message.time = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.time);
                    break;
                case /* string query */ 2:
                    message.query = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SingleProfile, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* google.protobuf.Timestamp time = 1; */
        if (message.time)
            Timestamp.internalBinaryWrite(message.time, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string query = 2; */
        if (message.query !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.query);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.SingleProfile
 */
export const SingleProfile = new SingleProfile$Type();
// @generated message type with reflection information, may provide speed optimized methods
class DiffProfile$Type extends MessageType<DiffProfile> {
    constructor() {
        super("parca.query.v1alpha1.DiffProfile", [
            { no: 1, name: "a", kind: "message", T: () => ProfileDiffSelection },
            { no: 2, name: "b", kind: "message", T: () => ProfileDiffSelection },
            { no: 3, name: "absolute", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<DiffProfile>): DiffProfile {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<DiffProfile>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: DiffProfile): DiffProfile {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.ProfileDiffSelection a */ 1:
                    message.a = ProfileDiffSelection.internalBinaryRead(reader, reader.uint32(), options, message.a);
                    break;
                case /* parca.query.v1alpha1.ProfileDiffSelection b */ 2:
                    message.b = ProfileDiffSelection.internalBinaryRead(reader, reader.uint32(), options, message.b);
                    break;
                case /* optional bool absolute */ 3:
                    message.absolute = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: DiffProfile, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.ProfileDiffSelection a = 1; */
        if (message.a)
            ProfileDiffSelection.internalBinaryWrite(message.a, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.ProfileDiffSelection b = 2; */
        if (message.b)
            ProfileDiffSelection.internalBinaryWrite(message.b, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* optional bool absolute = 3; */
        if (message.absolute !== undefined)
            writer.tag(3, WireType.Varint).bool(message.absolute);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.DiffProfile
 */
export const DiffProfile = new DiffProfile$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProfileDiffSelection$Type extends MessageType<ProfileDiffSelection> {
    constructor() {
        super("parca.query.v1alpha1.ProfileDiffSelection", [
            { no: 1, name: "mode", kind: "enum", T: () => ["parca.query.v1alpha1.ProfileDiffSelection.Mode", ProfileDiffSelection_Mode, "MODE_"] },
            { no: 2, name: "merge", kind: "message", oneof: "options", T: () => MergeProfile },
            { no: 3, name: "single", kind: "message", oneof: "options", T: () => SingleProfile }
        ]);
    }
    create(value?: PartialMessage<ProfileDiffSelection>): ProfileDiffSelection {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mode = 0;
        message.options = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<ProfileDiffSelection>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfileDiffSelection): ProfileDiffSelection {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.ProfileDiffSelection.Mode mode */ 1:
                    message.mode = reader.int32();
                    break;
                case /* parca.query.v1alpha1.MergeProfile merge */ 2:
                    message.options = {
                        oneofKind: "merge",
                        merge: MergeProfile.internalBinaryRead(reader, reader.uint32(), options, (message.options as any).merge)
                    };
                    break;
                case /* parca.query.v1alpha1.SingleProfile single */ 3:
                    message.options = {
                        oneofKind: "single",
                        single: SingleProfile.internalBinaryRead(reader, reader.uint32(), options, (message.options as any).single)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfileDiffSelection, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.ProfileDiffSelection.Mode mode = 1; */
        if (message.mode !== 0)
            writer.tag(1, WireType.Varint).int32(message.mode);
        /* parca.query.v1alpha1.MergeProfile merge = 2; */
        if (message.options.oneofKind === "merge")
            MergeProfile.internalBinaryWrite(message.options.merge, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.SingleProfile single = 3; */
        if (message.options.oneofKind === "single")
            SingleProfile.internalBinaryWrite(message.options.single, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ProfileDiffSelection
 */
export const ProfileDiffSelection = new ProfileDiffSelection$Type();
// @generated message type with reflection information, may provide speed optimized methods
class QueryRequest$Type extends MessageType<QueryRequest> {
    constructor() {
        super("parca.query.v1alpha1.QueryRequest", [
            { no: 1, name: "mode", kind: "enum", T: () => ["parca.query.v1alpha1.QueryRequest.Mode", QueryRequest_Mode, "MODE_"] },
            { no: 2, name: "diff", kind: "message", oneof: "options", T: () => DiffProfile },
            { no: 3, name: "merge", kind: "message", oneof: "options", T: () => MergeProfile },
            { no: 4, name: "single", kind: "message", oneof: "options", T: () => SingleProfile },
            { no: 5, name: "report_type", kind: "enum", T: () => ["parca.query.v1alpha1.QueryRequest.ReportType", QueryRequest_ReportType, "REPORT_TYPE_"] },
            { no: 6, name: "filter_query", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "node_trim_threshold", kind: "scalar", opt: true, T: 2 /*ScalarType.FLOAT*/ },
            { no: 8, name: "group_by", kind: "message", T: () => GroupBy },
            { no: 9, name: "source_reference", kind: "message", T: () => SourceReference },
            { no: 10, name: "runtime_filter", kind: "message", T: () => RuntimeFilter },
            { no: 11, name: "invert_call_stack", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ },
            { no: 12, name: "filter", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Filter },
            { no: 13, name: "sandwich_by_function", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<QueryRequest>): QueryRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mode = 0;
        message.options = { oneofKind: undefined };
        message.reportType = 0;
        message.filter = [];
        if (value !== undefined)
            reflectionMergePartial<QueryRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: QueryRequest): QueryRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.QueryRequest.Mode mode */ 1:
                    message.mode = reader.int32();
                    break;
                case /* parca.query.v1alpha1.DiffProfile diff */ 2:
                    message.options = {
                        oneofKind: "diff",
                        diff: DiffProfile.internalBinaryRead(reader, reader.uint32(), options, (message.options as any).diff)
                    };
                    break;
                case /* parca.query.v1alpha1.MergeProfile merge */ 3:
                    message.options = {
                        oneofKind: "merge",
                        merge: MergeProfile.internalBinaryRead(reader, reader.uint32(), options, (message.options as any).merge)
                    };
                    break;
                case /* parca.query.v1alpha1.SingleProfile single */ 4:
                    message.options = {
                        oneofKind: "single",
                        single: SingleProfile.internalBinaryRead(reader, reader.uint32(), options, (message.options as any).single)
                    };
                    break;
                case /* parca.query.v1alpha1.QueryRequest.ReportType report_type */ 5:
                    message.reportType = reader.int32();
                    break;
                case /* optional string filter_query = 6 [deprecated = true];*/ 6:
                    message.filterQuery = reader.string();
                    break;
                case /* optional float node_trim_threshold */ 7:
                    message.nodeTrimThreshold = reader.float();
                    break;
                case /* optional parca.query.v1alpha1.GroupBy group_by */ 8:
                    message.groupBy = GroupBy.internalBinaryRead(reader, reader.uint32(), options, message.groupBy);
                    break;
                case /* optional parca.query.v1alpha1.SourceReference source_reference */ 9:
                    message.sourceReference = SourceReference.internalBinaryRead(reader, reader.uint32(), options, message.sourceReference);
                    break;
                case /* optional parca.query.v1alpha1.RuntimeFilter runtime_filter = 10 [deprecated = true];*/ 10:
                    message.runtimeFilter = RuntimeFilter.internalBinaryRead(reader, reader.uint32(), options, message.runtimeFilter);
                    break;
                case /* optional bool invert_call_stack */ 11:
                    message.invertCallStack = reader.bool();
                    break;
                case /* repeated parca.query.v1alpha1.Filter filter */ 12:
                    message.filter.push(Filter.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* optional string sandwich_by_function */ 13:
                    message.sandwichByFunction = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: QueryRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.QueryRequest.Mode mode = 1; */
        if (message.mode !== 0)
            writer.tag(1, WireType.Varint).int32(message.mode);
        /* parca.query.v1alpha1.DiffProfile diff = 2; */
        if (message.options.oneofKind === "diff")
            DiffProfile.internalBinaryWrite(message.options.diff, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.MergeProfile merge = 3; */
        if (message.options.oneofKind === "merge")
            MergeProfile.internalBinaryWrite(message.options.merge, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.SingleProfile single = 4; */
        if (message.options.oneofKind === "single")
            SingleProfile.internalBinaryWrite(message.options.single, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.QueryRequest.ReportType report_type = 5; */
        if (message.reportType !== 0)
            writer.tag(5, WireType.Varint).int32(message.reportType);
        /* optional string filter_query = 6 [deprecated = true]; */
        if (message.filterQuery !== undefined)
            writer.tag(6, WireType.LengthDelimited).string(message.filterQuery);
        /* optional float node_trim_threshold = 7; */
        if (message.nodeTrimThreshold !== undefined)
            writer.tag(7, WireType.Bit32).float(message.nodeTrimThreshold);
        /* optional parca.query.v1alpha1.GroupBy group_by = 8; */
        if (message.groupBy)
            GroupBy.internalBinaryWrite(message.groupBy, writer.tag(8, WireType.LengthDelimited).fork(), options).join();
        /* optional parca.query.v1alpha1.SourceReference source_reference = 9; */
        if (message.sourceReference)
            SourceReference.internalBinaryWrite(message.sourceReference, writer.tag(9, WireType.LengthDelimited).fork(), options).join();
        /* optional parca.query.v1alpha1.RuntimeFilter runtime_filter = 10 [deprecated = true]; */
        if (message.runtimeFilter)
            RuntimeFilter.internalBinaryWrite(message.runtimeFilter, writer.tag(10, WireType.LengthDelimited).fork(), options).join();
        /* optional bool invert_call_stack = 11; */
        if (message.invertCallStack !== undefined)
            writer.tag(11, WireType.Varint).bool(message.invertCallStack);
        /* repeated parca.query.v1alpha1.Filter filter = 12; */
        for (let i = 0; i < message.filter.length; i++)
            Filter.internalBinaryWrite(message.filter[i], writer.tag(12, WireType.LengthDelimited).fork(), options).join();
        /* optional string sandwich_by_function = 13; */
        if (message.sandwichByFunction !== undefined)
            writer.tag(13, WireType.LengthDelimited).string(message.sandwichByFunction);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.QueryRequest
 */
export const QueryRequest = new QueryRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Filter$Type extends MessageType<Filter> {
    constructor() {
        super("parca.query.v1alpha1.Filter", [
            { no: 1, name: "stack_filter", kind: "message", oneof: "filter", T: () => StackFilter },
            { no: 2, name: "frame_filter", kind: "message", oneof: "filter", T: () => FrameFilter }
        ]);
    }
    create(value?: PartialMessage<Filter>): Filter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.filter = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<Filter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Filter): Filter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.StackFilter stack_filter */ 1:
                    message.filter = {
                        oneofKind: "stackFilter",
                        stackFilter: StackFilter.internalBinaryRead(reader, reader.uint32(), options, (message.filter as any).stackFilter)
                    };
                    break;
                case /* parca.query.v1alpha1.FrameFilter frame_filter */ 2:
                    message.filter = {
                        oneofKind: "frameFilter",
                        frameFilter: FrameFilter.internalBinaryRead(reader, reader.uint32(), options, (message.filter as any).frameFilter)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Filter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.StackFilter stack_filter = 1; */
        if (message.filter.oneofKind === "stackFilter")
            StackFilter.internalBinaryWrite(message.filter.stackFilter, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.FrameFilter frame_filter = 2; */
        if (message.filter.oneofKind === "frameFilter")
            FrameFilter.internalBinaryWrite(message.filter.frameFilter, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.Filter
 */
export const Filter = new Filter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class StackFilter$Type extends MessageType<StackFilter> {
    constructor() {
        super("parca.query.v1alpha1.StackFilter", [
            { no: 1, name: "function_name_stack_filter", kind: "message", oneof: "filter", T: () => FunctionNameStackFilter }
        ]);
    }
    create(value?: PartialMessage<StackFilter>): StackFilter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.filter = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<StackFilter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: StackFilter): StackFilter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.FunctionNameStackFilter function_name_stack_filter */ 1:
                    message.filter = {
                        oneofKind: "functionNameStackFilter",
                        functionNameStackFilter: FunctionNameStackFilter.internalBinaryRead(reader, reader.uint32(), options, (message.filter as any).functionNameStackFilter)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: StackFilter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.FunctionNameStackFilter function_name_stack_filter = 1; */
        if (message.filter.oneofKind === "functionNameStackFilter")
            FunctionNameStackFilter.internalBinaryWrite(message.filter.functionNameStackFilter, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.StackFilter
 */
export const StackFilter = new StackFilter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FunctionNameStackFilter$Type extends MessageType<FunctionNameStackFilter> {
    constructor() {
        super("parca.query.v1alpha1.FunctionNameStackFilter", [
            { no: 1, name: "function_to_filter", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "exclude", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<FunctionNameStackFilter>): FunctionNameStackFilter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.functionToFilter = "";
        message.exclude = false;
        if (value !== undefined)
            reflectionMergePartial<FunctionNameStackFilter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FunctionNameStackFilter): FunctionNameStackFilter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string function_to_filter */ 1:
                    message.functionToFilter = reader.string();
                    break;
                case /* bool exclude */ 2:
                    message.exclude = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FunctionNameStackFilter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string function_to_filter = 1; */
        if (message.functionToFilter !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.functionToFilter);
        /* bool exclude = 2; */
        if (message.exclude !== false)
            writer.tag(2, WireType.Varint).bool(message.exclude);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.FunctionNameStackFilter
 */
export const FunctionNameStackFilter = new FunctionNameStackFilter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FrameFilter$Type extends MessageType<FrameFilter> {
    constructor() {
        super("parca.query.v1alpha1.FrameFilter", [
            { no: 1, name: "binary_frame_filter", kind: "message", oneof: "filter", T: () => BinaryFrameFilter }
        ]);
    }
    create(value?: PartialMessage<FrameFilter>): FrameFilter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.filter = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<FrameFilter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FrameFilter): FrameFilter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.BinaryFrameFilter binary_frame_filter */ 1:
                    message.filter = {
                        oneofKind: "binaryFrameFilter",
                        binaryFrameFilter: BinaryFrameFilter.internalBinaryRead(reader, reader.uint32(), options, (message.filter as any).binaryFrameFilter)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FrameFilter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.BinaryFrameFilter binary_frame_filter = 1; */
        if (message.filter.oneofKind === "binaryFrameFilter")
            BinaryFrameFilter.internalBinaryWrite(message.filter.binaryFrameFilter, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.FrameFilter
 */
export const FrameFilter = new FrameFilter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class BinaryFrameFilter$Type extends MessageType<BinaryFrameFilter> {
    constructor() {
        super("parca.query.v1alpha1.BinaryFrameFilter", [
            { no: 1, name: "include_binaries", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<BinaryFrameFilter>): BinaryFrameFilter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.includeBinaries = [];
        if (value !== undefined)
            reflectionMergePartial<BinaryFrameFilter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: BinaryFrameFilter): BinaryFrameFilter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string include_binaries */ 1:
                    message.includeBinaries.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: BinaryFrameFilter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string include_binaries = 1; */
        for (let i = 0; i < message.includeBinaries.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.includeBinaries[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.BinaryFrameFilter
 */
export const BinaryFrameFilter = new BinaryFrameFilter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class RuntimeFilter$Type extends MessageType<RuntimeFilter> {
    constructor() {
        super("parca.query.v1alpha1.RuntimeFilter", [
            { no: 1, name: "show_python", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 2, name: "show_ruby", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 3, name: "show_interpreted_only", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<RuntimeFilter>): RuntimeFilter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.showPython = false;
        message.showRuby = false;
        message.showInterpretedOnly = false;
        if (value !== undefined)
            reflectionMergePartial<RuntimeFilter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: RuntimeFilter): RuntimeFilter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bool show_python */ 1:
                    message.showPython = reader.bool();
                    break;
                case /* bool show_ruby */ 2:
                    message.showRuby = reader.bool();
                    break;
                case /* bool show_interpreted_only */ 3:
                    message.showInterpretedOnly = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: RuntimeFilter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bool show_python = 1; */
        if (message.showPython !== false)
            writer.tag(1, WireType.Varint).bool(message.showPython);
        /* bool show_ruby = 2; */
        if (message.showRuby !== false)
            writer.tag(2, WireType.Varint).bool(message.showRuby);
        /* bool show_interpreted_only = 3; */
        if (message.showInterpretedOnly !== false)
            writer.tag(3, WireType.Varint).bool(message.showInterpretedOnly);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.RuntimeFilter
 */
export const RuntimeFilter = new RuntimeFilter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SourceReference$Type extends MessageType<SourceReference> {
    constructor() {
        super("parca.query.v1alpha1.SourceReference", [
            { no: 1, name: "build_id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "filename", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "source_only", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<SourceReference>): SourceReference {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.buildId = "";
        message.filename = "";
        message.sourceOnly = false;
        if (value !== undefined)
            reflectionMergePartial<SourceReference>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SourceReference): SourceReference {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string build_id */ 1:
                    message.buildId = reader.string();
                    break;
                case /* string filename */ 2:
                    message.filename = reader.string();
                    break;
                case /* bool source_only */ 3:
                    message.sourceOnly = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SourceReference, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string build_id = 1; */
        if (message.buildId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.buildId);
        /* string filename = 2; */
        if (message.filename !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.filename);
        /* bool source_only = 3; */
        if (message.sourceOnly !== false)
            writer.tag(3, WireType.Varint).bool(message.sourceOnly);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.SourceReference
 */
export const SourceReference = new SourceReference$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GroupBy$Type extends MessageType<GroupBy> {
    constructor() {
        super("parca.query.v1alpha1.GroupBy", [
            { no: 1, name: "fields", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<GroupBy>): GroupBy {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.fields = [];
        if (value !== undefined)
            reflectionMergePartial<GroupBy>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GroupBy): GroupBy {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string fields */ 1:
                    message.fields.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GroupBy, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string fields = 1; */
        for (let i = 0; i < message.fields.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.fields[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.GroupBy
 */
export const GroupBy = new GroupBy$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Top$Type extends MessageType<Top> {
    constructor() {
        super("parca.query.v1alpha1.Top", [
            { no: 1, name: "list", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TopNode },
            { no: 2, name: "reported", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 3, name: "total", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 4, name: "unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<Top>): Top {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.list = [];
        message.reported = 0;
        message.total = 0;
        message.unit = "";
        if (value !== undefined)
            reflectionMergePartial<Top>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Top): Top {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.query.v1alpha1.TopNode list */ 1:
                    message.list.push(TopNode.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* int32 reported */ 2:
                    message.reported = reader.int32();
                    break;
                case /* int32 total = 3 [deprecated = true];*/ 3:
                    message.total = reader.int32();
                    break;
                case /* string unit */ 4:
                    message.unit = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Top, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.query.v1alpha1.TopNode list = 1; */
        for (let i = 0; i < message.list.length; i++)
            TopNode.internalBinaryWrite(message.list[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* int32 reported = 2; */
        if (message.reported !== 0)
            writer.tag(2, WireType.Varint).int32(message.reported);
        /* int32 total = 3 [deprecated = true]; */
        if (message.total !== 0)
            writer.tag(3, WireType.Varint).int32(message.total);
        /* string unit = 4; */
        if (message.unit !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.unit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.Top
 */
export const Top = new Top$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TopNode$Type extends MessageType<TopNode> {
    constructor() {
        super("parca.query.v1alpha1.TopNode", [
            { no: 1, name: "meta", kind: "message", T: () => TopNodeMeta },
            { no: 2, name: "cumulative", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "flat", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "diff", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<TopNode>): TopNode {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.cumulative = 0n;
        message.flat = 0n;
        message.diff = 0n;
        if (value !== undefined)
            reflectionMergePartial<TopNode>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TopNode): TopNode {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.TopNodeMeta meta */ 1:
                    message.meta = TopNodeMeta.internalBinaryRead(reader, reader.uint32(), options, message.meta);
                    break;
                case /* int64 cumulative */ 2:
                    message.cumulative = reader.int64().toBigInt();
                    break;
                case /* int64 flat */ 3:
                    message.flat = reader.int64().toBigInt();
                    break;
                case /* int64 diff */ 4:
                    message.diff = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TopNode, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.TopNodeMeta meta = 1; */
        if (message.meta)
            TopNodeMeta.internalBinaryWrite(message.meta, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* int64 cumulative = 2; */
        if (message.cumulative !== 0n)
            writer.tag(2, WireType.Varint).int64(message.cumulative);
        /* int64 flat = 3; */
        if (message.flat !== 0n)
            writer.tag(3, WireType.Varint).int64(message.flat);
        /* int64 diff = 4; */
        if (message.diff !== 0n)
            writer.tag(4, WireType.Varint).int64(message.diff);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.TopNode
 */
export const TopNode = new TopNode$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TopNodeMeta$Type extends MessageType<TopNodeMeta> {
    constructor() {
        super("parca.query.v1alpha1.TopNodeMeta", [
            { no: 1, name: "location", kind: "message", T: () => Location },
            { no: 2, name: "mapping", kind: "message", T: () => Mapping },
            { no: 3, name: "function", kind: "message", T: () => Function },
            { no: 4, name: "line", kind: "message", T: () => Line }
        ]);
    }
    create(value?: PartialMessage<TopNodeMeta>): TopNodeMeta {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TopNodeMeta>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TopNodeMeta): TopNodeMeta {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.metastore.v1alpha1.Location location */ 1:
                    message.location = Location.internalBinaryRead(reader, reader.uint32(), options, message.location);
                    break;
                case /* parca.metastore.v1alpha1.Mapping mapping */ 2:
                    message.mapping = Mapping.internalBinaryRead(reader, reader.uint32(), options, message.mapping);
                    break;
                case /* parca.metastore.v1alpha1.Function function */ 3:
                    message.function = Function.internalBinaryRead(reader, reader.uint32(), options, message.function);
                    break;
                case /* parca.metastore.v1alpha1.Line line */ 4:
                    message.line = Line.internalBinaryRead(reader, reader.uint32(), options, message.line);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TopNodeMeta, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.metastore.v1alpha1.Location location = 1; */
        if (message.location)
            Location.internalBinaryWrite(message.location, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Mapping mapping = 2; */
        if (message.mapping)
            Mapping.internalBinaryWrite(message.mapping, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Function function = 3; */
        if (message.function)
            Function.internalBinaryWrite(message.function, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Line line = 4; */
        if (message.line)
            Line.internalBinaryWrite(message.line, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.TopNodeMeta
 */
export const TopNodeMeta = new TopNodeMeta$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Flamegraph$Type extends MessageType<Flamegraph> {
    constructor() {
        super("parca.query.v1alpha1.Flamegraph", [
            { no: 1, name: "root", kind: "message", T: () => FlamegraphRootNode },
            { no: 2, name: "total", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "height", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 5, name: "string_table", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "locations", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Location },
            { no: 7, name: "mapping", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Mapping },
            { no: 8, name: "function", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Function },
            { no: 9, name: "untrimmed_total", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "trimmed", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Flamegraph>): Flamegraph {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.total = 0n;
        message.unit = "";
        message.height = 0;
        message.stringTable = [];
        message.locations = [];
        message.mapping = [];
        message.function = [];
        message.untrimmedTotal = 0n;
        message.trimmed = 0n;
        if (value !== undefined)
            reflectionMergePartial<Flamegraph>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Flamegraph): Flamegraph {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.FlamegraphRootNode root */ 1:
                    message.root = FlamegraphRootNode.internalBinaryRead(reader, reader.uint32(), options, message.root);
                    break;
                case /* int64 total = 2 [deprecated = true];*/ 2:
                    message.total = reader.int64().toBigInt();
                    break;
                case /* string unit */ 3:
                    message.unit = reader.string();
                    break;
                case /* int32 height */ 4:
                    message.height = reader.int32();
                    break;
                case /* repeated string string_table */ 5:
                    message.stringTable.push(reader.string());
                    break;
                case /* repeated parca.metastore.v1alpha1.Location locations */ 6:
                    message.locations.push(Location.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated parca.metastore.v1alpha1.Mapping mapping */ 7:
                    message.mapping.push(Mapping.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated parca.metastore.v1alpha1.Function function */ 8:
                    message.function.push(Function.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* int64 untrimmed_total = 9 [deprecated = true];*/ 9:
                    message.untrimmedTotal = reader.int64().toBigInt();
                    break;
                case /* int64 trimmed */ 10:
                    message.trimmed = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Flamegraph, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.FlamegraphRootNode root = 1; */
        if (message.root)
            FlamegraphRootNode.internalBinaryWrite(message.root, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* int64 total = 2 [deprecated = true]; */
        if (message.total !== 0n)
            writer.tag(2, WireType.Varint).int64(message.total);
        /* string unit = 3; */
        if (message.unit !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.unit);
        /* int32 height = 4; */
        if (message.height !== 0)
            writer.tag(4, WireType.Varint).int32(message.height);
        /* repeated string string_table = 5; */
        for (let i = 0; i < message.stringTable.length; i++)
            writer.tag(5, WireType.LengthDelimited).string(message.stringTable[i]);
        /* repeated parca.metastore.v1alpha1.Location locations = 6; */
        for (let i = 0; i < message.locations.length; i++)
            Location.internalBinaryWrite(message.locations[i], writer.tag(6, WireType.LengthDelimited).fork(), options).join();
        /* repeated parca.metastore.v1alpha1.Mapping mapping = 7; */
        for (let i = 0; i < message.mapping.length; i++)
            Mapping.internalBinaryWrite(message.mapping[i], writer.tag(7, WireType.LengthDelimited).fork(), options).join();
        /* repeated parca.metastore.v1alpha1.Function function = 8; */
        for (let i = 0; i < message.function.length; i++)
            Function.internalBinaryWrite(message.function[i], writer.tag(8, WireType.LengthDelimited).fork(), options).join();
        /* int64 untrimmed_total = 9 [deprecated = true]; */
        if (message.untrimmedTotal !== 0n)
            writer.tag(9, WireType.Varint).int64(message.untrimmedTotal);
        /* int64 trimmed = 10; */
        if (message.trimmed !== 0n)
            writer.tag(10, WireType.Varint).int64(message.trimmed);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.Flamegraph
 */
export const Flamegraph = new Flamegraph$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FlamegraphArrow$Type extends MessageType<FlamegraphArrow> {
    constructor() {
        super("parca.query.v1alpha1.FlamegraphArrow", [
            { no: 1, name: "record", kind: "scalar", T: 12 /*ScalarType.BYTES*/ },
            { no: 2, name: "unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "height", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 4, name: "trimmed", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<FlamegraphArrow>): FlamegraphArrow {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.record = new Uint8Array(0);
        message.unit = "";
        message.height = 0;
        message.trimmed = 0n;
        if (value !== undefined)
            reflectionMergePartial<FlamegraphArrow>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FlamegraphArrow): FlamegraphArrow {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bytes record */ 1:
                    message.record = reader.bytes();
                    break;
                case /* string unit */ 2:
                    message.unit = reader.string();
                    break;
                case /* int32 height */ 3:
                    message.height = reader.int32();
                    break;
                case /* int64 trimmed */ 4:
                    message.trimmed = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FlamegraphArrow, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bytes record = 1; */
        if (message.record.length)
            writer.tag(1, WireType.LengthDelimited).bytes(message.record);
        /* string unit = 2; */
        if (message.unit !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.unit);
        /* int32 height = 3; */
        if (message.height !== 0)
            writer.tag(3, WireType.Varint).int32(message.height);
        /* int64 trimmed = 4; */
        if (message.trimmed !== 0n)
            writer.tag(4, WireType.Varint).int64(message.trimmed);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.FlamegraphArrow
 */
export const FlamegraphArrow = new FlamegraphArrow$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Source$Type extends MessageType<Source> {
    constructor() {
        super("parca.query.v1alpha1.Source", [
            { no: 1, name: "record", kind: "scalar", T: 12 /*ScalarType.BYTES*/ },
            { no: 2, name: "source", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<Source>): Source {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.record = new Uint8Array(0);
        message.source = "";
        message.unit = "";
        if (value !== undefined)
            reflectionMergePartial<Source>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Source): Source {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bytes record */ 1:
                    message.record = reader.bytes();
                    break;
                case /* string source */ 2:
                    message.source = reader.string();
                    break;
                case /* string unit */ 3:
                    message.unit = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Source, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bytes record = 1; */
        if (message.record.length)
            writer.tag(1, WireType.LengthDelimited).bytes(message.record);
        /* string source = 2; */
        if (message.source !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.source);
        /* string unit = 3; */
        if (message.unit !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.unit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.Source
 */
export const Source = new Source$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FlamegraphRootNode$Type extends MessageType<FlamegraphRootNode> {
    constructor() {
        super("parca.query.v1alpha1.FlamegraphRootNode", [
            { no: 1, name: "cumulative", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "diff", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "children", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => FlamegraphNode }
        ]);
    }
    create(value?: PartialMessage<FlamegraphRootNode>): FlamegraphRootNode {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.cumulative = 0n;
        message.diff = 0n;
        message.children = [];
        if (value !== undefined)
            reflectionMergePartial<FlamegraphRootNode>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FlamegraphRootNode): FlamegraphRootNode {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 cumulative */ 1:
                    message.cumulative = reader.int64().toBigInt();
                    break;
                case /* int64 diff */ 2:
                    message.diff = reader.int64().toBigInt();
                    break;
                case /* repeated parca.query.v1alpha1.FlamegraphNode children */ 3:
                    message.children.push(FlamegraphNode.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FlamegraphRootNode, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 cumulative = 1; */
        if (message.cumulative !== 0n)
            writer.tag(1, WireType.Varint).int64(message.cumulative);
        /* int64 diff = 2; */
        if (message.diff !== 0n)
            writer.tag(2, WireType.Varint).int64(message.diff);
        /* repeated parca.query.v1alpha1.FlamegraphNode children = 3; */
        for (let i = 0; i < message.children.length; i++)
            FlamegraphNode.internalBinaryWrite(message.children[i], writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.FlamegraphRootNode
 */
export const FlamegraphRootNode = new FlamegraphRootNode$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FlamegraphNode$Type extends MessageType<FlamegraphNode> {
    constructor() {
        super("parca.query.v1alpha1.FlamegraphNode", [
            { no: 1, name: "meta", kind: "message", T: () => FlamegraphNodeMeta },
            { no: 2, name: "cumulative", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 3, name: "diff", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "children", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => FlamegraphNode }
        ]);
    }
    create(value?: PartialMessage<FlamegraphNode>): FlamegraphNode {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.cumulative = 0n;
        message.diff = 0n;
        message.children = [];
        if (value !== undefined)
            reflectionMergePartial<FlamegraphNode>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FlamegraphNode): FlamegraphNode {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.FlamegraphNodeMeta meta */ 1:
                    message.meta = FlamegraphNodeMeta.internalBinaryRead(reader, reader.uint32(), options, message.meta);
                    break;
                case /* int64 cumulative */ 2:
                    message.cumulative = reader.int64().toBigInt();
                    break;
                case /* int64 diff */ 3:
                    message.diff = reader.int64().toBigInt();
                    break;
                case /* repeated parca.query.v1alpha1.FlamegraphNode children */ 4:
                    message.children.push(FlamegraphNode.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FlamegraphNode, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.FlamegraphNodeMeta meta = 1; */
        if (message.meta)
            FlamegraphNodeMeta.internalBinaryWrite(message.meta, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* int64 cumulative = 2; */
        if (message.cumulative !== 0n)
            writer.tag(2, WireType.Varint).int64(message.cumulative);
        /* int64 diff = 3; */
        if (message.diff !== 0n)
            writer.tag(3, WireType.Varint).int64(message.diff);
        /* repeated parca.query.v1alpha1.FlamegraphNode children = 4; */
        for (let i = 0; i < message.children.length; i++)
            FlamegraphNode.internalBinaryWrite(message.children[i], writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.FlamegraphNode
 */
export const FlamegraphNode = new FlamegraphNode$Type();
// @generated message type with reflection information, may provide speed optimized methods
class FlamegraphNodeMeta$Type extends MessageType<FlamegraphNodeMeta> {
    constructor() {
        super("parca.query.v1alpha1.FlamegraphNodeMeta", [
            { no: 1, name: "location", kind: "message", T: () => Location },
            { no: 2, name: "mapping", kind: "message", T: () => Mapping },
            { no: 3, name: "function", kind: "message", T: () => Function },
            { no: 4, name: "line", kind: "message", T: () => Line },
            { no: 5, name: "location_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ },
            { no: 6, name: "line_index", kind: "scalar", T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
    create(value?: PartialMessage<FlamegraphNodeMeta>): FlamegraphNodeMeta {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.locationIndex = 0;
        message.lineIndex = 0;
        if (value !== undefined)
            reflectionMergePartial<FlamegraphNodeMeta>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: FlamegraphNodeMeta): FlamegraphNodeMeta {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.metastore.v1alpha1.Location location */ 1:
                    message.location = Location.internalBinaryRead(reader, reader.uint32(), options, message.location);
                    break;
                case /* parca.metastore.v1alpha1.Mapping mapping */ 2:
                    message.mapping = Mapping.internalBinaryRead(reader, reader.uint32(), options, message.mapping);
                    break;
                case /* parca.metastore.v1alpha1.Function function */ 3:
                    message.function = Function.internalBinaryRead(reader, reader.uint32(), options, message.function);
                    break;
                case /* parca.metastore.v1alpha1.Line line */ 4:
                    message.line = Line.internalBinaryRead(reader, reader.uint32(), options, message.line);
                    break;
                case /* uint32 location_index */ 5:
                    message.locationIndex = reader.uint32();
                    break;
                case /* uint32 line_index */ 6:
                    message.lineIndex = reader.uint32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: FlamegraphNodeMeta, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.metastore.v1alpha1.Location location = 1; */
        if (message.location)
            Location.internalBinaryWrite(message.location, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Mapping mapping = 2; */
        if (message.mapping)
            Mapping.internalBinaryWrite(message.mapping, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Function function = 3; */
        if (message.function)
            Function.internalBinaryWrite(message.function, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Line line = 4; */
        if (message.line)
            Line.internalBinaryWrite(message.line, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* uint32 location_index = 5; */
        if (message.locationIndex !== 0)
            writer.tag(5, WireType.Varint).uint32(message.locationIndex);
        /* uint32 line_index = 6; */
        if (message.lineIndex !== 0)
            writer.tag(6, WireType.Varint).uint32(message.lineIndex);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.FlamegraphNodeMeta
 */
export const FlamegraphNodeMeta = new FlamegraphNodeMeta$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CallgraphNode$Type extends MessageType<CallgraphNode> {
    constructor() {
        super("parca.query.v1alpha1.CallgraphNode", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "meta", kind: "message", T: () => CallgraphNodeMeta },
            { no: 3, name: "cumulative", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 4, name: "flat", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<CallgraphNode>): CallgraphNode {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.cumulative = 0n;
        message.flat = 0n;
        if (value !== undefined)
            reflectionMergePartial<CallgraphNode>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CallgraphNode): CallgraphNode {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* parca.query.v1alpha1.CallgraphNodeMeta meta */ 2:
                    message.meta = CallgraphNodeMeta.internalBinaryRead(reader, reader.uint32(), options, message.meta);
                    break;
                case /* int64 cumulative */ 3:
                    message.cumulative = reader.int64().toBigInt();
                    break;
                case /* int64 flat */ 4:
                    message.flat = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CallgraphNode, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* parca.query.v1alpha1.CallgraphNodeMeta meta = 2; */
        if (message.meta)
            CallgraphNodeMeta.internalBinaryWrite(message.meta, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* int64 cumulative = 3; */
        if (message.cumulative !== 0n)
            writer.tag(3, WireType.Varint).int64(message.cumulative);
        /* int64 flat = 4; */
        if (message.flat !== 0n)
            writer.tag(4, WireType.Varint).int64(message.flat);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.CallgraphNode
 */
export const CallgraphNode = new CallgraphNode$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CallgraphNodeMeta$Type extends MessageType<CallgraphNodeMeta> {
    constructor() {
        super("parca.query.v1alpha1.CallgraphNodeMeta", [
            { no: 1, name: "location", kind: "message", T: () => Location },
            { no: 2, name: "mapping", kind: "message", T: () => Mapping },
            { no: 3, name: "function", kind: "message", T: () => Function },
            { no: 4, name: "line", kind: "message", T: () => Line }
        ]);
    }
    create(value?: PartialMessage<CallgraphNodeMeta>): CallgraphNodeMeta {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<CallgraphNodeMeta>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CallgraphNodeMeta): CallgraphNodeMeta {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.metastore.v1alpha1.Location location */ 1:
                    message.location = Location.internalBinaryRead(reader, reader.uint32(), options, message.location);
                    break;
                case /* parca.metastore.v1alpha1.Mapping mapping */ 2:
                    message.mapping = Mapping.internalBinaryRead(reader, reader.uint32(), options, message.mapping);
                    break;
                case /* parca.metastore.v1alpha1.Function function */ 3:
                    message.function = Function.internalBinaryRead(reader, reader.uint32(), options, message.function);
                    break;
                case /* parca.metastore.v1alpha1.Line line */ 4:
                    message.line = Line.internalBinaryRead(reader, reader.uint32(), options, message.line);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CallgraphNodeMeta, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.metastore.v1alpha1.Location location = 1; */
        if (message.location)
            Location.internalBinaryWrite(message.location, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Mapping mapping = 2; */
        if (message.mapping)
            Mapping.internalBinaryWrite(message.mapping, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Function function = 3; */
        if (message.function)
            Function.internalBinaryWrite(message.function, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* parca.metastore.v1alpha1.Line line = 4; */
        if (message.line)
            Line.internalBinaryWrite(message.line, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.CallgraphNodeMeta
 */
export const CallgraphNodeMeta = new CallgraphNodeMeta$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CallgraphEdge$Type extends MessageType<CallgraphEdge> {
    constructor() {
        super("parca.query.v1alpha1.CallgraphEdge", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "source", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "target", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "cumulative", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 5, name: "is_collapsed", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<CallgraphEdge>): CallgraphEdge {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.source = "";
        message.target = "";
        message.cumulative = 0n;
        message.isCollapsed = false;
        if (value !== undefined)
            reflectionMergePartial<CallgraphEdge>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CallgraphEdge): CallgraphEdge {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string source */ 2:
                    message.source = reader.string();
                    break;
                case /* string target */ 3:
                    message.target = reader.string();
                    break;
                case /* int64 cumulative */ 4:
                    message.cumulative = reader.int64().toBigInt();
                    break;
                case /* bool is_collapsed */ 5:
                    message.isCollapsed = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CallgraphEdge, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string source = 2; */
        if (message.source !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.source);
        /* string target = 3; */
        if (message.target !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.target);
        /* int64 cumulative = 4; */
        if (message.cumulative !== 0n)
            writer.tag(4, WireType.Varint).int64(message.cumulative);
        /* bool is_collapsed = 5; */
        if (message.isCollapsed !== false)
            writer.tag(5, WireType.Varint).bool(message.isCollapsed);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.CallgraphEdge
 */
export const CallgraphEdge = new CallgraphEdge$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Callgraph$Type extends MessageType<Callgraph> {
    constructor() {
        super("parca.query.v1alpha1.Callgraph", [
            { no: 1, name: "nodes", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => CallgraphNode },
            { no: 2, name: "edges", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => CallgraphEdge },
            { no: 3, name: "cumulative", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<Callgraph>): Callgraph {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.nodes = [];
        message.edges = [];
        message.cumulative = 0n;
        if (value !== undefined)
            reflectionMergePartial<Callgraph>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Callgraph): Callgraph {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated parca.query.v1alpha1.CallgraphNode nodes */ 1:
                    message.nodes.push(CallgraphNode.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* repeated parca.query.v1alpha1.CallgraphEdge edges */ 2:
                    message.edges.push(CallgraphEdge.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* int64 cumulative = 3 [deprecated = true];*/ 3:
                    message.cumulative = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Callgraph, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated parca.query.v1alpha1.CallgraphNode nodes = 1; */
        for (let i = 0; i < message.nodes.length; i++)
            CallgraphNode.internalBinaryWrite(message.nodes[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated parca.query.v1alpha1.CallgraphEdge edges = 2; */
        for (let i = 0; i < message.edges.length; i++)
            CallgraphEdge.internalBinaryWrite(message.edges[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* int64 cumulative = 3 [deprecated = true]; */
        if (message.cumulative !== 0n)
            writer.tag(3, WireType.Varint).int64(message.cumulative);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.Callgraph
 */
export const Callgraph = new Callgraph$Type();
// @generated message type with reflection information, may provide speed optimized methods
class QueryResponse$Type extends MessageType<QueryResponse> {
    constructor() {
        super("parca.query.v1alpha1.QueryResponse", [
            { no: 5, name: "flamegraph", kind: "message", oneof: "report", T: () => Flamegraph },
            { no: 6, name: "pprof", kind: "scalar", oneof: "report", T: 12 /*ScalarType.BYTES*/ },
            { no: 7, name: "top", kind: "message", oneof: "report", T: () => Top },
            { no: 8, name: "callgraph", kind: "message", oneof: "report", T: () => Callgraph },
            { no: 11, name: "flamegraph_arrow", kind: "message", oneof: "report", T: () => FlamegraphArrow },
            { no: 12, name: "source", kind: "message", oneof: "report", T: () => Source },
            { no: 13, name: "table_arrow", kind: "message", oneof: "report", T: () => TableArrow },
            { no: 14, name: "profile_metadata", kind: "message", oneof: "report", T: () => ProfileMetadata },
            { no: 9, name: "total", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 10, name: "filtered", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ }
        ]);
    }
    create(value?: PartialMessage<QueryResponse>): QueryResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.report = { oneofKind: undefined };
        message.total = 0n;
        message.filtered = 0n;
        if (value !== undefined)
            reflectionMergePartial<QueryResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: QueryResponse): QueryResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.Flamegraph flamegraph */ 5:
                    message.report = {
                        oneofKind: "flamegraph",
                        flamegraph: Flamegraph.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).flamegraph)
                    };
                    break;
                case /* bytes pprof */ 6:
                    message.report = {
                        oneofKind: "pprof",
                        pprof: reader.bytes()
                    };
                    break;
                case /* parca.query.v1alpha1.Top top */ 7:
                    message.report = {
                        oneofKind: "top",
                        top: Top.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).top)
                    };
                    break;
                case /* parca.query.v1alpha1.Callgraph callgraph */ 8:
                    message.report = {
                        oneofKind: "callgraph",
                        callgraph: Callgraph.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).callgraph)
                    };
                    break;
                case /* parca.query.v1alpha1.FlamegraphArrow flamegraph_arrow */ 11:
                    message.report = {
                        oneofKind: "flamegraphArrow",
                        flamegraphArrow: FlamegraphArrow.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).flamegraphArrow)
                    };
                    break;
                case /* parca.query.v1alpha1.Source source */ 12:
                    message.report = {
                        oneofKind: "source",
                        source: Source.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).source)
                    };
                    break;
                case /* parca.query.v1alpha1.TableArrow table_arrow */ 13:
                    message.report = {
                        oneofKind: "tableArrow",
                        tableArrow: TableArrow.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).tableArrow)
                    };
                    break;
                case /* parca.query.v1alpha1.ProfileMetadata profile_metadata */ 14:
                    message.report = {
                        oneofKind: "profileMetadata",
                        profileMetadata: ProfileMetadata.internalBinaryRead(reader, reader.uint32(), options, (message.report as any).profileMetadata)
                    };
                    break;
                case /* int64 total */ 9:
                    message.total = reader.int64().toBigInt();
                    break;
                case /* int64 filtered */ 10:
                    message.filtered = reader.int64().toBigInt();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: QueryResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.Flamegraph flamegraph = 5; */
        if (message.report.oneofKind === "flamegraph")
            Flamegraph.internalBinaryWrite(message.report.flamegraph, writer.tag(5, WireType.LengthDelimited).fork(), options).join();
        /* bytes pprof = 6; */
        if (message.report.oneofKind === "pprof")
            writer.tag(6, WireType.LengthDelimited).bytes(message.report.pprof);
        /* parca.query.v1alpha1.Top top = 7; */
        if (message.report.oneofKind === "top")
            Top.internalBinaryWrite(message.report.top, writer.tag(7, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.Callgraph callgraph = 8; */
        if (message.report.oneofKind === "callgraph")
            Callgraph.internalBinaryWrite(message.report.callgraph, writer.tag(8, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.FlamegraphArrow flamegraph_arrow = 11; */
        if (message.report.oneofKind === "flamegraphArrow")
            FlamegraphArrow.internalBinaryWrite(message.report.flamegraphArrow, writer.tag(11, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.Source source = 12; */
        if (message.report.oneofKind === "source")
            Source.internalBinaryWrite(message.report.source, writer.tag(12, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.TableArrow table_arrow = 13; */
        if (message.report.oneofKind === "tableArrow")
            TableArrow.internalBinaryWrite(message.report.tableArrow, writer.tag(13, WireType.LengthDelimited).fork(), options).join();
        /* parca.query.v1alpha1.ProfileMetadata profile_metadata = 14; */
        if (message.report.oneofKind === "profileMetadata")
            ProfileMetadata.internalBinaryWrite(message.report.profileMetadata, writer.tag(14, WireType.LengthDelimited).fork(), options).join();
        /* int64 total = 9; */
        if (message.total !== 0n)
            writer.tag(9, WireType.Varint).int64(message.total);
        /* int64 filtered = 10; */
        if (message.filtered !== 0n)
            writer.tag(10, WireType.Varint).int64(message.filtered);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.QueryResponse
 */
export const QueryResponse = new QueryResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SeriesRequest$Type extends MessageType<SeriesRequest> {
    constructor() {
        super("parca.query.v1alpha1.SeriesRequest", [
            { no: 1, name: "match", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start", kind: "message", T: () => Timestamp },
            { no: 3, name: "end", kind: "message", T: () => Timestamp }
        ]);
    }
    create(value?: PartialMessage<SeriesRequest>): SeriesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.match = [];
        if (value !== undefined)
            reflectionMergePartial<SeriesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SeriesRequest): SeriesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string match */ 1:
                    message.match.push(reader.string());
                    break;
                case /* google.protobuf.Timestamp start */ 2:
                    message.start = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.start);
                    break;
                case /* google.protobuf.Timestamp end */ 3:
                    message.end = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.end);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SeriesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string match = 1; */
        for (let i = 0; i < message.match.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.match[i]);
        /* google.protobuf.Timestamp start = 2; */
        if (message.start)
            Timestamp.internalBinaryWrite(message.start, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp end = 3; */
        if (message.end)
            Timestamp.internalBinaryWrite(message.end, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.SeriesRequest
 */
export const SeriesRequest = new SeriesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SeriesResponse$Type extends MessageType<SeriesResponse> {
    constructor() {
        super("parca.query.v1alpha1.SeriesResponse", []);
    }
    create(value?: PartialMessage<SeriesResponse>): SeriesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<SeriesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SeriesResponse): SeriesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SeriesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.SeriesResponse
 */
export const SeriesResponse = new SeriesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class LabelsRequest$Type extends MessageType<LabelsRequest> {
    constructor() {
        super("parca.query.v1alpha1.LabelsRequest", [
            { no: 1, name: "match", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "start", kind: "message", T: () => Timestamp },
            { no: 3, name: "end", kind: "message", T: () => Timestamp },
            { no: 4, name: "profile_type", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<LabelsRequest>): LabelsRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.match = [];
        if (value !== undefined)
            reflectionMergePartial<LabelsRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: LabelsRequest): LabelsRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string match */ 1:
                    message.match.push(reader.string());
                    break;
                case /* google.protobuf.Timestamp start */ 2:
                    message.start = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.start);
                    break;
                case /* google.protobuf.Timestamp end */ 3:
                    message.end = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.end);
                    break;
                case /* optional string profile_type */ 4:
                    message.profileType = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: LabelsRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string match = 1; */
        for (let i = 0; i < message.match.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.match[i]);
        /* google.protobuf.Timestamp start = 2; */
        if (message.start)
            Timestamp.internalBinaryWrite(message.start, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp end = 3; */
        if (message.end)
            Timestamp.internalBinaryWrite(message.end, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* optional string profile_type = 4; */
        if (message.profileType !== undefined)
            writer.tag(4, WireType.LengthDelimited).string(message.profileType);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.LabelsRequest
 */
export const LabelsRequest = new LabelsRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class LabelsResponse$Type extends MessageType<LabelsResponse> {
    constructor() {
        super("parca.query.v1alpha1.LabelsResponse", [
            { no: 1, name: "label_names", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "warnings", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<LabelsResponse>): LabelsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.labelNames = [];
        message.warnings = [];
        if (value !== undefined)
            reflectionMergePartial<LabelsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: LabelsResponse): LabelsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string label_names */ 1:
                    message.labelNames.push(reader.string());
                    break;
                case /* repeated string warnings */ 2:
                    message.warnings.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: LabelsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string label_names = 1; */
        for (let i = 0; i < message.labelNames.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.labelNames[i]);
        /* repeated string warnings = 2; */
        for (let i = 0; i < message.warnings.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.warnings[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.LabelsResponse
 */
export const LabelsResponse = new LabelsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ValuesRequest$Type extends MessageType<ValuesRequest> {
    constructor() {
        super("parca.query.v1alpha1.ValuesRequest", [
            { no: 1, name: "label_name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "match", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "start", kind: "message", T: () => Timestamp },
            { no: 4, name: "end", kind: "message", T: () => Timestamp },
            { no: 5, name: "profile_type", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ValuesRequest>): ValuesRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.labelName = "";
        message.match = [];
        if (value !== undefined)
            reflectionMergePartial<ValuesRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ValuesRequest): ValuesRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string label_name */ 1:
                    message.labelName = reader.string();
                    break;
                case /* repeated string match */ 2:
                    message.match.push(reader.string());
                    break;
                case /* google.protobuf.Timestamp start */ 3:
                    message.start = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.start);
                    break;
                case /* google.protobuf.Timestamp end */ 4:
                    message.end = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.end);
                    break;
                case /* optional string profile_type */ 5:
                    message.profileType = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ValuesRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string label_name = 1; */
        if (message.labelName !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.labelName);
        /* repeated string match = 2; */
        for (let i = 0; i < message.match.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.match[i]);
        /* google.protobuf.Timestamp start = 3; */
        if (message.start)
            Timestamp.internalBinaryWrite(message.start, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Timestamp end = 4; */
        if (message.end)
            Timestamp.internalBinaryWrite(message.end, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* optional string profile_type = 5; */
        if (message.profileType !== undefined)
            writer.tag(5, WireType.LengthDelimited).string(message.profileType);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ValuesRequest
 */
export const ValuesRequest = new ValuesRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ValuesResponse$Type extends MessageType<ValuesResponse> {
    constructor() {
        super("parca.query.v1alpha1.ValuesResponse", [
            { no: 1, name: "label_values", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "warnings", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ValuesResponse>): ValuesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.labelValues = [];
        message.warnings = [];
        if (value !== undefined)
            reflectionMergePartial<ValuesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ValuesResponse): ValuesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string label_values */ 1:
                    message.labelValues.push(reader.string());
                    break;
                case /* repeated string warnings */ 2:
                    message.warnings.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ValuesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string label_values = 1; */
        for (let i = 0; i < message.labelValues.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.labelValues[i]);
        /* repeated string warnings = 2; */
        for (let i = 0; i < message.warnings.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.warnings[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ValuesResponse
 */
export const ValuesResponse = new ValuesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ValueType$Type extends MessageType<ValueType> {
    constructor() {
        super("parca.query.v1alpha1.ValueType", [
            { no: 1, name: "type", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ValueType>): ValueType {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.type = "";
        message.unit = "";
        if (value !== undefined)
            reflectionMergePartial<ValueType>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ValueType): ValueType {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string type */ 1:
                    message.type = reader.string();
                    break;
                case /* string unit */ 2:
                    message.unit = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ValueType, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string type = 1; */
        if (message.type !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.type);
        /* string unit = 2; */
        if (message.unit !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.unit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ValueType
 */
export const ValueType = new ValueType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ShareProfileRequest$Type extends MessageType<ShareProfileRequest> {
    constructor() {
        super("parca.query.v1alpha1.ShareProfileRequest", [
            { no: 1, name: "query_request", kind: "message", T: () => QueryRequest },
            { no: 2, name: "description", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ShareProfileRequest>): ShareProfileRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<ShareProfileRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ShareProfileRequest): ShareProfileRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* parca.query.v1alpha1.QueryRequest query_request */ 1:
                    message.queryRequest = QueryRequest.internalBinaryRead(reader, reader.uint32(), options, message.queryRequest);
                    break;
                case /* optional string description */ 2:
                    message.description = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ShareProfileRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* parca.query.v1alpha1.QueryRequest query_request = 1; */
        if (message.queryRequest)
            QueryRequest.internalBinaryWrite(message.queryRequest, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* optional string description = 2; */
        if (message.description !== undefined)
            writer.tag(2, WireType.LengthDelimited).string(message.description);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ShareProfileRequest
 */
export const ShareProfileRequest = new ShareProfileRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ShareProfileResponse$Type extends MessageType<ShareProfileResponse> {
    constructor() {
        super("parca.query.v1alpha1.ShareProfileResponse", [
            { no: 1, name: "link", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ShareProfileResponse>): ShareProfileResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.link = "";
        if (value !== undefined)
            reflectionMergePartial<ShareProfileResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ShareProfileResponse): ShareProfileResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string link */ 1:
                    message.link = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ShareProfileResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string link = 1; */
        if (message.link !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.link);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ShareProfileResponse
 */
export const ShareProfileResponse = new ShareProfileResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TableArrow$Type extends MessageType<TableArrow> {
    constructor() {
        super("parca.query.v1alpha1.TableArrow", [
            { no: 1, name: "record", kind: "scalar", T: 12 /*ScalarType.BYTES*/ },
            { no: 2, name: "unit", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TableArrow>): TableArrow {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.record = new Uint8Array(0);
        message.unit = "";
        if (value !== undefined)
            reflectionMergePartial<TableArrow>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TableArrow): TableArrow {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* bytes record */ 1:
                    message.record = reader.bytes();
                    break;
                case /* string unit */ 2:
                    message.unit = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TableArrow, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* bytes record = 1; */
        if (message.record.length)
            writer.tag(1, WireType.LengthDelimited).bytes(message.record);
        /* string unit = 2; */
        if (message.unit !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.unit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.TableArrow
 */
export const TableArrow = new TableArrow$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ProfileMetadata$Type extends MessageType<ProfileMetadata> {
    constructor() {
        super("parca.query.v1alpha1.ProfileMetadata", [
            { no: 1, name: "mapping_files", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "labels", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ProfileMetadata>): ProfileMetadata {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.mappingFiles = [];
        message.labels = [];
        if (value !== undefined)
            reflectionMergePartial<ProfileMetadata>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ProfileMetadata): ProfileMetadata {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string mapping_files */ 1:
                    message.mappingFiles.push(reader.string());
                    break;
                case /* repeated string labels */ 2:
                    message.labels.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ProfileMetadata, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string mapping_files = 1; */
        for (let i = 0; i < message.mappingFiles.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.mappingFiles[i]);
        /* repeated string labels = 2; */
        for (let i = 0; i < message.labels.length; i++)
            writer.tag(2, WireType.LengthDelimited).string(message.labels[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message parca.query.v1alpha1.ProfileMetadata
 */
export const ProfileMetadata = new ProfileMetadata$Type();
/**
 * @generated ServiceType for protobuf service parca.query.v1alpha1.QueryService
 */
export const QueryService = new ServiceType("parca.query.v1alpha1.QueryService", [
    { name: "QueryRange", options: { "google.api.http": { get: "/profiles/query_range" } }, I: QueryRangeRequest, O: QueryRangeResponse },
    { name: "Query", options: { "google.api.http": { get: "/profiles/query" } }, I: QueryRequest, O: QueryResponse },
    { name: "Series", options: { "google.api.http": { get: "/profiles/series" } }, I: SeriesRequest, O: SeriesResponse },
    { name: "ProfileTypes", options: { "google.api.http": { get: "/profiles/types" } }, I: ProfileTypesRequest, O: ProfileTypesResponse },
    { name: "Labels", options: { "google.api.http": { get: "/profiles/labels" } }, I: LabelsRequest, O: LabelsResponse },
    { name: "Values", options: { "google.api.http": { get: "/profiles/labels/{label_name}/values" } }, I: ValuesRequest, O: ValuesResponse },
    { name: "ShareProfile", options: { "google.api.http": { post: "/profiles/share", body: "*" } }, I: ShareProfileRequest, O: ShareProfileResponse }
]);
