{"name": "@parca/web", "private": true, "version": "0.16.899", "description": "Parca Web Interface", "type": "module", "scripts": {"lint": "eslint --no-error-on-unmatched-pattern --ext .ts,.tsx,.js src/*", "nextjs-dev": "../../../node_modules/.bin/next dev", "clean": "rimraf .next && rimraf out", "test": "jest --coverage --config ../../../jest.config.js ./src/**/* ./__tests__/**/*", "dev": "vite", "build": "vite build"}, "config": {"port": 3000, "path": "/"}, "homepage": "PATH_PREFIX_VAR", "author": "", "license": "ISC", "dependencies": {"@alwaysmeticulous/recorder-loader": "^2.19.0", "@headlessui/react": "^1.7.19", "@iconify/react": "^4.0.0", "@parca/client": "workspace:*", "@parca/components": "workspace:*", "@parca/dynamicsize": "workspace:*", "@parca/hooks": "workspace:*", "@parca/icons": "workspace:*", "@parca/parser": "workspace:*", "@parca/profile": "workspace:*", "@parca/store": "workspace:*", "@parca/utilities": "workspace:*", "@protobuf-ts/grpcweb-transport": "^2.9.4", "@protobuf-ts/runtime-rpc": "^2.5.0", "@svgr/webpack": "6.5.1", "@tailwindcss/typography": "^0.5.8", "@tanstack/react-query": "^4.0.5", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "13.4.0", "autoprefixer": "10.4.21", "classnames": "^2.3.1", "immer": "9.0.21", "isomorphic-unfetch": "3.1.0", "lodash.debounce": "4.0.8", "lodash.throttle": "^4.1.1", "moment": "2.30.1", "postcss": "8.5.6", "postcss-flexbugs-fixes": "5.0.2", "postcss-preset-env": "8.5.1", "react": "18.3.1", "react-datepicker": "6.9.0", "react-dom": "18.3.1", "react-github-btn": "^1.4.0", "react-markdown": "^8.0.4", "react-redux": "^8.0.2", "react-router-dom": "6.30.1", "react-use": "17.6.0", "redux-persist": "^6.0.0", "tailwindcss": "3.2.4", "web-vitals": "3.5.2"}, "devDependencies": {"@types/lodash.throttle": "4.1.9", "@vitejs/plugin-react-swc": "3.10.2", "css-loader": "6.11.0", "eslint-config-prettier": "8.10.0", "eslint-plugin-import": "2.32.0", "jest": "29.7.0", "jest-runtime": "29.7.0", "pm2": "5.4.3", "tslint": "6.1.3", "tslint-config-prettier": "1.18.0", "tslint-plugin-prettier": "2.3.0", "tslint-react": "5.0.0", "vite-plugin-svgr": "4.3.0"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}