{"coredump-ref": "b815b983b62d0b2577d06918b1bc55d57649e8683cfc15eec2661e52564aefd8", "threads": [{"lwp": 91977, "frames": ["StubRoutines (2) [sha256_implCompressMB]+0 in :0", "byte[] ShaShenanigans.hashRandomStuff()+3 in ShaShenanigans.java:29", "void ShaShenanigans.shaShenanigans()+2 in ShaShenanigans.java:20", "void ShaShenanigans.main(java.lang.String[])+0 in ShaShenanigans.java:13", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7cf0cb", "libjvm.so+0x85f38b", "libjvm.so+0x86182f", "libjli.so+0x4037", "libjli.so+0x6f1b", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91975, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x806d7", "libjli.so+0x7a5b", "libjli.so+0x5193", "libjli.so+0x629b", "java+0xacf", "libc.so.6+0x2777f", "libc.so.6+0x27857", "java+0xb6f"]}, {"lwp": 91978, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x8732b", "libjvm.so+0xbfe27b", "libjvm.so+0xe59ff7", "libjvm.so+0xe5a08f", "<unwinding aborted due to error native_lr_unwinding_mid_trace>"]}, {"lwp": 91979, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e18f", "libjvm.so+0xb60ff7", "libjvm.so+0xb15217", "libjvm.so+0x6b70f7", "libjvm.so+0x5b77cb", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91980, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x8732b", "libjvm.so+0xbfe27b", "libjvm.so+0xe59ff7", "libjvm.so+0xe5a08f", "<unwinding aborted due to error native_lr_unwinding_mid_trace>"]}, {"lwp": 91981, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x8732b", "libjvm.so+0xbfe27b", "libjvm.so+0x6b89b7", "libjvm.so+0x5b77cb", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91982, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb15217", "libjvm.so+0x70a383", "libjvm.so+0x70a5a3", "libjvm.so+0x5b77cb", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91983, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb15217", "libjvm.so+0xe3fca3", "libjvm.so+0xe408db", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91984, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e18f", "libjvm.so+0xb60ff7", "libjvm.so+0xb152b3", "libjvm.so+0x8905a7", "void java.lang.ref.Reference.waitForReferencePendingList()+0 in Reference.java:0", "void java.lang.ref.Reference.processPendingReferences()+0 in Reference.java:253", "void java.lang.ref.Reference$ReferenceHandler.run()+0 in Reference.java:215", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7cf0cb", "libjvm.so+0x7d042b", "libjvm.so+0x88863b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91985, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e18f", "libjvm.so+0xb6063b", "libjvm.so+0xb35a87", "libjvm.so+0xd7e7b7", "libjvm.so+0x88974b", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove()+0 in ReferenceQueue.java:176", "void java.lang.ref.Finalizer$FinalizerThread.run()+17 in Finalizer.java:172", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7cf0cb", "libjvm.so+0x7d042b", "libjvm.so+0x88863b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91986, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x8732b", "libjvm.so+0xbfe27b", "libjvm.so+0xceea73", "libjvm.so+0xb4b30b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91987, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e18f", "libjvm.so+0xb60ff7", "libjvm.so+0xb15217", "libjvm.so+0xbfeccb", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91988, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb15217", "libjvm.so+0xb08c1f", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91989, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb152b3", "libjvm.so+0x59e33b", "libjvm.so+0x5a0b0b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91990, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb152b3", "libjvm.so+0x59e33b", "libjvm.so+0x5a0b0b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91991, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb15217", "libjvm.so+0xd73f33", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91992, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e18f", "libjvm.so+0xb60ff7", "libjvm.so+0xb15217", "libjvm.so+0xb2a18b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91993, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60f53", "libjvm.so+0xb15217", "libjvm.so+0xb29c2b", "libjvm.so+0xb29d07", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}, {"lwp": 91994, "frames": ["libc.so.6+0x7b654", "libc.so.6+0x7e47f", "libjvm.so+0xb60817", "libjvm.so+0xb35833", "libjvm.so+0xd7e7b7", "libjvm.so+0x88974b", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:155", "void jdk.internal.ref.CleanerImpl.run()+12 in CleanerImpl.java:140", "void java.lang.Thread.run()+1 in Thread.java:833", "void jdk.internal.misc.InnocuousThread.run()+2 in InnocuousThread.java:162", "StubRoutines (1) [call_stub_return_address]+0 in :0", "libjvm.so+0x7cf0cb", "libjvm.so+0x7d042b", "libjvm.so+0x88863b", "libjvm.so+0xdbefb7", "libjvm.so+0xdc3dcb", "libjvm.so+0xb56fbb", "libc.so.6+0x7edd7", "libc.so.6+0xe7e5b"]}], "modules": [{"ref": "72eb811bb6d61300f62f9e9e4d824759d3c6a8f510d157999d8d2adb6dc683d8", "local-path": "/usr/lib/aarch64-linux-gnu/libgcc_s.so.1"}, {"ref": "ae6bd25b1f9616e37fb652d1052af984576d22adacfd3bced9df82d075ad92c7", "local-path": "/usr/lib/aarch64-linux-gnu/libm.so.6"}, {"ref": "ff608307ebc72d45b92578b3eaaa0430b133c4a6625a2ed4088f8ea921973b4f", "local-path": "/usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.32"}, {"ref": "7870932cbadbac22b3df41b05d97050ca8a9a007b95c244678d7c6a9fe16d383", "local-path": "/usr/lib/jvm/java-17-openjdk-arm64/bin/java"}, {"ref": "4f5bcc96203b9c8287db6272082ec52c5f91d0d61b321cfbc0e5942154e66199", "local-path": "/usr/lib/jvm/java-17-openjdk-arm64/lib/libjli.so"}, {"ref": "b4091d46467304ba9420143b85651b94809bc99fabf676c88429235950411aba", "local-path": "/usr/lib/jvm/java-17-openjdk-arm64/lib/server/libjvm.so"}, {"ref": "ffb1ab496e6eced03ab679075f9f2c415c7728a145cc7f63d614497102d73822", "local-path": "/usr/lib/aarch64-linux-gnu/libz.so.1.2.13"}, {"ref": "95e19b060bda762e296a7b89f1f487f0516de5ae9756d42ed36179de31898169", "local-path": "/usr/lib/jvm/java-17-openjdk-arm64/lib/libjava.so"}, {"ref": "ae162a6a4dfedc12d2cdbebdecf62f24ca971ee0771d2b52bd5d2fd96a941b93", "local-path": "/usr/lib/jvm/java-17-openjdk-arm64/lib/libjimage.so"}, {"ref": "ae89390ee9874d9b0c4f7eda0ba8c798d631df0e600549d6a15f7237f03c0317", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}, {"ref": "388929059d814ffeedda0beaff53722c24c59838817324d95221c18ed851f21c", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}]}