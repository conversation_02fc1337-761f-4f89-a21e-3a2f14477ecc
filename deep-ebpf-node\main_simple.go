package main

import (
	"fmt"
	"os"

	"github.com/mexyusef/deep-ebpf-node/flags"
	"k8s.io/klog/v2"
)

func main() {
	klog.LogToStderr(true)
	klog.Infoln("Deep-eBPF Node Agent - Simple Test Version")
	klog.Infof("Version: %s", flags.Version)

	// Print configuration
	fmt.Printf("Configuration:\n")
	fmt.Printf("  Target PID: %d\n", *flags.TargetPID)
	fmt.Printf("  Target Binary: %s\n", *flags.TargetBinary)
	fmt.Printf("  Output Format: %s\n", *flags.OutputFormat)
	fmt.Printf("  Sample Rate: %d\n", *flags.SampleRate)

	// Check system requirements
	fmt.Printf("\nSystem Check:\n")
	
	// Check if running as root
	if os.Geteuid() != 0 {
		fmt.Printf("  ❌ Not running as root (required for eBPF)\n")
		fmt.Printf("  Run with: sudo ./deep-ebpf-node\n")
	} else {
		fmt.Printf("  ✅ Running as root\n")
	}

	// Check BPF filesystem
	if _, err := os.Stat("/sys/fs/bpf"); err == nil {
		fmt.Printf("  ✅ BPF filesystem mounted\n")
	} else {
		fmt.Printf("  ❌ BPF filesystem not mounted\n")
	}

	// Check kernel tracing
	tracingPaths := []string{"/sys/kernel/debug/tracing", "/sys/kernel/tracing"}
	tracingFound := false
	for _, path := range tracingPaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("  ✅ Kernel tracing available at %s\n", path)
			tracingFound = true
			break
		}
	}
	if !tracingFound {
		fmt.Printf("  ❌ Kernel tracing not available\n")
	}

	// Check uprobe support
	uprobePaths := []string{"/sys/kernel/debug/tracing/uprobe_events", "/sys/kernel/tracing/uprobe_events"}
	uprobeFound := false
	for _, path := range uprobePaths {
		if _, err := os.Stat(path); err == nil {
			fmt.Printf("  ✅ Uprobe support available at %s\n", path)
			uprobeFound = true
			break
		}
	}
	if !uprobeFound {
		fmt.Printf("  ❌ Uprobe support not available\n")
	}

	// Check BTF support
	if _, err := os.Stat("/sys/kernel/btf/vmlinux"); err == nil {
		fmt.Printf("  ✅ BTF support available\n")
	} else {
		fmt.Printf("  ⚠️  BTF support not available (optional)\n")
	}

	fmt.Printf("\nSimple test completed successfully!\n")
	fmt.Printf("Next step: Build full eBPF tracer\n")
}
