name: Release

on:
  release:
    types: [created]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  VERSION: ${{ github.event.release.tag_name }}

jobs:
  build-frontend:
    runs-on: ubuntu-22.04
    defaults:
      run:
        working-directory: ./front/
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 21
          registry-url: 'https://npm.pkg.github.com'
      - run: npm ci
      - run: npm version ${{ env.VERSION }}
      - run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - run: npm run build-prod
      - uses: actions/upload-artifact@v4
        with:
          name: static
          path: ./static

  build-images:
    needs: build-frontend
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4

      - uses: actions/download-artifact@v4

      - uses: docker/setup-qemu-action@v3
      - uses: docker/setup-buildx-action@v3

      - uses: docker/metadata-action@v5
        id: meta
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}

      - uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64, linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            VERSION=${{ steps.meta.outputs.version }}
            
      - name: extract amd64 binary from the image
        run: |
          docker create --platform linux/amd64 --name amd64 ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }} && 
          docker cp amd64:/usr/bin/coroot /tmp/coroot-amd64

      - name: extract arm64 binary from the image
        run: |
          docker create --platform linux/arm64 --name arm64 ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }} &&
          docker cp arm64:/usr/bin/coroot /tmp/coroot-arm64

      - name: upload amd64 binary
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: /tmp/coroot-amd64
          asset_name: coroot-amd64
          asset_content_type: application/octet-stream

      - name: upload arm64 binary
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: /tmp/coroot-arm64
          asset_name: coroot-arm64
          asset_content_type: application/octet-stream

      - uses: actions/github-script@v7
        env:
          RELEASE_ID: ${{ github.event.release.id }}
        with:
          script: |
            const { RELEASE_ID } = process.env
            github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: `${RELEASE_ID}`,
              prerelease: false,
              make_latest: true
            })
