package main

import (
	"bytes"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"runtime"
	"strings"

	"github.com/mexyusef/deep-ebpf-node/common"
	"github.com/mexyusef/deep-ebpf-node/ebpftracer"
	"github.com/mexyusef/deep-ebpf-node/flags"
	"github.com/mexyusef/deep-ebpf-node/output"
	"github.com/mexyusef/deep-ebpf-node/proc"
	"github.com/vishvananda/netns"
	"golang.org/x/sys/unix"
	"golang.org/x/time/rate"
	"k8s.io/klog/v2"
)

var (
	version = flags.Version
)

func uname() (string, string, error) {
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()

	f, err := os.Open("/proc/1/ns/uts")
	if err != nil {
		return "", "", err
	}
	defer f.Close()

	self, err := os.Open("/proc/self/ns/uts")
	if err != nil {
		return "", "", err
	}
	defer self.Close()

	defer func() {
		unix.Setns(int(self.Fd()), unix.CLONE_NEWUTS)
	}()

	err = unix.Setns(int(f.Fd()), unix.CLONE_NEWUTS)
	if err != nil {
		return "", "", err
	}
	var utsname unix.Utsname
	if err := unix.Uname(&utsname); err != nil {
		return "", "", err
	}
	hostname := string(bytes.Split(utsname.Nodename[:], []byte{0})[0])
	kernelVersion := string(bytes.Split(utsname.Release[:], []byte{0})[0])
	return hostname, kernelVersion, nil
}

func machineID() string {
	for _, p := range []string{"/etc/machine-id", "/var/lib/dbus/machine-id", "/sys/devices/virtual/dmi/id/product_uuid"} {
		payload, err := os.ReadFile(proc.HostPath(p))
		if err != nil {
			klog.Warningln("failed to read machine-id:", err)
			continue
		}
		id := strings.TrimSpace(strings.Replace(string(payload), "-", "", -1))
		klog.Infoln("machine-id: ", id)
		return id
	}
	return ""
}

func main() {
	klog.LogToStderr(false)
	klog.SetOutput(&RateLimitedLogOutput{limiter: rate.NewLimiter(rate.Limit(*flags.LogPerSecond), *flags.LogBurst)})

	klog.Infoln("deep-ebpf-node version:", version)

	hostname, kv, err := uname()
	if err != nil {
		klog.Exitln("failed to get uname:", err)
	}
	klog.Infoln("hostname:", hostname)
	klog.Infoln("kernel version:", kv)

	if err = common.SetKernelVersion(kv); err != nil {
		klog.Exitln(err)
	}

	if !common.GetKernelVersion().GreaterOrEqual(common.NewVersion(4, 16, 0)) {
		klog.Exitln("the minimum Linux kernel version required is 4.16 or later")
	}

	_ = machineID() // Get machine ID but don't use it for now

	// Initialize network namespaces for eBPF tracer
	hostNetNs, err := netns.GetFromPath("/proc/1/ns/net")
	if err != nil {
		klog.Exitln("failed to get host network namespace:", err)
	}
	defer hostNetNs.Close()

	selfNetNs, err := netns.Get()
	if err != nil {
		klog.Exitln("failed to get self network namespace:", err)
	}
	defer selfNetNs.Close()

	// Create event channel for function tracing events
	events := make(chan ebpftracer.Event, 10000)

	// Initialize eBPF tracer for function tracing
	// Disable L7 tracing since we focus on function tracing
	tracer := ebpftracer.NewTracer(hostNetNs, selfNetNs, true)
	defer tracer.Close()

	// Initialize output formatter based on flags
	var formatter output.Formatter
	switch *flags.OutputFormat {
	case "human":
		formatter = output.NewHumanFormatter()
	case "json":
		formatter = output.NewJSONFormatter()
	case "server":
		agentID := *flags.AgentID
		if agentID == "" {
			agentID = fmt.Sprintf("agent-%s", hostname)
		}
		formatter = output.NewServerFormatter(*flags.ServerEndpoint, agentID)
	case "otlp":
		formatter = output.NewOTLPFormatter(*flags.OTLPEndpoint)
	default:
		formatter = output.NewHumanFormatter() // Default to human-readable
	}

	// Start output processor
	go processEvents(events, formatter)

	// Start eBPF tracer
	klog.Infoln("🚀 UNIVERSAL EBPF FUNCTION TRACER STARTING...")
	klog.Infoln("⚡ Loading eBPF programs and initializing kernel interfaces...")
	if err := tracer.Run(events); err != nil {
		klog.Exitln("❌ FATAL ERROR: Failed to start eBPF tracer:", err)
	}

	// Attach function probes based on target configuration
	if *flags.TraceAllProcesses {
		klog.Infoln("🌍 SYSTEM-WIDE TRACING MODE: Monitoring all processes...")
		if err := tracer.AttachSystemWide(); err != nil {
			klog.Exitln("❌ FATAL ERROR: Failed to start system-wide tracing:", err)
		}
	} else {
		// Targeted tracing
		if *flags.TargetPID != 0 {
			klog.Infof("attaching to PID: %d", *flags.TargetPID)
			if err := tracer.AttachToProcess(uint32(*flags.TargetPID)); err != nil {
				klog.Exitln("failed to attach to process:", err)
			}
		}

		if *flags.TargetBinary != "" {
			klog.Infof("attaching to binary: %s", *flags.TargetBinary)
			if err := tracer.AttachToBinary(*flags.TargetBinary); err != nil {
				klog.Exitln("failed to attach to binary:", err)
			}
		}
	}

	// Start HTTP server for metrics and debugging
	if *flags.ListenAddress != "" {
		http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("OK"))
		})
		http.HandleFunc("/stats", func(w http.ResponseWriter, r *http.Request) {
			// TODO: Add tracer statistics
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("Stats endpoint"))
		})
		go func() {
			klog.Infoln("listening on:", *flags.ListenAddress)
			klog.Errorln(http.ListenAndServe(*flags.ListenAddress, nil))
		}()
	}

	// Keep the main goroutine alive
	select {}
}

func processEvents(events <-chan ebpftracer.Event, formatter output.Formatter) {
	for event := range events {
		// Filter events based on configuration
		if !shouldProcessEvent(event) {
			continue
		}

		// Format and output the event
		if err := formatter.FormatEvent(event); err != nil {
			klog.Warningln("failed to format event:", err)
		}
	}
}

func shouldProcessEvent(event ebpftracer.Event) bool {
	// Apply sampling if configured
	if *flags.SampleRate > 1 {
		// Simple sampling based on PID
		if event.Pid%uint32(*flags.SampleRate) != 0 {
			return false
		}
	}

	// Apply PID filtering if configured
	if *flags.TargetPID != 0 && event.Pid != uint32(*flags.TargetPID) {
		return false
	}

	// Apply function filtering if configured
	if len(*flags.FunctionPatterns) > 0 {
		// TODO: Implement function pattern matching
	}

	return true
}

type RateLimitedLogOutput struct {
	limiter *rate.Limiter
}

func (o *RateLimitedLogOutput) Write(data []byte) (int, error) {
	if !o.limiter.Allow() {
		return len(data), nil
	}
	return os.Stderr.Write(data)
}
