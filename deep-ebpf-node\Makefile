# Deep-eBPF Node Agent Makefile
# Based on coroot-node-agent build system

BINARY := deep-ebpf-node
GO := go
DOCKER := docker

# Version information
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "unknown")
COMMIT ?= $(shell git rev-parse HEAD 2>/dev/null || echo "unknown")
DATE ?= $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Build flags
LDFLAGS := -X github.com/mexyusef/deep-ebpf-node/flags.Version=$(VERSION)
BUILD_FLAGS := -ldflags "$(LDFLAGS)"

.PHONY: all ebpf agent clean test check-deps help

# Default target
all: ebpf agent

# Build eBPF programs using Docker (following coroot-node-agent pattern)
ebpf:
	@echo "Building eBPF programs..."
	cd ebpftracer/ebpf && \
	$(DOCKER) build -t deep-ebpf-builder . && \
	$(DOCKER) run --rm -v $(PWD)/ebpftracer/ebpf:/src deep-ebpf-builder
	@echo "eBPF programs built successfully"

# Build Go agent
agent:
	@echo "Building Go agent..."
	$(GO) mod tidy
	$(GO) build $(BUILD_FLAGS) -o $(BINARY) ./main.go
	@echo "Agent built successfully: $(BINARY)"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(BINARY)
	rm -f ebpftracer/ebpf/ebpf.o
	$(GO) clean

# Run tests
test:
	@echo "Running tests..."
	$(GO) test ./...

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@which $(GO) > /dev/null || (echo "Go is not installed" && exit 1)
	@which $(DOCKER) > /dev/null || (echo "Docker is not installed" && exit 1)
	@echo "Dependencies OK"

# Install dependencies
deps:
	@echo "Installing Go dependencies..."
	$(GO) mod download

# Format code
fmt:
	@echo "Formatting code..."
	$(GO) fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run || echo "golangci-lint not installed, skipping"

# Run the agent (requires root)
run: agent
	@echo "Running deep-ebpf-node (requires root privileges)..."
	sudo ./$(BINARY) --help

# Run with target PID
run-pid: agent
	@echo "Usage: make run-pid PID=<process_id>"
	@if [ -z "$(PID)" ]; then echo "Please specify PID: make run-pid PID=1234"; exit 1; fi
	sudo ./$(BINARY) --target-pid $(PID) --format human

# Run with target binary
run-binary: agent
	@echo "Usage: make run-binary BINARY=<binary_path>"
	@if [ -z "$(BINARY_PATH)" ]; then echo "Please specify BINARY_PATH: make run-binary BINARY_PATH=/usr/bin/myapp"; exit 1; fi
	sudo ./$(BINARY) --target-binary $(BINARY_PATH) --format human

# Development mode with debug logging
dev: agent
	@echo "Running in development mode..."
	sudo ./$(BINARY) --debug --log-level debug

# Build test programs for validation
test-programs:
	@echo "Building test programs..."
	@echo "TODO: Add test program compilation"

# Install the binary
install: agent
	@echo "Installing $(BINARY)..."
	sudo cp $(BINARY) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(BINARY)

# Uninstall the binary
uninstall:
	@echo "Uninstalling $(BINARY)..."
	sudo rm -f /usr/local/bin/$(BINARY)

# Check system requirements
check-system:
	@echo "Checking system requirements..."
	@echo "Kernel version: $(shell uname -r)"
	@uname -a
	@echo "Checking eBPF support..."
	@if [ -d /sys/fs/bpf ]; then echo "✓ BPF filesystem mounted"; else echo "✗ BPF filesystem not mounted"; fi
	@if [ -f /sys/kernel/btf/vmlinux ]; then echo "✓ BTF support available"; else echo "✗ BTF support not available"; fi
	@echo "Checking required tools..."
	@which clang > /dev/null && echo "✓ clang found" || echo "✗ clang not found"
	@which $(GO) > /dev/null && echo "✓ go found" || echo "✗ go not found"
	@which $(DOCKER) > /dev/null && echo "✓ docker found" || echo "✗ docker not found"

# Generate documentation
docs:
	@echo "Generating documentation..."
	$(GO) doc -all ./... > docs/API.md

# Create release
release: clean all
	@echo "Creating release $(VERSION)..."
	mkdir -p release
	cp $(BINARY) release/
	cp README.md release/
	tar -czf release/deep-ebpf-node-$(VERSION).tar.gz -C release .
	@echo "Release created: release/deep-ebpf-node-$(VERSION).tar.gz"

# Docker build
docker:
	@echo "Building Docker image..."
	$(DOCKER) build -t deep-ebpf-node:$(VERSION) .
	$(DOCKER) tag deep-ebpf-node:$(VERSION) deep-ebpf-node:latest

# Show help
help:
	@echo "Deep-eBPF Node Agent - Available targets:"
	@echo ""
	@echo "  all           - Build eBPF programs and Go agent (default)"
	@echo "  ebpf          - Build eBPF programs only"
	@echo "  agent         - Build Go agent only"
	@echo "  clean         - Clean build artifacts"
	@echo "  test          - Run tests"
	@echo "  check-deps    - Check dependencies"
	@echo "  deps          - Install Go dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  run           - Run the agent (shows help)"
	@echo "  run-pid       - Run with target PID (make run-pid PID=1234)"
	@echo "  run-binary    - Run with target binary (make run-binary BINARY_PATH=/usr/bin/myapp)"
	@echo "  dev           - Run in development mode"
	@echo "  test-programs - Build test programs"
	@echo "  install       - Install binary to /usr/local/bin"
	@echo "  uninstall     - Remove installed binary"
	@echo "  check-system  - Check system requirements"
	@echo "  docs          - Generate documentation"
	@echo "  release       - Create release package"
	@echo "  docker        - Build Docker image"
	@echo "  help          - Show this help message"
	@echo ""
	@echo "Usage examples:"
	@echo "  1. make check-system    # Verify system requirements"
	@echo "  2. make check-deps      # Check dependencies"
	@echo "  3. make all             # Build everything"
	@echo "  4. sudo make run-pid PID=1234  # Trace specific process"
	@echo ""
	@echo "For more information, see README.md"
