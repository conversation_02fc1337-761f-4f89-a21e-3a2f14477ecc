package flags

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/alecthomas/kingpin.v2"
	"k8s.io/klog/v2"
)

var (
	// Core function tracing flags
	TargetPID      = kingpin.Flag("target-pid", "Target process ID to trace").Default("0").Envar("TARGET_PID").Int()
	TargetBinary   = kingpin.Flag("target-binary", "Target binary path to trace").Default("").Envar("TARGET_BINARY").String()
	TargetFunction = kingpin.Flag("target-function", "Target function name to trace").Default("").Envar("TARGET_FUNCTION").String()
	TraceAllProcesses = kingpin.Flag("trace-all", "Trace all processes system-wide (requires no target-pid or target-binary)").Default("false").Envar("TRACE_ALL").Bool()
	ExcludePids    = kingpin.Flag("exclude-pids", "PIDs to exclude from system-wide tracing (comma-separated)").Default("").Envar("EXCLUDE_PIDS").String()
	IncludeKernel  = kingpin.Flag("include-kernel", "Include kernel processes in system-wide tracing").Default("false").Envar("INCLUDE_KERNEL").Bool()

	// Output configuration
	OutputFormat     = kingpin.Flag("format", "Output format: human, json, server, otlp").Default("human").Envar("OUTPUT_FORMAT").String()
	OutputFile       = kingpin.Flag("output", "Output file path (default: stdout)").Default("").Envar("OUTPUT_FILE").String()
	ServerEndpoint   = kingpin.Flag("server-endpoint", "Deep-eBPF server endpoint").Default("http://localhost:8080").Envar("SERVER_ENDPOINT").String()
	AgentID          = kingpin.Flag("agent-id", "Agent ID for server communication").Default("").Envar("AGENT_ID").String()
	OTLPEndpoint     = kingpin.Flag("otlp-endpoint", "OTLP endpoint for distributed tracing").Default("").Envar("OTLP_ENDPOINT").String()

	// Tracing configuration
	EnableStackTraces = kingpin.Flag("enable-stack-traces", "Enable call stack collection").Default("true").Envar("ENABLE_STACK_TRACES").Bool()
	EnableArguments   = kingpin.Flag("enable-arguments", "Enable function argument capture").Default("true").Envar("ENABLE_ARGUMENTS").Bool()
	MaxStackDepth     = kingpin.Flag("max-stack-depth", "Maximum stack trace depth").Default("64").Envar("MAX_STACK_DEPTH").Int()

	// Filtering configuration
	FunctionPatterns = kingpin.Flag("filter-functions", "Function name patterns to trace (glob patterns)").Envar("FILTER_FUNCTIONS").Strings()
	BinaryPatterns   = kingpin.Flag("filter-binaries", "Binary path patterns to trace (glob patterns)").Envar("FILTER_BINARIES").Strings()
	PIDWhitelist     = kingpin.Flag("pid-whitelist", "List of PIDs to trace (comma-separated)").Envar("PID_WHITELIST").Ints()

	// Sampling configuration
	SampleRate        = kingpin.Flag("sample-rate", "Sampling rate (1 = no sampling, 100 = sample every 100th event)").Default("1").Envar("SAMPLE_RATE").Int()
	MaxEventsPerSec   = kingpin.Flag("max-events-per-sec", "Maximum events per second (0 = no limit)").Default("0").Envar("MAX_EVENTS_PER_SEC").Int()
	AdaptiveSampling  = kingpin.Flag("adaptive-sampling", "Enable adaptive sampling based on load").Default("false").Envar("ADAPTIVE_SAMPLING").Bool()

	// Language-specific configuration
	Languages = kingpin.Flag("languages", "Target programming languages").Default("go", "c", "python", "java").Envar("LANGUAGES").Strings()

	// Performance configuration
	RingBufferSize      = kingpin.Flag("ring-buffer-size", "eBPF ring buffer size in bytes").Default("16777216").Envar("RING_BUFFER_SIZE").Int() // 16MB
	MaxConcurrentEvents = kingpin.Flag("max-concurrent-events", "Maximum concurrent event processing").Default("1000").Envar("MAX_CONCURRENT_EVENTS").Int()
	EventTimeout        = kingpin.Flag("event-timeout", "Event processing timeout").Default("30s").Envar("EVENT_TIMEOUT").Duration()

	// Network and server configuration
	ListenAddress = kingpin.Flag("listen", "Listen address for HTTP server (metrics, health checks)").Default("").Envar("LISTEN").String()

	// Logging configuration
	LogPerSecond = kingpin.Flag("log-per-second", "The number of logs per second").Default("10.0").Envar("LOG_PER_SECOND").Float64()
	LogBurst     = kingpin.Flag("log-burst", "The maximum number of tokens that can be consumed in a single call to allow").Default("100").Envar("LOG_BURST").Int()
	LogLevel     = kingpin.Flag("log-level", "Log level: debug, info, warn, error").Default("info").Envar("LOG_LEVEL").String()

	// Development and debugging flags
	DebugMode    = kingpin.Flag("debug", "Enable debug mode").Default("false").Envar("DEBUG").Bool()
	TestMode     = kingpin.Flag("test-mode", "Enable test mode").Default("false").Envar("TEST_MODE").Bool()
	DryRun       = kingpin.Flag("dry-run", "Dry run mode (don't attach probes)").Default("false").Envar("DRY_RUN").Bool()

	// Symbol resolution configuration
	EnableSymbolResolution = kingpin.Flag("enable-symbol-resolution", "Enable symbol resolution for better function names").Default("true").Envar("ENABLE_SYMBOL_RESOLUTION").Bool()
	SymbolCacheSize        = kingpin.Flag("symbol-cache-size", "Symbol cache size").Default("10000").Envar("SYMBOL_CACHE_SIZE").Int()
	PerfMapDirs            = kingpin.Flag("perf-map-dirs", "Directories to search for perf map files").Default("/tmp", "/var/tmp").Envar("PERF_MAP_DIRS").Strings()

	// Export configuration
	ExportInterval = kingpin.Flag("export-interval", "Export interval for batched exports").Default("5s").Envar("EXPORT_INTERVAL").Duration()
	BufferSize     = kingpin.Flag("buffer-size", "Export buffer size").Default("1000").Envar("BUFFER_SIZE").Int()

	// Security and permissions
	AllowUnsafeOperations = kingpin.Flag("allow-unsafe", "Allow potentially unsafe operations").Default("false").Envar("ALLOW_UNSAFE").Bool()

	// Version flag
	agentVersion = kingpin.Flag("version", "Print version and exit").Default("false").Bool()
	Version      = "unknown"
)

func GetString(fl *string) string {
	if fl == nil {
		return ""
	}
	return *fl
}

func GetInt(fl *int) int {
	if fl == nil {
		return 0
	}
	return *fl
}

func GetBool(fl *bool) bool {
	if fl == nil {
		return false
	}
	return *fl
}

func ValidateFlags() error {
	// Validate target configuration
	if *TraceAllProcesses {
		// System-wide tracing mode
		if *TargetPID != 0 || *TargetBinary != "" {
			return fmt.Errorf("cannot specify target options with --trace-all (system-wide tracing)")
		}
		klog.Infof("System-wide tracing enabled - monitoring all processes")
	} else {
		// Targeted tracing mode
		if *TargetPID == 0 && *TargetBinary == "" {
			return fmt.Errorf("either --target-pid, --target-binary, or --trace-all must be specified")
		}
	}

	if *TargetPID < 0 {
		return fmt.Errorf("target-pid must be non-negative")
	}

	// Validate output format
	validFormats := map[string]bool{
		"human": true,
		"json":  true,
		"server": true,
		"otlp":  true,
	}
	if !validFormats[*OutputFormat] {
		return fmt.Errorf("invalid output format: %s (valid: human, json, server, otlp)", *OutputFormat)
	}

	// Validate OTLP configuration
	if *OutputFormat == "otlp" && *OTLPEndpoint == "" {
		return fmt.Errorf("otlp-endpoint is required when using OTLP output format")
	}

	// Validate sampling configuration
	if *SampleRate <= 0 {
		return fmt.Errorf("sample-rate must be positive")
	}

	if *MaxEventsPerSec < 0 {
		return fmt.Errorf("max-events-per-sec must be non-negative")
	}

	// Validate performance configuration
	if *RingBufferSize <= 0 {
		return fmt.Errorf("ring-buffer-size must be positive")
	}

	if *MaxConcurrentEvents <= 0 {
		return fmt.Errorf("max-concurrent-events must be positive")
	}

	if *MaxStackDepth <= 0 || *MaxStackDepth > 256 {
		return fmt.Errorf("max-stack-depth must be between 1 and 256")
	}

	// Validate symbol resolution configuration
	if *SymbolCacheSize <= 0 {
		return fmt.Errorf("symbol-cache-size must be positive")
	}

	return nil
}

func PrintConfiguration() {
	fmt.Printf("Deep-eBPF Node Configuration:\n")
	fmt.Printf("  Target PID: %d\n", *TargetPID)
	fmt.Printf("  Target Binary: %s\n", *TargetBinary)
	fmt.Printf("  Target Function: %s\n", *TargetFunction)
	fmt.Printf("  Output Format: %s\n", *OutputFormat)
	fmt.Printf("  Output File: %s\n", *OutputFile)
	fmt.Printf("  Sample Rate: %d\n", *SampleRate)
	fmt.Printf("  Enable Stack Traces: %t\n", *EnableStackTraces)
	fmt.Printf("  Enable Arguments: %t\n", *EnableArguments)
	fmt.Printf("  Max Stack Depth: %d\n", *MaxStackDepth)
	fmt.Printf("  Languages: %v\n", *Languages)
	fmt.Printf("  Debug Mode: %t\n", *DebugMode)
}

func init() {
	if strings.HasSuffix(os.Args[0], ".test") {
		return
	}

	kingpin.HelpFlag.Short('h').Hidden()
	kingpin.Parse()

	if *agentVersion {
		fmt.Println("Deep-eBPF Node Version:", Version)
		os.Exit(0)
	}

	// Validate configuration
	if err := ValidateFlags(); err != nil {
		fmt.Fprintf(os.Stderr, "Configuration error: %v\n", err)
		os.Exit(1)
	}

	// Print configuration in debug mode
	if *DebugMode {
		PrintConfiguration()
	}
}
