// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

const ButtonGroup = ({
  children,
  ...props
}: {children: React.ReactNode} & JSX.IntrinsicElements['div']): JSX.Element => {
  return (
    <div className="flex flex-wrap items-baseline justify-center" {...props}>
      <div className="flex space-x-1">{children}</div>
    </div>
  );
};

export default ButtonGroup;
