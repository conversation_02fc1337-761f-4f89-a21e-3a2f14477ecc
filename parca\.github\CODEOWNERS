# These owners will be the default owners for everything in
# the repo. Unless a later match takes precedence,
# @global-owner1 and @global-owner2 will be requested for
# review when someone opens a pull request.
*       @parca-dev/maintainers

# Order is important; the last matching pattern takes the most
# precedence. When someone opens a pull request that only
# modifies JS files, only @js-owner and not the global
# owner(s) will be requested for a review.
*.js    @parca-dev/frontend-maintainers
*.ts    @parca-dev/frontend-maintainers
/ui     @parca-dev/frontend-maintainers

*.go @parca-dev/backend-maintainers

/pkg/debuginfo     @parca-dev/agent-maintainers
/pkg/symbol        @parca-dev/agent-maintainers
/pkg/symbolizer    @parca-dev/agent-maintainers

package.json
pnpm-lock.yaml
/go.mod
/go.sum
Dockerfile*
.github/workflows/*
