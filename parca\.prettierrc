{"printWidth": 100, "singleQuote": true, "bracketSpacing": false, "arrowParens": "avoid", "importOrder": ["^(react/(.*)$)|^(react$)", "<THIRD_PARTY_MODULES>", "", "^@parca/(.*)$", "", "^[./]"], "trailingComma": "es5", "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderMergeDuplicateImports": true, "importOrderBuiltinModulesToTop": true, "importOrderCombineTypeAndValueImports": true, "plugins": ["@ianvs/prettier-plugin-sort-imports"]}