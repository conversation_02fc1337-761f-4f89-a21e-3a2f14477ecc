// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import React from 'react';

import copyToClipboard from 'copy-to-clipboard';

interface Props {
  text: string;
  onCopy?: () => void;
  children: React.ReactElement;
}

export const CopyToClipboard = ({
  text,
  onCopy = () => {},
  children,
  ...props
}: Props): JSX.Element => {
  const handleCopy = (): void => {
    copyToClipboard(text);
    onCopy();
  };

  const elem = React.Children.only(children);

  return React.cloneElement(elem, {...props, onClick: handleCopy});
};
