{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["github>parca-dev/.github"], "schedule": ["after 8pm every weekday", "before 4am every weekday", "every weekend"], "updateNotScheduled": false, "packageRules": [{"description": "One week stability period for Buf packages", "matchFileNames": ["buf.gen.yaml"], "minimumReleaseAge": "7 days"}, {"description": "Group buf packages", "matchPackageNames": ["bufbuild/buf", "bufbuild/buf-setup-action"], "minimumReleaseAge": "7 days", "groupName": "buf"}, {"description": "Group grafana packages", "matchSourceUrls": ["https://github.com/grafana/grafana"], "groupName": "grafana"}, {"description": "Group grpc-gateway packages", "matchSourceUrls": ["https://github.com/grpc-ecosystem/grpc-gateway"], "groupName": "grpc-gateway"}, {"description": "Group grpc-health-probe packages", "matchSourceUrls": ["https://github.com/grpc-ecosystem/grpc-health-probe"], "groupName": "grpc-health-probe"}, {"description": "Group protobuf-ts packages", "matchSourceUrls": ["https://github.com/timostamm/protobuf-ts"], "groupName": "protobuf-ts"}, {"description": "Group prettier packages", "matchPackageNames": ["pre-commit/mirrors-prettier", "prettier"], "groupName": "prettier"}, {"description": "Group protobuf-go packages", "matchSourceUrls": ["https://github.com/protocolbuffers/protobuf-go"], "groupName": "protobuf-go"}, {"description": "Group vtprotobuf packages", "matchSourceUrls": ["https://github.com/planetscale/vtprotobuf"], "groupName": "vtprotobuf"}, {"description": "Disable tailwindcss upgrades", "matchPackageNames": ["tailwindcss", "prettier-plugin-tailwindcss"], "enabled": false}, {"description": "Disable eslint upgrades", "matchPackageNames": ["eslint"], "enabled": false}], "customManagers": [{"customType": "regex", "description": "Update Buf plugins", "managerFilePatterns": ["/(^|/)buf\\.gen\\.yaml$/"], "matchStrings": ["# renovate: datasource=(?<datasource>.+?) depName=(?<depName>.+?)(?: (?:packageName)=(?<packageName>.+?))?(?: versioning=(?<versioning>.+?))?\\s*-?\\s*plugin: ('|\")?.*:(?<currentValue>.+?)('|\")?\\s"]}]}