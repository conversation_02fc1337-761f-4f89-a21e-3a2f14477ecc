---
sidebar_position: 3
---

# Role-Based Access Control

Coroot's Role-Based Access Control (RBAC) feature allows teams to manage user permissions by assigning roles with specific access levels. 
This ensures that users only have access to the data and features relevant to their role, enhancing security and simplifying access management. 
RBAC helps maintain control over sensitive information while allowing seamless collaboration across the team.

Coroot's Community Edition includes three predefined roles: `Admin`, `Editor`, and `Viewer`, each with specific access levels. 
Admins have full control, Editors can make changes, and Viewers have read-only access. 
The permission sets for these roles are fixed and cannot be customized:


<img alt="Role-Based Access Control in Coroot Community" src="/img/docs/rbac_ce.png" class="card w-1200"/>

## Granular Role-Based Access Control (RBAC)

:::info
Granular Role-Based Access Control (RBAC) is available only in Coroot Enterprise (from $1 per CPU core/month). [Start](https://coroot.com/account) your free trial today.
:::

To manage Coroot roles, go to the **Project Settings**, click on **Organization**:

<img alt="Role-Based Access Control in Coroot Enterprise" src="/img/docs/rbac_ee.png" class="card w-1200"/>

To add a new role, click "Add role" and configure the permission policies.

For instance, you can grant full access to a project, or restrict access to viewing only specific applications within 
a particular category or even limit it to a specific application, providing fine-grained control over user permissions.

<img alt="Roles Customization in Coroot Enterprise" src="/img/docs/rbac_ee_custom_role.png" class="card w-800"/>
