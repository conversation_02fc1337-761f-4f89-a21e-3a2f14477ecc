{"coredump-ref": "9ac82f530b4977351b81b92b732e59e29509b304496de35bc5d86aac82c2135a", "threads": [{"lwp": 10811, "frames": ["parser.so+0x3489", "parser.so+0x3993", "parser.so+0x3f9b", "parser.so+0x465b", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1ba305", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x1c4e88", "libruby-2.5.so.2.5.5+0xe19bd", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1b9a59", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x1bfb28", "libruby-2.5.so.2.5.5+0xe262f", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1c238a", "libruby-2.5.so.2.5.5+0x1b9a59", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x1c0f0c", "libruby-2.5.so.2.5.5+0x1c1009", "libruby-2.5.so.2.5.5+0x1c1353", "libruby-2.5.so.2.5.5+0x1ba305", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x1c4e88", "libruby-2.5.so.2.5.5+0x2f4cb", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1b9a59", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x1c4e88", "libruby-2.5.so.2.5.5+0x2f4cb", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1c238a", "libruby-2.5.so.2.5.5+0x1b9a59", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x1c4e88", "libruby-2.5.so.2.5.5+0x2f4cb", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1c238a", "libruby-2.5.so.2.5.5+0x1b9a59", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0xd0a40", "libruby-2.5.so.2.5.5+0xd0ff7", "libruby-2.5.so.2.5.5+0xd110f", "libruby-2.5.so.2.5.5+0x1b47a0", "libruby-2.5.so.2.5.5+0x1c238a", "libruby-2.5.so.2.5.5+0x1ba305", "libruby-2.5.so.2.5.5+0x1bdef3", "libruby-2.5.so.2.5.5+0x94f13", "parse+0 in /usr/lib/ruby/vendor_ruby/json/common.rb:156", "run+0 in /tmp/puppet/benchmarks/serialization/benchmarker.rb:48", "run+0 in /tmp/puppet/benchmarks/serialization/benchmarker.rb:28", "generate_scenario_tasks+0 in /tmp/puppet/tasks/benchmark.rake:47", "measure+0 in /usr/lib/ruby/2.5.0/benchmark.rb:293", "item+0 in /usr/lib/ruby/2.5.0/benchmark.rb:375", "generate_scenario_tasks+0 in /tmp/puppet/tasks/benchmark.rake:46", "generate_scenario_tasks+0 in /tmp/puppet/tasks/benchmark.rake:44", "benchmark+0 in /usr/lib/ruby/2.5.0/benchmark.rb:173", "generate_scenario_tasks+0 in /tmp/puppet/tasks/benchmark.rake:42", "execute+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:281", "execute+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:281", "invoke_with_call_chain+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:219", "mon_synchronize+0 in /usr/lib/ruby/2.5.0/monitor.rb:226", "invoke_with_call_chain+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:199", "invoke_prerequisites+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:243", "invoke_prerequisites+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:241", "invoke_with_call_chain+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:218", "mon_synchronize+0 in /usr/lib/ruby/2.5.0/monitor.rb:226", "invoke_with_call_chain+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:199", "invoke+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/task.rb:188", "invoke_task+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:160", "top_level+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:116", "top_level+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:116", "run_with_threads+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:125", "top_level+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:110", "run+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:83", "standard_exception_handling+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:186", "run+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/lib/rake/application.rb:80", "<top (required)>+0 in /var/lib/gems/2.5.0/gems/rake-13.0.6/exe/rake:27", "<main>+0 in /usr/local/bin/rake:23", "libruby-2.5.so.2.5.5+0x96dbc", "libruby-2.5.so.2.5.5+0x9926d", "ruby2.5+0x10ea", "libc-2.28.so+0x2409a", "ruby2.5+0x1119"]}, {"lwp": 10814, "frames": ["libc-2.28.so+0xee819", "libruby-2.5.so.2.5.5+0x190133", "libpthread-2.28.so+0x7fa2", "libc-2.28.so+0xf94ce"]}], "modules": null}