{"coredump-ref": "3836a6042df0507db4a8a85d78e00a555a34b05dfc4c3451f1a58ee41ce3353e", "threads": [{"lwp": 10171, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x53882", "libjli.so+0x7ddd", "libjli.so+0x49af", "libjli.so+0x5bb4", "java+0x10a0", "ld-musl-x86_64.so.1+0x1ca02", "java+0x10bb"]}, {"lwp": 10172, "frames": ["ld-musl-x86_64.so.1+0x55352", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x58ac2", "libjava.so+0x197ef", "libjava.so+0x19280", "libjava.so+0xf695", "void java.io.FileOutputStream.writeBytes(byte[], int, int, boolean)+0 in FileOutputStream.java:0", "void java.io.FileOutputStream.write(byte[], int, int)+0 in FileOutputStream.java:326", "void java.io.BufferedOutputStream.flushBuffer()+1 in BufferedOutputStream.java:82", "void java.io.BufferedOutputStream.flush()+0 in BufferedOutputStream.java:140", "void java.io.PrintStream.write(byte[], int, int)+4 in PrintStream.java:482", "void sun.nio.cs.StreamEncoder.writeBytes()+11 in StreamEncoder.java:221", "void sun.nio.cs.StreamEncoder.implFlushBuffer()+1 in StreamEncoder.java:291", "void sun.nio.cs.StreamEncoder.flushBuffer()+2 in StreamEncoder.java:104", "void java.io.OutputStreamWriter.flushBuffer()+0 in OutputStreamWriter.java:185", "void java.io.PrintStream.newLine()+4 in PrintStream.java:546", "void java.io.PrintStream.println(java.lang.String)+2 in PrintStream.java:807", "void HelloWorld.main(java.lang.String[])+0 in HelloWorld.java:4", "StubRoutines (1)+0 in :0", "libjvm.so+0x42c7e0", "libjvm.so+0x45bddf", "libjvm.so+0x45c0a5", "libjli.so+0x333f", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10186, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a7df", "libjvm.so+0x548411", "libjvm.so+0x5487df", "libjvm.so+0x33870d", "libjvm.so+0x33a7b9", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10174, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10190, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a7df", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x60827e", "libjvm.so+0x608335", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10173, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10188, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a7df", "libjvm.so+0x548411", "libjvm.so+0x5487df", "libjvm.so+0x33870d", "libjvm.so+0x33a7b9", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10179, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10177, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10184, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x54ea5", "libjvm.so+0x565836", "libjvm.so+0x561496", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10180, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10176, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10182, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x55bb63", "libjvm.so+0x472582", "void java.lang.Object.wait(long)+0 in Object.java:0", "void java.lang.Object.wait()+0 in Object.java:502", "boolean java.lang.ref.Reference.tryHandlePending(boolean)+13 in Reference.java:191", "void java.lang.ref.Reference$ReferenceHandler.run()+0 in Reference.java:153", "StubRoutines (1)+0 in :0", "libjvm.so+0x42c7e0", "libjvm.so+0x42bcdd", "libjvm.so+0x42bd62", "libjvm.so+0x4700f8", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10175, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10178, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x3d8694", "libjvm.so+0x3d9720", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10187, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a7df", "libjvm.so+0x548411", "libjvm.so+0x5487df", "libjvm.so+0x33870d", "libjvm.so+0x33a7b9", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10189, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x547f40", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x5b58ab", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10183, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a5fa", "libjvm.so+0x55bb63", "libjvm.so+0x472582", "void java.lang.Object.wait(long)+0 in Object.java:0", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove(long)+8 in ReferenceQueue.java:144", "java.lang.ref.Reference java.lang.ref.ReferenceQueue.remove()+0 in ReferenceQueue.java:165", "void java.lang.ref.Finalizer$FinalizerThread.run()+17 in Finalizer.java:216", "StubRoutines (1)+0 in :0", "libjvm.so+0x42c7e0", "libjvm.so+0x42bcdd", "libjvm.so+0x42bd62", "libjvm.so+0x4700f8", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10185, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a7df", "libjvm.so+0x548411", "libjvm.so+0x5487df", "libjvm.so+0x33870d", "libjvm.so+0x33a7b9", "libjvm.so+0x60cff7", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}, {"lwp": 10181, "frames": ["ld-musl-x86_64.so.1+0x55354", "ld-musl-x86_64.so.1+0x5266d", "ld-musl-x86_64.so.1+0x51c6a", "ld-musl-x86_64.so.1+0x52a77", "libjvm.so+0x56a7df", "libjvm.so+0x548411", "libjvm.so+0x548789", "libjvm.so+0x635187", "libjvm.so+0x635512", "libjvm.so+0x567e30", "ld-musl-x86_64.so.1+0x53161", "ld-musl-x86_64.so.1+0x55320"]}], "modules": null}