// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import TextWithTooltip from '..';

export default {
  component: TextWithTooltip,
  title: 'Components/TextWithTooltip',
};

export const Default = {
  args: {
    text: "agent_revision='6cf956bb0a0dbebba64ba00abbff37e25a5e8234'",
    maxTextLength: 20,
    id: 'test-id',
  },
};
