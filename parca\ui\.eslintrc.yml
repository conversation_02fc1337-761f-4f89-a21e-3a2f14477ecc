# Parse all files with TypeScript.
extends:
  - 'standard-with-typescript'
  - 'prettier'
  - 'plugin:typescript-enum/recommended'
  - 'eslint:recommended'
  - 'plugin:@typescript-eslint/recommended'
  - 'plugin:react/recommended'
  - 'plugin:jest/recommended'
  - 'plugin:jest/style'
  - 'plugin:jest-dom/recommended'
  - 'plugin:react/jsx-runtime'
  - 'plugin:import/recommended'
  - 'plugin:import/typescript'
  - 'plugin:promise/recommended'
  - 'plugin:react-hooks/recommended'
  - 'plugin:storybook/recommended'

parser: '@typescript-eslint/parser'

parserOptions:
  project: './tsconfig.json'
  createDefaultProgram: true

plugins:
  - prettier
  - react
  - jest
  - jest-dom
  - '@typescript-eslint'
  - react-hooks
  - typescript-enum

settings:
  import/resolver:
    node:
      extensions:
        - .ts
        - .tsx
        - .js
        - .jsx
        - .mdx
  react:
    version: detect

env:
  browser: true

rules:
  react-hooks/rules-of-hooks: error
  react-hooks/exhaustive-deps: error

  no-unused-vars: 0
  '@typescript-eslint/no-unused-vars':
    - error
    - varsIgnorePattern: _
      argsIgnorePattern: _

  no-labels: [error, {allowLoop: true}]
  no-void: 0
  '@typescript-eslint/member-delimiter-style':
    - error
    - singleline:
        delimiter: 'semi'
        requireLast: false

  ban-ts-comment: 0
  '@typescript-eslint/ban-ts-comment': 0

  '@typescript-eslint/no-empty-function': 0

  import/named: 2
  import/namespace: 2
  import/default: 2
  import/export: 2

  promise/catch-or-return: [error, {allowFinally: true}]
