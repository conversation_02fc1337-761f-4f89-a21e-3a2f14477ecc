// Copyright 2024 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

package luajit

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestParseVarinfo(t *testing.T) {
	// Lifted from bcline function in bc.lua:
	// https://github.com/openresty/luajit2/blob/098183d/src/jit/bc.lua#L65
	b := []byte{0x66, 0x75, 0x6e, 0x63, 0x0, 0x0, 0xdc, 0x1, 0x70, 0x63, 0x0, 0x0, 0xdc, 0x1, 0x70,
		0x72, 0x65, 0x66, 0x69, 0x78, 0x0, 0x0, 0xdc, 0x1, 0x6c, 0x69, 0x6e, 0x65, 0x69, 0x6e,
		0x66, 0x6f, 0x0, 0x0, 0xdc, 0x1, 0x69, 0x6e, 0x73, 0x0, 0xa, 0xd2, 0x1, 0x6d, 0x0, 0x0,
		0xd2, 0x1, 0x6c, 0x0, 0x0, 0xd2, 0x1, 0x6d, 0x61, 0x0, 0xf, 0xc3, 0x1, 0x6d, 0x62, 0x0,
		0x0, 0xc3, 0x1, 0x6d, 0x63, 0x0, 0x0, 0xc3, 0x1, 0x61, 0x0, 0x7, 0xbc, 0x1, 0x6f, 0x69,
		0x64, 0x78, 0x0, 0x5, 0xb7, 0x1, 0x6f, 0x70, 0x0, 0x5, 0xb2, 0x1, 0x73, 0x0, 0x1, 0xb1,
		0x1, 0x64, 0x0, 0x27, 0x8a, 0x1, 0x6b, 0x63, 0x0, 0x17, 0x73, 0x66, 0x69, 0x0, 0x2c, 0x9,
		0x6b, 0x61, 0x0, 0x17, 0x8, 0x62, 0x0, 0xe, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x79,
		0xf5, 0x0}
	for _, tc := range []struct {
		name          string
		startpc, endp uint32
		slot          uint32
	}{
		{"func", 0, 220, 0},
		{"pc", 0, 220, 1},
		{"prefix", 0, 220, 2},
		{"lineinfo", 0, 220, 3},
		{"ins", 10, 220, 4},
		{"m", 10, 220, 5},
		{"l", 10, 220, 6},
		{"ma", 25, 220, 7},
		{"mb", 25, 220, 8},
		{"mc", 25, 220, 9},
		{"a", 32, 220, 10},
		{"oidx", 37, 220, 11},
		{"op", 42, 220, 12},
		{"s", 43, 220, 13},
		{"d", 82, 220, 14},
		{"kc", 105, 220, 15},
		{"fi", 149, 158, 16},
		{"ka", 172, 180, 16},
		{"b", 186, 201, 16},
	} {
		s := parseVarinfo(b, tc.startpc, tc.slot)
		require.Equal(t, s, tc.name)
	}
}
