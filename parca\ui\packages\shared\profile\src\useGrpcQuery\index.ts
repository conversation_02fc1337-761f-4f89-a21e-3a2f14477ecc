// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useQuery, type UseQueryResult} from '@tanstack/react-query';

interface Props<IRes> {
  key: unknown[];
  queryFn: () => Promise<IRes>;
  options?: {
    enabled?: boolean | undefined;
    staleTime?: number | undefined;
    retry?: number | boolean;
    keepPreviousData?: boolean | undefined;
  };
}

const useGrpcQuery = <IRes>({
  key,
  queryFn,
  options: {enabled = true, staleTime, retry, keepPreviousData} = {},
}: Props<IRes>): UseQueryResult<IRes> => {
  return useQuery<IRes>(
    key,
    async () => {
      return await queryFn();
    },
    {
      enabled,
      staleTime,
      retry,
      keepPreviousData,
    }
  );
};

export default useGrpcQuery;
