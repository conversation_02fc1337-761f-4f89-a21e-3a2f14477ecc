name: CodeQL (Go)

on:
  push:
    branches:
    - main
    - release-*
  pull_request:
    branches:
    - main
    - release-*
  merge_group:
    branches:
    - main
  schedule:
    - cron: '19 8 * * 1'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  # renovate: datasource=npm depName=pnpm versioning=npm
  PNPM_VERSION: '10.13.1'

jobs:
  skip-check:
    name: Skip check
    continue-on-error: true
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      should_skip: ${{ steps.skip-check.outputs.should_skip }}
    permissions:
      actions: write
      contents: read
    steps:
      - id: skip-check
        uses: fkirc/skip-duplicate-actions@f75f66ce1886f00957d99748a42c724f4330bdcf # v5.3.1
        with:
          do_not_skip: '["schedule", "workflow_dispatch"]'
          paths: |-
            [
              ".github/workflows/codeql-analysis-go.yml",
              "**.go",
              "go.mod",
              "go.sum"
            ]
          skip_after_successful_duplicate: false

  analyze:
    name: Analyze
    needs: skip-check
    if: ${{ needs.skip-check.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 30
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'go' ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: .go-version

      - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version-file: .node-version
          cache: 'pnpm'
          cache-dependency-path: ui/pnpm-lock.yaml

      - name: Initialize CodeQL
        uses: github/codeql-action/init@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2
        with:
          languages: ${{ matrix.language }}

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2
