package processor

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/mexyusef/deep-ebpf-server/storage"
	"k8s.io/klog"
)

// Config defines processor configuration
type Config struct {
	BufferSize        int
	CorrelationTTL    time.Duration
	AggregationWindow time.Duration
	MaxConcurrency    int
}

// Use storage types instead of duplicating them
type FunctionCall = storage.FunctionCall
type FunctionMetrics = storage.FunctionMetrics

// Processor handles function trace processing and correlation
type Processor struct {
	config  Config
	storage storage.Storage

	// Correlation state
	pendingCalls map[string]*PendingCall
	pendingLock  sync.RWMutex

	// Processing channels
	traceBuffer   chan interface{}
	metricsBuffer chan *FunctionMetrics

	ctx    context.Context
	cancel context.CancelFunc
}

// PendingCall represents a function call waiting for exit event
type PendingCall struct {
	EntryTrace *FunctionTrace
	StartTime  time.Time
}

// FunctionTrace represents a function trace event (from collector)
// This matches the collector.FunctionTrace type exactly
type FunctionTrace struct {
	AgentID      string                 `json:"agent_id"`
	Timestamp    time.Time              `json:"timestamp"`
	Type         string                 `json:"type"`
	PID          uint32                 `json:"pid"`
	ProcessName  string                 `json:"process_name"`
	FunctionName string                 `json:"function_name"`
	FunctionAddr uint64                 `json:"function_addr"`
	Duration     time.Duration          `json:"duration"`
	Arguments    []uint64               `json:"arguments"`
	ReturnValue  uint64                 `json:"return_value"`
	StackTrace   []uint64               `json:"stack_trace"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// New creates a new processor instance
func New(config Config, store storage.Storage) *Processor {
	ctx, cancel := context.WithCancel(context.Background())

	return &Processor{
		config:        config,
		storage:       store,
		pendingCalls:  make(map[string]*PendingCall),
		traceBuffer:   make(chan interface{}, config.BufferSize),
		metricsBuffer: make(chan *FunctionMetrics, 1000),
		ctx:           ctx,
		cancel:        cancel,
	}
}

// Start starts the processor
func (p *Processor) Start() error {
	// Start processing goroutines
	for i := 0; i < p.config.MaxConcurrency; i++ {
		go p.processTraces()
	}

	// Start metrics aggregation
	go p.aggregateMetrics()

	// Start cleanup goroutine
	go p.cleanupPendingCalls()

	klog.Infof("Processor started with %d workers", p.config.MaxConcurrency)
	return nil
}

// Close stops the processor
func (p *Processor) Close() {
	p.cancel()
	close(p.traceBuffer)
	close(p.metricsBuffer)
	klog.Infof("Processor stopped")
}

// ProcessTrace processes a function trace event
func (p *Processor) ProcessTrace(trace *FunctionTrace) error {
	select {
	case p.traceBuffer <- trace:
		return nil
	default:
		return fmt.Errorf("trace buffer full")
	}
}

// processTraces processes traces from the buffer
func (p *Processor) processTraces() {
	for {
		select {
		case item := <-p.traceBuffer:
			if item == nil {
				return // Channel closed
			}

			trace, ok := item.(*FunctionTrace)
			if !ok {
				continue
			}

			if err := p.handleTrace(trace); err != nil {
				klog.Errorf("Failed to handle trace: %v", err)
			}

		case <-p.ctx.Done():
			return
		}
	}
}

// handleTrace handles a single trace event
func (p *Processor) handleTrace(trace *FunctionTrace) error {
	callKey := p.getCallKey(trace)

	switch trace.Type {
	case "function-entry":
		return p.handleFunctionEntry(callKey, trace)
	case "function-exit":
		return p.handleFunctionExit(callKey, trace)
	default:
		klog.Warningf("Unknown trace type: %s", trace.Type)
		return nil
	}
}

// handleFunctionEntry handles function entry events
func (p *Processor) handleFunctionEntry(callKey string, trace *FunctionTrace) error {
	p.pendingLock.Lock()
	defer p.pendingLock.Unlock()

	// Store pending call
	p.pendingCalls[callKey] = &PendingCall{
		EntryTrace: trace,
		StartTime:  time.Now(),
	}

	klog.V(3).Infof("Function entry: %s in PID %d", trace.FunctionName, trace.PID)
	return nil
}

// handleFunctionExit handles function exit events
func (p *Processor) handleFunctionExit(callKey string, trace *FunctionTrace) error {
	p.pendingLock.Lock()
	pending, exists := p.pendingCalls[callKey]
	if exists {
		delete(p.pendingCalls, callKey)
	}
	p.pendingLock.Unlock()

	if !exists {
		// Orphaned exit event - create incomplete call
		call := &FunctionCall{
			ID:           p.generateCallID(trace),
			AgentID:      trace.AgentID,
			PID:          trace.PID,
			ProcessName:  trace.ProcessName,
			FunctionName: trace.FunctionName,
			FunctionAddr: trace.FunctionAddr,
			EndTime:      trace.Timestamp,
			Duration:     trace.Duration,
			ReturnValue:  trace.ReturnValue,
			StackTrace:   trace.StackTrace,
			Error:        "missing entry event",
			Metadata:     trace.Metadata,
		}
		return p.storage.StoreFunctionCall(call)
	}

	// Create complete function call
	call := &FunctionCall{
		ID:           p.generateCallID(pending.EntryTrace),
		AgentID:      pending.EntryTrace.AgentID,
		PID:          pending.EntryTrace.PID,
		ProcessName:  pending.EntryTrace.ProcessName,
		FunctionName: pending.EntryTrace.FunctionName,
		FunctionAddr: pending.EntryTrace.FunctionAddr,
		StartTime:    pending.EntryTrace.Timestamp,
		EndTime:      trace.Timestamp,
		Duration:     trace.Timestamp.Sub(pending.EntryTrace.Timestamp),
		Arguments:    pending.EntryTrace.Arguments,
		ReturnValue:  trace.ReturnValue,
		StackTrace:   pending.EntryTrace.StackTrace,
		Metadata:     pending.EntryTrace.Metadata,
	}

	// Store the complete call
	if err := p.storage.StoreFunctionCall(call); err != nil {
		return err
	}

	// Update metrics
	p.updateMetrics(call)

	klog.V(3).Infof("Function call completed: %s in PID %d, duration: %v", 
		call.FunctionName, call.PID, call.Duration)

	return nil
}

// updateMetrics updates function metrics
func (p *Processor) updateMetrics(call *FunctionCall) {
	// This is a simplified metrics update
	// In a real implementation, you'd want more sophisticated aggregation
	metrics := &FunctionMetrics{
		FunctionName:  call.FunctionName,
		CallCount:     1,
		TotalDuration: call.Duration,
		MinDuration:   call.Duration,
		MaxDuration:   call.Duration,
		AvgDuration:   call.Duration,
		LastSeen:      call.EndTime,
	}

	if call.Error != "" {
		metrics.ErrorCount = 1
	}

	select {
	case p.metricsBuffer <- metrics:
	default:
		klog.Warningf("Metrics buffer full, dropping metrics for %s", call.FunctionName)
	}
}

// aggregateMetrics aggregates function metrics
func (p *Processor) aggregateMetrics() {
	ticker := time.NewTicker(p.config.AggregationWindow)
	defer ticker.Stop()

	for {
		select {
		case metrics := <-p.metricsBuffer:
			if metrics == nil {
				return // Channel closed
			}
			
			// Store metrics (simplified)
			if err := p.storage.StoreFunctionMetrics(metrics); err != nil {
				klog.Errorf("Failed to store metrics: %v", err)
			}

		case <-ticker.C:
			// Periodic aggregation would go here
			
		case <-p.ctx.Done():
			return
		}
	}
}

// cleanupPendingCalls removes stale pending calls
func (p *Processor) cleanupPendingCalls() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.pendingLock.Lock()
			now := time.Now()
			for key, pending := range p.pendingCalls {
				if now.Sub(pending.StartTime) > p.config.CorrelationTTL {
					delete(p.pendingCalls, key)
					klog.V(2).Infof("Cleaned up stale pending call: %s", key)
				}
			}
			p.pendingLock.Unlock()

		case <-p.ctx.Done():
			return
		}
	}
}

// getCallKey generates a unique key for correlating function calls
func (p *Processor) getCallKey(trace *FunctionTrace) string {
	return fmt.Sprintf("%s:%d:%x", trace.AgentID, trace.PID, trace.FunctionAddr)
}

// generateCallID generates a unique ID for a function call
func (p *Processor) generateCallID(trace *FunctionTrace) string {
	return fmt.Sprintf("%s-%d-%x-%d", 
		trace.AgentID, trace.PID, trace.FunctionAddr, trace.Timestamp.UnixNano())
}

// GetStats returns processor statistics
func (p *Processor) GetStats() map[string]interface{} {
	p.pendingLock.RLock()
	pendingCount := len(p.pendingCalls)
	p.pendingLock.RUnlock()

	return map[string]interface{}{
		"pending_calls":     pendingCount,
		"trace_buffer":      len(p.traceBuffer),
		"metrics_buffer":    len(p.metricsBuffer),
		"correlation_ttl":   p.config.CorrelationTTL.String(),
		"aggregation_window": p.config.AggregationWindow.String(),
	}
}
