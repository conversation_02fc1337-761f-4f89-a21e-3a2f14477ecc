{"coredump-ref": "cc5cd2ce10d88f6ebdf19434e2b8a8d0882feab078ed0c04c679b24859c740d1", "threads": [{"lwp": 243879, "frames": ["debug/dwarf.(*unit).addrsize+0 in /usr/local/go/src/debug/dwarf/unit.go:37", "debug/dwarf.(*LineReader).readHeader+0 in /usr/local/go/src/debug/dwarf/line.go:209", "debug/dwarf.(*Data).LineReader+0 in /usr/local/go/src/debug/dwarf/line.go:174", "github.com/optimyze/prodfiler/libpf/dwarfextract.(*SymbolResolver).loadLineTable+0 in /media/psf/devel/prodfiler/libpf/dwarfextract/symbols.go:297", "github.com/optimyze/prodfiler/libpf/dwarfextract.(*SymbolResolver).loadCompilationUnit+0 in /media/psf/devel/prodfiler/libpf/dwarfextract/symbols.go:359", "github.com/optimyze/prodfiler/libpf/dwarfextract.(*SymbolResolver).ElasticDump+0 in /media/psf/devel/prodfiler/libpf/dwarfextract/elastic.go:50", "main.handleRegularExecutable+0 in /media/psf/devel/prodfiler/utils/symbhack/main.go:168", "main.tryMain+0 in /media/psf/devel/prodfiler/utils/symbhack/main.go:123", "main.main+0 in /media/psf/devel/prodfiler/utils/symbhack/main.go:43", "runtime.main+0 in /usr/local/go/src/runtime/proc.go:259", "runtime.goexit+0 in /usr/local/go/src/runtime/asm_arm64.s:1166"]}, {"lwp": 243876, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243878, "frames": ["runtime.usleep+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:142", "runtime.sysmon+0 in /usr/local/go/src/runtime/proc.go:5162", "runtime.mstart1+0 in /usr/local/go/src/runtime/proc.go:1428", "runtime.mstart0+0 in /usr/local/go/src/runtime/proc.go:1385", "runtime.mstart+0 in /usr/local/go/src/runtime/asm_arm64.s:129"]}, {"lwp": 243880, "frames": ["runtime.epollwait+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:801", "runtime.netpoll+0 in /usr/local/go/src/runtime/netpoll_epoll.go:126", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2829", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243881, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243882, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.templateThread+0 in /usr/local/go/src/runtime/proc.go:2201", "runtime.mstart1+0 in /usr/local/go/src/runtime/proc.go:1428", "runtime.mstart0+0 in /usr/local/go/src/runtime/proc.go:1385", "runtime.mstart+0 in /usr/local/go/src/runtime/asm_arm64.s:129"]}, {"lwp": 243883, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243884, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243885, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243886, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}, {"lwp": 243887, "frames": ["runtime.futex+0 in /usr/local/go/src/runtime/sys_linux_arm64.s:666", "runtime.futexsleep+0 in /usr/local/go/src/runtime/os_linux.go:70", "runtime.notesleep+0 in /usr/local/go/src/runtime/lock_futex.go:161", "runtime.mPark+0 in /usr/local/go/src/runtime/proc.go:1458", "runtime.stopm+0 in /usr/local/go/src/runtime/proc.go:2240", "runtime.findRunnable+0 in /usr/local/go/src/runtime/proc.go:2561", "runtime.schedule+0 in /usr/local/go/src/runtime/proc.go:3206", "runtime.park_m+0 in /usr/local/go/src/runtime/proc.go:3356", "runtime.mcall+0 in /usr/local/go/src/runtime/asm_arm64.s:193"]}], "modules": [{"ref": "d2d4a688141a686667220fa84ec8b4c9c4ec805c00c9a1f03ce0b8a45fa6c2e6", "local-path": "/usr/lib/aarch64-linux-gnu/ld-linux-aarch64.so.1"}, {"ref": "8a15b787643d08b76e8ab7b7f67d614282af6cf310c9cf20af8ede95a668c621", "local-path": "/media/psf/devel/prodfiler/utils/symbhack/symbhack"}, {"ref": "acf9cd2b988c12b0e9ba02cb457b5fc81ffab1b4683ac166edbced3333fe5482", "local-path": "/usr/lib/aarch64-linux-gnu/libc.so.6"}]}