package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/alecthomas/kingpin.v2"
	"gopkg.in/yaml.v3"
)

// Config represents the complete server configuration
type Config struct {
	ListenAddress string `yaml:"listen_address"`
	UrlBasePath   string `yaml:"url_base_path"`
	DataDir       string `yaml:"data_dir"`
	DeveloperMode bool   `yaml:"developer_mode"`

	Storage   StorageConfig   `yaml:"storage"`
	Collector CollectorConfig `yaml:"collector"`
	Processor ProcessorConfig `yaml:"processor"`
	API       APIConfig       `yaml:"api"`
}

// StorageConfig defines storage settings
type StorageConfig struct {
	Type           string        `yaml:"type"`           // "memory" or "persistent"
	RetentionDays  int           `yaml:"retention_days"`
	Compression    bool          `yaml:"compression"`
	MaxMemoryMB    int           `yaml:"max_memory_mb"`
	FlushInterval  time.Duration `yaml:"flush_interval"`
}

// CollectorConfig defines data collection settings
type CollectorConfig struct {
	ListenAddress   string        `yaml:"listen_address"`
	BufferSize      int           `yaml:"buffer_size"`
	AgentTimeout    time.Duration `yaml:"agent_timeout"`
	EnableDiscovery bool          `yaml:"enable_discovery"`
	MaxAgents       int           `yaml:"max_agents"`
}

// ProcessorConfig defines function trace processing settings
type ProcessorConfig struct {
	BufferSize        int           `yaml:"buffer_size"`
	CorrelationTTL    time.Duration `yaml:"correlation_ttl"`
	AggregationWindow time.Duration `yaml:"aggregation_window"`
	MaxConcurrency    int           `yaml:"max_concurrency"`
}

// APIConfig defines API server settings
type APIConfig struct {
	EnableCORS  bool `yaml:"enable_cors"`
	RateLimit   int  `yaml:"rate_limit"`
	AuthEnabled bool `yaml:"auth_enabled"`
}

var (
	configFile      = kingpin.Flag("config", "Configuration file path").Default("").String()
	listenAddress   = kingpin.Flag("listen", "Listen address").Default(":8080").String()
	urlBasePath     = kingpin.Flag("url-base-path", "URL base path").Default("/").String()
	dataDir         = kingpin.Flag("data-dir", "Data directory").Default("./data").String()
	developerMode   = kingpin.Flag("dev", "Developer mode").Default("false").Bool()
	storageType     = kingpin.Flag("storage-type", "Storage type (memory|persistent)").Default("persistent").String()
	retentionDays   = kingpin.Flag("retention-days", "Data retention in days").Default("30").Int()
	enableCORS      = kingpin.Flag("enable-cors", "Enable CORS").Default("true").Bool()
	rateLimit       = kingpin.Flag("rate-limit", "API rate limit per minute").Default("1000").Int()
	authEnabled     = kingpin.Flag("auth", "Enable authentication").Default("false").Bool()
)

// DefaultConfig returns a configuration with sensible defaults
func DefaultConfig() *Config {
	return &Config{
		ListenAddress: ":8080",
		UrlBasePath:   "/",
		DataDir:       "./data",
		DeveloperMode: false,

		Storage: StorageConfig{
			Type:          "persistent",
			RetentionDays: 30,
			Compression:   true,
			MaxMemoryMB:   1024,
			FlushInterval: 5 * time.Minute,
		},

		Collector: CollectorConfig{
			ListenAddress:   ":8081",
			BufferSize:      10000,
			AgentTimeout:    30 * time.Second,
			EnableDiscovery: true,
			MaxAgents:       1000,
		},

		Processor: ProcessorConfig{
			BufferSize:        50000,
			CorrelationTTL:    5 * time.Minute,
			AggregationWindow: 1 * time.Minute,
			MaxConcurrency:    10,
		},

		API: APIConfig{
			EnableCORS:  true,
			RateLimit:   1000,
			AuthEnabled: false,
		},
	}
}

// Load loads configuration from file and command line flags
func Load() (*Config, error) {
	cfg := DefaultConfig()

	// Load from file if specified
	if *configFile != "" {
		if err := loadFromFile(cfg, *configFile); err != nil {
			return nil, fmt.Errorf("failed to load config file: %w", err)
		}
	}

	// Override with command line flags
	cfg.ListenAddress = *listenAddress
	cfg.UrlBasePath = *urlBasePath
	cfg.DataDir = *dataDir
	cfg.DeveloperMode = *developerMode
	cfg.Storage.Type = *storageType
	cfg.Storage.RetentionDays = *retentionDays
	cfg.API.EnableCORS = *enableCORS
	cfg.API.RateLimit = *rateLimit
	cfg.API.AuthEnabled = *authEnabled

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return cfg, nil
}

// loadFromFile loads configuration from a YAML file
func loadFromFile(cfg *Config, filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	return yaml.Unmarshal(data, cfg)
}

// Save saves configuration to a YAML file
func (c *Config) Save(filename string) error {
	// Create directory if it doesn't exist
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	data, err := yaml.Marshal(c)
	if err != nil {
		return err
	}

	return os.WriteFile(filename, data, 0644)
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.ListenAddress == "" {
		return fmt.Errorf("listen_address cannot be empty")
	}

	if c.DataDir == "" {
		return fmt.Errorf("data_dir cannot be empty")
	}

	if c.Storage.Type != "memory" && c.Storage.Type != "persistent" {
		return fmt.Errorf("storage.type must be 'memory' or 'persistent'")
	}

	if c.Storage.RetentionDays <= 0 {
		return fmt.Errorf("storage.retention_days must be positive")
	}

	if c.Storage.MaxMemoryMB <= 0 {
		return fmt.Errorf("storage.max_memory_mb must be positive")
	}

	if c.Collector.BufferSize <= 0 {
		return fmt.Errorf("collector.buffer_size must be positive")
	}

	if c.Collector.AgentTimeout <= 0 {
		return fmt.Errorf("collector.agent_timeout must be positive")
	}

	if c.Collector.MaxAgents <= 0 {
		return fmt.Errorf("collector.max_agents must be positive")
	}

	if c.Processor.BufferSize <= 0 {
		return fmt.Errorf("processor.buffer_size must be positive")
	}

	if c.Processor.CorrelationTTL <= 0 {
		return fmt.Errorf("processor.correlation_ttl must be positive")
	}

	if c.Processor.AggregationWindow <= 0 {
		return fmt.Errorf("processor.aggregation_window must be positive")
	}

	if c.Processor.MaxConcurrency <= 0 {
		return fmt.Errorf("processor.max_concurrency must be positive")
	}

	if c.API.RateLimit <= 0 {
		return fmt.Errorf("api.rate_limit must be positive")
	}

	return nil
}

// GetExampleConfig returns an example configuration file content
func GetExampleConfig() string {
	return `# Deep-eBPF Server Configuration

# Server settings
listen_address: ":8080"
url_base_path: "/"
data_dir: "./data"
developer_mode: false

# Storage configuration
storage:
  type: "persistent"          # "memory" or "persistent"
  retention_days: 30
  compression: true
  max_memory_mb: 1024
  flush_interval: "5m"

# Data collector configuration
collector:
  listen_address: ":8081"
  buffer_size: 10000
  agent_timeout: "30s"
  enable_discovery: true
  max_agents: 1000

# Function trace processor configuration
processor:
  buffer_size: 50000
  correlation_ttl: "5m"
  aggregation_window: "1m"
  max_concurrency: 10

# API configuration
api:
  enable_cors: true
  rate_limit: 1000
  auth_enabled: false
`
}

// PrintConfig prints the current configuration
func (c *Config) PrintConfig() {
	fmt.Printf("Deep-eBPF Server Configuration:\n")
	fmt.Printf("  Listen Address: %s\n", c.ListenAddress)
	fmt.Printf("  URL Base Path: %s\n", c.UrlBasePath)
	fmt.Printf("  Data Directory: %s\n", c.DataDir)
	fmt.Printf("  Developer Mode: %t\n", c.DeveloperMode)
	fmt.Printf("  Storage Type: %s\n", c.Storage.Type)
	fmt.Printf("  Retention Days: %d\n", c.Storage.RetentionDays)
	fmt.Printf("  Collector Address: %s\n", c.Collector.ListenAddress)
	fmt.Printf("  Buffer Size: %d\n", c.Collector.BufferSize)
	fmt.Printf("  Enable CORS: %t\n", c.API.EnableCORS)
	fmt.Printf("  Rate Limit: %d\n", c.API.RateLimit)
	fmt.Printf("  Auth Enabled: %t\n", c.API.AuthEnabled)
}
