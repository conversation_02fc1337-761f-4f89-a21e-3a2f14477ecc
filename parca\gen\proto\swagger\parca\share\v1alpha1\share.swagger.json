{"swagger": "2.0", "info": {"title": "parca/share/v1alpha1/share.proto", "version": "version not set"}, "tags": [{"name": "ShareService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"QueryRequestReportType": {"type": "string", "enum": ["REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED", "REPORT_TYPE_PPROF", "REPORT_TYPE_TOP", "REPORT_TYPE_CALLGRAPH", "REPORT_TYPE_FLAMEGRAPH_TABLE", "REPORT_TYPE_FLAMEGRAPH_ARROW", "REPORT_TYPE_SOURCE", "REPORT_TYPE_TABLE_ARROW", "REPORT_TYPE_PROFILE_METADATA", "REPORT_TYPE_FLAMECHART"], "default": "REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED", "description": "- REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED: REPORT_TYPE_FLAMEGRAPH_UNSPECIFIED unspecified\n - REPORT_TYPE_PPROF: REPORT_TYPE_PPROF unspecified\n - REPORT_TYPE_TOP: REPORT_TYPE_TOP unspecified\n - REPORT_TYPE_CALLGRAPH: REPORT_TYPE_CALLGRAPH unspecified\n - REPORT_TYPE_FLAMEGRAPH_TABLE: REPORT_TYPE_FLAMEGRAPH_TABLE unspecified\n - REPORT_TYPE_FLAMEGRAPH_ARROW: REPORT_TYPE_FLAMEGRAPH_ARROW unspecified\n - REPORT_TYPE_SOURCE: REPORT_TYPE_SOURCE contains source code annotated with profiling information\n - REPORT_TYPE_TABLE_ARROW: REPORT_TYPE_TABLE_ARROW unspecified\n - REPORT_TYPE_PROFILE_METADATA: REPORT_TYPE_PROFILE_METADATA contains metadata about the profile i.e. binaries, labels\n - REPORT_TYPE_FLAMECHART: REPORT_TYPE_FLAMECHART contains flamechart representation of the report", "title": "ReportType is the type of report to return"}, "metastorev1alpha1Function": {"type": "object", "properties": {"id": {"type": "string", "description": "id is the unique identifier for the function."}, "startLine": {"type": "string", "format": "int64", "description": "start_line is the line number in the source file of the first line of the function."}, "name": {"type": "string", "description": "name is the name of the function."}, "systemName": {"type": "string", "description": "system_name describes the name of the function, as identified by the\nsystem. For instance, it can be a C++ mangled name."}, "filename": {"type": "string", "description": "filename is the name of the source file of the function."}, "nameStringIndex": {"type": "integer", "format": "int64", "description": "name_string_index is the index in the string table to the name associated with the function."}, "systemNameStringIndex": {"type": "integer", "format": "int64", "description": "system_name_string_index is the index in the string table to the system_name associated with the function."}, "filenameStringIndex": {"type": "integer", "format": "int64", "description": "filename_string_index is the index in the string table to the filename associated with the function."}}, "description": "Function describes metadata of a source code function."}, "metastorev1alpha1Line": {"type": "object", "properties": {"functionId": {"type": "string", "description": "function_id is the ID of the function."}, "line": {"type": "string", "format": "int64", "description": "line is the line number in the source file of the referenced function."}, "functionIndex": {"type": "integer", "format": "int64", "description": "function_index is the index in the functions table."}}, "description": "Line describes a source code function and its line number."}, "metastorev1alpha1Location": {"type": "object", "properties": {"id": {"type": "string", "description": "id is the unique identifier for the location."}, "address": {"type": "string", "format": "uint64", "description": "address is the memory address of the location if present."}, "mappingId": {"type": "string", "description": "mapping_id is the unique identifier for the mapping associated with the location."}, "isFolded": {"type": "boolean", "description": "is_folded indicates whether the location is folded into the previous location."}, "lines": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/metastorev1alpha1Line"}, "description": "lines are the call frames represented by this location. Multiple lines\nindicate they have been inlined."}, "mappingIndex": {"type": "integer", "format": "int64", "description": "mapping_index has the index into the mapping table where mappings are sent deduplicated."}}, "description": "Location describes a single location of a stack traces."}, "metastorev1alpha1Mapping": {"type": "object", "properties": {"id": {"type": "string", "description": "id is the unique identifier for the mapping."}, "start": {"type": "string", "format": "uint64", "description": "start is the start address of the mapping."}, "limit": {"type": "string", "format": "uint64", "description": "limit is the length of the address space of the mapping."}, "offset": {"type": "string", "format": "uint64", "description": "offset in the binary that corresponds to the first mapped address."}, "file": {"type": "string", "description": "file is the name of the file associated with the mapping."}, "buildId": {"type": "string", "description": "build_id is the build ID of the mapping."}, "hasFunctions": {"type": "boolean", "description": "has_functions indicates whether the mapping has associated functions."}, "hasFilenames": {"type": "boolean", "description": "has_filenames indicates whether the mapping has associated filenames."}, "hasLineNumbers": {"type": "boolean", "description": "has_line_numbers indicates whether the mapping has associated line numbers."}, "hasInlineFrames": {"type": "boolean", "description": "has_inline_frames indicates whether the mapping has associated inline frames."}, "fileStringIndex": {"type": "integer", "format": "int64", "description": "fileStringIndex is the index in the string table to the file name associated with the mapping."}, "buildIdStringIndex": {"type": "integer", "format": "int64", "description": "build_id_string_index is the index in the string table to the build ID of the mapping."}}, "description": "Mapping describes a memory mapping."}, "parcasharev1alpha1ProfileTypesResponse": {"type": "object", "properties": {"types": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1ProfileType"}, "description": "list of available profile types."}, "description": {"type": "string", "description": "description of the profile uploaded."}}, "description": "ProfileTypesResponse represents the response with the list of available profile types."}, "parcasharev1alpha1QueryResponse": {"type": "object", "properties": {"flamegraph": {"$ref": "#/definitions/v1alpha1Flamegraph", "title": "flamegraph is a flamegraph representation of the report"}, "pprof": {"type": "string", "format": "byte", "title": "pprof is a pprof profile as compressed bytes"}, "top": {"$ref": "#/definitions/v1alpha1Top", "title": "top is a top list representation of the report"}, "callgraph": {"$ref": "#/definitions/v1alpha1Callgraph", "title": "callgraph is a callgraph nodes and edges representation of the report"}, "flamegraphArrow": {"$ref": "#/definitions/v1alpha1FlamegraphArrow", "title": "flamegraph_arrow is a flamegraph encoded as a arrow record"}, "source": {"$ref": "#/definitions/queryv1alpha1Source", "title": "source is the source report type result"}, "tableArrow": {"$ref": "#/definitions/v1alpha1TableArrow", "title": "table_arrow is a table encoded as a arrow record"}, "profileMetadata": {"$ref": "#/definitions/v1alpha1ProfileMetadata", "title": "profile_metadata contains metadata about the profile i.e. binaries, labels"}, "total": {"type": "string", "format": "int64", "description": "total is the total number of samples shown in the report."}, "filtered": {"type": "string", "format": "int64", "description": "filtered is the number of samples filtered out of the report."}}, "description": "QueryResponse is the returned report for the given query."}, "parcasharev1alpha1UploadResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "id of the uploaded profile."}, "link": {"type": "string", "description": "link that can be used to access the profile."}}, "description": "UploadResponse represents the response with the link that can be used to access the profile."}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "queryv1alpha1Source": {"type": "object", "properties": {"record": {"type": "string", "format": "byte", "description": "An arrow record that contains a row per source code line with value and diff columns for flat and cumulative."}, "source": {"type": "string", "description": "The actual source file content."}, "unit": {"type": "string", "description": "The unit of the values in the record."}}, "description": "Source is the result of the source report type."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1alpha1BinaryFrameFilter": {"type": "object", "properties": {"includeBinaries": {"type": "array", "items": {"type": "string"}, "title": "include_binaries is the list of binaries to filter by"}}, "title": "BinaryFrameFilter is a filter for filtering by binaries"}, "v1alpha1Callgraph": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1CallgraphNode"}, "title": "nodes are the nodes in the callgraph"}, "edges": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1CallgraphEdge"}, "title": "edges are the edges connecting nodes in the callgraph"}, "cumulative": {"type": "string", "format": "int64", "description": "cumulative is the total cumulative value of the callgraph\nUse total from the top level query response instead."}}, "title": "Callgraph is the callgraph report type"}, "v1alpha1CallgraphEdge": {"type": "object", "properties": {"id": {"type": "string", "title": "id is the unique id of the edge"}, "source": {"type": "string", "title": "source represents the id of the source node"}, "target": {"type": "string", "title": "target represents the id of the target node"}, "cumulative": {"type": "string", "format": "int64", "title": "cumulative is the cumulative value of the edge"}, "isCollapsed": {"type": "boolean", "title": "is_collapsed indicates if the edge is collapsed"}}, "title": "CallgraphEdge represents an edge in the graph"}, "v1alpha1CallgraphNode": {"type": "object", "properties": {"id": {"type": "string", "title": "id is the unique id of the node"}, "meta": {"$ref": "#/definitions/v1alpha1CallgraphNodeMeta", "title": "meta is the metadata about the node"}, "cumulative": {"type": "string", "format": "int64", "title": "cumulative is the cumulative value of the node"}, "flat": {"type": "string", "format": "int64", "title": "flat is the flat value of the node"}}, "title": "CallgraphNode represents a node in the graph"}, "v1alpha1CallgraphNodeMeta": {"type": "object", "properties": {"location": {"$ref": "#/definitions/metastorev1alpha1Location", "title": "location is the location for the code"}, "mapping": {"$ref": "#/definitions/metastorev1alpha1Mapping", "title": "mapping is the mapping into code"}, "function": {"$ref": "#/definitions/metastorev1alpha1Function", "title": "function is the function information"}, "line": {"$ref": "#/definitions/metastorev1alpha1Line", "title": "line is the line location"}}, "title": "TopNodeMeta is the metadata for a given node"}, "v1alpha1Filter": {"type": "object", "properties": {"stackFilter": {"$ref": "#/definitions/v1alpha1StackFilter", "title": "stack_filter is a filter for filtering by stacks"}, "frameFilter": {"$ref": "#/definitions/v1alpha1FrameFilter", "title": "frame_filter is a filter for filtering by frames"}}, "title": "Filter to apply to the query request"}, "v1alpha1Flamegraph": {"type": "object", "properties": {"root": {"$ref": "#/definitions/v1alpha1FlamegraphRootNode", "title": "root is the root of the flame graph"}, "total": {"type": "string", "format": "int64", "description": "total is the total weight of the flame graph\nUse total from the top level query response instead."}, "unit": {"type": "string", "title": "unit is the unit represented by the flame graph"}, "height": {"type": "integer", "format": "int32", "title": "height is the max height of the graph"}, "stringTable": {"type": "array", "items": {"type": "string"}, "description": "string_table holds all deduplicated strings used in the meta data."}, "locations": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/metastorev1alpha1Location"}, "description": "locations deduplicated by their ID to be referenced by nodes."}, "mapping": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/metastorev1alpha1Mapping"}, "description": "mapping deduplicated by their ID to be referenced by nodes."}, "function": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/metastorev1alpha1Function"}, "description": "function deduplicated by their ID to be referenced by nodes."}, "untrimmedTotal": {"type": "string", "format": "int64", "description": "untrimmed_total is the total weight of the flame graph before trimming.\nUse trimmed instead."}, "trimmed": {"type": "string", "format": "int64", "description": "trimmed is the amount of cumulative value trimmed from the flame graph."}}, "title": "Flamegraph is the flame graph report type"}, "v1alpha1FlamegraphArrow": {"type": "object", "properties": {"record": {"type": "string", "format": "byte", "title": "record is the arrow record containing the actual flamegraph data"}, "unit": {"type": "string", "title": "unit is the unit represented by the flame graph"}, "height": {"type": "integer", "format": "int32", "title": "height is the max height of the graph"}, "trimmed": {"type": "string", "format": "int64", "description": "trimmed is the amount of cumulative value trimmed from the flame graph."}}, "title": "Flamegraph is the flame graph report type"}, "v1alpha1FlamegraphNode": {"type": "object", "properties": {"meta": {"$ref": "#/definitions/v1alpha1FlamegraphNodeMeta", "title": "meta is the metadata about the node"}, "cumulative": {"type": "string", "format": "int64", "title": "cumulative is the cumulative value of the node"}, "diff": {"type": "string", "format": "int64", "title": "diff is the diff"}, "children": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1FlamegraphNode"}, "title": "children are the child nodes"}}, "title": "FlamegraphNode represents a node in the graph"}, "v1alpha1FlamegraphNodeMeta": {"type": "object", "properties": {"location": {"$ref": "#/definitions/metastorev1alpha1Location", "title": "location is the location for the code"}, "mapping": {"$ref": "#/definitions/metastorev1alpha1Mapping", "title": "mapping is the mapping into code"}, "function": {"$ref": "#/definitions/metastorev1alpha1Function", "title": "function is the function information"}, "line": {"$ref": "#/definitions/metastorev1alpha1Line", "title": "line is the line location"}, "locationIndex": {"type": "integer", "format": "int64", "description": "location_index has the index to the deduplicated location in the location table."}, "lineIndex": {"type": "integer", "format": "int64", "description": "line_index is the line index within the referenced location."}}, "title": "FlamegraphNodeMeta is the metadata for a given node"}, "v1alpha1FlamegraphRootNode": {"type": "object", "properties": {"cumulative": {"type": "string", "format": "int64", "title": "cumulative is the cumulative value of the graph"}, "diff": {"type": "string", "format": "int64", "title": "diff is the diff"}, "children": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1FlamegraphNode"}, "title": "children are the list of the children of the root node"}}, "title": "FlamegraphRootNode is a root node of a flame graph"}, "v1alpha1FrameFilter": {"type": "object", "properties": {"binaryFrameFilter": {"$ref": "#/definitions/v1alpha1BinaryFrameFilter", "title": "binary_frame_filter is the list of binary names to filter by"}}, "title": "FrameFilter is a filter for filtering by frames"}, "v1alpha1FunctionNameStackFilter": {"type": "object", "properties": {"functionToFilter": {"type": "string", "title": "function_to_filter is the function name to filter by"}, "exclude": {"type": "boolean", "title": "exclude determines whether to exclude stacks matching the function"}}, "title": "FunctionNameStackFilter is a filter for filtering by function name"}, "v1alpha1GroupBy": {"type": "object", "properties": {"fields": {"type": "array", "items": {"type": "string"}, "description": "the names of the fields to group by.\nspecial fields are the ones prefixed with \"labels.\" which are grouping by pprof labels."}}, "title": "GroupBy encapsulates the repeated fields to group by"}, "v1alpha1ProfileMetadata": {"type": "object", "properties": {"mappingFiles": {"type": "array", "items": {"type": "string"}, "title": "mapping_files is the list of binaries in the profile"}, "labels": {"type": "array", "items": {"type": "string"}, "title": "labels is the list of labels in the profile"}}, "title": "ProfileMetadata contains metadata about the profile i.e. binaries, labels"}, "v1alpha1ProfileType": {"type": "object", "properties": {"name": {"type": "string", "description": "name is the name of the profile type."}, "sampleType": {"type": "string", "description": "sample_type is the type of the samples in the profile."}, "sampleUnit": {"type": "string", "description": "sample_unit is the unit of the samples in the profile."}, "periodType": {"type": "string", "description": "period_type is the type of the periods in the profile."}, "periodUnit": {"type": "string", "description": "period_unit is the unit of the periods in the profile."}, "delta": {"type": "boolean", "description": "delta describes whether the profile is a delta profile."}}, "description": "ProfileType is the type of a profile as well as the units the profile type is available in."}, "v1alpha1RuntimeFilter": {"type": "object", "properties": {"showPython": {"type": "boolean", "description": "Whether to show frames of the python runtime."}, "showRuby": {"type": "boolean", "description": "Whether to show frames of the ruby runtime."}, "showInterpretedOnly": {"type": "boolean", "description": "Whether to only show interpreted frames."}}, "description": "RuntimeFilter configures which runtimes to filter frames out for."}, "v1alpha1StackFilter": {"type": "object", "properties": {"functionNameStackFilter": {"$ref": "#/definitions/v1alpha1FunctionNameStackFilter", "title": "function_name_stack_filter is the function name to filter by"}}, "title": "StackFilter is a filter for filtering by stacks"}, "v1alpha1TableArrow": {"type": "object", "properties": {"record": {"type": "string", "format": "byte", "title": "record is the arrow record containing the actual table data"}, "unit": {"type": "string", "title": "unit is the unit represented by the flame graph"}}, "title": "TableArrow has the table encoded as a arrow record"}, "v1alpha1Top": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1alpha1TopNode"}, "title": "list are the list of ordered elements of the table"}, "reported": {"type": "integer", "format": "int32", "title": "reported is the number of lines reported"}, "total": {"type": "integer", "format": "int32", "description": "total is the number of lines that exist in the report\nUse total from the top level query response instead."}, "unit": {"type": "string", "title": "unit is the unit represented by top table"}}, "title": "Top is the top report type"}, "v1alpha1TopNode": {"type": "object", "properties": {"meta": {"$ref": "#/definitions/v1alpha1TopNodeMeta", "title": "meta is the metadata about the node"}, "cumulative": {"type": "string", "format": "int64", "title": "cumulative is the cumulative value of the node"}, "flat": {"type": "string", "format": "int64", "title": "flat is the flat value of the node"}, "diff": {"type": "string", "format": "int64", "title": "diff is the diff value between two profiles"}}, "title": "TopNode is a node entry in a top list"}, "v1alpha1TopNodeMeta": {"type": "object", "properties": {"location": {"$ref": "#/definitions/metastorev1alpha1Location", "title": "location is the location for the code"}, "mapping": {"$ref": "#/definitions/metastorev1alpha1Mapping", "title": "mapping is the mapping into code"}, "function": {"$ref": "#/definitions/metastorev1alpha1Function", "title": "function is the function information"}, "line": {"$ref": "#/definitions/metastorev1alpha1Line", "title": "line is the line location"}}, "title": "TopNodeMeta is the metadata for a given node"}}}