{"swagger": "2.0", "info": {"title": "grpc/health/v1/health.proto", "version": "version not set"}, "tags": [{"name": "Health"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"HealthCheckResponseServingStatus": {"type": "string", "enum": ["UNKNOWN", "SERVING", "NOT_SERVING", "SERVICE_UNKNOWN"], "default": "UNKNOWN", "description": " - SERVICE_UNKNOWN: Used only by the Watch method."}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1HealthCheckResponse": {"type": "object", "properties": {"status": {"$ref": "#/definitions/HealthCheckResponseServingStatus"}}}, "v1HealthListResponse": {"type": "object", "properties": {"statuses": {"type": "object", "additionalProperties": {"$ref": "#/definitions/v1HealthCheckResponse"}, "description": "statuses contains all the services and their respective status."}}}}}