// Copyright 2022 The Parca Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/* eslint-disable */

/// <reference types="vite-plugin-svgr/client" />

import CloseIcon from './CloseIcon';
import ArrowForward from './assets/arrow-forward.svg?react';
import Checkmark from './assets/checkmark.svg?react';
import CompareArrows from './assets/compare-arrows.svg?react';
import GitHub from './assets/github.svg?react';
import LinkedIn from './assets/linkedin.svg?react';
import ParcaSmall from './assets/logo-small.svg?react';
import Parca from './assets/logo.svg?react';
import RedX from './assets/red-x.svg?react';
import Twitter from './assets/twitter.svg?react';

export {
  Parca,
  ParcaSmall,
  CloseIcon,
  CompareArrows,
  Checkmark,
  RedX,
  GitHub,
  LinkedIn,
  Twitter,
  ArrowForward,
};
