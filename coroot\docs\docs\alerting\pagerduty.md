---
sidebar_position: 5
---

# Pagerduty

## Configure Pagerduty

To configure an **Events API V2** integration in your Pagerduty account:
* Navigate to **Services** → **Service Directory**
  <img alt="Pagerduty Services" src="/img/docs/pagerduty-integration-step1.png" class="card w-800"/>
* Choose a target service (or create a new one)
* Go to **Integrations** → **+ Add another integration**
  <img alt="Pagerduty add integration" src="/img/docs/pagerduty-integration-step2.png" class="card w-800"/>
* Choose the **Events API V2** integration
  <img alt="Pagerduty Events API v2" src="/img/docs/pagerduty-integration-step3.png" class="card w-800"/>
* Change the **Integration Name**, if necessary (e.g. Coroot)
* Copy the **Integration Key**
  <img alt="Pagerduty Integration Key" src="/img/docs/pagerduty-integration-step4.png" class="card w-800"/>


## Configure Coroot

* Go to the **Project Settings** → **Integrations**
* Create a Pagerduty integration
* Paste the Integration Key to the form
  <img alt="Coroot Pagerduty Integration" src="/img/docs/pagerduty-integration.png" class="card w-800"/>
* You can also send a test alert to check the integration
  <img alt="Coroot Pagerduty Test Alert" src="/img/docs/pagerduty-integration-test.png" class="card w-800"/>
