# Deep-eBPF Node Configuration
# Following Parca/coroot configuration patterns for comprehensive process filtering

general:
  enabled: true
  log_level: "info"
  process_name: "deep-ebpf-node"
  graceful_shutdown: true
  config_file: "config.yaml"

# Filtering configuration (following coroot-node-agent pattern)
filtering:
  enabled: true
  
  # Process-based filtering
  process_filters:
    # PID range filtering
    min_pid: 1
    max_pid: 65535
    
    # Include only these PIDs (empty = all PIDs)
    include_pids: []
    
    # Exclude these specific PIDs
    exclude_pids: []
    
    # Include only these process names (empty = all processes, supports glob patterns)
    include_process_names: []
    
    # Exclude these process names (supports glob patterns)
    exclude_process_names:
      - "systemd*"           # All systemd processes
      - "kthreadd"           # Kernel thread daemon
      - "ksoftirqd*"         # Kernel soft IRQ daemons
      - "migration*"         # Kernel migration threads
      - "rcu_*"              # RCU kernel threads
      - "watchdog*"          # Watchdog threads
      - "deep-ebpf-node*"    # Our own processes (self-exclusion)
      - "redis*"             # Redis processes
      - "dockerd"            # Docker daemon
      - "docker-proxy"       # Docker proxy processes
      - "containerd*"        # Container runtime
      - "dbus-daemon"        # D-Bus daemon
      - "wsl-pro-service"    # WSL Pro service
      - "cron"               # Cron daemon
      - "rsyslogd"           # Syslog daemon
      - "agetty"             # Getty processes
      - "polkitd"            # PolicyKit daemon
      - "unattended-upgr"    # Unattended upgrades
      - "login"              # Login processes
      - "sudo"               # Sudo processes (exclude parent processes)
  
  # Path-based filtering
  path_filters:
    # Include only these binary paths (empty = all paths, supports glob patterns)
    include_paths: []
    
    # Exclude these binary paths (supports glob patterns)
    exclude_paths:
      - "/init"                    # Init process
      - "/usr/lib/*"               # System libraries
      - "/usr/bin/*"               # System binaries
      - "/usr/sbin/*"              # System admin binaries
      - "/usr/libexec/*"           # System executables
      - "/lib/*"                   # Core libraries
      - "/lib64/*"                 # 64-bit libraries
      - "/sbin/*"                  # System binaries
      - "/usr/local/bin/redis*"    # Redis binaries
      - "/usr/bin/udevadm"         # udev admin tool (causes attachment spam)
    
    # Exclude these directories (exact prefix match)
    exclude_directories:
      - "/init"
      - "/usr/lib"
      - "/usr/bin"
      - "/usr/sbin"
      - "/usr/libexec"
      - "/lib"
      - "/lib64"
      - "/sbin"
  
  # System-level filtering
  system_filters:
    skip_kernel_threads: true      # Skip kernel threads (PID < 2)
    skip_system_processes: true    # Skip known system processes
    skip_container_runtime: true   # Skip Docker/containerd processes
    skip_self_processes: true      # Skip our own processes and parents

# Sampling configuration
sampling:
  enabled: false                  # Disable sampling for deterministic tracing
  rate: 1.0                      # Sample rate (1.0 = no sampling)
  max_events_per_second: 0       # Max events per second (0 = no limit)
  adaptive_sampling: false       # Disable adaptive sampling

# Output configuration
output:
  format: "human"                # Output format: human, json, csv
  destination: "stdout"          # Output destination: stdout, file
  buffer_size: 1024             # Buffer size for output
  flush_interval: "5s"          # Flush interval for buffered output

# Performance tuning
performance:
  ring_buffer_size: 262144      # eBPF ring buffer size
  worker_threads: 4             # Number of worker threads
  batch_size: 100               # Batch size for processing
  memory_limit: "100MB"         # Memory limit
  cpu_limit: "500m"             # CPU limit

# Security settings
security:
  drop_privileges: false        # Drop privileges after initialization
  user: "root"                  # User to run as
  group: "root"                 # Group to run as
  capabilities:                 # Required capabilities
    - "SYS_ADMIN"
    - "BPF"
    - "PERFMON"

# Debugging and monitoring
debug:
  enabled: false                # Enable debug mode
  profile_cpu: false           # Enable CPU profiling
  profile_memory: false        # Enable memory profiling
  metrics_port: 8080           # Metrics endpoint port
  health_check_port: 8081      # Health check endpoint port

# Integration settings
integration:
  vector:
    enabled: false             # Enable Vector integration
    endpoint: "http://localhost:8686"
    batch_size: 1000
    flush_interval: "10s"
  
  otlp:
    enabled: false             # Enable OTLP export
    endpoint: "http://localhost:4317"
    headers: {}
    timeout: "30s"
  
  prometheus:
    enabled: false             # Enable Prometheus metrics
    port: 9090
    path: "/metrics"
